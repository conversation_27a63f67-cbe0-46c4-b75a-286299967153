# Dependencies
/node_modules
/.pnp
.pnp.js

node_modules



# Testing
/coverage

# Production
/build
/dist

# Development
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# TypeScript cache
*.tsbuildinfo

# Next.js
.next/
out/

# Vercel
.vercel

# PWA files
public/sw.js
public/workbox-*.js