# PTE Cross-Sectional Scoring Implementation

## Overview
This implementation follows the official PTE Academic scoring methodology with cross-sectional weightage distribution as specified in the PTE scoring rules document.

## Key Features

### 1. Cross-Sectional Scoring Matrix
Questions contribute to multiple skills according to official PTE weightage:

#### High Impact Cross-Sectional Questions:
- **Read Aloud**: 60% Speaking + 40% Reading
- **Repeat Sentence**: 70% Speaking + 30% Listening  
- **Re-tell Lecture**: 50% Speaking + 50% Listening
- **Reading & Writing Fill in Blanks**: 50% Reading + 50% Writing
- **Summarize Spoken Text**: 60% Listening + 40% Writing

#### Medium Impact Cross-Sectional Questions:
- **Summarize Written Text**: 60% Writing + 40% Reading
- **Listening Fill in Blanks**: 70% Listening + 30% Writing
- **Write from Dictation**: 60% Listening + 40% Writing

#### Single Skill Questions:
- **Describe Image**: 100% Speaking
- **Write Essay**: 100% Writing
- **Multiple Choice Questions**: 100% respective section
- **Re-order Paragraphs**: 100% Reading

### 2. Score Calculation
- Raw scores are converted to percentages
- Cross-sectional weightage is applied
- Scores are scaled to 10-90 range (PTE standard)
- Overall score uses complex algorithmic weighting (not simple average)

### 3. Implementation Files

#### Core Files:
- `src/utils/pteScoring.js` - Main scoring engine
- `src/components/speech-ace-component/ResultSection.jsx` - Section-level scoring
- `src/components/speech-ace-component/MockTestResult.jsx` - Overall test results

#### Test Files:
- `src/utils/pteScoring.test.js` - Validation tests

## Usage

### Basic Usage:
```javascript
import { PTECrossSectionalScoring } from '../utils/pteScoring';

const pteScoring = new PTECrossSectionalScoring();

// Calculate cross-sectional scores for all questions
const crossSectionalScores = pteScoring.aggregateScores(questionResults);

// Calculate overall score
const overallScore = pteScoring.calculateOverallScore(crossSectionalScores);
```

### Question Result Format:
```javascript
const questionResult = {
  type: "read-aloud",        // Question type (must match scoring matrix)
  score: 80,                 // Raw score achieved
  maxScore: 100,             // Maximum possible score
  section: "speaking"        // Original section
};
```

## Supported Question Types

### Speaking Section:
- read-aloud / read aloud
- repeat-sentence / repeat sentence  
- describe-image / describe image
- re-tell-lecture / retell-lecture / retell lecture
- answer-short-question / answer short question
- respond-to-situation / respond to situation

### Writing Section:
- summarize-written-text / summarize written text
- write-essay / essay-writing / essay writing
- write-email / write email

### Reading Section:
- reading-writing-fill-in-the-blanks / reading & writing：fill in the blanks
- multiple-choice-multiple / multiple choice (multiple)
- re-order-paragraphs / reorder-paragraphs / re-order paragraphs
- reading-fill-in-blanks / reading：fill in the blanks / reading fill in the blanks
- multiple-choice-single / multiple choice (single)
- multi-single, multi-multi, fill-the-blanks

### Listening Section:
- summarize-spoken-text / summarize spoken text
- listening-fill-in-blanks / listening：fill in the blanks / fill-in-blanks
- highlight-correct-summary / highlight correct summary
- select-missing-word / select missing word / missing-word
- highlight-incorrect-words / highlight incorrect words / highlight-words
- write-from-dictation / write from dictation
- multi-choice

## Validation

### Test the Implementation:
```javascript
// In browser console:
import { testPTEScoring } from '../utils/pteScoring.test.js';
testPTEScoring();
```

### Expected Behavior:
1. Cross-sectional scores should reflect proper weightage distribution
2. Overall scores should be in 10-90 range
3. All question types should be recognized and scored appropriately
4. Scores should aggregate correctly across sections

## Benefits

1. **Accurate PTE Simulation**: Matches official scoring methodology
2. **Cross-Sectional Awareness**: Shows how questions contribute to multiple skills
3. **Proper Weightage**: High-impact questions have appropriate influence
4. **Scalable**: Easy to add new question types
5. **Transparent**: Clear breakdown of score contributions

## Future Enhancements

1. **Enabling Skills Integration**: Add grammar, fluency, pronunciation sub-scores
2. **Difficulty Weighting**: Adjust scores based on question difficulty
3. **Time-based Penalties**: Implement time-based score adjustments
4. **Advanced Analytics**: Detailed performance insights and recommendations
