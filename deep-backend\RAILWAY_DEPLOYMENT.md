# Railway Deployment Guide

This guide covers deploying the Deep Insight Backend to Railway with full FFmpeg support.

## Why Railway?

- ✅ **FFmpeg Support**: Native FFmpeg binary support for audio compression
- ✅ **Persistent Storage**: Better for file processing workflows
- ✅ **Longer Request Timeouts**: Up to 10 minutes vs Vercel's 10 seconds
- ✅ **Traditional Server Environment**: Better for complex audio processing tasks

## Prerequisites

1. Railway account: [https://railway.app](https://railway.app)
2. GitHub repository connected
3. Environment variables configured

## Deployment Steps

### 1. Connect Repository to Railway

1. Go to [Railway Dashboard](https://railway.app/dashboard)
2. Click "New Project"
3. Select "Deploy from GitHub repo"
4. Choose your `deep-backend` repository
5. Railway will auto-detect the Node.js environment

### 2. Environment Variables

Set these in Railway dashboard under "Variables":

```env
NODE_ENV=production
SPEECH_ACE_API_KEY=your_speech_ace_key_here
OPENAI_API_KEY=your_openai_key_here
PORT=3000
```

### 3. Domain Configuration

1. Railway will provide a domain like `your-app-name.railway.app`
2. Update your frontend to use the Railway URL:
   ```javascript
   const API_BASE_URL = "https://your-app-name.railway.app";
   ```

### 4. FFmpeg Verification

Railway includes FFmpeg by default. You can verify it's working by:

1. Check deployment logs for: `🎵 FFmpeg support enabled for Railway deployment`
2. Test audio compression endpoints
3. Monitor compression performance in logs

## Configuration Files

- `railway.toml` - Railway configuration
- `nixpacks.toml` - Build configuration with FFmpeg
- `Dockerfile` - Alternative deployment method
- `package.json` - Updated with Railway-compatible scripts

## Monitoring

### Health Check

- Endpoint: `https://your-app-name.railway.app/api/test`
- Should return: `{"message": "Backend is working!"}`

### Audio Processing Test

- Upload an audio file to any speech analysis endpoint
- Check logs for compression method used:
  - `Railway environment detected, preferring FFmpeg compression`
  - Look for compression ratios in logs

## Advantages over Vercel

| Feature           | Railway         | Vercel           |
| ----------------- | --------------- | ---------------- |
| FFmpeg Support    | ✅ Native       | ❌ Not available |
| Request Timeout   | 10 minutes      | 10 seconds       |
| File Processing   | ✅ Full support | ⚠️ Limited       |
| Audio Compression | ✅ High quality | ⚠️ Basic only    |
| Memory Limits     | Higher          | Lower            |
| Cold Starts       | Minimal         | More frequent    |

## Troubleshooting

### Build Issues

- Check `railway.toml` configuration
- Verify `nixpacks.toml` has FFmpeg listed
- Check build logs for dependency installation

### Runtime Issues

- Check environment variables are set
- Verify Railway domain is in CORS configuration
- Monitor application logs for FFmpeg errors

### Performance Optimization

- Railway automatically scales based on usage
- Monitor memory usage during audio processing
- Adjust compression settings if needed

## Cost Considerations

Railway offers:

- **Hobby Plan**: $5/month with generous limits
- **Pro Plan**: $20/month for production workloads
- **Free Tier**: 500 hours/month (limited resources)

## Next Steps

1. Deploy to Railway using the provided configuration
2. Test all audio processing endpoints
3. Update frontend to use Railway domain
4. Monitor performance and adjust as needed
5. Set up custom domain if required

## Support

For Railway-specific issues:

- Railway Documentation: [https://docs.railway.app](https://docs.railway.app)
- Railway Discord: [https://discord.gg/railway](https://discord.gg/railway)
