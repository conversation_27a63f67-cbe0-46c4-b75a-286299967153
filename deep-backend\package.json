{"name": "deep-backend", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node --watch server.js", "test": "echo \"Error: no test specified\" && exit 1", "install-ffmpeg": "echo 'Please install FFmpeg on your system: https://ffmpeg.org/download.html'"}, "dependencies": {"axios": "^1.7.7", "compression": "^1.7.5", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.1", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.1", "multer": "^1.4.5-lts.1", "natural": "^8.0.1", "openai": "^4.73.0"}, "engines": {"node": ">=18.0.0"}, "description": "Backend server for Deep Insight Academy with audio processing and AI scoring capabilities", "keywords": ["audio", "speech-analysis", "ai", "education", "pte"], "author": "Deep Insight Academy", "license": "ISC"}