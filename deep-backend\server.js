import express from "express";
import cors from "cors";
import multer from "multer";
import dotenv from "dotenv";
import { analyzeSpeech, analyzePTETask } from "./speechAceService.js";
import OpenAI from "openai";
import compression from "compression";
import axios from "axios";
import process from "process";

dotenv.config();

// OpenAI Model Configuration - Using the best available models
const OPENAI_MODELS = {
  // GPT-4o: Most capable model for complex reasoning and analysis
  PREMIUM: "gpt-4o",
  // GPT-4o-mini: Fast and efficient for simpler tasks
  EFFICIENT: "gpt-4o-mini",
  // GPT-4-turbo: Alternative for complex tasks if GPT-4o is unavailable
  FALLBACK: "gpt-4-turbo",
};

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const app = express();
app.use(compression());

// Add body parsing middleware with increased limits for audio
app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ extended: true, limit: "50mb" }));

// Configure CORS
app.use(
  cors({
    origin: [
      "http://localhost:5173",
      "https://api.deepinsightacademy.com",
      "https://deep-insight-customer-v1.vercel.app",
      "http://**********",

      "http://127.0.0.1:5500",
      "https://www.deepinsightacademy.com",
      // Allow Railway domain (will be dynamically set)
      process.env.RAILWAY_STATIC_URL || "https://*.railway.app",
      "https://deepinsightacademy.com"
    ],
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "Content-Length"],
  })
);

// Explicitly handle preflight requests for all routes
app.options("*", cors());

// Enhanced multer configuration for audio uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // Increased to 50MB limit
    fieldSize: 50 * 1024 * 1024, // Increased field size limit
  },
  fileFilter: (req, file, cb) => {
    // Accept audio files
    if (
      file.mimetype.startsWith("audio/") ||
      file.mimetype === "application/octet-stream"
    ) {
      cb(null, true);
    } else {
      cb(new Error("Only audio files are allowed"), false);
    }
  },
});

// Add request validation middleware with better error handling
const validateAudioRequest = (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        error: "No audio file provided",
        details: "The request must include an audio file",
        code: "MISSING_AUDIO_FILE",
      });
    }

    // Check for text parameter in read-aloud requests
    if (req.path === "/api/speech-analysis" && !req.body.text) {
      return res.status(400).json({
        error: "No text provided",
        details: "The request must include text to analyze",
        code: "MISSING_TEXT",
      });
    }

    // Validate audio file format and size
    const fileSize = req.file.size;
    const maxSize = 50 * 1024 * 1024; // 50MB

    if (fileSize > maxSize) {
      return res.status(413).json({
        error: "File too large",
        details: `Audio file must be less than ${
          maxSize / 1024 / 1024
        }MB. Current size: ${(fileSize / 1024 / 1024).toFixed(2)}MB`,
        code: "FILE_TOO_LARGE",
      });
    }

    // Log file details for debugging
    console.log("Audio file validation passed:", {
      originalName: req.file.originalname,
      mimetype: req.file.mimetype,
      size: `${(fileSize / 1024 / 1024).toFixed(2)}MB`,
      fieldname: req.file.fieldname,
    });

    next();
  } catch (error) {
    console.error("Audio validation error:", error);
    res.status(400).json({
      error: "Audio validation failed",
      details: error.message,
      code: "VALIDATION_ERROR",
    });
  }
};

// Function to ensure all required fields are present (currently not used but kept for future use)
/* function ensureRequiredFields(data, summaryText) {
  const fixed = { ...data };

  // Top-level maxScore should be 8 (sum of all individual maxScores)
  if (!Object.prototype.hasOwnProperty.call(fixed, "maxScore")) {
    fixed.maxScore = 8;
    console.log("Added missing maxScore field");
  }

  // Make sure totalScore exists and is accurate
  if (!Object.prototype.hasOwnProperty.call(fixed, "totalScore")) {
    // Calculate from individual scores
    const scores = ["content", "form", "grammar", "vocabulary"].map(
      (field) => fixed[field]?.score || 0
    );
    fixed.totalScore = scores.reduce((sum, score) => sum + score, 0);
    console.log("Added missing totalScore field");
  }

  // Make sure wordCount exists
  if (!Object.prototype.hasOwnProperty.call(fixed, "wordCount")) {
    // This will be overwritten with actual word count later
    // Calculate the actual word count from the text
    const actualWordCount = fixed.text
      ? fixed.text.trim().split(/\s+/).length
      : 0;
    fixed.wordCount = actualWordCount;
    console.log("Added missing wordCount field");
  }

  // Make sure annotatedText exists
  if (!Object.prototype.hasOwnProperty.call(fixed, "annotatedText")) {
    fixed.annotatedText = summaryText || "";
    console.log("Added missing annotatedText field");
  }

  // Check each scoring category
  ["content", "form", "grammar", "vocabulary"].forEach((category) => {
    if (!Object.prototype.hasOwnProperty.call(fixed, category)) {
      fixed[category] = {
        score: 1,
        maxScore: 2,
        suggestion: `${category} evaluation missing from API response`,
      };
      console.log(`Added missing ${category} category`);
    } else {
      // Make sure each category has all its required fields
      if (!Object.hasOwn(fixed[category], "score")) {
        fixed[category].score = 1;
        console.log(`Added missing score in ${category}`);
      }
      if (!Object.hasOwn(fixed[category], "maxScore")) {
        fixed[category].maxScore = 2;
        console.log(`Added missing maxScore in ${category}`);
      }
      if (!Object.hasOwn(fixed[category], "suggestion")) {
        fixed[category].suggestion = "";
        console.log(`Added missing suggestion in ${category}`);
      }
    }
  });

  return fixed;
} */

// Test endpoint
app.get("/answers", (req, res) => {
  console.log("GET /answers request received:", req.headers); // Debug log
  res.json({ message: "Answers endpoint working!" });
});
app.post("/api/email-scoring", async (req, res) => {
  try {
    // Debug log to see the entire request body
    console.log("Received complete request body:", JSON.stringify(req.body));

    // Properly extract the text field from the request body
    const text = req.body.text;
    const prompt = req.body.prompt || req.body.question || "";
    const user_id = req.body.user_id || "anonymous";

    // Add additional validation
    if (!text || typeof text !== "string" || text.trim() === "") {
      console.log("Text validation failed:", { text });
      return res.status(400).json({
        error: "Missing required fields",
        details: "Email text is required",
      });
    }

    console.log("Processing email scoring request:", {
      textLength: text.length,
      promptLength: prompt?.length || 0,
      userId: user_id,
    });

    // Call OpenAI for scoring with fallback prompt if needed
    const defaultPrompt = "Write an email addressing a formal situation";
    const result = await scoreEmailWithAI(text, prompt || defaultPrompt);

    // Send the result
    res.json(result);
  } catch (error) {
    console.error("Error processing email scoring:", {
      message: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      error: "Email scoring failed",
      details: error.message || "Unknown error occurred",
    });
  }
});

// Function to score email using OpenAI
async function scoreEmailWithAI(emailText, promptText) {
  // Calculate word count here for accurate validation in post-processing
  const wordCount = emailText.trim().split(/\s+/).length;

  // Create a more robust system prompt for OpenAI to score the email
  const systemPrompt = `
You are an expert English language assessor specializing in email writing evaluation for standardized tests like PTE, IELTS, and TOEFL.

CRITICAL SCORING RULES:
1. If the email content is COMPLETELY IRRELEVANT to the prompt or doesn't address the main requirements, ALL SCORES MUST be ZERO (0) for ALL criteria. This is a critical rule.
2. If the email lacks proper structure (no proper greeting/salutation, body paragraphs, and closing), the Form score MUST be 0 and severely impact Organization score.
3. If the email is less than 30 or more than 140 words, ALL scores must be 0 across all categories (Content, Form, Email Convention, Organization, Grammar, Spelling, Vocabulary). This is a critical rule.

Evaluate the following email based on these criteria:
1. Content (score out of 3): 
   - 0: Completely irrelevant or missing required elements or Does not properly deal with the task; the task and/or topics may have been largely misunderstood. May not deal properly with the prompt due to significant amounts of pre-prepared/memorized material
   - 1: Partially addresses the prompt with major omissions or Attempts to address the task but is not successful; the task and/ or topics may have been misunderstood
   - 2: Addresses most requirements with minor omissions or Addresses the task with some success, and demonstrates some understanding of the task
   - 3: Fully addresses all requirements with appropriate detail or Addresses the requirements of the task sufficiently and appropriately

2. Form (score out of 2):
   - 0: Contains fewer than 30 words, or more than 140 words. The response is written in capital letters, and/or contains no punctuation
   - 1: Contains 30-49 words or 121-140 words
   - 2: Contains 50-120 words

3. Email Conventions (score out of 2):
   - 0: Completely missing both salutation/greeting AND closing/sign-off, OR uses highly inappropriate conventions that violate professional email standards and Email conventions are limited or missing
   - 1: Contains either greeting OR closing but not both; OR both are present but with noticeable errors (incorrect format, overly casual for formal context, spelling errors in names/titles) and Email conventions are used inconsistently with elements missing and/or used ineffectively (too brief or inappropriate register)
   - 2: Contains appropriate greeting AND closing that match the context/relationship specified in the prompt; uses correct punctuation and formatting for both and Email conventions are obvious, appropriate and used correctly in keeping with the format of the prompt

4. Organization (score out of 2):
   - 0: Disorganized, illogical sequence
   - 1: Some organization but lacks coherence/transitions
   - 2: Well-organized with logical flow and good transitions

5. Vocabulary (score out of 2):
   - 0: Very limited vocabulary with frequent errors in word choice, collocation, and usage
   - 1: Adequate vocabulary with some errors in word choice, collocation, or register
   - 2: Varied and appropriate vocabulary with minimal errors
   - IMPORTANT: Be extremely strict in vocabulary assessment! Check for:
     * Appropriate register (formal vs. informal language)
     * Correct collocations (words that naturally go together)
     * Precise word choice (not vague or overly general)
     * Range of vocabulary (repetition vs. variety)
     * Idiomatic usage
     * Topic-specific vocabulary appropriate to the context

6. Grammar (score out of 2):
   - 0: Frequent major grammatical errors that impede understanding
   - 1: Some grammatical errors that don't significantly impede understanding
   - 2: Few or no grammatical errors
   - IMPORTANT: Be extremely strict in grammar assessment! Check for:
     * Subject-verb agreement
     * Tense consistency and appropriate usage
     * Article usage (a, an, the)
     * Preposition usage
     * Pronoun reference and agreement
     * Sentence structure (fragments, run-ons)
     * Modal verb usage
     * Plural/singular forms
     * Conditional structures
     * Passive/active voice appropriateness

7. Spelling (score out of 2):
   - 0: Frequent spelling errors (more than 5 errors)
   - 1: Some spelling errors (2-5 errors)
   - 2: Few or no spelling errors (0-1 errors)
   - IMPORTANT: Be extremely strict in spelling assessment! Check for:
     * Proper nouns correctly capitalized
     * Common words spelled correctly
     * Contractions formed correctly
     * British vs. American spelling consistency
     * Hyphenation used correctly
     * Commonly confused words (their/there/they're, your/you're, etc.)

The email has ${wordCount} words and was written in response to this prompt: "${promptText}"

PRE-SCORING CHECK:
Before assigning any scores, determine:
1. Is the content relevant to the prompt? (Yes/No)
2. Does it follow basic email structure? (Yes/No)
3. Is the word count between 30 and 140 words inclusive? (Yes/No)

If the answer to question 1 is "No", ALL scores must be 0 for ALL categories.
If the answer to question 2 is "No", Form score must be 0.
If the answer to question 3 is "No", ALL scores must be 0 for ALL categories.

Provide your assessment in this JSON format:
{
  "relevanceCheck": {
    "isRelevant": true/false,
    "explanation": "<detailed explanation of relevance assessment>"
  },
  "structureCheck": {
    "hasProperStructure": true/false,
    "explanation": "<detailed explanation of structure assessment>"
  },
  "wordCountCheck": {
    "isWithinLimits": true/false,
    "wordCount": ${wordCount},
    "explanation": "<explanation of word count assessment>"
  },
  "content": {
    "score": <number>,
    "maxScore": 3,
    "suggestion": "<brief feedback with specific examples>"
  },
  "form": {
    "score": <number>,
    "maxScore": 2,
    "suggestion": "<brief feedback>"
  },
  "conventions": {
    "score": <number>,
    "maxScore": 2,
    "suggestion": "<brief feedback with specific examples of proper/improper conventions>"
  },
  "organization": {
    "score": <number>,
    "maxScore": 2,
    "suggestion": "<brief feedback>"
  },
  "vocabulary": {
    "score": <number>,
    "maxScore": 2,
    "suggestion": "<brief feedback with specific examples of vocabulary issues or strengths>"
  },
  "grammar": {
    "score": <number>,
    "maxScore": 2,
    "suggestion": "<brief feedback with specific examples of grammar errors found>"
  },
  "spelling": {
    "score": <number>,
    "maxScore": 2,
    "suggestion": "<brief feedback with specific misspelled words listed>"
  },
  "totalScore": <sum of all scores>,
  "maxScore": 15,
  "overallFeedback": "<general assessment of the email with main strengths and areas for improvement>",
  "annotatedText": "<HTML formatted email with colored spans for errors/feedback>"
}

For "annotatedText", use the following precise color coding:
- <span style="color:red">text</span> for grammar errors (be specific about each error type)
- <span style="color:orange">text</span> for vocabulary errors (mark poor word choices, informal words, repetitions)
- <span style="color:blue">text</span> for spelling errors (mark each misspelled word)
- <span style="color:purple">text</span> for structural/convention issues (greeting, closing, etc.)
- <span style="color:green">text</span> for content/organization issues

IMPORTANT: 
- If content is irrelevant (relevanceCheck.isRelevant is false), set ALL scores to 0 and include a detailed explanation in ALL categories of why scores are 0 due to irrelevance.
- If word count is outside the 30-140 range (wordCountCheck.isWithinLimits is false), include a clear explanation in ALL categories that scores are 0 due to word count violation.
- Be extremely thorough and strict in your grammar, vocabulary, and spelling assessments.
`;

  try {
    const response = await openai.chat.completions.create({
      model: OPENAI_MODELS.PREMIUM, // Using GPT-4o for most accurate email scoring
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: emailText },
      ],
      response_format: { type: "json_object" },
      temperature: 0.1, // Very low temperature for consistent and accurate scoring
      max_tokens: 2500, // Increased for detailed feedback
    });

    // Parse the JSON response
    const scoreData = JSON.parse(response.choices[0].message.content);

    // Add word count information if not already included
    if (!scoreData.wordCountCheck) {
      scoreData.wordCountCheck = {
        isWithinLimits: wordCount >= 30 && wordCount <= 140,
        wordCount: wordCount,
        explanation:
          wordCount < 30
            ? `Email is too short (${wordCount} words). Minimum required is 30 words.`
            : wordCount > 140
            ? `Email is too long (${wordCount} words). Maximum allowed is 140 words.`
            : `Email length (${wordCount} words) is within acceptable limits (30-140 words).`,
      };
    }

    // ===== ENFORCEMENT OF CRITICAL RULES =====

    // 1. Apply word count rule - if outside limits, all scores are zero
    if (scoreData.wordCountCheck && !scoreData.wordCountCheck.isWithinLimits) {
      const wordCountMessage = scoreData.wordCountCheck.explanation;
      const failureReason = `All scores are 0: ${wordCountMessage}`;

      // Set all scores to zero
      scoreData.content.score = 0;
      scoreData.form.score = 0;
      scoreData.conventions.score = 0;
      scoreData.organization.score = 0;
      scoreData.vocabulary.score = 0;
      scoreData.grammar.score = 0;
      scoreData.spelling.score = 0;
      scoreData.totalScore = 0;

      // Update all suggestions to include the word count explanation
      scoreData.content.suggestion = failureReason;
      scoreData.form.suggestion = failureReason;
      scoreData.conventions.suggestion = failureReason;
      scoreData.organization.suggestion = failureReason;
      scoreData.vocabulary.suggestion = failureReason;
      scoreData.grammar.suggestion = failureReason;
      scoreData.spelling.suggestion = failureReason;

      if (!scoreData.overallFeedback) {
        scoreData.overallFeedback = failureReason;
      }

      // No need to apply other rules - word count failure overrides everything
      return scoreData;
    }

    // 2. If content is irrelevant, ALL scores are set to zero (new requirement)
    if (scoreData.relevanceCheck && !scoreData.relevanceCheck.isRelevant) {
      const irrelevanceReason = scoreData.relevanceCheck.explanation;
      const failureReason = `All scores are 0: Email content is irrelevant to the prompt. ${irrelevanceReason}`;

      // Set all scores to zero
      scoreData.content.score = 0;
      scoreData.form.score = 0;
      scoreData.conventions.score = 0;
      scoreData.organization.score = 0;
      scoreData.vocabulary.score = 0;
      scoreData.grammar.score = 0;
      scoreData.spelling.score = 0;
      scoreData.totalScore = 0;

      // Update all suggestions to include the irrelevance explanation
      scoreData.content.suggestion = failureReason;
      scoreData.form.suggestion = failureReason;
      scoreData.conventions.suggestion = failureReason;
      scoreData.organization.suggestion = failureReason;
      scoreData.vocabulary.suggestion = failureReason;
      scoreData.grammar.suggestion = failureReason;
      scoreData.spelling.suggestion = failureReason;

      if (!scoreData.overallFeedback) {
        scoreData.overallFeedback = failureReason;
      }

      return scoreData;
    }

    // 3. If structure is improper, ensure form score is 0
    if (
      scoreData.structureCheck &&
      !scoreData.structureCheck.hasProperStructure
    ) {
      scoreData.form.score = 0;

      // Make sure form suggestion explains the structure issue
      if (!scoreData.form.suggestion.includes("structure")) {
        scoreData.form.suggestion = `Score is 0: ${scoreData.structureCheck.explanation}`;
      }

      // Ensure organization score is penalized for structural issues
      if (scoreData.organization.score > 1) {
        scoreData.organization.score = 1;
        if (!scoreData.organization.suggestion.includes("structure")) {
          scoreData.organization.suggestion =
            "Score reduced due to structural issues: " +
            scoreData.organization.suggestion;
        }
      }
    }

    // ===== ENHANCED GRAMMAR/VOCABULARY/SPELLING VALIDATION =====

    // Validate grammar score - if suggestion mentions errors but score is 2, reduce to 1
    if (
      scoreData.grammar.score === 2 &&
      (scoreData.grammar.suggestion.toLowerCase().includes("error") ||
        scoreData.grammar.suggestion.toLowerCase().includes("incorrect") ||
        scoreData.grammar.suggestion.toLowerCase().includes("mistake"))
    ) {
      scoreData.grammar.score = 1;
      scoreData.grammar.suggestion =
        "Score reduced to 1: " + scoreData.grammar.suggestion;
    }

    // Validate vocabulary score - if suggestion mentions issues but score is 2, reduce to 1
    if (
      scoreData.vocabulary.score === 2 &&
      (scoreData.vocabulary.suggestion.toLowerCase().includes("limited") ||
        scoreData.vocabulary.suggestion.toLowerCase().includes("improve") ||
        scoreData.vocabulary.suggestion.toLowerCase().includes("repetit") ||
        scoreData.vocabulary.suggestion
          .toLowerCase()
          .includes("could be better"))
    ) {
      scoreData.vocabulary.score = 1;
      scoreData.vocabulary.suggestion =
        "Score reduced to 1: " + scoreData.vocabulary.suggestion;
    }

    // // Validate spelling score - if suggestion mentions spelling errors but score is 2, reduce to 1
    // if (
    //   scoreData.spelling.score === 2 &&
    //   (scoreData.spelling.suggestion.toLowerCase().includes("error") ||
    //     scoreData.spelling.suggestion.toLowerCase().includes("incorrect") ||
    //     scoreData.spelling.suggestion.toLowerCase().includes("misspell"))
    // ) {
    //   scoreData.spelling.score = 1;
    //   scoreData.spelling.suggestion =
    //     "Score reduced to 1: " + scoreData.spelling.suggestion;
    // }

    // Recalculate total score based on all the updated scores
    scoreData.totalScore =
      scoreData.content.score +
      scoreData.form.score +
      scoreData.conventions.score +
      scoreData.organization.score +
      scoreData.vocabulary.score +
      scoreData.grammar.score +
      scoreData.spelling.score;

    // Add overall feedback if missing
    if (!scoreData.overallFeedback) {
      const totalPossible = 15;
      const percentage = Math.round(
        (scoreData.totalScore / totalPossible) * 100
      );

      let assessmentLevel;
      if (percentage >= 90) assessmentLevel = "excellent";
      else if (percentage >= 80) assessmentLevel = "very good";
      else if (percentage >= 70) assessmentLevel = "good";
      else if (percentage >= 60) assessmentLevel = "satisfactory";
      else if (percentage >= 50) assessmentLevel = "adequate";
      else assessmentLevel = "needs improvement";

      scoreData.overallFeedback = `This email demonstrates an ${assessmentLevel} level of writing (${scoreData.totalScore}/${totalPossible}, ${percentage}%). `;

      // Add strengths
      if (scoreData.content.score >= 2) {
        scoreData.overallFeedback +=
          "The content addresses the prompt effectively. ";
      }
      if (scoreData.grammar.score >= 1.5) {
        scoreData.overallFeedback += "Grammar usage is strong. ";
      }
      if (scoreData.vocabulary.score >= 1.5) {
        scoreData.overallFeedback += "Vocabulary is appropriate and varied. ";
      }

      // Add areas for improvement
      const improvements = [];
      if (scoreData.content.score < 2)
        improvements.push("addressing the prompt more completely");
      if (scoreData.form.score < 2) improvements.push("improving email format");
      if (scoreData.conventions.score < 2)
        improvements.push("using proper email conventions");
      if (scoreData.organization.score < 2)
        improvements.push("better organizing your ideas");
      if (scoreData.grammar.score < 1.5)
        improvements.push("correcting grammar errors");
      if (scoreData.vocabulary.score < 1.5)
        improvements.push("expanding vocabulary usage");
      if (scoreData.spelling.score < 1.5)
        improvements.push("checking spelling");

      if (improvements.length > 0) {
        scoreData.overallFeedback +=
          "Areas for improvement include: " + improvements.join(", ") + ".";
      }
    }

    // Final validation of the scores
    const expectedProperties = [
      "content",
      "form",
      "conventions",
      "organization",
      "vocabulary",
      "grammar",
      "spelling",
      "totalScore",
      "maxScore",
      "annotatedText",
    ];

    for (const prop of expectedProperties) {
      if (!Object.hasOwn(scoreData, prop)) {
        console.warn(
          `Missing property in AI response: ${prop}, adding default value.`
        );
        if (prop === "totalScore") {
          // Recalculate total score if missing
          scoreData.totalScore =
            (scoreData.content?.score || 0) +
            (scoreData.form?.score || 0) +
            (scoreData.conventions?.score || 0) +
            (scoreData.organization?.score || 0) +
            (scoreData.vocabulary?.score || 0) +
            (scoreData.grammar?.score || 0) +
            (scoreData.spelling?.score || 0);
        } else if (prop === "maxScore") {
          scoreData.maxScore = 15;
        } else if (prop === "annotatedText") {
          scoreData.annotatedText = emailText;
        } else {
          // Create a default category object if missing
          scoreData[prop] = {
            score: 0,
            maxScore: prop === "content" ? 3 : 2,
            suggestion: `No assessment provided for ${prop}`,
          };
        }
      }
    }

    return scoreData;
  } catch (error) {
    console.error("Error in email scoring:", error);
    throw new Error(`Failed to score email: ${error.message}`);
  }
}

app.post("/api/summarize-scoring", async (req, res) => {
  try {
    // Debug log to see the entire request body
    console.log("Received complete request body:", JSON.stringify(req.body));

    // Extract the summary text and original text from the request body
    const summaryText = req.body.text;
    const originalText = req.body.originalText || "";
    const prompt = req.body.prompt || "";
    const user_id = req.body.user_id || "anonymous";

    // Add validation
    if (
      !summaryText ||
      typeof summaryText !== "string" ||
      summaryText.trim() === ""
    ) {
      console.log("Text validation failed:", { summaryText });
      return res.status(400).json({
        error: "Missing required fields",
        details: "Summary text is required",
      });
    }

    console.log("Processing summary scoring request:", {
      textLength: summaryText.length,
      originalTextLength: originalText?.length || 0,
      promptLength: prompt?.length || 0,
      userId: user_id,
    });

    // Call OpenAI for scoring with the custom criteria
    const result = await scoreSummaryWithCustomCriteria(
      summaryText,
      originalText,
      prompt
    );

    // Send the result
    res.json(result);
  } catch (error) {
    console.error("Error processing summary scoring:", {
      message: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      error: "Summary scoring failed",
      details: error.message || "Unknown error occurred",
    });
  }
});

// Function to score summary using OpenAI
async function scoreSummaryWithCustomCriteria(
  summaryText,
  originalText,
  promptText
) {
  const actualWordCount = summaryText.trim().split(/\s+/).length;

  // Calculate form score based on word count
  let correctFormScore = 2;
  if (actualWordCount < 25 || actualWordCount > 50) {
    const deviation = Math.min(
      Math.abs(actualWordCount - 25),
      Math.abs(actualWordCount - 50)
    );

    if (deviation > 10) correctFormScore = 0;
    else if (deviation > 5) correctFormScore = 1;
    else correctFormScore = 1.5;
  }

  const systemPrompt = `
You are an expert English language assessor specializing in summary writing evaluation for standardized tests like PTE. You must be extremely strict and accurate in identifying grammatical and vocabulary errors.

Evaluate the following summary based on exactly these four criteria:
1. Content (score out of 2): 
   - Score 2: Contains all main points from the original text with excellent relevance
   - Score 1: Contains some main points but misses key information
   - Score 0: Missing most main points or completely irrelevant
   
2. Form (score out of 2): 
   - Score 2: Perfect length (25-50 words) with appropriate structure and paragraph organization
   - Score 1: Within 5 words of the required range (20-24 or 51-55 words) with good structure
   - Score 1.5: Within 5-10 words of the required range (15-19 or 56-60 words) or has minor structure issues
   - Score 0: More than 10 words outside the required range (<15 or >60 words) or major structure problems
   
3. Grammar (score out of 2): 
   - Score 2: No grammatical errors, excellent sentence structure
   - Score 1: Minor grammatical errors that don't impede understanding
   - Score 0: Severe grammatical errors that make comprehension difficult
   - IMPORTANT: Be extremely strict! Check for subject-verb agreement, tense consistency, article usage, prepositions, conjunctions, run-on sentences, fragments, and sentence structure. Mark any deviation from standard English grammar.
   
4. Vocabulary (score out of 2): 
   - Score 2: Excellent word choice, effective paraphrasing, appropriate academic vocabulary
   - Score 1: Some appropriate vocabulary but limited range or minor word choice issues
   - Score 0: Poor word choice, repetitive language, inappropriate register
   - IMPORTANT: Be extremely strict! Check for word collocation, word form, register, precision, and appropriateness. Mark any use of informal language, repetitive vocabulary, or incorrect word forms.

${
  originalText
    ? 'The summary was written based on this original text: "' +
      originalText +
      '"'
    : ""
}
${
  promptText
    ? 'The summary was written in response to this prompt: "' + promptText + '"'
    : ""
}

Provide your assessment in this JSON format:
{
  "content": { 
    "score": <number>, 
    "maxScore": 2, 
    "suggestion": "<brief feedback>" 
  },
  "form": { 
    "score": <number>, 
    "maxScore": 2, 
    "suggestion": "<brief feedback>" 
  },
  "grammar": { 
    "score": <number>, 
    "maxScore": 2, 
    "suggestion": "<brief feedback with specific error examples>" 
  },
  "vocabulary": { 
    "score": <number>, 
    "maxScore": 2, 
    "suggestion": "<brief feedback with specific word choice examples>" 
  },
  "totalScore": <sum of all scores>,
  "maxScore": 8,
  "annotatedText": "<HTML formatted summary with colored spans for errors/feedback>",
  "wordCount": <number of words in the summary>
}

For "annotatedText", use the following color coding:
- <span style="color:red">text</span> for grammar errors (be detailed with these - mark every single error)
- <span style="color:orange">text</span> for vocabulary issues (mark poor word choices, informal words, repetitions)
- <span style="color:blue">text</span> for form issues (including length problems)
- <span style="color:green">text</span> for content issues
`;

  // Helper function to ensure all required fields are present
  function ensureRequiredFields(data) {
    if (!data) data = {};

    // Ensure all component scores exist
    const components = ["content", "form", "grammar", "vocabulary"];
    for (const component of components) {
      if (!data[component]) {
        data[component] = {
          score: component === "form" ? correctFormScore : 1,
          maxScore: 2,
          suggestion: `Default ${component} evaluation`,
        };
      }
    }

    // Calculate total score if missing
    if (!data.totalScore) {
      data.totalScore =
        (data.content.score || 0) +
        (data.form.score || 0) +
        (data.grammar.score || 0) +
        (data.vocabulary.score || 0);
    }

    // Add other required fields
    if (!data.maxScore) data.maxScore = 8;
    if (!data.annotatedText) data.annotatedText = summaryText;
    if (!data.wordCount) data.wordCount = actualWordCount;

    return data;
  }

  try {
    console.log("Making API request to OpenAI...");

    const response = await openai.chat.completions.create({
      model: OPENAI_MODELS.EFFICIENT, // Using GPT-4o-mini for efficient single sentence scoring
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: summaryText },
      ],
      temperature: 0.1, // Very low temperature for consistent scoring
      max_tokens: 1500, // Adequate for single sentence feedback
    });

    console.log("OpenAI API Response received successfully");

    let content = response.choices[0]?.message?.content?.trim();

    if (!content) {
      throw new Error("Empty response received from OpenAI API");
    }

    console.log("Processing API response content...");

    // Try to extract JSON from the content
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      content = jsonMatch[0];
    }

    let scoreData;
    try {
      scoreData = JSON.parse(content);
    } catch (parseError) {
      console.error("Failed to parse JSON from API response:", parseError);
      console.error("Raw content:", content);
      throw new Error("Failed to parse scoring data from API response");
    }

    // Check and fix missing fields instead of throwing an error
    const requiredKeys = [
      "content",
      "form",
      "grammar",
      "vocabulary",
      "totalScore",
      "maxScore",
      "annotatedText",
    ];

    let missingKeys = [];
    for (const key of requiredKeys) {
      if (!Object.hasOwn(scoreData, key)) {
        missingKeys.push(key);
      }
    }

    if (missingKeys.length > 0) {
      console.log(`Missing fields in API response: ${missingKeys.join(", ")}`);
      scoreData = ensureRequiredFields(scoreData);
    }

    // Ensure form score is accurate based on word count
    if (Math.abs(scoreData.form.score - correctFormScore) > 0.5) {
      console.log(
        `Correcting form score from ${scoreData.form.score} to ${correctFormScore}`
      );

      // Generate appropriate feedback
      let formFeedback;
      if (actualWordCount < 25) {
        formFeedback = `Too short (${actualWordCount} words). Required range is 25-50 words.`;
      } else if (actualWordCount > 50) {
        formFeedback = `Too long (${actualWordCount} words). Required range is 25-50 words.`;
      } else {
        formFeedback = `Good length (${actualWordCount} words), within the required 25-50 word range.`;
      }

      // Update score data
      const oldFormScore = scoreData.form.score;
      scoreData.form.score = correctFormScore;
      scoreData.form.suggestion = formFeedback;

      // Recalculate total score
      scoreData.totalScore =
        scoreData.totalScore - oldFormScore + correctFormScore;
    }

    return scoreData;
  } catch (error) {
    console.error("Error in scoring summary:", error.message);

    if (error.response) {
      console.error("OpenAI API Error Details:", {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
      });
    }

    // Instead of using fallback, throw a clear error to be handled by the caller
    throw new Error(`Failed to score summary: ${error.message}`);
  }
}

async function scoreEssayWithAI(essayText, promptText) {
  // Calculate actual word count for validation
  const actualWordCount = essayText.trim().split(/\s+/).length;

  // Calculate appropriate form score based on word count
  let correctFormScore = 2; // Start with max score

  if (actualWordCount < 200 || actualWordCount > 300) {
    // Outside the required range
    const deviation = Math.min(
      Math.abs(actualWordCount - 200),
      Math.abs(actualWordCount - 300)
    );

    // Reduce score based on how far outside the range
    if (deviation > 50) {
      correctFormScore = 0;
    } else if (deviation > 25) {
      correctFormScore = 0.5;
    } else {
      correctFormScore = 1;
    }
  }

  // Create system prompt for essay scoring with the 7 criteria
  const systemPrompt = `
You are an expert English language assessor specializing in essay evaluation for standardized tests like PTE, IELTS, and TOEFL.

CRITICAL SCORING RULES:
1. If the essay content is IRRELEVANT to the prompt or doesn't properly address the main requirements, ALL SCORES MUST be ZERO (0) for ALL criteria. This is a MANDATORY rule that cannot be overridden. Be extremely strict about this - even partial relevance is not sufficient.
2. If the essay is less than 150 or more than 350 words, the form score must be 0.

Evaluate the following essay based on these seven criteria:
1. Content (score out of 3): Relevance to the topic, quality and depth of ideas, appropriate examples
2. Form (score out of 2): Appropriate essay structure, meets 200-300 word target
   - Score 2: Perfect length (200-300 words) with appropriate structure
   - Score 1: Slightly outside word range or minor structure issues
   - Score 0: Far outside word range or major structure issues
3. Grammar (score out of 2): Grammatical accuracy, sentence structures
4. Spelling (score out of 2): Spelling and punctuation accuracy
5. Vocabulary Range (score out of 2): Diversity and appropriateness of vocabulary
6. General Linguistic Range (score out of 2): Variety of sentence structures and expressions
7. Development, Structure and Coherence (score out of 2): Organization, paragraph structure, logical flow, cohesion

The essay was written in response to this prompt: "${promptText}"
Time allowed: 20 minutes
Word count target: 200-300 words

PRE-SCORING CHECK:
Before assigning any scores, determine:
1. Is the content relevant to the prompt? (Yes/No)
2. Does it address the key requirements of the prompt? (Yes/No)
3. Is the word count between 150 and 350 words inclusive? (Yes/No)

If the answer to question 1 or 2 is "No", ALL scores must be 0 for ALL categories.

Provide your assessment in this JSON format:
{
  "relevanceCheck": {
    "isRelevant": true/false,
    "explanation": "<detailed explanation of relevance assessment>"
  },
  "content": { 
    "score": <number>, 
    "maxScore": 3, 
    "suggestion": "<brief feedback>" 
  },
  "form": { 
    "score": <number>, 
    "maxScore": 2, 
    "suggestion": "<brief feedback>" 
  },
  "grammar": { 
    "score": <number>, 
    "maxScore": 2, 
    "suggestion": "<brief feedback>" 
  },
  "spelling": { 
    "score": <number>, 
    "maxScore": 2, 
    "suggestion": "<brief feedback>" 
  },
  "vocabularyRange": { 
    "score": <number>, 
    "maxScore": 2, 
    "suggestion": "<brief feedback>" 
  },
  "generalLinguisticRange": { 
    "score": <number>, 
    "maxScore": 2, 
    "suggestion": "<brief feedback>" 
  },
  "developmentStructureCoherence": { 
    "score": <number>, 
    "maxScore": 2, 
    "suggestion": "<brief feedback>" 
  },
  "totalScore": <sum of all scores>,
  "maxScore": 15,
  "annotatedText": "<HTML formatted essay with colored spans for errors/feedback>",
  "wordCount": <number of words in essay>
}

For "annotatedText", wrap problematic parts in:
- <span style="color:red">error</span> for grammar errors
- <span style="color:orange">error</span> for vocabulary errors
- <span style="color:blue">error</span> for spelling errors
- <span style="color:green">error</span> for content/structure issues

IMPORTANT: 
- If content is irrelevant (relevanceCheck.isRelevant is false), set ALL scores to 0 and include a detailed explanation in ALL categories of why scores are 0 due to irrelevance.
- Word count must be 200-300 words. If outside this range, the form score should be reduced:
  - Within range (200-300): form score of 2
  - Slightly outside (175-199 or 301-325): form score of 1
  - Moderately outside (150-174 or 326-350): form score of 0.5
  - Far outside (<150 or >350): form score of 0
- Content should include clear opinion, relevant arguments, and appropriate examples
- Grammar must be accurate with a variety of structures
- Vocabulary should demonstrate range and accuracy
- Essay should have clear introduction, body paragraphs, and conclusion
`;

  try {
    // Call OpenAI API for scoring
    const response = await openai.chat.completions.create({
      model: OPENAI_MODELS.PREMIUM, // Using GPT-4o for most accurate essay scoring
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: essayText },
      ],
      response_format: { type: "json_object" },
      temperature: 0.1, // Very low temperature for consistent scoring
      max_tokens: 3000, // Increased for detailed feedback on longer essays
    });

    // Parse and validate response
    let scoreData;
    try {
      scoreData = JSON.parse(response.choices[0].message.content);
      console.log(
        "Raw AI response for essay scoring:",
        JSON.stringify(scoreData)
      );
    } catch (parseError) {
      console.error("Error parsing OpenAI response:", parseError);
      console.error("Raw response:", response.choices[0].message.content);
      throw new Error("Failed to parse AI scoring response");
    }

    // Ensure all required properties exist
    const requiredProperties = [
      "content",
      "form",
      "grammar",
      "spelling",
      "vocabularyRange",
      "generalLinguisticRange",
      "developmentStructureCoherence",
      "totalScore",
      "maxScore",
      "annotatedText",
      "wordCount",
    ];

    for (const prop of requiredProperties) {
      if (!Object.hasOwn(scoreData, prop)) {
        throw new Error(`Missing property in AI response: ${prop}`);
      }
    }

    // Set or correct word count
    scoreData.wordCount = actualWordCount;

    // Check for irrelevant content in multiple ways to ensure we catch it
    let isIrrelevant = false;
    let irrelevanceReason = "";

    // Method 1: Check the explicit relevanceCheck object if present
    if (
      scoreData.relevanceCheck &&
      scoreData.relevanceCheck.isRelevant === false
    ) {
      isIrrelevant = true;
      irrelevanceReason =
        scoreData.relevanceCheck.explanation ||
        "Content does not address the prompt";
      console.log("Detected irrelevant content via relevanceCheck object");
    }

    // Method 2: Check content score - if it's 0, it's likely irrelevant
    if (scoreData.content && scoreData.content.score === 0) {
      isIrrelevant = true;
      irrelevanceReason =
        scoreData.content.suggestion ||
        "Content score is 0, indicating irrelevance";
      console.log("Detected irrelevant content via zero content score");
    }

    // Method 3: Check for keywords in suggestions that indicate irrelevance
    if (scoreData.content && scoreData.content.suggestion) {
      const lowerSuggestion = scoreData.content.suggestion.toLowerCase();
      if (
        lowerSuggestion.includes("irrelevant") ||
        lowerSuggestion.includes("not related") ||
        lowerSuggestion.includes("off-topic") ||
        lowerSuggestion.includes("does not address") ||
        lowerSuggestion.includes("unrelated")
      ) {
        isIrrelevant = true;
        irrelevanceReason = scoreData.content.suggestion;
        console.log(
          "Detected irrelevant content via keyword matching in suggestion"
        );
      }
    }

    // Apply zero scores if content is irrelevant
    if (isIrrelevant) {
      const failureReason = `All scores are 0: Essay content is irrelevant to the prompt. ${irrelevanceReason}`;
      console.log(
        "Essay scored zero due to irrelevance detection, reason:",
        irrelevanceReason
      );

      // Set all scores to zero
      scoreData.content.score = 0;
      scoreData.form.score = 0;
      scoreData.grammar.score = 0;
      scoreData.spelling.score = 0;
      scoreData.vocabularyRange.score = 0;
      scoreData.generalLinguisticRange.score = 0;
      scoreData.developmentStructureCoherence.score = 0;
      scoreData.totalScore = 0;

      // Update all suggestions to include the irrelevance explanation
      scoreData.content.suggestion = failureReason;
      scoreData.form.suggestion = failureReason;
      scoreData.grammar.suggestion = failureReason;
      scoreData.spelling.suggestion = failureReason;
      scoreData.vocabularyRange.suggestion = failureReason;
      scoreData.generalLinguisticRange.suggestion = failureReason;
      scoreData.developmentStructureCoherence.suggestion = failureReason;

      // Make sure we have a relevanceCheck object for consistency
      if (!scoreData.relevanceCheck) {
        scoreData.relevanceCheck = {
          isRelevant: false,
          explanation: irrelevanceReason,
        };
      }

      return scoreData;
    }

    // Validate form score based on word count
    if (Math.abs(scoreData.form.score - correctFormScore) > 0.5) {
      console.log(
        `Correcting form score from ${scoreData.form.score} to ${correctFormScore}`
      );

      // Generate appropriate feedback
      let formFeedback;
      if (actualWordCount < 200) {
        formFeedback = `Too short (${actualWordCount} words). Required range is 200-300 words.`;
      } else if (actualWordCount > 300) {
        formFeedback = `Too long (${actualWordCount} words). Required range is 200-300 words.`;
      } else {
        formFeedback = `Good length (${actualWordCount} words), within the required 200-300 word range.`;
      }

      // Update score data
      const oldFormScore = scoreData.form.score;
      scoreData.form.score = correctFormScore;
      scoreData.form.suggestion = formFeedback;

      // Recalculate total score
      scoreData.totalScore =
        scoreData.totalScore - oldFormScore + correctFormScore;
    }

    return scoreData;
  } catch (error) {
    console.error("Error in AI scoring:", error);

    // Create a fallback response if OpenAI fails completely
    if (error.message.includes("OpenAI") || error.message.includes("parse")) {
      console.log("Using fallback scoring mechanism");

      // Format-specific feedback
      let formFeedback;
      if (actualWordCount < 200) {
        formFeedback = `Too short (${actualWordCount} words). Required range is 200-300 words.`;
      } else if (actualWordCount > 300) {
        formFeedback = `Too long (${actualWordCount} words). Required range is 200-300 words.`;
      } else {
        formFeedback = `Good length (${actualWordCount} words), within the required 200-300 word range.`;
      }

      // Calculate a basic score for other metrics (simplified)
      const basicScore = Math.min(Math.floor(actualWordCount / 100), 2);

      // Simple fallback scoring
      return {
        content: {
          score: Math.min(basicScore + 1, 3),
          maxScore: 3,
          suggestion: "Content evaluation unavailable - system fallback used.",
        },
        form: {
          score: correctFormScore,
          maxScore: 2,
          suggestion: formFeedback,
        },
        grammar: {
          score: 1,
          maxScore: 2,
          suggestion: "Grammar evaluation unavailable - system fallback used.",
        },
        spelling: {
          score: 1,
          maxScore: 2,
          suggestion: "Spelling evaluation unavailable - system fallback used.",
        },
        vocabularyRange: {
          score: 1,
          maxScore: 2,
          suggestion:
            "Vocabulary evaluation unavailable - system fallback used.",
        },
        generalLinguisticRange: {
          score: 1,
          maxScore: 2,
          suggestion:
            "Linguistic range evaluation unavailable - system fallback used.",
        },
        developmentStructureCoherence: {
          score: 1,
          maxScore: 2,
          suggestion:
            "Structure evaluation unavailable - system fallback used.",
        },
        totalScore: 6 + correctFormScore,
        maxScore: 15,
        annotatedText: essayText,
        wordCount: actualWordCount,
      };
    }

    // Re-throw other errors
    throw new Error(`Failed to score essay: ${error.message}`);
  }
}

// New endpoint for single-sentence summary scoring
app.post("/api/single-sentence-summary-scoring", async (req, res) => {
  try {
    // Debug log to see the entire request body
    console.log("Received complete request body:", JSON.stringify(req.body));

    // Extract the summary text and original text from the request body
    const summaryText = req.body.text;
    const originalText = req.body.originalText || "";
    const prompt = req.body.prompt || "";
    const user_id = req.body.user_id || "anonymous";

    // Add validation
    if (
      !summaryText ||
      typeof summaryText !== "string" ||
      summaryText.trim() === ""
    ) {
      console.log("Text validation failed:", { summaryText });
      return res.status(400).json({
        error: "Missing required fields",
        details: "Summary text is required",
      });
    }

    console.log("Processing single-sentence summary scoring request:", {
      textLength: summaryText.length,
      originalTextLength: originalText?.length || 0,
      promptLength: prompt?.length || 0,
      userId: user_id,
    });

    // Call OpenAI for scoring with the single-sentence criteria
    const result = await scoreSingleSentenceSummary(
      summaryText,
      originalText,
      prompt
    );

    // Send the result
    res.json(result);
  } catch (error) {
    console.error("Error processing single-sentence summary scoring:", {
      message: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      error: "Single-sentence summary scoring failed",
      details: error.message || "Unknown error occurred",
    });
  }
});

// Function to score single-sentence summary using OpenAI with validation
async function scoreSingleSentenceSummary(
  summaryText,
  originalText,
  promptText
) {
  function isSingleSentence(text) {
    const trimmed = text.trim();

    // Empty or too short text cannot be a valid sentence
    if (trimmed.length === 0 || trimmed.split(/\s+/).length < 2) return false;

    // Count sentence-ending punctuation
    const matches = trimmed.match(/[.!?]+/g);
    if (!matches) return false;

    // Only 1 terminal punctuation at the end and nowhere else
    const endsProperly = /[.!?]$/.test(trimmed);
    const onlyOneEnd = matches.length === 1;

    return endsProperly && onlyOneEnd;
  }

  const actualWordCount = summaryText.trim().split(/\s+/).length;
  const isSingle = isSingleSentence(summaryText);
  const correctFormScore =
    actualWordCount >= 5 && actualWordCount <= 75 && isSingle ? 1 : 0;

  // If form is incorrect, we'll set all scores to 0 later
  const formIsCorrect = correctFormScore === 1;

  const systemPrompt = `
You are an expert English language assessor specializing in summary writing evaluation for standardized tests like PTE. You must be extremely strict and accurate in identifying grammatical and vocabulary errors.

Evaluate the following summary based on exactly these four criteria:
1. Content (score out of 2): 
   - Score 2: Contains all main points from the original text with excellent relevance
   - Score 1: Contains some main points but misses key information
   - Score 0: Missing most main points or completely irrelevant
   
2. Form (BINARY score - EITHER 0 OR 1 ONLY): 
   - Score 1 ONLY IF: The summary is EXACTLY ONE SENTENCE (one period/full stop) AND between 5-75 words.
   - Score 0 OTHERWISE: If there are multiple sentences OR fewer than 5 words OR more than 75 words.
   
3. Grammar (score out of 2): 
   - Score 2: No grammatical errors, excellent sentence structure
   - Score 1: Minor grammatical errors that don't impede understanding
   - Score 0: Severe grammatical errors that make comprehension difficult
   - IMPORTANT: Be extremely strict! Check for subject-verb agreement, tense consistency, article usage, prepositions, conjunctions, and sentence structure. Mark any deviation from standard English grammar.
   
4. Vocabulary (score out of 2): 
   - Score 2: Excellent word choice, effective paraphrasing, appropriate academic vocabulary
   - Score 1: Some appropriate vocabulary but limited range or minor word choice issues
   - Score 0: Poor word choice, repetitive language, inappropriate register
   - IMPORTANT: Be extremely strict! Check for word collocation, word form, register, precision, and appropriateness. Mark any use of informal language, repetitive vocabulary, or incorrect word forms.

${
  originalText
    ? `The summary was written based on this original text: "${originalText}"`
    : ""
}
${
  promptText
    ? `The summary was written in response to this prompt: "${promptText}"`
    : ""
}

Respond in this JSON format:
{
  "content": { "score": <number>, "maxScore": 2, "suggestion": "<brief feedback>" },
  "form": { "score": <number>, "maxScore": 1, "suggestion": "<brief feedback>" },
  "grammar": { "score": <number>, "maxScore": 2, "suggestion": "<brief feedback>" },
  "vocabulary": { "score": <number>, "maxScore": 2, "suggestion": "<brief feedback>" },
  "totalScore": <sum of all scores>,
  "maxScore": 7,
  "annotatedText": "<HTML formatted summary with colored spans for errors/feedback>",
  "wordCount": <number of words in the summary>
}
`;

  try {
    const response = await openai.chat.completions.create({
      model: OPENAI_MODELS.EFFICIENT, // Using GPT-4o-mini for efficient single sentence scoring
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: summaryText },
      ],
      response_format: { type: "json_object" },
      temperature: 0.1, // Very low temperature for consistent scoring
      max_tokens: 1500, // Adequate for single sentence feedback
    });

    let scoreData;
    try {
      scoreData = JSON.parse(response.choices[0].message.content);
    } catch (parseError) {
      console.error("Failed to parse OpenAI response:", parseError);
      throw new Error("Invalid JSON format from AI");
    }

    // Ensure required categories
    const required = ["content", "form", "grammar", "vocabulary"];
    for (const cat of required) {
      if (!scoreData[cat] || typeof scoreData[cat].score !== "number") {
        throw new Error(`Missing or invalid ${cat} data`);
      }
      if (!scoreData[cat].maxScore) {
        scoreData[cat].maxScore = cat === "form" ? 1 : 2;
      }
    }

    // Fix form score if AI misjudged
    if (scoreData.form.score !== correctFormScore) {
      scoreData.form.score = correctFormScore;
      scoreData.form.suggestion =
        correctFormScore === 1
          ? "Meets single sentence and word count requirements."
          : `Form requirements not met: ${
              !isSingle
                ? "not a single sentence"
                : actualWordCount < 5
                ? "fewer than 5 words"
                : "more than 75 words"
            }`;
    }

    // IMPORTANT CHANGE: If form is incorrect, set all other scores to 0
    if (!formIsCorrect) {
      // Set all scores to 0 except form (which is already 0)
      scoreData.content.score = 0;
      scoreData.content.suggestion = "No scoring due to incorrect form.";
      scoreData.grammar.score = 0;
      scoreData.grammar.suggestion = "Wrong form. No scoring.";
      scoreData.vocabulary.score = 0;
      scoreData.vocabulary.suggestion = "Wrong form. No scoring.";
      scoreData.totalScore = 0; // Total score is zero
    } else {
      // If form is correct, calculate total score normally
      scoreData.totalScore =
        scoreData.content.score +
        scoreData.form.score +
        scoreData.grammar.score +
        scoreData.vocabulary.score;
    }

    // Ensure word count and maxScore
    scoreData.wordCount = actualWordCount;
    if (!scoreData.maxScore) scoreData.maxScore = 7;

    if (!scoreData.annotatedText) {
      scoreData.annotatedText = summaryText;
    }

    return scoreData;
  } catch (err) {
    console.error("Scoring failed:", err);
    // Fallback response
    const fallbackScores = {
      content: {
        score: formIsCorrect ? 1 : 0,
        maxScore: 2,
        suggestion: formIsCorrect
          ? "Fallback scoring: content partially meets expectations."
          : "No scoring due to incorrect form.",
      },
      form: {
        score: correctFormScore,
        maxScore: 1,
        suggestion:
          correctFormScore === 1
            ? "Meets format requirements."
            : `Format issue: ${
                !isSingle
                  ? "not a single sentence"
                  : actualWordCount < 5
                  ? "too short"
                  : "too long"
              }`,
      },
      grammar: {
        score: formIsCorrect ? 1 : 0,
        maxScore: 2,
        suggestion: formIsCorrect
          ? "Fallback: basic grammar is assumed acceptable."
          : "Wrong form. No scoring.",
      },
      vocabulary: {
        score: formIsCorrect ? 1 : 0,
        maxScore: 2,
        suggestion: formIsCorrect
          ? "Fallback: basic vocabulary assumed acceptable."
          : "Wrong form. No scoring.",
      },
      totalScore: formIsCorrect ? correctFormScore + 3 : 0,
      maxScore: 7,
      annotatedText: summaryText,
      wordCount: actualWordCount,
    };

    return fallbackScores;
  }
}

// Function to score Summarize Spoken Text using OpenAI with 5-criteria system
async function scoreSummarizeSpokenTextWithAI(
  summaryText,
  originalText,
  promptText
) {
  const actualWordCount = summaryText.trim().split(/\s+/).length;

  // Calculate form score based on word count (50-70 words for SST)
  let correctFormScore = 2;
  if (actualWordCount < 50 || actualWordCount > 70) {
    const deviation = Math.min(
      Math.abs(actualWordCount - 50),
      Math.abs(actualWordCount - 70)
    );

    if (deviation > 15) correctFormScore = 0;
    else if (deviation > 8) correctFormScore = 1;
    else correctFormScore = 1.5;
  }

  const systemPrompt = `
You are an expert English language assessor specializing in Summarize Spoken Text evaluation for PTE Academic tests.

Evaluate the following summary based on exactly these five criteria:
1. Content (score out of 2): 
   - Score 2: Contains all main points from the original audio with excellent relevance
   - Score 1: Contains some main points but misses key information  
   - Score 0: Missing most main points or completely irrelevant

2. Form (score out of 2): 
   - Score 2: Perfect length (50-70 words) with appropriate structure
   - Score 1: Within 8 words of the required range (42-49 or 71-78 words) with good structure
   - Score 0: More than 8 words outside the required range (<42 or >78 words) or major structure problems

3. Grammar (score out of 2): 
   - Score 2: No grammatical errors, excellent sentence structure
   - Score 1: Minor grammatical errors that don't impede understanding
   - Score 0: Severe grammatical errors that make comprehension difficult
   - IMPORTANT: Be extremely strict! Check for subject-verb agreement, tense consistency, article usage, prepositions, conjunctions, run-on sentences, fragments, and sentence structure.

4. Spelling (score out of 2): 
   - Score 2: No spelling errors
   - Score 1: 1-2 minor spelling errors
   - Score 0: 3 or more spelling errors or major spelling mistakes
   - IMPORTANT: Check every word carefully for spelling accuracy.

5. Vocabulary (score out of 2): 
   - Score 2: Excellent word choice, effective paraphrasing, appropriate academic vocabulary with proper collocations
   - Score 1: Some appropriate vocabulary but limited range or minor word choice issues
   - Score 0: Poor word choice, repetitive language, inappropriate register, or incorrect collocations
   - IMPORTANT: Be extremely strict! Check for word collocation, word form, register, precision, and appropriateness.

${
  originalText
    ? `The summary was written based on this original audio transcript: "${originalText}"`
    : ""
}
${
  promptText
    ? `The summary was written in response to this prompt: "${promptText}"`
    : ""
}

Word count target: 50-70 words
Time allowed: 10 minutes

Provide your assessment in this JSON format:
{
  "content": { 
    "score": <number>, 
    "maxScore": 2, 
    "suggestion": "<brief feedback>" 
  },
  "form": { 
    "score": <number>, 
    "maxScore": 2, 
    "suggestion": "<brief feedback>" 
  },
  "grammar": { 
    "score": <number>, 
    "maxScore": 2, 
    "suggestion": "<brief feedback with specific error examples>" 
  },
  "spelling": { 
    "score": <number>, 
    "maxScore": 2, 
    "suggestion": "<brief feedback with specific spelling errors if any>" 
  },
  "vocabulary": { 
    "score": <number>, 
    "maxScore": 2, 
    "suggestion": "<brief feedback with specific word choice examples>" 
  },
  "totalScore": <sum of all scores>,
  "maxScore": 10,
  "annotatedText": "<HTML formatted summary with colored spans for errors/feedback>",
  "wordCount": <number of words in the summary>
}

For "annotatedText", use the following color coding:
- <span style="color:red">text</span> for grammar errors (mark every single error)
- <span style="color:orange">text</span> for vocabulary issues (mark poor word choices, informal words, repetitions, collocations)
- <span style="color:blue">text</span> for spelling errors (mark each misspelled word)
- <span style="color:purple">text</span> for form issues (including length problems)
- <span style="color:green">text</span> for content issues
`;

  try {
    console.log("Making API request to OpenAI for SST scoring...");

    const response = await openai.chat.completions.create({
      model: OPENAI_MODELS.PREMIUM, // Using GPT-4o for most accurate scoring
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: summaryText },
      ],
      response_format: { type: "json_object" },
      temperature: 0.1, // Very low temperature for consistent scoring
      max_tokens: 2000, // Adequate for detailed feedback
    });

    console.log("OpenAI API Response received successfully for SST");

    let scoreData;
    try {
      scoreData = JSON.parse(response.choices[0].message.content);
    } catch (parseError) {
      console.error("Failed to parse SST JSON from API response:", parseError);
      throw new Error("Failed to parse SST scoring data from API response");
    }

    // Ensure all required properties exist
    const requiredProperties = [
      "content",
      "form",
      "grammar",
      "spelling",
      "vocabulary",
      "totalScore",
      "maxScore",
      "annotatedText",
      "wordCount",
    ];

    for (const prop of requiredProperties) {
      if (!Object.hasOwn(scoreData, prop)) {
        console.warn(
          `Missing property in SST AI response: ${prop}, adding default value.`
        );
        if (prop === "totalScore") {
          scoreData.totalScore =
            (scoreData.content?.score || 0) +
            (scoreData.form?.score || 0) +
            (scoreData.grammar?.score || 0) +
            (scoreData.spelling?.score || 0) +
            (scoreData.vocabulary?.score || 0);
        } else if (prop === "maxScore") {
          scoreData.maxScore = 10;
        } else if (prop === "annotatedText") {
          scoreData.annotatedText = summaryText;
        } else if (prop === "wordCount") {
          scoreData.wordCount = actualWordCount;
        } else {
          scoreData[prop] = {
            score: 0,
            maxScore: 2,
            suggestion: `No assessment provided for ${prop}`,
          };
        }
      }
    }

    // Ensure form score is accurate based on word count
    if (Math.abs(scoreData.form.score - correctFormScore) > 0.5) {
      console.log(
        `Correcting SST form score from ${scoreData.form.score} to ${correctFormScore}`
      );

      let formFeedback;
      if (actualWordCount < 50) {
        formFeedback = `Too short (${actualWordCount} words). Required range is 50-70 words.`;
      } else if (actualWordCount > 70) {
        formFeedback = `Too long (${actualWordCount} words). Required range is 50-70 words.`;
      } else {
        formFeedback = `Good length (${actualWordCount} words), within the required 50-70 word range.`;
      }

      const oldFormScore = scoreData.form.score;
      scoreData.form.score = correctFormScore;
      scoreData.form.suggestion = formFeedback;

      // Recalculate total score
      scoreData.totalScore =
        scoreData.totalScore - oldFormScore + correctFormScore;
    }

    // Ensure word count is set correctly
    scoreData.wordCount = actualWordCount;

    return scoreData;
  } catch (error) {
    console.error("Error in SST scoring:", error.message);

    if (error.response) {
      console.error("OpenAI API Error Details for SST:", {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
      });
    }

    // Fallback scoring for SST
    const fallbackScores = {
      content: {
        score: 1,
        maxScore: 2,
        suggestion: "API unavailable - fallback scoring used.",
      },
      form: {
        score: correctFormScore,
        maxScore: 2,
        suggestion:
          correctFormScore === 2
            ? `Good length (${actualWordCount} words), within the required 50-70 word range.`
            : `Length issue (${actualWordCount} words). Required range is 50-70 words.`,
      },
      grammar: {
        score: 1,
        maxScore: 2,
        suggestion: "Grammar evaluation unavailable - fallback scoring used.",
      },
      spelling: {
        score: 1,
        maxScore: 2,
        suggestion: "Spelling evaluation unavailable - fallback scoring used.",
      },
      vocabulary: {
        score: 1,
        maxScore: 2,
        suggestion:
          "Vocabulary evaluation unavailable - fallback scoring used.",
      },
      totalScore: 4 + correctFormScore,
      maxScore: 10,
      annotatedText: summaryText,
      wordCount: actualWordCount,
    };

    return fallbackScores;
  }
}

// API endpoint for Summarize Spoken Text scoring (SST - specific 5-criteria scoring)
app.post("/api/summarize-spoken-text-scoring", async (req, res) => {
  try {
    console.log("Received SST scoring request:", JSON.stringify(req.body));

    const summaryText = req.body.text;
    const originalText = req.body.originalText || "";
    const prompt = req.body.prompt || "";
    const user_id = req.body.user_id || "anonymous";

    // Validate input
    if (
      !summaryText ||
      typeof summaryText !== "string" ||
      summaryText.trim() === ""
    ) {
      return res.status(400).json({
        error: "Missing required fields",
        details: "Summary text is required",
      });
    }

    console.log("Processing SST scoring request:", {
      textLength: summaryText.length,
      originalTextLength: originalText?.length || 0,
      promptLength: prompt?.length || 0,
      userId: user_id,
    });

    // Call OpenAI for scoring with SST-specific criteria
    const result = await scoreSummarizeSpokenTextWithAI(
      summaryText,
      originalText,
      prompt
    );

    res.json(result);
  } catch (error) {
    console.error("Error processing SST scoring:", error);
    res.status(500).json({
      error: "SST scoring failed",
      details: error.message || "Unknown error occurred",
    });
  }
});

// API endpoint for essay scoring
app.post("/api/essay-scoring", async (req, res) => {
  try {
    console.log("Received essay scoring request:", JSON.stringify(req.body));

    const essayText = req.body.text;
    const prompt = req.body.prompt || "";

    // Validate input
    if (
      !essayText ||
      typeof essayText !== "string" ||
      essayText.trim() === ""
    ) {
      return res.status(400).json({
        error: "Missing required fields",
        details: "Essay text is required",
      });
    }

    // Count words to provide an early warning if outside range
    const wordCount = essayText.trim().split(/\s+/).length;
    console.log(`Essay has ${wordCount} words (target range: 200-300 words)`);

    // Call OpenAI for scoring with the 7-criteria model
    const result = await scoreEssayWithAI(essayText, prompt);
    res.json(result);
  } catch (error) {
    console.error("Error processing essay scoring:", error);
    res.status(500).json({
      error: "Essay scoring failed",
      details: error.message || "Unknown error occurred",
    });
  }
});

// Enhanced read-aloud endpoint with better error handling
app.post(
  "/api/speech-analysis",
  upload.single("audio"),
  validateAudioRequest,
  async (req, res) => {
    const startTime = Date.now();

    try {
      const text = req.body.text?.trim();

      if (!text) {
        return res.status(400).json({
          error: "Text required",
          details: "Text for speech analysis is required",
          code: "MISSING_TEXT",
        });
      }

      console.log("Processing speech analysis request:", {
        fileSize: `${(req.file.size / 1024 / 1024).toFixed(2)}MB`,
        mimeType: req.file.mimetype,
        textLength: text.length,
        fileName: req.file.originalname || "unknown",
      });

      // Set a longer timeout for the response (3 minutes)
      res.setTimeout(180000, () => {
        console.log("Speech analysis request timed out");
        if (!res.headersSent) {
          res.status(408).json({
            error: "Request timeout",
            details:
              "Speech analysis took too long to complete. Try with a shorter audio recording.",
            code: "REQUEST_TIMEOUT",
          });
        }
      });

      const results = await analyzeSpeech(req.file.buffer, text);

      const processingTime = Date.now() - startTime;
      console.log(
        `Speech analysis completed successfully in ${(
          processingTime / 1000
        ).toFixed(2)}s`
      );

      if (!res.headersSent) {
        res.json({
          ...results,
          processing_time_ms: processingTime,
          success: true,
        });
      }
    } catch (error) {
      console.error("Speech analysis error:", {
        message: error.message,
        code: error.code,
        status: error.response?.status,
        processingTime: `${(Date.now() - startTime) / 1000}s`,
      });

      if (!res.headersSent) {
        // Determine appropriate status code
        let statusCode = 500;
        let errorCode = "UNKNOWN_ERROR";

        if (
          error.message.includes("timeout") ||
          error.code === "ECONNABORTED"
        ) {
          statusCode = 408;
          errorCode = "ANALYSIS_TIMEOUT";
        } else if (
          error.message.includes("too large") ||
          error.response?.status === 413
        ) {
          statusCode = 413;
          errorCode = "FILE_TOO_LARGE";
        } else if (error.response?.status === 429) {
          statusCode = 429;
          errorCode = "RATE_LIMIT_EXCEEDED";
        } else if (
          error.message.includes("network") ||
          error.code === "ENOTFOUND"
        ) {
          statusCode = 503;
          errorCode = "NETWORK_ERROR";
        } else if (
          error.response?.status >= 400 &&
          error.response?.status < 500
        ) {
          statusCode = error.response.status;
          errorCode = "CLIENT_ERROR";
        }

        res.status(statusCode).json({
          error: "Speech analysis failed",
          details:
            error.message || "An unknown error occurred during speech analysis",
          code: errorCode,
          processing_time_ms: Date.now() - startTime,
        });
      }
    }
  }
);
// Enhanced audio upload endpoint for SST questions with compression
app.post(
  "/api/upload-audio",
  upload.single("audio"),
  validateAudioRequest,
  async (req, res) => {
    const startTime = Date.now();

    try {
      console.log("Processing audio upload:", {
        fileSize: `${(req.file.size / 1024 / 1024).toFixed(2)}MB`,
        mimeType: req.file.mimetype,
        originalName: req.file.originalname,
      });

      // Set timeout for upload request
      res.setTimeout(300000, () => {
        // 5 minutes
        console.log("Audio upload request timed out");
        if (!res.headersSent) {
          res.status(408).json({
            error: "Upload timeout",
            details:
              "Audio upload took too long to complete. Try with a smaller file.",
            code: "UPLOAD_TIMEOUT",
          });
        }
      });

      // Convert buffer to base64 for admin API upload
      const base64Data = req.file.buffer.toString("base64");

      try {
        // Upload to admin API with retry logic
        const uploadResponse = await axios.post(
          "https://api.deepinsightacademy.com/upload",
          {
            fileName: req.file.originalname,
            fileData: base64Data,
            fileType: req.file.mimetype || "audio/mp3",
          },
          {
            timeout: 120000, // 2 minutes timeout for admin API
            maxContentLength: 100 * 1024 * 1024, // 100MB max content length
            maxBodyLength: 100 * 1024 * 1024, // 100MB max body length
          }
        );

        const processingTime = Date.now() - startTime;
        console.log(
          `Audio upload completed successfully in ${(
            processingTime / 1000
          ).toFixed(2)}s:`,
          uploadResponse.data.fileUrl
        );

        if (!res.headersSent) {
          res.json({
            success: true,
            url: uploadResponse.data.fileUrl,
            fileUrl: uploadResponse.data.fileUrl,
            message: "Audio uploaded successfully",
            processing_time_ms: processingTime,
            file_size_mb: (req.file.size / 1024 / 1024).toFixed(2),
          });
        }
      } catch (adminError) {
        const processingTime = Date.now() - startTime;
        console.error("Admin API upload failed:", {
          message: adminError.message,
          status: adminError.response?.status,
          data: adminError.response?.data,
          processingTime: `${(processingTime / 1000).toFixed(2)}s`,
        });

        if (!res.headersSent) {
          // Determine appropriate error response
          let statusCode = 500;
          let errorCode = "UPLOAD_FAILED";
          let details =
            adminError.response?.data?.message || adminError.message;

          if (adminError.code === "ECONNABORTED") {
            statusCode = 408;
            errorCode = "ADMIN_API_TIMEOUT";
            details = "Upload to storage service timed out. Please try again.";
          } else if (adminError.response?.status === 413) {
            statusCode = 413;
            errorCode = "FILE_TOO_LARGE_FOR_STORAGE";
            details =
              "File too large for storage service. Please compress your audio.";
          } else if (
            adminError.response?.status >= 400 &&
            adminError.response?.status < 500
          ) {
            statusCode = adminError.response.status;
            errorCode = "STORAGE_CLIENT_ERROR";
          } else if (
            adminError.code === "ENOTFOUND" ||
            adminError.message.includes("network")
          ) {
            statusCode = 503;
            errorCode = "STORAGE_SERVICE_UNAVAILABLE";
            details =
              "Storage service temporarily unavailable. Please try again later.";
          }

          res.status(statusCode).json({
            error: "Upload failed",
            details,
            code: errorCode,
            adminApiError: true,
            processing_time_ms: processingTime,
          });
        }
      }
    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error("Audio upload processing error:", {
        message: error.message,
        processingTime: `${(processingTime / 1000).toFixed(2)}s`,
      });

      if (!res.headersSent) {
        res.status(500).json({
          error: "Audio upload failed",
          details:
            error.message || "Unknown error occurred during upload processing",
          code: "PROCESSING_ERROR",
          processing_time_ms: processingTime,
        });
      }
    }
  }
);

// Enhanced PTE tasks endpoint with better error handling
app.post(
  "/api/pte/:taskType",
  upload.single("audio"),
  validateAudioRequest,
  async (req, res) => {
    const startTime = Date.now();

    try {
      const { taskType } = req.params;
      const { context } = req.body;

      const validTaskTypes = [
        "describe-image",
        "retell-lecture",
        "answer-question",
      ];

      if (!validTaskTypes.includes(taskType)) {
        return res.status(400).json({
          error: "Invalid task type",
          details: `Task type must be one of: ${validTaskTypes.join(", ")}`,
          code: "INVALID_TASK_TYPE",
        });
      }

      console.log("Processing PTE task:", {
        taskType,
        fileSize: `${(req.file.size / 1024 / 1024).toFixed(2)}MB`,
        mimeType: req.file.mimetype,
        contextLength: context?.length || 0,
        hasContext: !!context,
        fileName: req.file.originalname || "unknown",
      });

      // Set a longer timeout for the response (3 minutes)
      res.setTimeout(180000, () => {
        console.log(`PTE ${taskType} analysis request timed out`);
        if (!res.headersSent) {
          res.status(408).json({
            error: "Request timeout",
            details: `PTE ${taskType} analysis took too long to complete. Try with a shorter audio recording.`,
            code: "REQUEST_TIMEOUT",
          });
        }
      });

      const results = await analyzePTETask(req.file.buffer, taskType, context);

      const processingTime = Date.now() - startTime;
      console.log(
        `PTE ${taskType} analysis completed successfully in ${(
          processingTime / 1000
        ).toFixed(2)}s`
      );

      if (!res.headersSent) {
        res.json({
          ...results,
          task_type: taskType,
          processing_time_ms: processingTime,
          success: true,
        });
      }
    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error(`PTE ${req.params.taskType || "unknown"} task error:`, {
        message: error.message,
        code: error.code,
        status: error.response?.status,
        processingTime: `${(processingTime / 1000).toFixed(2)}s`,
      });

      if (!res.headersSent) {
        // Determine appropriate status code and error code
        let statusCode = 500;
        let errorCode = "UNKNOWN_ERROR";

        if (
          error.message.includes("timeout") ||
          error.code === "ECONNABORTED"
        ) {
          statusCode = 408;
          errorCode = "ANALYSIS_TIMEOUT";
        } else if (
          error.message.includes("too large") ||
          error.response?.status === 413
        ) {
          statusCode = 413;
          errorCode = "FILE_TOO_LARGE";
        } else if (error.response?.status === 429) {
          statusCode = 429;
          errorCode = "RATE_LIMIT_EXCEEDED";
        } else if (
          error.message.includes("network") ||
          error.code === "ENOTFOUND"
        ) {
          statusCode = 503;
          errorCode = "NETWORK_ERROR";
        } else if (error.message.includes("Invalid task type")) {
          statusCode = 400;
          errorCode = "INVALID_TASK_TYPE";
        } else if (
          error.response?.status >= 400 &&
          error.response?.status < 500
        ) {
          statusCode = error.response.status;
          errorCode = "CLIENT_ERROR";
        }

        res.status(statusCode).json({
          error: "PTE task analysis failed",
          details:
            error.message ||
            "An unknown error occurred during PTE task analysis",
          code: errorCode,
          task_type: req.params.taskType,
          processing_time_ms: processingTime,
        });
      }
    }
  }
);

app.get("/api/test", (req, res) => {
  res.json({ message: "Backend is working!" });
});

// Handle other routes - MUST be last
app.use("*", (req, res) => {
  res.status(404).json({ error: "Not Found" });
});

// Export the express app for Vercel
export default app;

// Start the server for Railway and local development
// Only skip starting for Vercel (which handles this automatically)
if (!process.env.VERCEL) {
  const PORT = process.env.PORT || 5000;
  app.listen(PORT, () => {
    const environment = process.env.RAILWAY_ENVIRONMENT
      ? "Railway"
      : process.env.NODE_ENV === "production"
      ? "Production"
      : "Local";

    console.log(`🚀 Server running on port ${PORT} (${environment})`);
    console.log(`📁 Environment: ${process.env.NODE_ENV || "development"}`);
    console.log(
      `🔑 SPEECH_ACE_API_KEY configured: ${!!process.env.SPEECH_ACE_API_KEY}`
    );
    console.log(
      `🤖 OpenAI API Key configured: ${!!process.env.OPENAI_API_KEY}`
    );

    // Check FFmpeg availability on Railway
    if (process.env.RAILWAY_ENVIRONMENT) {
      console.log(`🎵 FFmpeg support enabled for Railway deployment`);
    }
  });
}

// API endpoint for Summarize Spoken Text scoring (SST - specific 5-criteria scoring)
app.post("/api/summarize-spoken-text-scoring", async (req, res) => {
  try {
    console.log("Received SST scoring request:", JSON.stringify(req.body));

    const summaryText = req.body.text;
    const originalText = req.body.originalText || "";
    const prompt = req.body.prompt || "";
    const user_id = req.body.user_id || "anonymous";

    // Validate input
    if (
      !summaryText ||
      typeof summaryText !== "string" ||
      summaryText.trim() === ""
    ) {
      return res.status(400).json({
        error: "Missing required fields",
        details: "Summary text is required",
      });
    }

    console.log("Processing SST scoring request:", {
      textLength: summaryText.length,
      originalTextLength: originalText?.length || 0,
      promptLength: prompt?.length || 0,
      userId: user_id,
    });

    // Call OpenAI for scoring with SST-specific criteria
    const result = await scoreSummarizeSpokenTextWithAI(
      summaryText,
      originalText,
      prompt
    );

    res.json(result);
  } catch (error) {
    console.error("Error processing SST scoring:", error);
    res.status(500).json({
      error: "SST scoring failed",
      details: error.message || "Unknown error occurred",
    });
  }
});
