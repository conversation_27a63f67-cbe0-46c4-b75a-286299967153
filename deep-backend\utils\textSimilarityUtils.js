import natural from "natural";

/**
 * Better vector creation for text comparison
 * @param {string[]} words - Array of words to convert to a vector
 * @returns {Object} - Term frequency vector
 */
export const getVector = (words) => {
  // Create a term frequency vector
  const vector = {};
  const wordCount = words.length;

  // Count occurrences of each word
  words.forEach((word) => {
    if (word.trim() === "") return;
    vector[word] = (vector[word] || 0) + 1;
  });

  // Normalize term frequencies by document length
  // This helps account for different text lengths
  Object.keys(vector).forEach((term) => {
    vector[term] = vector[term] / wordCount;
  });

  return vector;
};

/**
 * Improved text normalization with better handling of contractions and special cases
 * @param {string} text - Text to normalize
 * @returns {string[]} - Array of normalized words
 */
export const normalizeText = (text) => {
  if (!text) return [];

  return text
    .toLowerCase()
    .replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, "") // Remove punctuation but keep apostrophes
    .replace(/\s{2,}/g, " ") // Remove extra spaces
    .split(/\s+/) // Split into words
    .filter((word) => word.length > 0); // Remove empty words
};

/**
 * Calculate cosine similarity between two vectors with improved handling
 * @param {Object} vectorA - First vector
 * @param {Object} vectorB - Second vector
 * @returns {number} - Similarity score between 0 and 1
 */
export const cosineSimilarity = (vectorA, vectorB) => {
  // Get all unique terms from both vectors
  const terms = new Set([...Object.keys(vectorA), ...Object.keys(vectorB)]);

  let dotProduct = 0;
  let magnitudeA = 0;
  let magnitudeB = 0;

  // Calculate dot product and magnitudes
  terms.forEach((term) => {
    const a = vectorA[term] || 0;
    const b = vectorB[term] || 0;
    dotProduct += a * b;
    magnitudeA += a * a;
    magnitudeB += b * b;
  });

  // Calculate magnitudes
  magnitudeA = Math.sqrt(magnitudeA);
  magnitudeB = Math.sqrt(magnitudeB);

  // Avoid division by zero
  if (magnitudeA === 0 || magnitudeB === 0) {
    return 0;
  }

  // Return cosine similarity
  const similarity = dotProduct / (magnitudeA * magnitudeB);

  // Handle floating point errors (similarity should be between 0 and 1)
  return Math.min(1, Math.max(0, similarity));
};

/**
 * Enhanced similarity calculation with word overlap metrics and completeness penalty
 * @param {string} providedText - The original text that should be matched
 * @param {string} transcript - The transcript to compare against the provided text
 * @returns {Object} - Various similarity metrics
 */
export const calculateTextSimilarity = (providedText, transcript) => {
  // Normalize texts
  const providedWords = normalizeText(providedText);
  const transcriptWords = normalizeText(transcript);

  // Create vectors
  const providedVector = getVector(providedWords);
  const transcriptVector = getVector(transcriptWords);

  // Calculate cosine similarity
  const cosine = cosineSimilarity(providedVector, transcriptVector);

  // Calculate word overlap metrics
  const uniqueProvidedWords = new Set(providedWords);
  const uniqueTranscriptWords = new Set(transcriptWords);

  // Words that appear in both texts
  const intersection = new Set(
    [...uniqueProvidedWords].filter((x) => uniqueTranscriptWords.has(x))
  );

  // Jaccard similarity: intersection size / union size
  const union = new Set([...uniqueProvidedWords, ...uniqueTranscriptWords]);
  const jaccard = union.size > 0 ? intersection.size / union.size : 0;

  // Word overlap percentage from transcript to provided text
  const overlapPercentage =
    uniqueTranscriptWords.size > 0
      ? intersection.size / uniqueTranscriptWords.size
      : 0;

  // Calculate completeness - how much of the provided text is covered
  const completeness =
    uniqueProvidedWords.size > 0
      ? intersection.size / uniqueProvidedWords.size
      : 0;

  // Word count ratio - penalizes very short transcripts
  const wordCountRatio = Math.min(
    1,
    transcriptWords.length / providedWords.length
  );

  // Calculate the content coverage - how much of the provided text content is matched
  // This strongly penalizes incomplete responses
  const contentCoverage = completeness * wordCountRatio;

  // Calculate sequence matching (check for words in the right order)
  let sequenceScore = 0;
  if (providedWords.length > 0 && transcriptWords.length > 0) {
    // Simple implementation of longest common subsequence
    let commonSequenceLength = 0;
    let lastMatchIndex = -1;

    for (let i = 0; i < transcriptWords.length; i++) {
      for (let j = lastMatchIndex + 1; j < providedWords.length; j++) {
        if (transcriptWords[i] === providedWords[j]) {
          commonSequenceLength++;
          lastMatchIndex = j;
          break;
        }
      }
    }

    sequenceScore = commonSequenceLength / providedWords.length;
  }

  // Combined score with stronger emphasis on completeness
  // New formula that puts more weight on completeness and sequence
  const combinedScore =
    cosine * 0.3 + jaccard * 0.15 + completeness * 0.35 + sequenceScore * 0.2;

  return {
    cosine,
    jaccard,
    overlapPercentage,
    completeness,
    wordCountRatio,
    sequenceScore,
    combinedScore,
    matchedWords: [...intersection],
    totalUniqueWordsInProvided: uniqueProvidedWords.size,
    totalUniqueWordsInTranscript: uniqueTranscriptWords.size,
  };
};
