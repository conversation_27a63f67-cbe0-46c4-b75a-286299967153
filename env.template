# API Configuration
# The base URL for the backend API
VITE_API_BASE_URL=https://api.deepinsightacademy.com

# Instructions:
# 1. Copy this file to .env in the project root
# 2. Update the VITE_API_BASE_URL if needed for different environments
# 3. The application will use this value, or fallback to the production URL if not set

# Example for different environments:
# Development: VITE_API_BASE_URL=http://localhost:3000
# Staging: VITE_API_BASE_URL=https://staging-api.deepinsightacademy.com  
# Production: VITE_API_BASE_URL=https://api.deepinsightacademy.com 