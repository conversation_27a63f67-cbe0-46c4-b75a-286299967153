// Mock Test Creation Script for PTE Exam
// Test ID: 6849c969d0001a608d091534
// This script creates a comprehensive mock test with questions from all sections

const mockTestQuestions = {
  testId: "6849c969d0001a608d091534",
  testInfo: {
    testName: "June Mock Test",
    testDuration: "120",
    totalMarks: 90,
    readingQuestionsDuration: 30,
    speakingQuestionsDuration: 60,
    listeningQuestionsDuration: 30,
    difficultyLevel: "easy",
  },

  // SPEAKING SECTION QUESTIONS
  speakingQuestions: [
    {
      questionId: "6849c969d0001a608d091537",
      mocktestId: "6849c969d0001a608d091534",
      categoryId: "6787cc2b486e6c04269a34e1", // Read Aloud
      questionNumber: "1",
      prompt:
        "Scientists have discovered a new species of deep-sea fish that can survive in extreme pressure conditions. This remarkable creature has adapted to life in the ocean's deepest trenches, where sunlight never reaches and temperatures remain near freezing. The fish possesses unique biological features that allow it to thrive in an environment that would be deadly to most other marine life.",
      section: "speaking",
      difficulty: "easy",
      maxScore: 5,
      duration: 40,
      category: {
        categoryId: "6787cc2b486e6c04269a34e1",
        name: "Read Aloud",
        description:
          "Look at the text below. In 40 seconds, you must read this text aloud as naturally and clearly as possible. You have 40 seconds to read aloud.",
        section: "speaking",
      },
      additionalProp1: {
        prepTime: "35",
        answerTime: "40",
      },
    },
    {
      questionId: "6849c969d0001a608d091538",
      mocktestId: "6849c969d0001a608d091534",
      categoryId: "67838fcf2e266d08ba713eed", // Repeat Sentence
      questionNumber: "2",
      prompt:
        "You will hear a sentence. Please repeat the sentence exactly as you hear it. You will hear the sentence only once.",
      section: "speaking",
      difficulty: "easy",
      maxScore: 5,
      duration: 15,
      media: {
        url: "https://example.com/audio/repeat-sentence-1.mp3",
        type: "audio",
      },
      category: {
        categoryId: "67838fcf2e266d08ba713eed",
        name: "Repeat Sentence",
        description:
          "You will hear a sentence. Please repeat the sentence exactly as you hear it. You will hear the sentence only once.",
        section: "speaking",
      },
      additionalProp1: {
        prepTime: "3",
        answerTime: "15",
      },
    },
    {
      questionId: "6849c969d0001a608d091539",
      mocktestId: "6849c969d0001a608d091534",
      categoryId: "678391852e266d08ba713eee", // Describe Image
      questionNumber: "3",
      prompt:
        "Look at the image below. In 25 seconds, please speak into the microphone and describe in detail what the image is showing. You will have 40 seconds to give your response.",
      section: "speaking",
      difficulty: "easy",
      maxScore: 5,
      duration: 40,
      imageUrl: "https://example.com/images/describe-image-1.jpg",
      category: {
        categoryId: "678391852e266d08ba713eee",
        name: "Describe Image",
        description:
          "Look at the image below. In 25 seconds, please speak into the microphone and describe in detail what the image is showing.",
        section: "speaking",
      },
      additionalProp1: {
        prepTime: "25",
        answerTime: "40",
      },
    },
    {
      questionId: "6849c969d0001a608d091540",
      mocktestId: "6849c969d0001a608d091534",
      categoryId: "6783923483cd4009d9cddafa", // Answer Short Question
      questionNumber: "4",
      prompt:
        "You will hear a question. Please give a simple and short answer. Often just one or a few words is enough.",
      section: "speaking",
      difficulty: "easy",
      maxScore: 1,
      duration: 10,
      media: {
        url: "https://example.com/audio/answer-short-1.mp3",
        type: "audio",
      },
      category: {
        categoryId: "6783923483cd4009d9cddafa",
        name: "Answer Short Question",
        description:
          "You will hear a question. Please give a simple and short answer. Often just one or a few words is enough.",
        section: "speaking",
      },
      additionalProp1: {
        prepTime: "3",
        answerTime: "10",
      },
    },
    {
      questionId: "6849c969d0001a608d09153a",
      mocktestId: "6849c969d0001a608d091534",
      categoryId: "67e5acdd73e32a15ba3d88c8", // Retell Lecture
      questionNumber: "5",
      prompt:
        "You will hear a lecture. After listening to the lecture, in 10 seconds, please speak into the microphone and retell what you have just heard from the lecture in your own words. You will have 40 seconds to give your response.",
      section: "speaking",
      difficulty: "easy",
      maxScore: 5,
      duration: 50,
      audioUrl:
        "https://www2.cs.uic.edu/~i101/SoundFiles/BabyElephantWalk60.wav",
      media: {
        url: "https://www2.cs.uic.edu/~i101/SoundFiles/BabyElephantWalk60.wav",
        type: "audio",
      },
      lectureText:
        "Climate change represents one of the most significant challenges facing our planet today. Rising global temperatures, caused primarily by greenhouse gas emissions from human activities, are leading to melting ice caps, rising sea levels, and extreme weather patterns. Scientists emphasize that immediate action is needed to reduce carbon emissions and transition to renewable energy sources to mitigate these effects.",
      category: {
        categoryId: "67e5acdd73e32a15ba3d88c8",
        name: "Retell Lecture",
        description:
          "You will hear a lecture. After listening to the lecture, in 10 seconds, please speak into the microphone and retell what you have just heard from the lecture in your own words.",
        section: "speaking",
      },
      additionalProp1: {
        prepTime: "10",
        answerTime: "40",
        keywords:
          "climate change, global warming, greenhouse gases, carbon emissions, renewable energy, sea levels, extreme weather",
      },
    },
  ],

  // WRITING SECTION QUESTIONS
  writingQuestions: [
    {
      questionId: "6849c969d0001a608d091541",
      mocktestId: "6849c969d0001a608d091534",
      categoryId: "6783926b83cd4009d9cddafb", // Summarize Written Text
      questionNumber: "6",
      prompt:
        "Read the passage below and summarize it using one sentence. Type your response in the box at the bottom of the screen. You have 10 minutes to finish this task. Your response will be judged on the quality of your writing and on how well your response presents the key points in the passage.\n\nClimate change represents one of the most pressing challenges of our time, affecting ecosystems, weather patterns, and human societies across the globe. Rising global temperatures have led to melting ice caps, rising sea levels, and more frequent extreme weather events. Scientists worldwide are working on innovative solutions, including renewable energy technologies, carbon capture systems, and sustainable agriculture practices. However, addressing climate change requires not only technological innovation but also significant changes in human behavior, government policies, and international cooperation. The urgency of this issue cannot be overstated, as the window for effective action continues to narrow with each passing year.",
      section: "writing",
      difficulty: "easy",
      maxScore: 2,
      duration: 600,
      category: {
        categoryId: "6783926b83cd4009d9cddafb",
        name: "Summarize Written Text",
        description:
          "Read the passage below and summarize it using one sentence. You have 10 minutes to finish this task.",
        section: "writing",
      },
    },
    {
      questionId: "6849c969d0001a608d091542",
      mocktestId: "6849c969d0001a608d091534",
      categoryId: "6849c969d0001a608d091535", // Essay Writing
      questionNumber: "7",
      prompt:
        "Some people believe that the increasing use of technology in education is beneficial for students' learning outcomes. Others argue that traditional teaching methods are more effective. Discuss both views and give your own opinion. Support your arguments with relevant examples and evidence. Write at least 200 words.",
      section: "writing",
      difficulty: "easy",
      maxScore: 3,
      duration: 1200,
      category: {
        categoryId: "6849c969d0001a608d091535",
        name: "Write Essay",
        description:
          "You will have 20 minutes to plan, write and revise an essay about the topic below. Your response will be judged on how well you develop a position, organize your ideas, present supporting details, and control the elements of standard written English.",
        section: "writing",
      },
    },
    {
      questionId: "6849c969d0001a608d091543",
      mocktestId: "6849c969d0001a608d091534",
      categoryId: "678392a883cd4009d9cddafc", // Write Email
      questionNumber: "8",
      prompt:
        "You work at a local community center and need to write an email to the city council requesting funding for a new youth program. Write an email to Ms. Sarah Johnson, the council representative, explaining the need for this program and requesting their support.",
      section: "writing",
      difficulty: "easy",
      maxScore: 2,
      duration: 540,
      condition: "Your email should include the following three points:",
      blanksData: [
        "Description of the proposed youth program and its objectives",
        "Explanation of why this program is needed in the community",
        "Specific funding amount requested and how it will be used",
      ],
      category: {
        categoryId: "678392a883cd4009d9cddafc",
        name: "Write Email",
        description:
          "Read the description of a situation. Then write an email about the situation. You will have 9 minutes.",
        section: "writing",
      },
    },
  ],

  // READING SECTION QUESTIONS
  readingQuestions: [
    {
      questionId: "6849c969d0001a608d091544",
      mocktestId: "6849c969d0001a608d091534",
      categoryId: "67800fc7d4e7d9147dd9525d", // Multiple Choice Single Answer
      questionNumber: "9",
      prompt:
        "Artificial intelligence (AI) has revolutionized many industries, from healthcare to transportation. Machine learning algorithms can now diagnose diseases with remarkable accuracy, sometimes surpassing human doctors. In the automotive industry, self-driving cars use AI to navigate complex traffic situations. However, the rapid advancement of AI also raises important ethical questions about job displacement, privacy, and the need for human oversight in critical decision-making processes.\n\nWhat is the main purpose of this passage?",
      section: "reading",
      difficulty: "easy",
      maxScore: 1,
      duration: 120,
      options: [
        {
          text: "To explain how AI works in different industries",
          isCorrect: false,
        },
        {
          text: "To discuss both benefits and concerns of AI advancement",
          isCorrect: true,
        },
        {
          text: "To argue against the use of AI in healthcare",
          isCorrect: false,
        },
        {
          text: "To promote self-driving cars as the future of transportation",
          isCorrect: false,
        },
      ],
      category: {
        categoryId: "67800fc7d4e7d9147dd9525d",
        name: "Multiple Choice (Single Answer)",
        description:
          "Read the text and answer the question by selecting the correct response.",
        section: "reading",
      },
    },
    {
      questionId: "6849c969d0001a608d091545",
      mocktestId: "6849c969d0001a608d091534",
      categoryId: "6780113ae0dfdc154eff11b6", // Multiple Choice Multiple Answers
      questionNumber: "10",
      prompt:
        "Renewable energy sources have become increasingly important in the fight against climate change. Solar panels convert sunlight directly into electricity through photovoltaic cells. Wind turbines harness the power of moving air to generate clean energy. Hydroelectric dams use flowing water to produce electricity without emissions. Geothermal energy taps into the Earth's internal heat. Each of these technologies offers unique advantages and faces specific challenges in terms of cost, efficiency, and environmental impact.\n\nWhich of the following are mentioned as renewable energy sources in the passage?",
      section: "reading",
      difficulty: "easy",
      maxScore: 2,
      duration: 180,
      options: [
        { text: "Solar energy", isCorrect: true },
        { text: "Nuclear power", isCorrect: false },
        { text: "Wind energy", isCorrect: true },
        { text: "Natural gas", isCorrect: false },
        { text: "Hydroelectric power", isCorrect: true },
        { text: "Geothermal energy", isCorrect: true },
        { text: "Coal power", isCorrect: false },
      ],
      category: {
        categoryId: "6780113ae0dfdc154eff11b6",
        name: "Multiple Choice (Multiple Answers)",
        description:
          "Read the text and answer the question by selecting all the correct responses.",
        section: "reading",
      },
    },
    {
      questionId: "6849c969d0001a608d091546",
      mocktestId: "6849c969d0001a608d091534",
      categoryId: "67801456e0dfdc154eff11ba", // Reading & Writing Fill in the Blanks
      questionNumber: "11",
      prompt:
        "The Internet of Things (IoT) refers to the growing network of physical devices that are connected to the internet and can (1) ___ data with each other. These smart devices, ranging from household appliances to industrial machinery, are equipped with sensors and software that enable them to (2) ___ information and respond to commands remotely. The IoT has (3) ___ applications in various sectors, including healthcare, agriculture, and smart cities. However, the widespread adoption of IoT devices also raises important (4) ___ about data security and privacy protection.",
      section: "reading",
      difficulty: "easy",
      maxScore: 4,
      duration: 240,
      dropdownOptions: [
        {
          blank: 1,
          options: ["share", "hide", "destroy", "ignore"],
          correctAnswer: "share",
        },
        {
          blank: 2,
          options: ["collect", "delete", "corrupt", "lose"],
          correctAnswer: "collect",
        },
        {
          blank: 3,
          options: ["limited", "numerous", "few", "restricted"],
          correctAnswer: "numerous",
        },
        {
          blank: 4,
          options: ["celebrations", "parties", "concerns", "jokes"],
          correctAnswer: "concerns",
        },
      ],
      category: {
        categoryId: "67801456e0dfdc154eff11ba",
        name: "Reading & Writing: Fill in the Blanks",
        description:
          "Below is a text with blanks. Choose the correct word for each blank to complete the text.",
        section: "reading",
      },
    },
    {
      questionId: "6849c969d0001a608d091547",
      mocktestId: "6849c969d0001a608d091534",
      categoryId: "678011ade0dfdc154eff11b8", // Re-order Paragraphs
      questionNumber: "12",
      prompt:
        "The following paragraphs are in the wrong order. Put them in the correct order.",
      section: "reading",
      difficulty: "easy",
      maxScore: 3,
      duration: 300,
      paragraphs: [
        {
          id: "para1",
          text: "As a result, many countries are now investing heavily in renewable energy infrastructure and implementing policies to reduce their carbon footprint.",
          correctOrder: 4,
        },
        {
          id: "para2",
          text: "Global warming, caused primarily by greenhouse gas emissions from human activities, has become one of the most significant challenges facing humanity.",
          correctOrder: 1,
        },
        {
          id: "para3",
          text: "The effects of climate change are already visible in the form of rising sea levels, extreme weather events, and disrupted ecosystems around the world.",
          correctOrder: 2,
        },
        {
          id: "para4",
          text: "Scientists have been warning for decades about the potential consequences of continued fossil fuel consumption and deforestation.",
          correctOrder: 3,
        },
      ],
      category: {
        categoryId: "678011ade0dfdc154eff11b8",
        name: "Re-order Paragraphs",
        description:
          "The text boxes in the left panel have been placed in a random order. Restore the original order by dragging the text boxes from the left panel to the right panel.",
        section: "reading",
      },
    },
  ],

  // LISTENING SECTION QUESTIONS
  listeningQuestions: [
    {
      questionId: "6849c969d0001a608d091548",
      mocktestId: "6849c969d0001a608d091534",
      categoryId: "6849c969d0001a608d091536", // Summarize Spoken Text
      questionNumber: "12",
      prompt:
        "You will hear a lecture. Write a summary for a fellow student who was not present at the lecture. You should write 50-70 words.",
      section: "listening",
      difficulty: "easy",
      maxScore: 3,
      duration: 600,
      media: {
        url: "https://example.com/audio/lecture-summary-1.mp3",
        type: "audio",
      },
      originalText:
        "The lecture discussed the importance of biodiversity in maintaining healthy ecosystems. The professor explained how different species interact with each other and their environment, creating complex food webs and nutrient cycles. Human activities such as deforestation and pollution have led to significant biodiversity loss, which threatens the stability of natural systems. Conservation efforts, including the establishment of protected areas and sustainable practices, are essential for preserving biodiversity for future generations.",
      category: {
        categoryId: "6849c969d0001a608d091536",
        name: "Summarize Spoken Text",
        description:
          "You will hear a lecture. Write a summary for a fellow student who was not present at the lecture.",
        section: "listening",
      },
    },
    {
      questionId: "6849c969d0001a608d091549",
      mocktestId: "6849c969d0001a608d091534",
      categoryId: "67801bda382bce18e30d11ca", // Multiple Choice Multiple Answers (Listening)
      questionNumber: "13",
      prompt:
        "Listen to the recording and answer the question by selecting all the correct responses. You will hear the recording only once.",
      section: "listening",
      difficulty: "easy",
      maxScore: 2,
      duration: 90,
      media: {
        url: "https://example.com/audio/multiple-choice-listening-1.mp3",
        type: "audio",
      },
      options: [
        {
          text: "The speaker mentions environmental benefits",
          isCorrect: true,
        },
        { text: "Cost savings are discussed", isCorrect: true },
        { text: "The speaker talks about health risks", isCorrect: false },
        { text: "Technology limitations are mentioned", isCorrect: false },
        { text: "Government support is discussed", isCorrect: true },
      ],
      category: {
        categoryId: "67801bda382bce18e30d11ca",
        name: "Multiple Choice (Multiple Answers)",
        description:
          "Listen to the recording and answer the question by selecting all the correct responses.",
        section: "listening",
      },
    },
    {
      questionId: "6849c969d0001a608d091550",
      mocktestId: "6849c969d0001a608d091534",
      categoryId: "67801d77382bce18e30d11cc", // Fill in the Blanks (Listening)
      questionNumber: "14",
      prompt:
        "You will hear a recording. Type the missing words in each blank.",
      section: "listening",
      difficulty: "easy",
      maxScore: 5,
      duration: 120,
      media: {
        url: "https://example.com/audio/fill-blanks-listening-1.mp3",
        type: "audio",
      },
      transcript:
        "The study of marine biology has revealed fascinating insights into ocean (1) ___________. Researchers have discovered that many deep-sea creatures have developed unique (2) ___________ to survive in extreme conditions. These adaptations include specialized organs for (3) ___________ light and methods for conserving (4) ___________. The research has important implications for understanding how life might exist in other (5) ___________ environments.",
      blanks: [
        { position: 1, correctAnswer: "ecosystems" },
        { position: 2, correctAnswer: "adaptations" },
        { position: 3, correctAnswer: "producing" },
        { position: 4, correctAnswer: "energy" },
        { position: 5, correctAnswer: "extreme" },
      ],
      category: {
        categoryId: "67801d77382bce18e30d11cc",
        name: "Fill in the Blanks",
        description:
          "You will hear a recording. Type the missing words in each blank.",
        section: "listening",
      },
    },
    {
      questionId: "6849c969d0001a608d091551",
      mocktestId: "6849c969d0001a608d091534",
      categoryId: "678023f5382bce18e30d11d2", // Highlight Incorrect Words
      questionNumber: "15",
      prompt:
        "You will hear a recording. Below is a transcription of the recording. Some words in the transcription differ from what the speaker said. Please click on the words that are different.",
      section: "listening",
      difficulty: "easy",
      maxScore: 4,
      duration: 90,
      media: {
        url: "https://example.com/audio/highlight-incorrect-1.mp3",
        type: "audio",
      },
      transcript:
        "Space exploration has always captured the human imagination. Recent missions to Mars have provided valuable data about the planet's atmosphere and geological history. Scientists believe that Mars once had liquid water on its surface, making it a potential candidate for past or present life. Future missions will focus on searching for signs of microscopic organisms and preparing for eventual human colonization.",
      incorrectWords: [
        { word: "atmosphere", correctWord: "surface", position: 15 },
        { word: "geological", correctWord: "climate", position: 17 },
        { word: "microscopic", correctWord: "ancient", position: 43 },
      ],
      category: {
        categoryId: "678023f5382bce18e30d11d2",
        name: "Highlight Incorrect Words",
        description:
          "You will hear a recording. Some words in the transcription differ from what the speaker said. Click on the words that are different.",
        section: "listening",
      },
    },
    {
      questionId: "6849c969d0001a608d091552",
      mocktestId: "6849c969d0001a608d091534",
      categoryId: "678024ac382bce18e30d11d3", // Write from Dictation
      questionNumber: "16",
      prompt:
        "You will hear a sentence. Type the sentence in the box below exactly as you hear it. Write as much of the sentence as you can. You will hear the sentence only once.",
      section: "listening",
      difficulty: "easy",
      maxScore: 3,
      duration: 60,
      media: {
        url: "https://example.com/audio/dictation-1.mp3",
        type: "audio",
      },
      correctAnswer:
        "Technology has revolutionized the way we communicate and access information.",
      category: {
        categoryId: "678024ac382bce18e30d11d3",
        name: "Write from Dictation",
        description:
          "You will hear a sentence. Type the sentence in the box below exactly as you hear it.",
        section: "listening",
      },
    },
  ],
};

// Database insertion script
const insertMockTestQuestions = async () => {
  console.log(
    "Starting mock test creation for Test ID:",
    mockTestQuestions.testId
  );

  try {
    // Insert all speaking questions
    for (const question of mockTestQuestions.speakingQuestions) {
      console.log(
        `Inserting Speaking Question ${question.questionNumber}: ${question.category.name}`
      );
      // API call to insert question
      // await insertQuestion(question);
    }

    // Insert all writing questions
    for (const question of mockTestQuestions.writingQuestions) {
      console.log(
        `Inserting Writing Question ${question.questionNumber}: ${question.category.name}`
      );
      // API call to insert question
      // await insertQuestion(question);
    }

    // Insert all reading questions
    for (const question of mockTestQuestions.readingQuestions) {
      console.log(
        `Inserting Reading Question ${question.questionNumber}: ${question.category.name}`
      );
      // API call to insert question
      // await insertQuestion(question);
    }

    // Insert all listening questions
    for (const question of mockTestQuestions.listeningQuestions) {
      console.log(
        `Inserting Listening Question ${question.questionNumber}: ${question.category.name}`
      );
      // API call to insert question
      // await insertQuestion(question);
    }

    console.log("Mock test creation completed successfully!");
    console.log(
      "Total questions created:",
      mockTestQuestions.speakingQuestions.length +
        mockTestQuestions.writingQuestions.length +
        mockTestQuestions.readingQuestions.length +
        mockTestQuestions.listeningQuestions.length
    );
  } catch (error) {
    console.error("Error creating mock test:", error);
  }
};

// Export for use in other files
export { mockTestQuestions, insertMockTestQuestions };

// Run the script
insertMockTestQuestions();
