{"name": "educrat-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@fortawesome/fontawesome-svg-core": "^6.4.2", "@fortawesome/free-regular-svg-icons": "^6.4.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/react-fontawesome": "^0.2.0", "@fullcalendar/core": "^6.1.9", "@fullcalendar/daygrid": "^6.1.9", "@fullcalendar/react": "^6.1.9", "@mui/icons-material": "^6.3.1", "@mui/material": "^5.14.15", "@popperjs/core": "2.11.8", "@ramonak/react-progress-bar": "^5.3.0", "@react-native-async-storage/async-storage": "^2.0.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "aos": "^2.3.4", "axios": "^1.9.0", "bootstrap": "^5.1.3", "date-fns": "^4.1.0", "extendable-media-recorder": "^9.2.14", "framer-motion": "^12.18.1", "gsap": "^3.12.2", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "react": "^18.2.0", "react-audio-player": "^0.17.0", "react-beautiful-dnd": "^13.1.1", "react-calendar": "^4.6.1", "react-circular-progressbar": "^2.1.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-h5-audio-player": "^3.9.3", "react-helmet-async": "^1.3.0", "react-highlight-words": "^0.20.0", "react-js-loader": "^0.1.3", "react-modal": "^3.16.1", "react-modal-video": "^2.0.1", "react-particles": "^2.12.2", "react-router-dom": "^6.17.0", "react-toastify": "^10.0.5", "recharts": "^2.9.0", "recorder-js": "^1.0.7", "sass": "^1.69.5", "svg": "^0.1.0", "swiper": "^8.0.0", "tsparticles": "^2.12.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react-swc": "^3.3.2", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "tailwindcss": "^3.4.14", "vite": "^4.4.5"}}