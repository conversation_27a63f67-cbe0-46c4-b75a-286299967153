# PTE Academic Cross-Sectional Scoring Rules for AI Implementation

## Overview
PTE Academic uses an **Integrated Scoring System** where individual question types contribute to multiple communicative skills. This cross-sectional approach means a single task can impact scores across different sections, making it crucial for AI scoring systems to implement proper weightage distribution.

## Core Scoring Framework

### 1. Score Range and Structure
- **Overall Score Range**: 10-90 points
- **Communicative Skills**: Speaking, Writing, Reading, Listening (each 10-90)
- **Enabling Skills**: Grammar, Oral Fluency, Pronunciation, Spelling, Vocabulary, Written Discourse (internal scoring, not reported separately)

### 2. Scoring Methods
- **Correct/Incorrect**: Binary scoring (1 point or 0 points)
- **Partial Credit**: Gradual scoring based on accuracy, content, and linguistic skills
- **Quality Assessment**: Holistic evaluation of language proficiency

## Cross-Sectional Scoring Matrix

### Speaking & Writing Section (Part 1: 54-67 minutes)

| Question Type | Primary Skill | Secondary Skills | Approximate Questions | Cross-Sectional Impact |
|---------------|---------------|------------------|----------------------|----------------------|
| **Read Aloud** | Speaking (60%) | Reading (40%) | 6-7 | High impact on both Speaking fluency and Reading comprehension |
| **Repeat Sentence** | Speaking (70%) | Listening (30%) | 10-12 | Critical for Listening score (≈32 marks speaking, ≈20 marks listening) |
| **Describe Image** | Speaking (100%) | None | 6-7 | Pure speaking task (≈11 marks speaking, ≈9.9 marks listening indirect) |
| **Re-tell Lecture** | Speaking (50%) | Listening (50%) | 3-4 | Balanced cross-sectional contribution (≈40% listening section weight) |
| **Answer Short Question** | Speaking (80%) | Listening (20%) | 10-12 | Minimal cross-sectional impact |
| **Summarize Written Text** | Writing (60%) | Reading (40%) | 2-3 | ≈40% contribution to Reading section, ≈11% to Writing |
| **Write Essay** | Writing (100%) | None | 1-2 | Pure writing assessment |

### Reading Section (Part 2: 29-30 minutes)

| Question Type | Primary Skill | Secondary Skills | Approximate Questions | Cross-Sectional Impact |
|---------------|---------------|------------------|----------------------|----------------------|
| **Reading & Writing Fill in Blanks** | Reading (50%) | Writing (50%) | 5-6 | ≈25% of total Reading score, ≈25% of total Writing score |
| **Multiple Choice (Multiple Answer)** | Reading (100%) | None | 2-3 | Negative marking applied (-1 for incorrect) |
| **Re-order Paragraphs** | Reading (100%) | None | 2-3 | Pure reading comprehension |
| **Reading Fill in Blanks** | Reading (100%) | None | 4-5 | Single skill assessment |
| **Multiple Choice (Single Answer)** | Reading (100%) | None | 2-3 | Lower weightage in overall scoring |

### Listening Section (Part 3: 30-43 minutes)

| Question Type | Primary Skill | Secondary Skills | Approximate Questions | Cross-Sectional Impact |
|---------------|---------------|------------------|----------------------|----------------------|
| **Summarize Spoken Text** | Listening (60%) | Writing (40%) | 2-3 | Significant cross-sectional contribution |
| **Multiple Choice (Multiple Answer)** | Listening (100%) | None | 2-3 | Negative marking system |
| **Fill in the Blanks** | Listening (70%) | Writing (30%) | 2-3 | Moderate cross-sectional impact |
| **Highlight Correct Summary** | Listening (100%) | None | 2-3 | Lower overall weightage |
| **Multiple Choice (Single Answer)** | Listening (100%) | None | 2-3 | Minimal impact on overall score |
| **Select Missing Word** | Listening (100%) | None | 2-3 | Single skill assessment |
| **Highlight Incorrect Words** | Listening (80%) | Reading (20%) | 2-3 | Minor cross-sectional contribution |
| **Write from Dictation** | Listening (60%) | Writing (40%) | 3-4 | 1 mark per correctly spelled word |

## AI Implementation Guidelines

### 1. Scoring Algorithm Structure

```python
class PTECrossSectionalScoring:
    def __init__(self):
        self.skill_scores = {
            'speaking': 0,
            'writing': 0, 
            'reading': 0,
            'listening': 0
        }
        self.enabling_skills = {
            'grammar': 0,
            'oral_fluency': 0,
            'pronunciation': 0,
            'spelling': 0,
            'vocabulary': 0,
            'written_discourse': 0
        }
        
    def calculate_cross_sectional_score(self, question_type, response_data):
        # Implement specific scoring logic for each question type
        pass
```

### 2. Weightage Distribution Rules

#### High Impact Questions (Priority for AI Training):
1. **Read Aloud**: 60% Speaking + 40% Reading
2. **Repeat Sentence**: 70% Speaking + 30% Listening  
3. **Re-tell Lecture**: 50% Speaking + 50% Listening
4. **Reading & Writing Fill in Blanks**: 50% Reading + 50% Writing
5. **Summarize Spoken Text**: 60% Listening + 40% Writing

#### Medium Impact Questions:
1. **Summarize Written Text**: 60% Writing + 40% Reading
2. **Fill in Blanks (Listening)**: 70% Listening + 30% Writing
3. **Write from Dictation**: 60% Listening + 40% Writing

#### Single Skill Questions (Lower Priority):
- Multiple Choice Questions (all types)
- Describe Image
- Write Essay
- Re-order Paragraphs

### 3. Scoring Parameters by Question Type

#### Speaking Tasks Scoring:
- **Content**: 0-5 points (relevance and accuracy)
- **Oral Fluency**: 0-5 points (rhythm, phrasing, stress)
- **Pronunciation**: 0-5 points (intelligibility, phonemes)

#### Writing Tasks Scoring:
- **Content**: 0-3 points (relevance and development)
- **Grammar**: 0-2 points (accuracy and complexity)
- **Vocabulary**: 0-2 points (appropriateness and range)
- **Spelling**: 0-2 points (accuracy)
- **Written Discourse**: 0-2 points (coherence and structure)

#### Reading Tasks Scoring:
- **Comprehension**: Binary or partial based on accuracy
- **Inference**: Ability to understand implied meaning
- **Detail Recognition**: Specific information identification

#### Listening Tasks Scoring:
- **Comprehension**: Understanding of main ideas
- **Detail Recognition**: Specific information capture
- **Inference**: Understanding implied information

### 4. Overall Score Calculation

```python
def calculate_overall_score(skill_scores, enabling_skills):
    # Not a simple average - uses complex algorithmic weighting
    # Factors in both communicative and enabling skills
    # Applies Pearson's proprietary scaling algorithm
    
    weighted_communicative = apply_communicative_weights(skill_scores)
    enabling_average = calculate_enabling_average(enabling_skills)
    
    overall_score = complex_scaling_algorithm(
        weighted_communicative, 
        enabling_average
    )
    
    return round_to_nearest_whole(overall_score)
```

### 5. Special Scoring Rules

#### Negative Marking:
- **Multiple Choice (Multiple Answer)**: -1 for each incorrect selection
- **Minimum Score**: 0 (never goes below zero for any question)

#### Partial Credit Rules:
- **Write from Dictation**: 1 point per correctly spelled word
- **Fill in Blanks**: Proportional scoring based on correct responses
- **Summarize Tasks**: Weighted scoring across multiple criteria

#### Time-Based Penalties:
- **No Response**: 0 points for all criteria
- **Incomplete Response**: Proportional reduction in scores
- **Exceeding Time Limits**: Automatic cutoff, score based on completed portion

### 6. Quality Assurance Parameters

#### Content Scoring:
- **Relevance**: Response addresses the prompt
- **Completeness**: All required elements covered
- **Accuracy**: Factual correctness where applicable

#### Language Quality:
- **Fluency**: Smooth, natural delivery
- **Complexity**: Appropriate use of advanced structures
- **Appropriateness**: Register and style suitable for academic context

### 7. AI Training Recommendations

#### Focus Areas for Mock Test Development:
1. **High-Weightage Questions**: Prioritize Read Aloud, Repeat Sentence, Re-tell Lecture
2. **Cross-Sectional Impact**: Train models to recognize multi-skill contributions
3. **Enabling Skills Integration**: Include grammar, fluency, pronunciation in all assessments
4. **Partial Credit Logic**: Implement gradual scoring mechanisms
5. **Real-time Feedback**: Provide immediate scoring with detailed breakdowns

#### Validation Metrics:
- **Accuracy Rate**: ≥95% correlation with official PTE scoring
- **Consistency**: Minimal variation in repeated assessments
- **Fairness**: Unbiased evaluation across different accents and backgrounds
- **Speed**: Real-time scoring capability for mock tests

### 8. Implementation Checklist

- [ ] Cross-sectional weightage matrix implemented
- [ ] All 20 question types covered
- [ ] Enabling skills integration completed
- [ ] Partial credit algorithms developed
- [ ] Negative marking system active
- [ ] Overall score calculation verified
- [ ] Quality assurance parameters set
- [ ] Real-time feedback system ready
- [ ] Accuracy validation completed
- [ ] Performance benchmarking done

## Technical Notes for AI Development

### Data Processing:
- **Audio Analysis**: Advanced speech recognition for pronunciation, fluency
- **Text Analysis**: NLP for grammar, vocabulary, content assessment  
- **Image Processing**: Computer vision for describe image tasks
- **Cross-Modal Integration**: Combining multiple input types for comprehensive scoring

### Machine Learning Models:
- **Multi-task Learning**: Single model handling multiple question types
- **Transfer Learning**: Leveraging pre-trained language models
- **Ensemble Methods**: Combining multiple specialized models
- **Continuous Learning**: Model updates based on new data patterns

This comprehensive framework ensures your AI mock test system accurately replicates the official PTE Academic scoring methodology with proper cross-sectional weightage distribution.