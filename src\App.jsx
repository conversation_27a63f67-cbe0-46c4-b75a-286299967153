import "./styles/index.scss";

import "@fortawesome/fontawesome-svg-core/styles.css";
import "react-calendar/dist/Calendar.css";
import { BrowserRouter, Routes, Route } from "react-router-dom";

import AOS from "aos";
import "aos/dist/aos.css";
import { useEffect, useState } from "react";
import Context from "@/context/Context";
import HomePage1 from "./pages";

import PricingPage from "./components/speech-ace-component/pricing.jsx";

import TermsPage from "./pages/others/terms";
import HelpCenterPage from "./pages/others/help-center";
import LoginPage from "./pages/others/login";
import SignupPage from "./pages/others/signup";
import SignupVerifyOtpPage from "./pages/others/signup-verify-otp";
import ForgotPasswordPage from "./pages/others/forgot-password";
import VerifyOtpPage from "./pages/others/verify-otp";
import ResetPasswordPage from "./pages/others/reset-password";
import UIElementsPage from "./pages/others/ui-elements";

import ScrollTopBehaviour from "./components/common/ScrollTopBehaviour";
import NotFoundPage from "./pages/not-found";
import { CateogoryDetail } from "./pages/cateogoryDetail";
import AsyncStorage from "@react-native-async-storage/async-storage";
import CommonTest from "./pages/commonTest/CommonTest";
import MockTest from "./pages/mocktest/MockTest";
import ResultComponent from "./components/speech-ace-component/MockTestResult";
import ResultPage from "./pages/resultPage/MockTestResultPage";
import { AuthProvider } from "./components/others/AuthContext";
import ManagePlans from "./pages/membership/ManagePlans";
import AudioCompressionProvider from "./components/common/AudioCompressionProvider";
import MockTestWithIntro from "./pages/mocktest-intro/MockTestWithIntro";

function App() {
  useEffect(() => {
    AOS.init({
      duration: 700,
      offset: 120,
      easing: "ease-out",
      once: true,
    });
  }, []);

  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    const checkAuthStatus = async () => {
      const token = localStorage.getItem("token");
      console.log(token, "..");
      if (token) {
        setIsAuthenticated(true);
      }
    };
    checkAuthStatus();
  }, []);

  useEffect(() => {
    console.log(isAuthenticated, "...>>");
  }, [isAuthenticated]);

  return (
    <>
      <Context>
        <BrowserRouter>
          <AudioCompressionProvider>
            <AuthProvider>
              <Routes>
                <Route path="/">
                  {/* <Route path="/" element={<LoginPage />} /> */}
                  <Route path="/" element={<HomePage1 />} />
                  <Route path="login" element={<LoginPage />} />
                  {/*<Route path="signUp" element={<SignupPage />} />
              <Route path="home-1" element={<HomePage1 />} />
              <Route path="home-2" element={<HomePage2 />} />
              <Route path="home-3" element={<HomePage3 />} />
              <Route path="home-4" element={<HomePage4 />} />
              <Route path="home-5" element={<HomePage5 />} />
              <Route path="home-6" element={<HomePage6 />} />
              <Route path="home-7" element={<HomePage7 />} />
              <Route path="home-8" element={<HomePage8 />} />
              <Route path="home-9" element={<HomePage9 />} />
              <Route path="home-10" element={<HomePage10 />} />*/}

                  {/* <Route path="courses-list-1" element={<CourseListPage1 />} />
              <Route path="courses-list-2" element={<CourseListPage2 />} />
              <Route path="courses-list-3" element={<CourseListPage3 />} />
              <Route path="courses-list-4" element={<CourseListPage4 />} />
              <Route path="courses-list-5" element={<CourseListPage5 />} />
              <Route path="courses-list-6" element={<CourseListPage6 />} />
              <Route path="courses-list-7" element={<CourseListPage7 />} />
              <Route path="courses-list-8" element={<CourseListPage8 />} /> */}
                  <Route
                    path="/categoryDetail/:practiceId"
                    element={<CateogoryDetail />}
                  />
                  <Route
                    path="/commonTest/:practiceId"
                    element={<CommonTest />}
                  />
                  <Route path="/mockTest/:practiceId" element={<MockTest />} />
                  <Route path="/mockTest/result" element={<ResultPage />} />
                  <Route
                    path="/mocktest-intro-demo"
                    element={<MockTestWithIntro />}
                  />
                  {/* <Route path="courses/:id" element={<CourseSinglePage1 />} />
              <Route
                path="courses-single-2/:id"
                element={<CourseSinglePage2 />}
              />
              <Route
                path="courses-single-3/:id"
                element={<CourseSinglePage3 />}
              />
              <Route
                path="courses-single-4/:id"
                element={<CourseSinglePage4 />}
              />
              <Route
                path="courses-single-5/:id"
                element={<CourseSinglePage5 />}
              />
              <Route
                path="courses-single-6/:id"
                element={<CourseSinglePage6 />}
              /> */}

                  {/* <Route path="course-cart" element={<CourseCartPage />} />
              <Route path="course-checkout" element={<CourseCheckoutPage />} />
              {/* <Route path='courses-single-5/:id' element={<CourseSinglePage6 />} /> */}

                  {/* <Route path="lesson-single-1" element={<LessonSinglePage1 />} />
              <Route path="lesson-single-2" element={<LessonSinglePage2 />} />

              <Route
                path="instructors-list-1"
                element={<InstractorListPage1 />}
              />
              <Route
                path="instructors-list-2"
                element={<InstractorListPage2 />}
              />

              <Route
                path="instructors/:id"
                element={<InstractorSinglePage />}
              />

              <Route
                path="instructor-become"
                element={<InstractoBacomePage />}
              /> */}

                  {/* <Route path="dashboard" element={<DashboardPage />} />
              <Route path="dshb-courses" element={<DshbCoursesPage />} />
              <Route path="dshb-bookmarks" element={<DshbBookmarksPage />} />
              <Route path="dshb-listing" element={<DshbListingPage />} />
              <Route path="dshb-reviews" element={<DshbReviewsPage />} />
              <Route path="dshb-settings" element={<DshbSettingsPage />} />
              <Route
                path="dshb-administration"
                element={<DshbAdministrationPage />}
              />
              <Route path="dshb-assignment" element={<DshbAssignmentPage />} />
              <Route path="dshb-calendar" element={<DshbCalenderPage />} />
              <Route path="dshb-dashboard" element={<DshbDashboardPage />} />
              <Route path="dshb-dictionary" element={<DshbDictionaryPage />} />
              <Route path="dshb-forums" element={<DshbForumsPage />} />
              <Route path="dshb-grades" element={<DshbGradesPage />} />
              <Route path="dshb-messages" element={<DshbMessagesPage />} />
              <Route
                path="dshb-participants"
                element={<DshbPartcipentPage />}
              />
              <Route path="dshb-quiz" element={<DshbQuizPage />} />
              <Route path="dshb-survey" element={<DshbServeyPage />} />

              <Route path="event-list-1" element={<EventListPage1 />} />
              <Route path="event-list-2" element={<EventListPage2 />} />
              <Route path="events/:id" element={<EventSingPage />} />
              <Route path="event-cart" element={<EventCartPage />} />
              <Route path="event-checkout" element={<EventCheckoutPage />} />

              <Route path="blog-list-1" element={<BlogListpage1 />} />
              <Route path="blog-list-2" element={<BlogListpage2 />} />
              <Route path="blog-list-3" element={<BlogListpage3 />} />
              <Route path="blogs/:id" element={<BlogdetailsPage />} />

              <Route path="about-1" element={<AboutPage1 />} />
              <Route path="about-2" element={<AboutPage2 />} />

              <Route path="contact-1" element={<ContactPage1 />} />
              <Route path="contact-2" element={<ContactPage2 />} />

              <Route path="shop-cart" element={<ShopCartPage />} />
              <Route path="shop-checkout" element={<ShopCheckoutPage />} />
              <Route path="shop-list" element={<ShopListPage />} />
              <Route path="shop-order" element={<ShopOrderPage />} />
              <Route path="shop/:id" element={<ShopdetailsPage />} />  */}

                  <Route path="pricing" element={<PricingPage />} />
                  <Route path="manage-plans" element={<ManagePlans />} />
                  <Route path="not-found" element={<NotFoundPage />} />
                  <Route path="*" element={<NotFoundPage />} />
                  <Route path="terms" element={<TermsPage />} />
                  <Route path="help-center" element={<HelpCenterPage />} />
                  <Route path="login" element={<LoginPage />} />
                  <Route path="signup" element={<SignupPage />} />
                  <Route
                    path="signup-verify-otp"
                    element={<SignupVerifyOtpPage />}
                  />
                  <Route
                    path="forgot-password"
                    element={<ForgotPasswordPage />}
                  />
                  <Route path="verify-otp" element={<VerifyOtpPage />} />
                  <Route
                    path="reset-password"
                    element={<ResetPasswordPage />}
                  />
                  <Route path="ui-elements" element={<UIElementsPage />} />
                </Route>
              </Routes>
              <ScrollTopBehaviour />
            </AuthProvider>
          </AudioCompressionProvider>
        </BrowserRouter>
      </Context>
    </>
  );
}

export default App;
