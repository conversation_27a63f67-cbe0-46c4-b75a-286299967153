// This service will handle dictionary API calls
// and provide fallback mechanisms if the primary API fails

const DictionaryService = {
  // Primary API - Free Dictionary API
  async getWordDefinitionPrimary(word) {
    const response = await fetch(
      `https://api.dictionaryapi.dev/api/v2/entries/en/${word}`
    );
    if (!response.ok) {
      throw new Error("Word not found in primary dictionary");
    }
    return await response.json();
  },

  // Secondary API - WordsAPI (requires API key)
  async getWordDefinitionSecondary(word, apiKey) {
    try {
      const response = await fetch(
        `https://wordsapiv1.p.rapidapi.com/words/${word}`,
        {
          headers: {
            "X-RapidAPI-Key": apiKey,
            "X-RapidAPI-Host": "wordsapiv1.p.rapidapi.com",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Word not found in secondary dictionary");
      }

      const data = await response.json();

      // Transform the data to match the format from the primary API
      return this.transformSecondaryApiData(data, word);
    } catch (error) {
      throw error;
    }
  },

  // Transform WordsAPI data to match Free Dictionary API format
  transformSecondaryApiData(data, word) {
    const meanings = [];

    if (data.results && data.results.length > 0) {
      // Group definitions by part of speech
      const definitionsByPartOfSpeech = {};

      data.results.forEach((result) => {
        const partOfSpeech = result.partOfSpeech || "unknown";

        if (!definitionsByPartOfSpeech[partOfSpeech]) {
          definitionsByPartOfSpeech[partOfSpeech] = [];
        }

        definitionsByPartOfSpeech[partOfSpeech].push({
          definition: result.definition,
          example: result.examples ? result.examples[0] : "",
          synonyms: result.synonyms || [],
          antonyms: result.antonyms || [],
        });
      });

      // Convert to the expected format
      Object.entries(definitionsByPartOfSpeech).forEach(
        ([partOfSpeech, definitions]) => {
          meanings.push({
            partOfSpeech,
            definitions,
          });
        }
      );
    }

    // Create a response object that mimics the Free Dictionary API format
    return [
      {
        word,
        phonetics: [
          {
            text: data.pronunciation ? `/${data.pronunciation.all}/` : "",
            audio: "",
          },
        ],
        meanings,
      },
    ];
  },

  // Fallback for when both APIs fail
  getFallbackDefinition(word) {
    return [
      {
        word,
        phonetics: [{ text: `/${word}/`, audio: "" }],
        meanings: [
          {
            partOfSpeech: "unknown",
            definitions: [
              {
                definition:
                  "Definition not available. Try checking a dictionary.",
                example: "",
                synonyms: [],
                antonyms: [],
              },
            ],
          },
        ],
      },
    ];
  },

  // Main function to get word definition, trying multiple sources
  async getWordDefinition(word, apiKey = "") {
    try {
      // Try primary API first
      const data = await this.getWordDefinitionPrimary(word);
      return data;
    } catch (primaryError) {
      try {
        // If primary API fails and we have an API key for the secondary
        if (apiKey) {
          const secondaryData = await this.getWordDefinitionSecondary(
            word,
            apiKey
          );
          return secondaryData;
        }
        throw new Error("No API key for secondary source");
      } catch (secondaryError) {
        // If both APIs fail, use fallback
        console.log("All dictionary APIs failed, using fallback", {
          primaryError,
          secondaryError,
        });
        return this.getFallbackDefinition(word);
      }
    }
  },

  // Get pronunciation URLs, supporting both UK and US accents
  getPronunciationUrls(data) {
    if (!data || !data[0] || !data[0].phonetics) {
      return { uk: null, us: null };
    }

    const phonetics = data[0].phonetics;

    // Try to find UK and US specific pronunciations
    let ukPronunciation =
      phonetics.find(
        (p) =>
          p.audio && (p.audio.includes("uk") || p.audio.includes("british"))
      )?.audio || null;

    let usPronunciation =
      phonetics.find(
        (p) =>
          p.audio && (p.audio.includes("us") || p.audio.includes("american"))
      )?.audio || null;

    // If no specific accent was found but there's audio available, use the first one
    if (
      !ukPronunciation &&
      !usPronunciation &&
      phonetics.some((p) => p.audio)
    ) {
      const firstAudioUrl = phonetics.find((p) => p.audio)?.audio;
      ukPronunciation = firstAudioUrl;
      usPronunciation = firstAudioUrl;
    }

    return { uk: ukPronunciation, us: usPronunciation };
  },
};

export default DictionaryService;
