import { useEffect, useState } from 'react';
import { fetchAllCategories, fetchSubCategory, fetchSubCategoryDetail} from '../services/services';

export const useAllCategory = () => {
  const [category, setCategory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCategory = async () => {
      try {
        setLoading(true);
        const data = await fetchAllCategories();
        setCategory(data.data);
      } catch (error) { 
        setError(error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategory();
  }, []);

  return { category, loading, error };
};

export const useSubCategory = (selectedSubCategoryId) => {
  const [category, setCategory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (selectedSubCategoryId) {
      const fetchCategory = async () => {
        try {
          setLoading(true);
          const data = await fetchSubCategory(selectedSubCategoryId);
          setCategory(data.data);
        } catch (error) {
          setError(error);
        } finally {
          setLoading(false);
        }
      };

      fetchCategory();
    }
  }, [selectedSubCategoryId]);

  return { category, loading, error };
};

export const useSubCategorydetail = (selectedSubCategoryId) => {
  const [category, setCategory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (selectedSubCategoryId) {
      const fetchCategory = async () => {
        try {
          setLoading(true);
          const data = await fetchSubCategoryDetail(selectedSubCategoryId);
          setCategory(data.data);
        } catch (error) {
          setError(error);
        } finally {
          setLoading(false);
        }
      };

      fetchCategory();
    }
  }, [selectedSubCategoryId]);

  return { category, loading, error };
};


