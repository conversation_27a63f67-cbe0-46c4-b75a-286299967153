import axios from "axios";
import { server } from "./server";

export const postRequests = async (API_URL, requestData) => {
  console.log("Calling HTTP POST Request....");

  const config = {
    headers: {
      "Content-Type": "application/json",
    },
  };

  try {
    const res = await axios.post(API_URL, requestData, config);
    console.log("postRequest res.data ==  ", res.data);
    return res; // Return the full axios response object
  } catch (error) {
    console.log("Post Api URL while error ===  ", API_URL, "  ", requestData);
    console.log(
      "Error caught at service.js file = ",
      error.response?.data?.error?.details || error.message
    );
    throw error; // Throw the error so the caller can handle it
  }
};

export const postRequest = async (API_URL, requestData) => {
  console.log("Calling HTTP POST Request....");

  const config = {
    headers: {
      // 'Content-Type': 'application/json',
    },
  };
  let response;
  await axios
    .post(API_URL, requestData, config)
    .then((res) => {
      console.log("postRequest res.data ==  ", res.data);
      if (res.status) {
        response = res.data;
      } else {
        response = res.statusText;
      }
    })
    .catch((error) => {
      console.log("Post Api URL while error ===  ", API_URL, "  ", requestData);

      console.log(
        "Error caught at servidce.js file = ",
        error.response.data.error.details
      );
    });
  return response;
};
export const getRequest = async (API_URL, header) => {
  let response;
  const config = {
    params: header,
    //  Authorization: `Bearer ${token}`
    // 'Content-Type': 'application/json',

    // 'token':'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IlZWdEE2TXI5Q2UiLCJpYXQiOjE2NTYwOTY1MTAsImV4cCI6MjI2MDg5NjUxMH0.JfrmN09MvKzP-840Yn-VuE2IuSlbS4FfJyE5ReMBzW8'
    // 'Authorization': "Bearer " + "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IlZWdEE2TXI5Q2UiLCJpYXQiOjE2NTYwOTY1MTAsImV4cCI6MjI2MDg5NjUxMH0.JfrmN09MvKzP-840Yn-VuE2IuSlbS4FfJyE5ReMBzW8"
  };

  await axios
    .get(API_URL)
    .then((res) => {
      if (res.status) {
        response = res.data;
        // console.log(response);
      } else {
        response = res.statusText;
      }
    })
    .catch((error) => {
      console.log("Get Api URL while error ===  ", API_URL);
      console.log(
        "Error caught at service.js file = ",
        error.response.data.error
      );
    });
  return response;
};
export const patchRequest = async (API_URL, requestData) => {
  // console.log('Calling HTTP POST Request....')

  const config = {
    headers: {
      //  Authorization: `Bearer ${token}`
      // 'Content-Type': 'application/json',
      // 'authtoken':token
      // 'token':'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IlZWdEE2TXI5Q2UiLCJpYXQiOjE2NTYwOTY1MTAsImV4cCI6MjI2MDg5NjUxMH0.JfrmN09MvKzP-840Yn-VuE2IuSlbS4FfJyE5ReMBzW8'
      // 'Authorization': "Bearer " + "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IlZWdEE2TXI5Q2UiLCJpYXQiOjE2NTYwOTY1MTAsImV4cCI6MjI2MDg5NjUxMH0.JfrmN09MvKzP-840Yn-VuE2IuSlbS4FfJyE5ReMBzW8"
    },
  };
  let response;
  await axios
    .patch(API_URL, requestData, config)
    .then((res) => {
      // console.log('postRequest res ==  ', res);
      // console.log('postRequest res.data ==  ', res.data);
      if (res.status) {
        response = res.status;
      } else {
        response = res.message;
      }
    })
    .catch((error) => {
      console.log("Post Api URL while error ===  ", API_URL, "  ", requestData);
      console.log(
        "Error caught at service.js file = ",
        error.response.data.error.details
      );
    });
  return response;
};

export const uploadFile = async (fileName, fileData, fileType) => {
  try {
    const uri = server.uri + "upload";
    const uploadFileData = await postRequest(uri, {
      fileName: fileName,
      fileData,
      fileType,
    });
    if (uploadFileData) {
      return uploadFileData?.fileUrl;
    }
  } catch (error) {
    console.log(error);
  }
};
