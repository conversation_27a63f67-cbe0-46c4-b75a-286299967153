import { server } from "./server";
import { getRequest, postRequest, patchRequest } from "./controller";

export const mocktestService = {
  // Get mocktest with user-specific logs
  async getMocktestWithLogs(mocktestId, userId) {
    try {
      const filter = {
        where: {},
        include: [
          {
            relation: "logs",
            scope: {
              where: {
                userId: userId,
                mocktestId: mocktestId,
              },
            },
          },
        ],
      };

      const uri = `${server.uri}mocktests?filter=${encodeURIComponent(
        JSON.stringify(filter)
      )}`;
      const mocktests = await getRequest(uri);

      if (mocktests && mocktests.length > 0) {
        // Find the specific mocktest
        const mocktest = mocktests.find(
          (mt) => mt.testId === mocktestId || mt.id === mocktestId
        );
        return mocktest || null;
      }

      return null;
    } catch (error) {
      console.error("Error fetching mocktest with logs:", error);
      throw error;
    }
  },

  // Create new mocktest log for first-time attempt
  async createMocktestLog(mocktestId, userId, testData) {
    try {
      const logData = {
        mocktestId: mocktestId,
        userId: userId,
        testName: testData.testName || "PTE Practice Test",
        attemptNumber: 1,
        startTime: new Date().toISOString(),
        currentSection: 0,
        currentQuestion: 0,
        status: "in_progress",
        sections: {
          speaking: { completed: false, timeSpent: 0 },
          reading: { completed: false, timeSpent: 0 },
          listening: { completed: false, timeSpent: 0 },
        },
        totalTimeSpent: 0,
        completedAt: null,
        finalScore: null,
      };

      const uri = `${server.uri}logs`;
      const result = await postRequest(uri, logData);

      if (result) {
        // Store logId for reference
        localStorage.setItem("logId", result.id || result.logId);
        localStorage.setItem("attemptNumber", "1");
        return result;
      }

      return null;
    } catch (error) {
      console.error("Error creating mocktest log:", error);
      throw error;
    }
  },

  // Update existing mocktest log for subsequent attempts
  async updateMocktestLog(logId, updateData) {
    try {
      const uri = `${server.uri}logs/${logId}`;
      const result = await patchRequest(uri, updateData);
      return result;
    } catch (error) {
      console.error("Error updating mocktest log:", error);
      throw error;
    }
  },

  // Get or create mocktest attempt
  async getOrCreateMocktestAttempt(mocktestId, userId) {
    try {
      // First, get mocktest with existing logs
      const mocktest = await this.getMocktestWithLogs(mocktestId, userId);

      if (!mocktest) {
        throw new Error("Mocktest not found");
      }

      // Check if user has existing logs for this mocktest
      const existingLogs = mocktest.logs || [];

      if (existingLogs.length > 0) {
        // User has attempted this test before
        // COMMENTED OUT RESUME FUNCTIONALITY FOR TESTING
        // const lastLog = existingLogs[existingLogs.length - 1];
        // if (lastLog.status === "in_progress") {
        //   // Resume existing attempt
        //   localStorage.setItem("logId", lastLog.id || lastLog.logId);
        //   localStorage.setItem(
        //     "attemptNumber",
        //     lastLog.attemptNumber.toString()
        //   );
        //   return {
        //     type: "resume",
        //     logId: lastLog.id || lastLog.logId,
        //     attemptNumber: lastLog.attemptNumber,
        //     currentSection: lastLog.currentSection || 0,
        //     currentQuestion: lastLog.currentQuestion || 0,
        //     mocktest: mocktest,
        //   };
        // } else {

        // Always create new attempt (resume functionality disabled)
        const newAttemptNumber = existingLogs.length + 1;
        const newLog = await this.createNewAttempt(
          mocktestId,
          userId,
          newAttemptNumber,
          mocktest
        );
        return {
          type: "new_attempt",
          logId: newLog.id || newLog.logId,
          attemptNumber: newAttemptNumber,
          currentSection: 0,
          currentQuestion: 0,
          mocktest: mocktest,
        };
      } else {
        // First time attempting this test
        const newLog = await this.createMocktestLog(
          mocktestId,
          userId,
          mocktest
        );
        return {
          type: "first_attempt",
          logId: newLog.id || newLog.logId,
          attemptNumber: 1,
          currentSection: 0,
          currentQuestion: 0,
          mocktest: mocktest,
        };
      }
    } catch (error) {
      console.error("Error in getOrCreateMocktestAttempt:", error);
      throw error;
    }
  },

  // Create new attempt for existing user
  async createNewAttempt(mocktestId, userId, attemptNumber, testData) {
    try {
      const logData = {
        mocktestId: mocktestId,
        userId: userId,
        testName: testData.testName || "PTE Practice Test",
        attemptNumber: attemptNumber,
        startTime: new Date().toISOString(),
        currentSection: 0,
        currentQuestion: 0,
        status: "in_progress",
        sections: {
          speaking: { completed: false, timeSpent: 0 },
          reading: { completed: false, timeSpent: 0 },
          listening: { completed: false, timeSpent: 0 },
        },
        totalTimeSpent: 0,
        completedAt: null,
        finalScore: null,
      };

      const uri = `${server.uri}logs`;
      const result = await postRequest(uri, logData);

      if (result) {
        localStorage.setItem("logId", result.id || result.logId);
        localStorage.setItem("attemptNumber", attemptNumber.toString());
        return result;
      }

      return null;
    } catch (error) {
      console.error("Error creating new attempt:", error);
      throw error;
    }
  },

  // Save and exit functionality
  async saveAndExit(logId, currentData) {
    try {
      const updateData = {
        currentSection: currentData.section,
        currentQuestion: currentData.question,
        status: "paused",
        lastSavedAt: new Date().toISOString(),
        sections: currentData.sections,
        totalTimeSpent: currentData.totalTimeSpent,
      };

      const uri = `${server.uri}logs/${logId}`;
      const result = await patchRequest(uri, updateData);
      return result;
    } catch (error) {
      console.error("Error saving and exiting:", error);
      throw error;
    }
  },

  // Complete test
  async completeTest(logId, finalData) {
    try {
      const updateData = {
        status: "completed",
        completedAt: new Date().toISOString(),
        finalScore: finalData.score,
        sections: finalData.sections,
        totalTimeSpent: finalData.totalTimeSpent,
      };

      const uri = `${server.uri}logs/${logId}`;
      const result = await patchRequest(uri, updateData);
      return result;
    } catch (error) {
      console.error("Error completing test:", error);
      throw error;
    }
  },

  // Get user's attempt history for a mocktest
  async getUserAttemptHistory(mocktestId, userId) {
    try {
      const filter = {
        where: {
          mocktestId: mocktestId,
          userId: userId,
        },
        order: "attemptNumber ASC",
      };

      const uri = `${server.uri}logs?filter=${encodeURIComponent(
        JSON.stringify(filter)
      )}`;
      const logs = await getRequest(uri);
      return logs || [];
    } catch (error) {
      console.error("Error fetching attempt history:", error);
      throw error;
    }
  },

  // Check if user has any mocktest attempts (for free trial eligibility)
  async hasAnyMocktestAttempts(userId) {
    try {
      const filter = {
        where: {
          userId: userId,
        },
        limit: 1, // We only need to know if any exist
      };

      const uri = `${server.uri}logs?filter=${encodeURIComponent(
        JSON.stringify(filter)
      )}`;
      const logs = await getRequest(uri);
      return logs && logs.length > 0;
    } catch (error) {
      console.error("Error checking user mocktest attempts:", error);
      throw error;
    }
  },
};

export default mocktestService;
