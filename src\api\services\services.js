import api from "./api";

export const fetchAllCategories = async () => {
    try {
      const response = await api.get('/user/categories/getall');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch latest articles:', error);
      throw error;
    }
  };

  export const fetchSubCategory = async (sub_category_id) => {
    try {
      const token = localStorage.getItem('token');
      console.log(token,"..")
      const response = await api.get(`/user/practice/getall?sub_category_id=${sub_category_id}`, {
        headers: {
          Authorization: `Bearer ${token}`, 
        },
      });
      console.log(response,"....")
      return response.data;
    } catch (error) {
      console.error("Error fetching data:", error);
      throw error;
    }
  };
  
  export const fetchSubCategoryDetail = async (sub_category_id) => {
    try {
      const token = localStorage.getItem('token');
      console.log(token,"..")
      const response = await api.get(`/user/practice/get?practice_id=${sub_category_id}`, {
        headers: {
          Authorization: `Bearer ${token}`, 
        },
      });
      console.log(response,"....")
      return response.data;
    } catch (error) {
      console.error("Error fetching data:", error);
      throw error;
    }
  };
  