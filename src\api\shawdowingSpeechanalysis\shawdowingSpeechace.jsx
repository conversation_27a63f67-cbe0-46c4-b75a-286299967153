// Enhanced service for speech analysis with detailed word-level scoring

const EnhancedSpeechAnalysisService = {
  /**
   * Get detailed speech analysis including word-level scores
   * @param {string} text - The text to analyze
   * @param {string} questionId - Optional question ID for cached results
   * @returns {Promise<Object>} - Comprehensive speech analysis data
   */
  async getDetailedSpeechAnalysis(text, questionId = null) {
    try {
      // Use production API for detailed speech analysis
      const apiUrl =
        "https://deep-backend.vercel.app/api/detailed-speech-analysis";

      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          text: text,
          questionId: questionId,
          analysisType: "full-pronunciation-analysis",
        }),
      });

      if (!response.ok) {
        console.error(`API error: ${response.status}`);
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Failed to get detailed speech analysis:", error);
      // Fall back to local basic analysis if the detailed analysis fails
      return this.generateBasicAnalysis(text);
    }
  },

  /**
   * Process raw speech analysis data to extract word-level performance metrics
   * @param {Object} analysisData - Raw analysis data from API
   * @returns {Object} - Processed data with word-level metrics
   */
  processRawAnalysisData(analysisData) {
    // If already processed or not available, return as is
    if (!analysisData || analysisData.processed) {
      return analysisData;
    }

    try {
      const processed = {
        ...analysisData,
        processed: true,
        wordScores: [],
      };

      // Extract word scores if available
      if (analysisData.speech_score?.word_score_list) {
        processed.wordScores = analysisData.speech_score.word_score_list.map(
          (word) => ({
            word: word.word,
            qualityScore: word.quality_score,
            syllables: word.syllable_score_list || [],
            phonemes: word.phone_score_list || [],
            problemAreas: this.identifyProblemAreas(word),
          })
        );
      }

      return processed;
    } catch (error) {
      console.error("Error processing analysis data:", error);
      return analysisData;
    }
  },

  /**
   * Identify specific problem areas in word pronunciation
   * @param {Object} wordData - Word-level data from analysis
   * @returns {Array} - List of problem areas
   */
  identifyProblemAreas(wordData) {
    const problems = [];

    // Check overall word quality
    if (wordData.quality_score < 60) {
      problems.push("overall-quality");
    }

    // Check phones (phonemes)
    if (wordData.phone_score_list) {
      wordData.phone_score_list.forEach((phone) => {
        if (phone.quality_score < 50) {
          problems.push(`phone-${phone.phone}`);
        }
      });
    }

    // Check syllable stress
    if (wordData.syllable_score_list) {
      wordData.syllable_score_list.forEach((syllable) => {
        if (syllable.stress_score < 80 && syllable.stress_level > 0) {
          problems.push("stress-pattern");
        }
      });
    }

    return problems;
  },

  /**
   * Generates speech patterns from analysis data
   * @param {Object} analysisData - Speech analysis data
   * @returns {Object} - Speech patterns categorized
   */
  extractSpeechPatterns(analysisData) {
    const patterns = {
      pauses: [],
      loss: [],
      linking: [],
      weak: [],
    };

    if (!analysisData || !analysisData.speech_score) {
      return patterns;
    }

    // Process word list
    const wordList = analysisData.speech_score.word_score_list || [];

    wordList.forEach((word, index) => {
      // Identify weak pronunciations
      if (word.quality_score < 60) {
        patterns.weak.push(index);
      }

      // Identify potential pauses
      if (
        word.ending_punctuation ||
        (analysisData.speech_score?.fluency?.all_pause_list &&
          analysisData.speech_score.fluency.all_pause_list.some((pause) => {
            const wordStart = word.phone_score_list?.[0]?.extent?.[0] || 0;
            const wordEnd =
              word.phone_score_list?.[word.phone_score_list?.length - 1]
                ?.extent?.[1] || 0;
            return pause[0] >= wordStart && pause[1] <= wordEnd;
          }))
      ) {
        patterns.pauses.push(index);
      }

      // Identify words likely to be reduced
      const cleanWord = word.word.toLowerCase();
      if (
        [
          "the",
          "a",
          "an",
          "of",
          "to",
          "for",
          "and",
          "in",
          "on",
          "at",
          "by",
        ].includes(cleanWord)
      ) {
        patterns.loss.push(index);
      }

      // Identify potential linking
      if (index < wordList.length - 1) {
        const nextWord = wordList[index + 1];
        if (word.phone_score_list && nextWord.phone_score_list) {
          const lastPhone =
            word.phone_score_list[word.phone_score_list.length - 1];
          const firstPhone = nextWord.phone_score_list[0];

          // Check consonant to vowel transitions
          const vowels = [
            "aa",
            "ae",
            "ah",
            "ao",
            "aw",
            "ay",
            "eh",
            "er",
            "ey",
            "ih",
            "iy",
            "ow",
            "oy",
            "uh",
            "uw",
          ];

          if (
            lastPhone &&
            firstPhone &&
            !vowels.includes(lastPhone.phone) &&
            vowels.includes(firstPhone.phone)
          ) {
            patterns.linking.push(index);
          }
        }
      }
    });

    return patterns;
  },

  /**
   * Generate basic analysis for fallback
   * @param {string} text - The text to analyze
   * @returns {Object} - Basic analysis structure
   */
  generateBasicAnalysis(text) {
    // Split text into words
    const words = text.split(/\s+/);

    // Create basic word score list
    const wordScoreList = words.map((word) => {
      const cleanWord = word.replace(/[.,!?;:"'()]/g, "");
      return {
        word: cleanWord,
        quality_score: this.estimateWordDifficulty(cleanWord) ? 75 : 90,
        ending_punctuation: word.match(/[.,!?;:]$/)
          ? word.match(/[.,!?;:]$/)[0]
          : null,
        phone_score_list: [],
        syllable_score_list: [],
      };
    });

    return {
      status: "success",
      speech_score: {
        transcript: text,
        word_score_list: wordScoreList,
        ielts_score: {
          pronunciation: 5,
          fluency: 5,
          overall: 5,
        },
        cefr_score: {
          pronunciation: "B1",
          fluency: "B1",
          overall: "B1",
        },
      },
    };
  },

  /**
   * Estimate if a word is likely difficult to pronounce
   * @param {string} word - The word to check
   * @returns {boolean} - True if the word might be difficult
   */
  estimateWordDifficulty(word) {
    const lowerWord = word.toLowerCase();

    // Check for common difficult sound combinations
    const difficultPatterns = [
      "th",
      "ph",
      "ch",
      "sh",
      "tl",
      "dl",
      "str",
      "spr",
      "scr",
      "xtr",
      "ght",
      "sch",
      "spl",
      "thm",
      "rld",
      "dth",
      "ngth",
    ];

    if (difficultPatterns.some((pattern) => lowerWord.includes(pattern))) {
      return true;
    }

    // Check word length
    if (lowerWord.length > 8) {
      return true;
    }

    // Check for three or more consonants in a row
    if (/[bcdfghjklmnpqrstvwxyz]{3,}/i.test(lowerWord)) {
      return true;
    }

    return false;
  },

  /**
   * Get basic speech patterns for the provided text
   * @param {string} text - The text to analyze
   * @returns {Promise<Object>} - Speech pattern data
   */
  async getSpeechPatterns(text, questionId = null) {
    try {
      // Try to get detailed analysis first
      const detailedAnalysis = await this.getDetailedSpeechAnalysis(
        text,
        questionId
      );

      // Extract patterns from the detailed analysis
      return this.extractSpeechPatterns(detailedAnalysis);
    } catch (error) {
      console.error("Error in speech pattern analysis:", error);

      // Fall back to the old local analysis method
      return this.analyzeSpeechPatternsLocally(text);
    }
  },

  /**
   * Analyze speech patterns locally (fallback method)
   * @param {string} text - The text to analyze
   * @returns {Object} - Speech pattern data
   */
  analyzeSpeechPatternsLocally(text) {
    // Split text into words for analysis
    const words = text.split(/\s+/);

    // Initialize patterns
    const patterns = {
      pauses: [],
      loss: [],
      linking: [],
      weak: [],
    };

    // Analyze each word
    words.forEach((word, index) => {
      const cleanWord = word.replace(/[.,!?;:"'()]/g, "").toLowerCase();

      // Words followed by pauses (typically at punctuation)
      if (
        word.includes(",") ||
        word.includes(".") ||
        word.includes("!") ||
        word.includes("?") ||
        word.includes(";") ||
        word.includes(":")
      ) {
        patterns.pauses.push(index);
      }

      // Words that are commonly reduced or lost in speech
      if (
        [
          "the",
          "a",
          "an",
          "of",
          "to",
          "for",
          "and",
          "that",
          "with",
          "in",
          "by",
        ].includes(cleanWord)
      ) {
        patterns.loss.push(index);
      }

      // Words that should link to the next word
      if (index < words.length - 1) {
        const nextWord = words[index + 1]
          .replace(/[.,!?;:"'()]/g, "")
          .toLowerCase();

        // Consonant-vowel linking
        if (
          cleanWord.match(/[bcdfghjklmnpqrstvwxz]$/) &&
          nextWord.match(/^[aeiou]/)
        ) {
          patterns.linking.push(index);
        }

        // Common linking patterns
        if (cleanWord.endsWith("n") && nextWord.match(/^[aeiou]/)) {
          patterns.linking.push(index);
        }
        if (cleanWord.endsWith("r") && nextWord.match(/^[aeiou]/)) {
          patterns.linking.push(index);
        }
        if (
          (cleanWord.endsWith("t") || cleanWord.endsWith("d")) &&
          (nextWord.startsWith("y") || nextWord.startsWith("th"))
        ) {
          patterns.linking.push(index);
        }
      }

      // Words that require careful pronunciation
      if (
        cleanWord.length > 7 ||
        cleanWord.includes("tl") ||
        cleanWord.includes("dl") ||
        cleanWord.includes("rl") ||
        cleanWord.includes("str") ||
        cleanWord.includes("spr") ||
        cleanWord.includes("xpl") ||
        cleanWord.includes("sch")
      ) {
        patterns.weak.push(index);
      }
    });

    return patterns;
  },
};
