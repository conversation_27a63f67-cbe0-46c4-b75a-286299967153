// // import React from 'react';
// // import Modal from 'react-modal';
// // import '../../public/assets/css/main.css';

// // const AiScoreModal = ({ isOpen, onClose, data }) => {
// //     // console.log(data, "modal data");

// //     const customStyles = {
// //         content: {
// //             top: '50%',
// //             left: '50%',
// //             right: 'auto',
// //             bottom: 'auto',
// //             marginRight: '-50%',
// //             transform: 'translate(-50%, -50%)',
// //             width: '80vw',
// //             height: '90vh',
// //             borderRadius: '12px',
// //             padding: '20px',
// //         },
// //         overlay: {
// //             backgroundColor: 'rgba(0, 0, 0, 0.7)',
// //         },
// //     };

// //     return (
// //         <Modal
// //             isOpen={isOpen}
// //             onRequestClose={onClose}
// //             style={customStyles}
// //             shouldCloseOnOverlayClick={true}
// //         >

// //             <h2 style={{ color: '#140342', fontSize: 15, fontWeight: '600' }}>AI Score</h2>
// //             {/* <div style={{display:'flex',flexDirection:'row'}}>
// //                 <div style={{display:'flex',flexDirection:'row'}}>

// //                 </div>

// //             </div> */}
// //             <table style={{ width: '100%', borderCollapse: 'collapse', marginTop: '20px' }}>
// //                 <thead>
// //                     <tr>
// //                         <th style={{ border: 'none', padding: '10px', textAlign: 'left', color: '#2F00FF', fontSize: 18 }}>Components</th>
// //                         <th style={{ border: 'none', padding: '10px', textAlign: 'left', color: '#2F00FF', fontSize: 18 }}>Score</th>
// //                     </tr>
// //                 </thead>
// //                 <tbody>
// //                     {data && (
// //                         <>
// //                             <tr>
// //                                 <td style={{ border: 'none', padding: '10px', color: '#140342', fontSize: 15, fontWeight: '600' }}>Content</td>
// //                                 {/* <td style={{ border: 'none', padding: '10px', fontSize: 15, fontWeight: '600' }}>{data.content || '0/10'}</td> */}
// //                                 <td style={{ border: 'none', padding: '10px', fontSize: 15, fontWeight: '600' }}>{data.content !== undefined ? `${data.content}/10` : '0/10'}</td>
// //                             </tr>
// //                             <tr>
// //                                 <td style={{ border: 'none', padding: '10px', color: '#140342', fontSize: 15, fontWeight: '600' }}>Pronunciation</td>
// //                                 <td style={{ border: 'none', padding: '10px', fontSize: 15, fontWeight: '600' }}>
// //                                     {data.pronunciation_accuracy !== undefined ? `${data.pronunciation_accuracy}/10` : '0/10'}
// //                                 </td>
// //                             </tr>
// //                             <tr>
// //                                 <td style={{ border: 'none', padding: '10px', color: '#140342', fontSize: 15, fontWeight: '600' }}>Fluency</td>
// //                                 <td style={{ border: 'none', padding: '10px', fontSize: 15, fontWeight: '600' }}>
// //                                     {data.fluency !== undefined ? `${data.fluency}/10` : '0/10'}
// //                                 </td>
// //                             </tr>
// //                         </>
// //                     )}
// //                 </tbody>

// //             </table>
// //             <div style={{ display: 'flex', flexDirection: 'row',marginBottom:10,marginTop:10 }}>

// //                 <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginRight: 10,gap:5 }}>
// //                     <div style={{ color: '#140342', fontSize: 14, fontWeight: '600'}}>Max Score ：</div>
// //                     <p style={{ color: 'black', fontSize: 14, fontWeight: '600'}}>10,</p>

// //                 </div>
// //                 {
// //                     data.total_score &&      <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginRight: 10,gap:5 }}>
// //                     <div style={{ color: '#140342', fontSize: 14, fontWeight: '600'}}>Your Score :  </div>
// //                     <p style={{ color: 'black', fontSize: 14, fontWeight: '600'}}>{data.total_score !== undefined ? `${data.total_score}` : '0'}</p>

// //                 </div>

// //                 }

// //             </div>

// //             <div style={{ display: 'flex', justifyContent: 'space-between', flexDirection: 'row' }}>
// //                 <p style={{ color: '#140342', fontSize: 12, fontWeight: '600' }}>AI Speech Recognition:</p>

// //                 <div style={{ display: 'flex', flexDirection: 'row' }}>
// //                     <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginRight: 10 }}>
// //                         <div style={{ height: 10, width: 10, backgroundColor: '#140342', borderRadius: '50%', marginRight: 5 }}></div>
// //                         <p style={{ color: 'black', fontSize: 12 }}>Good</p>

// //                     </div>
// //                     <div style={{ display: 'flex', flexDirection: 'row', marginRight: 10, alignItems: 'center' }}>
// //                         <div style={{ height: 10, width: 10, backgroundColor: 'yellow', borderRadius: '50%', marginRight: 5 }}></div>
// //                         <p style={{ color: 'black', fontSize: 12 }}>Average</p>

// //                     </div>
// //                     <div style={{ display: 'flex', flexDirection: 'row', marginRight: 10, alignItems: 'center' }}>
// //                         <div style={{ height: 10, width: 10, backgroundColor: 'red', borderRadius: '50%', marginRight: 5 }}></div>
// //                         <p style={{ color: 'black', fontSize: 12 }}>Bad  /  Pause</p>

// //                     </div>
// //                 </div>
// //             </div>
// //             {
// //                 data && data.text.length > 0 && <p style={{ color: '#140342', fontSize: 20, marginTop: 40, paddingLeft: 10, paddingRight: 10 }}>
// //                     {data.text}
// //                 </p>
// //             }

// //             {/* <button onClick={onClose}>Close</button> */}
// //             <div style={{ display: 'flex', flex: 1, height: '50%', alignItems: 'flex-end', justifyContent: 'end' }}>
// //                 <button style={{ backgroundColor: '#D9D9D9', borderWidth: 1, borderColor: '#140342', borderStyle: 'solid', marginRight: 10, paddingLeft: 20, paddingRight: 20, display: 'flex', flexDirection: 'row', justifyItems: 'center', alignItems: 'center', gap: 10, alignSelf: 'flex-end' }} onClick={onClose}>
// //                     <p style={{ color: '#522CFF', fontSize: 12 }}>Close</p>
// //                 </button>
// //             </div>

// //         </Modal>
// //     );
// // };

// // export default AiScoreModal;
// import React from 'react';
// import Modal from 'react-modal';
// import '../../public/assets/css/main.css';

// const AiScoreModal = ({ isOpen, onClose, data }) => {
//     const customStyles = {
//         content: {
//             top: '50%',
//             left: '50%',
//             right: 'auto',
//             bottom: 'auto',
//             marginRight: '-50%',
//             transform: 'translate(-50%, -50%)',
//             width: '80vw',
//             height: '90vh',
//             borderRadius: '12px',
//             padding: '20px',
//         },
//         overlay: {
//             backgroundColor: 'rgba(0, 0, 0, 0.7)',
//         },
//     };

//     // Check if data is valid
//     const hasData = data && typeof data === 'object';

//     return (
//         <Modal
//             isOpen={isOpen}
//             onRequestClose={onClose}
//             style={customStyles}
//             shouldCloseOnOverlayClick={true}
//         >
//             <h2 style={{ color: '#140342', fontSize: 15, fontWeight: '600' }}>AI Score</h2>
//             <table style={{ width: '100%', borderCollapse: 'collapse', marginTop: '20px' }}>
//                 <thead>
//                     <tr>
//                         <th style={{ border: 'none', padding: '10px', textAlign: 'left', color: '#2F00FF', fontSize: 18 }}>Components</th>
//                         <th style={{ border: 'none', padding: '10px', textAlign: 'left', color: '#2F00FF', fontSize: 18 }}>Score</th>
//                     </tr>
//                 </thead>
//                 <tbody>
//                     {hasData && (
//                         <>
//                             <tr>
//                                 <td style={{ border: 'none', padding: '10px', color: '#140342', fontSize: 15, fontWeight: '600' }}>Content</td>
//                                 <td style={{ border: 'none', padding: '10px', fontSize: 15, fontWeight: '600' }}>{data.content !== undefined ? `${data.content}/10` : '0/10'}</td>
//                             </tr>
//                             <tr>
//                                 <td style={{ border: 'none', padding: '10px', color: '#140342', fontSize: 15, fontWeight: '600' }}>Pronunciation</td>
//                                 <td style={{ border: 'none', padding: '10px', fontSize: 15, fontWeight: '600' }}>{data.pronunciation_accuracy !== undefined ? `${data.pronunciation_accuracy}/10` : '0/10'}</td>
//                             </tr>
//                             <tr>
//                                 <td style={{ border: 'none', padding: '10px', color: '#140342', fontSize: 15, fontWeight: '600' }}>Fluency</td>
//                                 <td style={{ border: 'none', padding: '10px', fontSize: 15, fontWeight: '600' }}>{data.fluency !== undefined ? `${data.fluency}/10` : '0/10'}</td>
//                             </tr>
//                         </>
//                     )}
//                 </tbody>
//             </table>
//             <div style={{ display: 'flex', flexDirection: 'row', marginBottom: 10, marginTop: 10 }}>
//                 <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginRight: 10, gap: 5 }}>
//                     <div style={{ color: '#140342', fontSize: 14, fontWeight: '600' }}>Max Score:</div>
//                     <p style={{ color: 'black', fontSize: 14, fontWeight: '600' }}>10,</p>
//                 </div>
//                 {hasData && (
//                     <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginRight: 10, gap: 5 }}>
//                         <div style={{ color: '#140342', fontSize: 14, fontWeight: '600' }}>Your Score:</div>
//                         <p style={{ color: 'black', fontSize: 14, fontWeight: '600' }}>{data.total_score !== undefined ? `${data.total_score}` : '0'}</p>
//                     </div>
//                 )}
//             </div>
//             <div style={{ display: 'flex', justifyContent: 'space-between', flexDirection: 'row' }}>
//                 <p style={{ color: '#140342', fontSize: 12, fontWeight: '600' }}>AI Speech Recognition:</p>
//                 <div style={{ display: 'flex', flexDirection: 'row' }}>
//                     <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginRight: 10 }}>
//                         <div style={{ height: 10, width: 10, backgroundColor: '#140342', borderRadius: '50%', marginRight: 5 }}></div>
//                         <p style={{ color: 'black', fontSize: 12 }}>Good</p>
//                     </div>
//                     <div style={{ display: 'flex', flexDirection: 'row', marginRight: 10, alignItems: 'center' }}>
//                         <div style={{ height: 10, width: 10, backgroundColor: 'yellow', borderRadius: '50%', marginRight: 5 }}></div>
//                         <p style={{ color: 'black', fontSize: 12 }}>Average</p>
//                     </div>
//                     <div style={{ display: 'flex', flexDirection: 'row', marginRight: 10, alignItems: 'center' }}>
//                         <div style={{ height: 10, width: 10, backgroundColor: 'red', borderRadius: '50%', marginRight: 5 }}></div>
//                         <p style={{ color: 'black', fontSize: 12 }}>Bad / Pause</p>
//                     </div>
//                 </div>
//             </div>
//             {hasData && data.text && data.text.length > 0 && (
//                 <p style={{ color: '#140342', fontSize: 20, marginTop: 40, paddingLeft: 10, paddingRight: 10 }}>
//                     {data.text}
//                 </p>
//             )}
//             <div style={{ display: 'flex', flex: 1, height: '50%', alignItems: 'flex-end', justifyContent: 'end' }}>
//                 <button style={{ backgroundColor: '#D9D9D9', borderWidth: 1, borderColor: '#140342', borderStyle: 'solid', marginRight: 10, paddingLeft: 20, paddingRight: 20, display: 'flex', flexDirection: 'row', justifyItems: 'center', alignItems: 'center', gap: 10, alignSelf: 'flex-end' }} onClick={onClose}>
//                     <p style={{ color: '#522CFF', fontSize: 12 }}>Close</p>
//                 </button>
//             </div>
//         </Modal>
//     );
// };

// export default AiScoreModal;
// import React from 'react';
// import Modal from 'react-modal';
// import '../../public/assets/css/main.css';

// const AiScoreModal = ({ isOpen, onClose, data }) => {
//     // console.log(data, "modal data");

//     const customStyles = {
//         content: {
//             top: '50%',
//             left: '50%',
//             right: 'auto',
//             bottom: 'auto',
//             marginRight: '-50%',
//             transform: 'translate(-50%, -50%)',
//             width: '80vw',
//             height: '90vh',
//             borderRadius: '12px',
//             padding: '20px',
//         },
//         overlay: {
//             backgroundColor: 'rgba(0, 0, 0, 0.7)',
//         },
//     };

//     return (
//         <Modal
//             isOpen={isOpen}
//             onRequestClose={onClose}
//             style={customStyles}
//             shouldCloseOnOverlayClick={true}
//         >

//             <h2 style={{ color: '#140342', fontSize: 15, fontWeight: '600' }}>AI Score</h2>
//             {/* <div style={{display:'flex',flexDirection:'row'}}>
//                 <div style={{display:'flex',flexDirection:'row'}}>

//                 </div>

//             </div> */}
//             <table style={{ width: '100%', borderCollapse: 'collapse', marginTop: '20px' }}>
//                 <thead>
//                     <tr>
//                         <th style={{ border: 'none', padding: '10px', textAlign: 'left', color: '#2F00FF', fontSize: 18 }}>Components</th>
//                         <th style={{ border: 'none', padding: '10px', textAlign: 'left', color: '#2F00FF', fontSize: 18 }}>Score</th>
//                     </tr>
//                 </thead>
//                 <tbody>
//                     {data && (
//                         <>
//                             <tr>
//                                 <td style={{ border: 'none', padding: '10px', color: '#140342', fontSize: 15, fontWeight: '600' }}>Content</td>
//                                 {/* <td style={{ border: 'none', padding: '10px', fontSize: 15, fontWeight: '600' }}>{data.content || '0/10'}</td> */}
//                                 <td style={{ border: 'none', padding: '10px', fontSize: 15, fontWeight: '600' }}>{data.content !== undefined ? `${data.content}/10` : '0/10'}</td>
//                             </tr>
//                             <tr>
//                                 <td style={{ border: 'none', padding: '10px', color: '#140342', fontSize: 15, fontWeight: '600' }}>Pronunciation</td>
//                                 <td style={{ border: 'none', padding: '10px', fontSize: 15, fontWeight: '600' }}>
//                                     {data.pronunciation_accuracy !== undefined ? `${data.pronunciation_accuracy}/10` : '0/10'}
//                                 </td>
//                             </tr>
//                             <tr>
//                                 <td style={{ border: 'none', padding: '10px', color: '#140342', fontSize: 15, fontWeight: '600' }}>Fluency</td>
//                                 <td style={{ border: 'none', padding: '10px', fontSize: 15, fontWeight: '600' }}>
//                                     {data.fluency !== undefined ? `${data.fluency}/10` : '0/10'}
//                                 </td>
//                             </tr>
//                         </>
//                     )}
//                 </tbody>

//             </table>
//             <div style={{ display: 'flex', flexDirection: 'row',marginBottom:10,marginTop:10 }}>

//                 <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginRight: 10,gap:5 }}>
//                     <div style={{ color: '#140342', fontSize: 14, fontWeight: '600'}}>Max Score ：</div>
//                     <p style={{ color: 'black', fontSize: 14, fontWeight: '600'}}>10,</p>

//                 </div>
//                 {
//                     data.total_score &&      <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginRight: 10,gap:5 }}>
//                     <div style={{ color: '#140342', fontSize: 14, fontWeight: '600'}}>Your Score :  </div>
//                     <p style={{ color: 'black', fontSize: 14, fontWeight: '600'}}>{data.total_score !== undefined ? `${data.total_score}` : '0'}</p>

//                 </div>

//                 }

//             </div>

//             <div style={{ display: 'flex', justifyContent: 'space-between', flexDirection: 'row' }}>
//                 <p style={{ color: '#140342', fontSize: 12, fontWeight: '600' }}>AI Speech Recognition:</p>

//                 <div style={{ display: 'flex', flexDirection: 'row' }}>
//                     <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginRight: 10 }}>
//                         <div style={{ height: 10, width: 10, backgroundColor: '#140342', borderRadius: '50%', marginRight: 5 }}></div>
//                         <p style={{ color: 'black', fontSize: 12 }}>Good</p>

//                     </div>
//                     <div style={{ display: 'flex', flexDirection: 'row', marginRight: 10, alignItems: 'center' }}>
//                         <div style={{ height: 10, width: 10, backgroundColor: 'yellow', borderRadius: '50%', marginRight: 5 }}></div>
//                         <p style={{ color: 'black', fontSize: 12 }}>Average</p>

//                     </div>
//                     <div style={{ display: 'flex', flexDirection: 'row', marginRight: 10, alignItems: 'center' }}>
//                         <div style={{ height: 10, width: 10, backgroundColor: 'red', borderRadius: '50%', marginRight: 5 }}></div>
//                         <p style={{ color: 'black', fontSize: 12 }}>Bad  /  Pause</p>

//                     </div>
//                 </div>
//             </div>
//             {
//                 data && data.text.length > 0 && <p style={{ color: '#140342', fontSize: 20, marginTop: 40, paddingLeft: 10, paddingRight: 10 }}>
//                     {data.text}
//                 </p>
//             }

//             {/* <button onClick={onClose}>Close</button> */}
//             <div style={{ display: 'flex', flex: 1, height: '50%', alignItems: 'flex-end', justifyContent: 'end' }}>
//                 <button style={{ backgroundColor: '#D9D9D9', borderWidth: 1, borderColor: '#140342', borderStyle: 'solid', marginRight: 10, paddingLeft: 20, paddingRight: 20, display: 'flex', flexDirection: 'row', justifyItems: 'center', alignItems: 'center', gap: 10, alignSelf: 'flex-end' }} onClick={onClose}>
//                     <p style={{ color: '#522CFF', fontSize: 12 }}>Close</p>
//                 </button>
//             </div>

//         </Modal>
//     );
// };

// export default AiScoreModal;

import React, { useState } from 'react';
import Modal from 'react-modal';
import '../../public/assets/css/main.css';
import ProgressBar from '@ramonak/react-progress-bar';
import { CircularProgressbar, buildStyles } from 'react-circular-progressbar';
import 'react-circular-progressbar/dist/styles.css';

// import AudioPlayer from 'react-h5-audio-player';

import AudioPlayer from 'react-audio-player';
import zIndex from '@mui/material/styles/zIndex';

const AiScoreModal = ({ isOpen, onClose, data }) => {
    const [activeTab, setActiveTab] = useState('pronunciation');
    const customStyles = {
        content: {
            top: '55%',
            left: '50%',
            right: 'auto',
            bottom: 'auto',
            marginRight: '-50%',
            transform: 'translate(-50%, -50%)',
            width: '80vw',
            height: '80vh',
            borderRadius: '12px',
            padding: '20px',

        },
        overlay: {
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
        },
    };

    const hasData = data && typeof data === 'object';

    const maxScore = 90;
    const totalScore = hasData && data.total_score ? data.total_score : 0;
    const progressPercentage = (totalScore / maxScore) * 100;
    const maxScoreProgress = 100
    return (
        <Modal
            isOpen={isOpen}
            onRequestClose={onClose}
            style={customStyles}
            shouldCloseOnOverlayClick={true}
        >
            <h2 style={{ color: '#140342', fontSize: 15, fontWeight: '600' }}>AI Score</h2>
            <div style={{ display: 'flex', flexDirection: 'row', gap: 50, marginTop: 50, width: 'auto', marginLeft: 50 }}>
                {
                    hasData && <>
                        <div style={{ display: 'flex', flexDirection: 'row', gap: 30, padding: 40, borderRadius: 10, alignItems: 'center', boxShadow: '0 4px 20px rgba(20, 4, 66, 0.5) ' }}>
                            <div style={{ marginTop: 20, textAlign: 'center' }}>
                                <div style={{ width: 120, height: 120, margin: '0 auto' }}>
                                    <CircularProgressbar
                                        value={progressPercentage}
                                        text={`${totalScore}/${maxScore}`}
                                        styles={buildStyles({
                                            pathColor: '#140342',
                                            textColor: '#140342',
                                            trailColor: '#d6d6d6',
                                            padding: 10,
                                            textSize: 15,
                                            borderWidth: 200
                                        })}
                                    />
                                </div>
                            </div>
                            <div style={{ display: 'flex', flexDirection: 'column', gap: 5 }}>
                                <p style={{ color: '#140342', fontSize: 15, fontWeight: '500' }}>Content : {data.content !== undefined ? `${data.content}/90` : '0/90'} </p>
                                <p style={{ color: '#140342', fontSize: 15, fontWeight: '500' }}>Pronunciation : {data.pronunciation_accuracy !== undefined ? `${data.pronunciation_accuracy}/90` : '0/90'} </p>
                                <p style={{ color: '#140342', fontSize: 15, fontWeight: '500' }}>Fluency : {data.fluency !== undefined ? `${data.fluency}/90` : '0/90'} </p>
                            </div>
                        </div>
                        <div style={{ display: 'flex', marginTop: 30 }}>
                            <AudioPlayer
                                src={data.audio_url}
                                controls
                                autoPlay={false}
                                loop={false}
                                style={{ width: '40vw' }}
                                showJumpControls={false}
                            />
                        </div></>
                }

            </div>
            <div style={{ display: 'flex', flexDirection: 'row', gap: 50, marginTop: 20, width: 'auto', marginLeft: 50 }}>
                <div style={{ display: 'flex', flexDirection: 'column', gap: 5, width: '40%' }}>
                    <p style={{ fontSize: 15, color: 'black', fontWeight: '500' }}>Skill Analysis</p>
                    <p style={{ fontSize: 13, color: '#a6a6a6', fontWeight: '500' }}>The following scores show how you have performed in different aspects as compared to other users. These scores can be used to analyse how you can improve in this question, but they are not directly related to the PTE scores.</p>
                    {
                        hasData && <>   <div style={{ display: 'flex', flexDirection: 'column', gap: 10, padding: 10, borderRadius: 10, borderWidth: '0.1px', borderColor: '#a6a6a6', borderStyle: 'solid' }}>

                            <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 10 }}>
                                <p style={{ width: '30%' }}>Pronunciation <br /> Accuracy</p>
                                <ProgressBar
                                    completed={data.pronunciation_accuracy}
                                    maxCompleted={maxScoreProgress}
                                    bgColor="#140342"
                                    labelColor="#140342"
                                    height="20px"
                                    width='150px'
                                    isLabelVisible={true}

                                />
                                <p style={{ color: 'black', fontSize: 12 }}>{data.pronunciation_accuracy}%</p>
                            </div>
                            <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 10 }}>
                                <p style={{ width: '30%' }}>Speed</p>
                                <ProgressBar
                                    completed={data.speed}
                                    maxCompleted={maxScoreProgress}
                                    bgColor="#140342"
                                    width='150px'
                                    labelColor="#140342"
                                    height="20px"
                                    isLabelVisible={true}
                                // style={{ width: '100%' }}
                                />
                                <p style={{ color: 'black', fontSize: 12 }}>{data.speed}%</p>
                            </div>


                            <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 10 }}>
                                <p style={{ width: '30%' }}>Fluent</p>
                                <ProgressBar
                                    completed={data.fluency}
                                    maxCompleted={maxScoreProgress}
                                    bgColor="#140342"
                                    labelColor="#140342"
                                    height="20px"
                                    width='150px'
                                    isLabelVisible={true}
                                />
                                <p style={{ color: 'black', fontSize: 12 }}>{data.fluency}%</p>
                            </div>
                            <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 10 }}>
                                <p style={{ width: '30%' }}>Stress</p>
                                <ProgressBar
                                    completed={data.stress}
                                    maxCompleted={maxScoreProgress}
                                    bgColor="#140342"
                                    labelColor="#140342"
                                    height="20px"
                                    isLabelVisible={true}
                                    width='150px'
                                />
                                <p style={{ color: 'black', fontSize: 12 }}>{data.stress}%</p>
                            </div>


                        </div></>
                    }

                </div>
                <div style={{ display: 'flex', marginTop: 30, flexDirection: 'column' }}>
                    <div style={{ display: 'flex', gap: 20, marginBottom: 20 }}>
                        <div
                            onClick={() => setActiveTab('pronunciation')}
                            style={{
                                cursor: 'pointer',
                                padding: '10px 20px',
                                borderRadius: '5px',
                                // backgroundColor: activeTab === 'pronunciation' ? '#4caf50' : '#ddd',
                                color: activeTab === 'pronunciation' ? '#140342' : '#666666',
                                height: '45px'
                            }}
                        >
                            Pronunciation Accuracy
                        </div>
                        <div
                            onClick={() => setActiveTab('stress')}
                            style={{
                                cursor: 'pointer',
                                padding: '10px 20px',
                                borderRadius: '5px',
                                // backgroundColor: activeTab === 'stress' ? '#140342' : '#ddd',
                                color: activeTab === 'stress' ? '#140342' : '#666666',
                                height: '45px'
                            }}
                        >
                            Stress
                        </div>
                    </div>

                    {/* Tab Content */}
                    <div>
                        {activeTab === 'pronunciation' && (
                            <div style={{ display: 'flex', flexDirection: 'column' }}>
                                <div style={{ display: 'flex', flexDirection: 'column', gap: 10, marginLeft: '20px' }}>
                                    <div style={{ display: 'flex', flexDirection: 'row' }}>
                                        <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginRight: 15 }}>
                                            <div style={{ height: 18, width: 18, backgroundColor: '#140342', borderRadius: '50%', marginRight: 5 }}></div>
                                            <p style={{ color: 'black', fontSize: 15 }}>Good</p>
                                        </div>
                                        <div style={{ display: 'flex', flexDirection: 'row', marginRight: 15, alignItems: 'center' }}>
                                            <div style={{ height: 18, width: 18, backgroundColor: 'yellow', borderRadius: '50%', marginRight: 5 }}></div>
                                            <p style={{ color: 'black', fontSize: 15 }}>Average</p>
                                        </div>
                                        <div style={{ display: 'flex', flexDirection: 'row', marginRight: 10, alignItems: 'center' }}>
                                            <div style={{ height: 18, width: 18, backgroundColor: 'red', borderRadius: '50%', marginRight: 5 }}></div>
                                            <p style={{ color: 'black', fontSize: 15 }}>Bad / Pause</p>
                                        </div>
                                    </div>
                                </div>
                                <div style={{ display: 'flex', flexDirection: 'column', gap: 10, marginLeft: '20px', marginTop: 20 }}>
                                    {/* {
                                        hasData &&  (data.text ? <p style={{ fontSize: 15, color: 'black', fontWeight: '500' }}>{data.text}</p> 
                                            :
                                             <p style={{ fontSize: 25,marginTop:20, color: 'black', fontWeight: '500' }}>No data found!!</p>)
   
                                    }
                                   */}

                                    {
                                        hasData && (data.text ?
                                            <p style={{ fontSize: 15, color: 'black', fontWeight: '500' }} dangerouslySetInnerHTML={{ __html: data.text }} />
                                            :
                                            <p style={{ fontSize: 25, marginTop: 20, color: 'black', fontWeight: '500' }}>No data found!!</p>)
                                    }


                                </div>
                            </div>
                        )}
                    </div>
                    {/* {activeTab === 'stress' && (
                            <div style={{ display: 'flex', flexDirection: 'column', gap: 10 }}>
                                <p>Stress</p>
                                <ProgressBar
                                    completed={data.stress}
                                    maxCompleted={maxScore}
                                    bgColor="#140342"
                                    labelColor="#140342"
                                    height="20px"
                                    isLabelVisible={true}
                                />
                                <p style={{ color: 'black', fontSize: 12 }}>100%</p>
                            </div>
                        )}   </div> */}

                </div>
            </div>

            {/* <table style={{ width: '100%', borderCollapse: 'collapse', marginTop: '20px' }}>
                <thead>
                    <tr>
                        <th style={{ border: 'none', padding: '10px', textAlign: 'left', color: '#2F00FF', fontSize: 18 }}>Components</th>
                        <th style={{ border: 'none', padding: '10px', textAlign: 'left', color: '#2F00FF', fontSize: 18 }}>Score</th>
                    </tr>
                </thead>
                <tbody>
                    {hasData && (
                        <>
                            <tr>
                                <td style={{ border: 'none', padding: '10px', color: '#140342', fontSize: 15, fontWeight: '600' }}>Content</td>
                                <td style={{ border: 'none', padding: '10px', fontSize: 15, fontWeight: '600' }}>{data.content !== undefined ? `${data.content}/10` : '0/10'}</td>
                            </tr>
                            <tr>
                                <td style={{ border: 'none', padding: '10px', color: '#140342', fontSize: 15, fontWeight: '600' }}>Pronunciation</td>
                                <td style={{ border: 'none', padding: '10px', fontSize: 15, fontWeight: '600' }}>{data.pronunciation_accuracy !== undefined ? `${data.pronunciation_accuracy}/10` : '0/10'}</td>
                            </tr>
                            <tr>
                                <td style={{ border: 'none', padding: '10px', color: '#140342', fontSize: 15, fontWeight: '600' }}>Fluency</td>
                                <td style={{ border: 'none', padding: '10px', fontSize: 15, fontWeight: '600' }}>{data.fluency !== undefined ? `${data.fluency}/10` : '0/10'}</td>
                            </tr>
                        </>
                    )}
                </tbody>
            </table> */}
            {/* <div style={{ display: 'flex', flexDirection: 'row', marginBottom: 10, marginTop: 10 }}>
                <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginRight: 10, gap: 5 }}>
                    <div style={{ color: '#140342', fontSize: 14, fontWeight: '600' }}>Max Score:</div>
                    <p style={{ color: 'black', fontSize: 14, fontWeight: '600' }}>10,</p>
                </div>
                {hasData && (
                    <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginRight: 10, gap: 5 }}>
                        <div style={{ color: '#140342', fontSize: 14, fontWeight: '600' }}>Your Score:</div>
                        <p style={{ color: 'black', fontSize: 14, fontWeight: '600' }}>{data.total_score !== undefined ? `${data.total_score}` : '0'}</p>
                    </div>
                )}
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', flexDirection: 'row' }}>
                <p style={{ color: '#140342', fontSize: 12, fontWeight: '600' }}>AI Speech Recognition:</p>
                <div style={{ display: 'flex', flexDirection: 'row' }}>
                    <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginRight: 10 }}>
                        <div style={{ height: 10, width: 10, backgroundColor: '#140342', borderRadius: '50%', marginRight: 5 }}></div>
                        <p style={{ color: 'black', fontSize: 12 }}>Good</p>
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'row', marginRight: 10, alignItems: 'center' }}>
                        <div style={{ height: 10, width: 10, backgroundColor: 'yellow', borderRadius: '50%', marginRight: 5 }}></div>
                        <p style={{ color: 'black', fontSize: 12 }}>Average</p>
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'row', marginRight: 10, alignItems: 'center' }}>
                        <div style={{ height: 10, width: 10, backgroundColor: 'red', borderRadius: '50%', marginRight: 5 }}></div>
                        <p style={{ color: 'black', fontSize: 12 }}>Bad / Pause</p>
                    </div>
                </div>
            </div>
            {hasData && data.text && data.text.length > 0 && (
                <p style={{ color: '#140342', fontSize: 20, marginTop: 40, paddingLeft: 10, paddingRight: 10 }}>
                    {data.text}
                </p>
            )} */}
            <div style={{ display: 'flex', flex: 1, height: '50%', alignItems: 'flex-end', justifyContent: 'end' }}>
                <button style={{ backgroundColor: '#D9D9D9', borderWidth: 1, borderColor: '#140342', borderStyle: 'solid', marginRight: 10, paddingLeft: 20, paddingRight: 20, display: 'flex', flexDirection: 'row', justifyItems: 'center', alignItems: 'center', gap: 10, alignSelf: 'flex-end' }} onClick={onClose}>
                    <p style={{ color: '#522CFF', fontSize: 12 }}>Close</p>
                </button>
            </div>
        </Modal>
    );
};

export default AiScoreModal;
