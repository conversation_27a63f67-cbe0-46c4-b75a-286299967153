import React from 'react';
import { useNavigate } from 'react-router-dom';

const LoginModal = ({ isOpen, onClose }) => {
    const navigate = useNavigate();

    if (!isOpen) return null;

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'white',
                    padding: 20,
                    borderRadius: '20px',
                    gap: 20,
                    width: '300px', // Adjust width as needed
                }}
                onClick={(e) => e.stopPropagation()}
            >
                <h4>Login Required</h4>
                <p style={{ fontWeight: '500', textAlign: 'center' }}>Please log in to access this feature.</p>
                <div style={{ display: 'flex', gap: 10 }}>
                    <button
                        style={{
                            backgroundColor: '#140342',
                            borderColor: '#140342',
                            borderRadius: 4,
                            borderStyle: 'solid',
                            borderWidth: 1,
                            padding: '10px 20px',
                            color: 'white',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                        }}
                        onClick={() => {
                            navigate('/login');
                            onClose();
                        }}
                    >
                        Login
                    </button>
                    <button
                        style={{
                            borderColor: '#140342',
                            borderRadius: 4,
                            borderStyle: 'solid',
                            borderWidth: 1,
                            padding: '10px 20px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                        }}
                        onClick={onClose}
                    >
                        Close
                    </button>
                </div>
            </div>
        </div>
    );
};

export default LoginModal;
