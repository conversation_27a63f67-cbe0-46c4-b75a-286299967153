import React, { useEffect, useState, useCallback, useRef } from "react";
import Modal from "react-modal";
import { useNavigate } from "react-router-dom";
import "../../public/assets/css/main.css";
import Loader from "./common/Loader";
import { server } from "@/api/services/server";
import { getRequest } from "@/api/services/controller";
import { useAuth } from "./others/AuthContext";

// Import MUI components
import {
  Typography,
  Chip,
  Box,
  InputBase,
  Paper,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  Button,
  Divider,
  Stack,
  Grid,
  Tabs,
  Tab,
  useMediaQuery,
  useTheme as useMuiTheme,
  Tooltip,
  Badge,
  Avatar,
  Card,
  CardContent,
  LinearProgress,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
} from "@mui/material";

// Import MUI icons
import SearchIcon from "@mui/icons-material/Search";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import FilterAltOffIcon from "@mui/icons-material/FilterAltOff";
import NavigateBeforeIcon from "@mui/icons-material/NavigateBefore";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import CloseIcon from "@mui/icons-material/Close";
import VisibilityIcon from "@mui/icons-material/Visibility";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import BookmarkIcon from "@mui/icons-material/Bookmark";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import BookmarkBorderIcon from "@mui/icons-material/BookmarkBorder";
import FilterListIcon from "@mui/icons-material/FilterList";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";

// Define theme colors
const theme = {
  primary: "#140342", // Main purple color
  lightPurple: "#f9f7ff", // Lighter background color
  purple: "#9780ff", // Secondary purple
  green: "#4CAF50", // Green for success
  red: "#FF3C00", // Red for error/alert
  amber: "#F59E0B", // Amber/orange for warnings
  lightGray: "#F1F5F9", // Light gray for backgrounds
  gray: "#64748B", // Gray for text
  darkGray: "#334155", // Darker gray for titles
  white: "#FFFFFF", // White
  shadow: "0 2px 4px rgba(0,0,0,0.05)", // Light box shadow
  borderRadius: "8px", // Default border radius
};

const getAbbreviation = (name) => {
  const words = name?.split(" ").filter((word) => word.length > 0);
  const abbreviation = words
    ?.map((word) => word.charAt(0).toUpperCase())
    .join("");
  return abbreviation;
};

// Function to determine if a date is within current week
const isWithinCurrentWeek = (dateStr) => {
  const date = new Date(dateStr);
  const now = new Date();

  // Get the start of the current week (Sunday)
  const startOfWeek = new Date(now);
  startOfWeek.setDate(now.getDate() - now.getDay());
  startOfWeek.setHours(0, 0, 0, 0);

  // Get the end of the current week (Saturday)
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6);
  endOfWeek.setHours(23, 59, 59, 999);

  return date >= startOfWeek && date <= endOfWeek;
};

// Function to determine if a date is within current month
const isWithinCurrentMonth = (dateStr) => {
  const date = new Date(dateStr);
  const now = new Date();

  return (
    date.getMonth() === now.getMonth() &&
    date.getFullYear() === now.getFullYear()
  );
};

// Format date to display nicely
const formatDate = (dateStr) => {
  if (!dateStr) return "";
  const date = new Date(dateStr);
  const now = new Date();
  const diffTime = Math.abs(now - date);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays <= 1) {
    return "Today";
  } else {
    return date.toLocaleDateString("en-US", { month: "short", day: "numeric" });
  }
};

const SubCategoryModal = ({
  isOpen,
  onClose,
  data,
  onShadowToggle,
  onMarkChange,
}) => {
  const selectedSubCategoryId = data ? data : null;
  const [subCategoryData, setSubCategoryData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const dataCache = useRef(new Map());
  const [sortedData, setSortedData] = useState([]);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState("success");

  // MUI theme and responsive breakpoints
  const muiTheme = useMuiTheme();
  const isSm = useMediaQuery(muiTheme.breakpoints.down("sm"));
  const isMd = useMediaQuery(muiTheme.breakpoints.down("md"));

  // Filter states
  const [timePeriod, setTimePeriod] = useState("all");
  const [sortBy, setSortBy] = useState("Q Num"); // Changed default to Q Num
  const [bookmarkFilter, setBookmarkFilter] = useState("");
  const [practiceFilter, setPracticeFilter] = useState("");
  const [difficultyFilter, setDifficultyFilter] = useState("");
  const [shadowFilter, setShadowFilter] = useState(false);

  // Get current month name
  const getCurrentMonthName = () => {
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    return monthNames[new Date().getMonth()];
  };

  // Get user context
  const { user } = useAuth();

  // Fetch data with caching
  const fetchData = useCallback(async () => {
    if (!selectedSubCategoryId) return;

    try {
      setIsLoading(true);

      // Check if we have a cached version
      if (dataCache.current.has(selectedSubCategoryId)) {
        setSubCategoryData(dataCache.current.get(selectedSubCategoryId));
        setIsLoading(false);
        return;
      }

      // Get userId from AuthContext or localStorage
      const userId = user?.id || localStorage.getItem("isUserId");

      // Build filter query with user-specific questionmetas and apply specific limits for different question types
      const getQuestionLimit = (categoryId) => {
        // Respond to situation - 20 questions
        if (categoryId === "67b34f839ab23dd6196d1a91") return 90;
        
        // Repeat Sentence - 25 questions
        if (categoryId === "67838fcf2e266d08ba713eed") return 20;
        
        // Answer Short Question - 20 questions
        if (categoryId === "6783923483cd4009d9cddafa") return 20;
        
        // Write Essay - 50 questions
        // if (categoryId === "67d893d673e32a15ba3d88c1") return 20;
        
        // Highlight Incorrect Words - 10 questions
        if (categoryId === "678023f5382bce18e30d11d2") return 8;
        
        // Reading categories - 10 questions except Fill in the Blanks and Multiple Choice Multiple
        if (categoryId === "678011ade0dfdc154eff11b8" || // Re-order Paragraphs
            categoryId === "67800fc7d4e7d9147dd9525d") { // Multiple Choice (Single)
          return 10;
        }
        
        // Default limit for other categories
        return 100;
      };
      
      const filterQuery = {
        where: { categoryId: selectedSubCategoryId },
        limit: getQuestionLimit(selectedSubCategoryId),
        include: [
          { relation: "category" },
          ...(userId
            ? [
                {
                  relation: "questionmetas",
                  scope: {
                    where: { userId: userId },
                  },
                },
              ]
            : []),
        ],
      };

      const uri = `${server.uri}questions?filter=${encodeURIComponent(
        JSON.stringify(filterQuery)
      )}`;
      const data = await getRequest(uri);

      if (data) {
        // Cache the data
        dataCache.current.set(selectedSubCategoryId, data);
        setSubCategoryData(data);
      }
    } catch (error) {
      console.error("Error fetching data: ", error);
      showSnackbar("Failed to load questions", "error");
    } finally {
      setIsLoading(false);
    }
  }, [selectedSubCategoryId, user?.id]);

  // Initial data fetch
  useEffect(() => {
    if (isOpen && selectedSubCategoryId) {
      fetchData();
    }
  }, [fetchData, isOpen, selectedSubCategoryId]);

  const navigate = useNavigate();
  Modal.setAppElement("#root");

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 8; // Show exactly 8 items per page

  // Show snackbar notification
  const showSnackbar = (message, severity = "success") => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Derived data
  const abbreviation =
    subCategoryData && subCategoryData?.length > 0
      ? getAbbreviation(subCategoryData[0]?.category?.name)
      : "";

  // Filter and sorting logic
  useEffect(() => {
    if (subCategoryData && subCategoryData?.length > 0) {
      let filtered = [...subCategoryData];

      // Apply time period filter
      if (timePeriod === "weekly") {
        filtered = filtered.filter((item) => {
          const createdDate = item.additionalProp1?.createdAt || null;
          return createdDate && isWithinCurrentWeek(createdDate);
        });
      } else if (timePeriod === "monthly") {
        filtered = filtered.filter((item) => {
          const createdDate = item.additionalProp1?.createdAt || null;
          return createdDate && isWithinCurrentMonth(createdDate);
        });
      }

      // Apply search filter
      if (searchTerm) {
        const lowercaseSearch = searchTerm.toLowerCase();
        filtered = filtered.filter(
          (item) =>
            (item.prompt &&
              item.prompt.toLowerCase().includes(lowercaseSearch)) ||
            (item.questionNumber &&
              item.questionNumber.toString().includes(lowercaseSearch)) ||
            (item.additionalProp1?.questionName &&
              item.additionalProp1.questionName
                .toLowerCase()
                .includes(lowercaseSearch))
        );
      }

      // Apply sort filter
      if (sortBy === "New") {
        filtered = filtered.sort((a, b) => {
          const dateA = a.additionalProp1?.createdAt
            ? new Date(a.additionalProp1.createdAt)
            : new Date(0);
          const dateB = b.additionalProp1?.createdAt
            ? new Date(b.additionalProp1.createdAt)
            : new Date(0);
          return dateB.getTime() - dateA.getTime();
        });
      } else if (sortBy === "Q Num") {
        filtered = filtered.sort(
          (a, b) =>
            parseInt(a.questionNumber || 0) - parseInt(b.questionNumber || 0)
        );
      }

      // Apply bookmark filter (using user-specific questionmetas)
      if (bookmarkFilter === "All Marked") {
        filtered = filtered.filter((item) => {
          const userMeta =
            item.questionmetas && item.questionmetas.length > 0
              ? item.questionmetas[0]
              : null;
          return userMeta?.markLabel && userMeta.markLabel !== "";
        });
      } else if (
        ["Review", "Important", "Challenging", "Favorite", "Later"].includes(
          bookmarkFilter
        )
      ) {
        filtered = filtered.filter((item) => {
          const userMeta =
            item.questionmetas && item.questionmetas.length > 0
              ? item.questionmetas[0]
              : null;
          return userMeta?.markLabel === bookmarkFilter;
        });
      } else if (bookmarkFilter === "Not Marked") {
        filtered = filtered.filter((item) => {
          const userMeta =
            item.questionmetas && item.questionmetas.length > 0
              ? item.questionmetas[0]
              : null;
          return !userMeta?.markLabel || userMeta.markLabel === "";
        });
      }

      // Apply practice status filter
      if (practiceFilter === "Practiced") {
        filtered = filtered.filter((item) => {
          const userMeta =
            item.questionmetas && item.questionmetas.length > 0
              ? item.questionmetas[0]
              : null;
          return userMeta?.practiceStatus === "Done";
        });
      } else if (practiceFilter === "Unpracticed") {
        filtered = filtered.filter((item) => {
          const userMeta =
            item.questionmetas && item.questionmetas.length > 0
              ? item.questionmetas[0]
              : null;
          return userMeta?.practiceStatus !== "Done";
        });
      }

      // Apply difficulty filter
      if (["Easy", "Medium", "Hard"].includes(difficultyFilter)) {
        filtered = filtered.filter(
          (item) =>
            item.difficulty &&
            item.difficulty.toLowerCase() === difficultyFilter.toLowerCase()
        );
      }

      // Apply shadow filter
      if (shadowFilter) {
        filtered = filtered.filter(
          (item) => item.additionalProp1?.isShadowed === true
        );
      }

      setSortedData(filtered);
      setCurrentPage(1);
    }
  }, [
    subCategoryData,
    searchTerm,
    sortBy,
    bookmarkFilter,
    practiceFilter,
    difficultyFilter,
    shadowFilter,
    timePeriod,
  ]);

  // Compute pagination data
  const paginatedData = sortedData?.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  const totalPages = Math.ceil(sortedData?.length / itemsPerPage);

  // Modal animation
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  // Handle item click
  const handleItemClick = (item) => {
    onClose();
    navigate(`/commonTest/${item.questionId}`);
  };

  // Clear all filters
  const handleClearFilters = () => {
    setSortBy("New");
    setBookmarkFilter("");
    setPracticeFilter("");
    setDifficultyFilter("");
    setShadowFilter(false);
    setSearchTerm("");
    setTimePeriod("all");
    showSnackbar("All filters cleared");
  };

  // Forward shadow toggle to parent
  const handleShadowToggle = (item, event) => {
    event.stopPropagation();
    if (onShadowToggle) {
      onShadowToggle(item);

      // Update UI optimistically
      const newData = subCategoryData.map((q) =>
        q.questionId === item.questionId
          ? {
              ...q,
              additionalProp1: {
                ...q.additionalProp1,
                isShadowed: !q.additionalProp1?.isShadowed,
              },
            }
          : q
      );
      setSubCategoryData(newData);
      dataCache.current.set(selectedSubCategoryId, newData);
    }
  };

  // Forward mark change to parent
  const handleMarkQuestion = (item, color, event) => {
    event.stopPropagation();
    if (onMarkChange) {
      onMarkChange(item, color);

      // Update UI optimistically
      const newData = subCategoryData.map((q) =>
        q.questionId === item.questionId
          ? {
              ...q,
              additionalProp1: {
                ...q.additionalProp1,
                markColor: color,
              },
            }
          : q
      );
      setSubCategoryData(newData);
      dataCache.current.set(selectedSubCategoryId, newData);
    }
  };

  // Reset practice status confirmation
  const openResetConfirmation = () => {
    setShowConfirmDialog(true);
  };

  const closeResetConfirmation = () => {
    setShowConfirmDialog(false);
  };

  // Reset practice status for all questions
  const handleResetPracStatus = async () => {
    closeResetConfirmation();
    setIsLoading(true);

    try {
      // Optimistically update UI
      const updatedData = subCategoryData?.map((item) => ({
        ...item,
        additionalProp1: {
          ...item.additionalProp1,
          practiceStatus: "Undone",
          practiceCount: 0,
        },
      }));

      setSubCategoryData(updatedData);
      dataCache.current.set(selectedSubCategoryId, updatedData);

      // Perform API updates in background
      const resetPromises = subCategoryData?.map((item) => {
        const updatedItem = {
          ...item,
          additionalProp1: {
            ...item.additionalProp1,
            practiceStatus: "Undone",
            practiceCount: 0,
          },
        };

        return fetch(`${server.uri}questions/${item.questionId}`, {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updatedItem),
        });
      });

      await Promise.all(resetPromises);
      showSnackbar("Practice status reset successfully");
    } catch (error) {
      console.error("Error resetting practice status: ", error);
      showSnackbar("Failed to reset practice status", "error");
      // If error, refetch data
      fetchData();
    } finally {
      setIsLoading(false);
    }
  };

  // Pagination handlers
  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty) => {
    switch (difficulty?.toLowerCase()) {
      case "easy":
        return {
          bg: theme.green,
          text: "white",
        };
      case "medium":
        return {
          bg: theme.amber,
          text: "white",
        };
      case "hard":
        return {
          bg: theme.red,
          text: "white",
        };
      default:
        return {
          bg: theme.lightGray,
          text: theme.gray,
        };
    }
  };

  // Calculate practice statistics
  const totalQuestions = subCategoryData?.length || 0;
  const practicedCount =
    subCategoryData.filter((item) => {
      // Check user-specific practice status from questionmetas
      const userMeta =
        item.questionmetas && item.questionmetas.length > 0
          ? item.questionmetas[0]
          : null;
      return userMeta?.practiceStatus === "Done";
    })?.length || 0;

  const currentMonth = getCurrentMonthName();

  // Empty state component
  const EmptyState = () => (
    <Box
      sx={{
        textAlign: "center",
        py: 5,
        px: 2,
        bgcolor: theme.white,
        borderRadius: theme.borderRadius,
        boxShadow: theme.shadow,
      }}
    >
      <Typography variant="h6" sx={{ color: theme.darkGray, mb: 1 }}>
        No questions found
      </Typography>
      <Typography variant="body2" sx={{ color: theme.gray, mb: 3 }}>
        {searchTerm
          ? "No questions match your search criteria."
          : timePeriod !== "all"
          ? `No questions found for ${
              timePeriod === "weekly" ? "this week" : `${currentMonth}`
            }.`
          : "No questions available in this category."}
      </Typography>
      {(searchTerm ||
        bookmarkFilter ||
        practiceFilter ||
        difficultyFilter ||
        shadowFilter ||
        sortBy !== "New" ||
        timePeriod !== "all") && (
        <Button
          variant="outlined"
          startIcon={<FilterAltOffIcon />}
          onClick={handleClearFilters}
          size="small"
          sx={{
            borderColor: theme.purple,
            color: theme.purple,
          }}
        >
          Clear Filters
        </Button>
      )}
    </Box>
  );

  // Question row component
  const QuestionRow = ({ item }) => {
    // Get user-specific data from questionmetas
    const userMeta =
      item.questionmetas && item.questionmetas.length > 0
        ? item.questionmetas[0]
        : null;

    const isPracticed = userMeta?.practiceStatus === "Done";
    const isShadowed = item.additionalProp1?.isShadowed === true;
    const markLabel = userMeta?.markLabel || "";
    const difficulty = item.difficulty?.toLowerCase() || "";
    const difficultyColor = getDifficultyColor(difficulty);
    const practiceCount = parseInt(userMeta?.practiceCount || 0);

    // Get bookmark option details based on label
    const getBookmarkOption = (label) => {
      const bookmarkOptions = [
        {
          label: "Review",
          icon: "🔄",
          color: "#FFA500",
          bgColor: "#fff3e0",
        },
        {
          label: "Important",
          icon: "⭐",
          color: "#FF0000",
          bgColor: "#ffebee",
        },
        {
          label: "Challenging",
          icon: "🔥",
          color: "#800080",
          bgColor: "#f3e5f5",
        },
        {
          label: "Favorite",
          icon: "❤️",
          color: "#008000",
          bgColor: "#e8f5e9",
        },
        {
          label: "Later",
          icon: "⏰",
          color: "#607D8B",
          bgColor: "#f3f4f6",
        },
      ];
      return bookmarkOptions.find((option) => option.label === label) || null;
    };

    const bookmarkOption = getBookmarkOption(markLabel);
    const leftBorderColor = bookmarkOption
      ? bookmarkOption.color
      : "transparent";

    return (
      <Box
        sx={{
          mb: 1,
          p: 2,
          borderRadius: theme.borderRadius,
          boxShadow: theme.shadow,
          bgcolor: theme.white,
          cursor: "pointer",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          borderLeft: bookmarkOption ? `6px solid ${leftBorderColor}` : "none",
          "&:hover": {
            bgcolor: theme.lightPurple,
          },
          position: "relative",
        }}
        onClick={() => handleItemClick(item)}
      >
        {/* Question Info */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            flex: "1 1 auto",
            overflow: "hidden",
          }}
        >
          <Box sx={{ mr: 1.5, minWidth: 35, textAlign: "center" }}>
            <Typography
              variant="subtitle2"
              sx={{
                color: theme.primary,
                fontWeight: 700,
                bgcolor: "rgba(20, 3, 66, 0.1)",
                borderRadius: "4px",
                px: 1,
                py: 0.5,
              }}
            >
              #{item.questionNumber || "N/A"}
            </Typography>
          </Box>

          <Typography
            variant="body1"
            noWrap
            sx={{
              fontWeight: 500,
              color: theme.darkGray,
              flex: "1 1 auto",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {item?.additionalProp1?.questionName ||
              item?.prompt ||
              "Untitled Question"}
          </Typography>
        </Box>

        {/* Action Badges */}
        <Box sx={{ display: "flex", alignItems: "center", gap: 1, ml: 1 }}>
          {/* Bookmark Display */}
          {bookmarkOption && (
            <Tooltip title={`Marked as ${bookmarkOption.label}`}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer",
                  px: 1,
                  py: 0.5,
                  borderRadius: "12px",
                  bgcolor: bookmarkOption.bgColor,
                  border: `1px solid ${bookmarkOption.color}`,
                  minWidth: "fit-content",
                }}
                onClick={(e) => handleMarkQuestion(item, "", e)}
              >
                <span style={{ fontSize: "12px", marginRight: "4px" }}>
                  {bookmarkOption.icon}
                </span>
                <Typography
                  variant="caption"
                  sx={{
                    color: bookmarkOption.color,
                    fontWeight: 600,
                    fontSize: "0.7rem",
                  }}
                >
                  {bookmarkOption.label}
                </Typography>
              </Box>
            </Tooltip>
          )}

          {/* Shadow Status */}
          {isShadowed && (
            <Chip
              label="Shadow"
              size="small"
              sx={{
                bgcolor: theme.purple,
                color: theme.white,
                height: 24,
                fontSize: "0.75rem",
              }}
              onClick={(e) => handleShadowToggle(item, e)}
            />
          )}

          {/* Practice Status */}
          {isPracticed ? (
            <Chip
              label={
                practiceCount > 1 ? `Practiced x ${practiceCount}` : "Done"
              }
              size="small"
              sx={{
                bgcolor: theme.green,
                color: theme.white,
                height: 24,
                fontSize: "0.75rem",
                minWidth: 45,
              }}
            />
          ) : (
            <Chip
              label="Undone"
              size="small"
              sx={{
                bgcolor: "transparent",
                color: theme.gray,
                border: `1px solid ${theme.lightGray}`,
                height: 24,
                fontSize: "0.75rem",
              }}
            />
          )}

          {/* Difficulty */}
          {difficulty && (
            <Chip
              label={difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}
              size="small"
              sx={{
                bgcolor: difficultyColor.bg,
                color: difficultyColor.text,
                height: 24,
                fontSize: "0.75rem",
                minWidth: 55,
              }}
            />
          )}

          {/* Add to Shadow (if not shadowed) */}
          {!isShadowed && (
            <Tooltip title="Add to Shadow">
              <IconButton
                size="small"
                onClick={(e) => handleShadowToggle(item, e)}
                sx={{ color: theme.gray, p: 0.5 }}
              >
                <VisibilityIcon sx={{ fontSize: 18 }} />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Box>
    );
  };

  if (isLoading && subCategoryData.length === 0) {
    return (
      <Modal
        isOpen={isOpen}
        onRequestClose={onClose}
        className="modal"
        overlayClassName="overlay"
        shouldCloseOnOverlayClick={false}
      >
        <Box
          sx={{
            p: 3,
            bgcolor: theme.white,
            borderRadius: theme.borderRadius,
            boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
            maxWidth: "400px",
            mx: "auto",
            textAlign: "center",
          }}
        >
          <LinearProgress sx={{ mb: 2 }} />
          <Typography variant="h6" sx={{ color: theme.primary }}>
            Loading questions...
          </Typography>
        </Box>
      </Modal>
    );
  }

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="modal"
      overlayClassName="overlay"
      shouldCloseOnOverlayClick={true}
      style={{
        content: {
          width: "70%", // This will make it take up 95% of the viewport
          padding: "8px 8px",
          backgroundColor: theme.primary,
        },
        overlay: {
          backgroundColor: "rgba(0, 0, 0, 0.6)", // Optional: adjust overlay transparency
        },
      }}
    >
      <Box
        sx={{
          bgcolor: theme.lightPurple,
          borderRadius: theme.borderRadius,
          maxHeight: "100vh",
          display: "flex",
          flexDirection: "column",
          width: "100%",
          maxWidth: "1500px",
          mx: "auto",
          overflow: "hidden",
          boxShadow: "0 8px 24px rgba(0,0,0,0.12)",
        }}
      >
        {/* Title Section */}
        <Box
          sx={{
            bgcolor: theme.white,
            p: 2,
            borderBottom: `1px solid ${theme.lightGray}`,
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            position: "sticky",
            top: 0,
            zIndex: 2,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Avatar
              sx={{
                bgcolor: theme.primary,
                width: 48,
                height: 48,
                mr: 2,
              }}
            >
              {abbreviation}
            </Avatar>

            <Box>
              <Typography
                variant="h6"
                sx={{
                  color: theme.primary,
                  fontWeight: 700,
                }}
              >
                {subCategoryData && subCategoryData?.length > 0
                  ? subCategoryData[0]?.category?.name
                  : "Loading..."}
              </Typography>

              <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
                <Chip
                  label={`${practicedCount} Practiced`}
                  size="small"
                  sx={{ bgcolor: theme.green, color: theme.white, height: 24 }}
                />
                <Chip
                  label={`${totalQuestions} Total`}
                  size="small"
                  sx={{
                    bgcolor: theme.primary,
                    color: theme.white,
                    height: 24,
                  }}
                />
              </Box>
            </Box>
          </Box>

          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Paper
              sx={{
                display: "flex",
                alignItems: "center",
                height: 40,
                width: { xs: 120, sm: 200, md: 250 },
                px: 1,
                borderRadius: 20,
              }}
            >
              <InputBase
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                sx={{ ml: 1, flex: 1 }}
              />
              <IconButton type="button" sx={{ p: "5px" }} aria-label="search">
                <SearchIcon />
              </IconButton>
            </Paper>

            <Tooltip title="Reset Practice Status">
              <Button
                variant="outlined"
                size="small"
                startIcon={<RestartAltIcon />}
                onClick={openResetConfirmation}
                sx={{
                  borderColor: theme.red,
                  color: theme.red,
                  height: 40,
                  display: { xs: "none", md: "flex" },
                }}
              >
                Reset
              </Button>
            </Tooltip>

            <IconButton onClick={onClose} sx={{ color: theme.darkGray }}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Tab Section - Time Period */}
        <Box
          sx={{
            bgcolor: theme.white,
            position: "sticky",
            top: "71px",
            zIndex: 1,
            borderBottom: `1px solid ${theme.lightGray}`,
          }}
        >
          <Tabs
            value={timePeriod}
            onChange={(e, newValue) => setTimePeriod(newValue)}
            variant="fullWidth"
            sx={{
              ".MuiTabs-indicator": {
                backgroundColor: theme.primary,
                height: 3,
              },
              ".MuiTab-root": {
                textTransform: "none",
                fontWeight: 500,
                color: theme.gray,
                py: 1.5,
              },
              ".MuiTab-root.Mui-selected": {
                color: theme.primary,
                fontWeight: 700,
              },
            }}
          >
            <Tab label="All" value="all" />
            <Tab label="Weekly Practice" value="weekly" />
            <Tab label={`${currentMonth} Practice`} value="monthly" />
          </Tabs>
        </Box>

        {/* Filters Row */}
        <Box
          sx={{
            px: 2,
            py: 1.5,
            bgcolor: theme.white,
            display: "flex",
            alignItems: "center",
            flexWrap: { xs: "wrap", md: "nowrap" },
            gap: 1,
            position: "sticky",
            top: "123px",
            zIndex: 1,
            borderBottom: `1px solid ${theme.lightGray}`,
          }}
        >
          {/* Sort By */}
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <Select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              displayEmpty
              variant="outlined"
              startAdornment={
                <Box
                  component="span"
                  sx={{ display: "flex", alignItems: "center", mr: 1 }}
                >
                  {sortBy === "New" ? (
                    <ArrowDownwardIcon fontSize="small" />
                  ) : (
                    <ArrowUpwardIcon fontSize="small" />
                  )}
                </Box>
              }
              sx={{
                height: 36,
                ".MuiOutlinedInput-notchedOutline": {
                  borderColor: theme.lightGray,
                },
                "&:hover .MuiOutlinedInput-notchedOutline": {
                  borderColor: theme.primary,
                },
              }}
            >
              <MenuItem value="New">New</MenuItem>
              <MenuItem value="Q Num">Q Num</MenuItem>
            </Select>
          </FormControl>

          {/* Mark Filter */}
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <Select
              value={bookmarkFilter}
              onChange={(e) => setBookmarkFilter(e.target.value)}
              displayEmpty
              variant="outlined"
              renderValue={(selected) => (selected === "" ? "Mark" : selected)}
              sx={{
                height: 36,
                ".MuiOutlinedInput-notchedOutline": {
                  borderColor: theme.lightGray,
                },
                "&:hover .MuiOutlinedInput-notchedOutline": {
                  borderColor: theme.primary,
                },
              }}
            >
              <MenuItem value="">
                <em>All</em>
              </MenuItem>
              <MenuItem value="All Marked">All Marked</MenuItem>
              <MenuItem value="Review">🔄 Review</MenuItem>
              <MenuItem value="Important">⭐ Important</MenuItem>
              <MenuItem value="Challenging">🔥 Challenging</MenuItem>
              <MenuItem value="Favorite">❤️ Favorite</MenuItem>
              <MenuItem value="Later">⏰ Later</MenuItem>
              <MenuItem value="Not Marked">Not Marked</MenuItem>
            </Select>
          </FormControl>

          {/* Practice Status */}
          <FormControl size="small" sx={{ minWidth: 140 }}>
            <Select
              value={practiceFilter}
              onChange={(e) => setPracticeFilter(e.target.value)}
              displayEmpty
              variant="outlined"
       
              renderValue={(selected) =>
                selected === "" ? "Prac Status" : selected
              }
              sx={{
                height: 36,
                ".MuiOutlinedInput-notchedOutline": {
                  borderColor: theme.lightGray,
                },
                "&:hover .MuiOutlinedInput-notchedOutline": {
                  borderColor: theme.primary,
                },
              }}
            >
              <MenuItem value="">
                <em>All</em>
              </MenuItem>
              <MenuItem value="Practiced">Practiced</MenuItem>
              <MenuItem value="Unpracticed">Unpracticed</MenuItem>
            </Select>
          </FormControl>

          {/* Difficulty */}
          <FormControl size="small" sx={{ minWidth: 100 }}>
            <Select
              value={difficultyFilter}
              onChange={(e) => setDifficultyFilter(e.target.value)}
              displayEmpty
              variant="outlined"
          
              renderValue={(selected) =>
                selected === "" ? "Difficulty" : selected
              }
              sx={{
                height: 36,
                ".MuiOutlinedInput-notchedOutline": {
                  borderColor: theme.lightGray,
                },
                "&:hover .MuiOutlinedInput-notchedOutline": {
                  borderColor: theme.primary,
                },
              }}
            >
              <MenuItem value="">
                <em>All</em>
              </MenuItem>
              <MenuItem value="Easy">Easy</MenuItem>
              <MenuItem value="Medium">Medium</MenuItem>
              <MenuItem value="Hard">Hard</MenuItem>
            </Select>
          </FormControl>

          {/* Shadowing */}
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <Select
              value={shadowFilter ? "Shadowed" : ""}
              onChange={(e) => setShadowFilter(e.target.value === "Shadowed")}
              displayEmpty
              variant="outlined"
             
              renderValue={(selected) =>
                selected === "" ? "Shadowing" : selected
              }
              sx={{
                height: 36,
                ".MuiOutlinedInput-notchedOutline": {
                  borderColor: theme.lightGray,
                },
                "&:hover .MuiOutlinedInput-notchedOutline": {
                  borderColor: theme.primary,
                },
              }}
            >
              <MenuItem value="">
                <em>All</em>
              </MenuItem>
              <MenuItem value="Shadowed">Shadowed Only</MenuItem>
            </Select>
          </FormControl>

          {/* Spacer */}
          <Box sx={{ flexGrow: 1 }} />

          {/* Clear Filters Button */}
          <Button
            variant="outlined"
            startIcon={<FilterAltOffIcon />}
            size="small"
            onClick={handleClearFilters}
            sx={{
              borderColor: theme.lightGray,
              color: theme.gray,
              height: 36,
              "&:hover": {
                borderColor: theme.primary,
                color: theme.primary,
              },
              display: {
                xs:
                  bookmarkFilter ||
                  practiceFilter ||
                  difficultyFilter ||
                  shadowFilter ||
                  sortBy !== "New" ||
                  timePeriod !== "all"
                    ? "flex"
                    : "none",
                sm: "flex",
              },
            }}
          >
            Clear Filters
          </Button>

          {/* Mobile Reset Button */}
          <Button
            variant="outlined"
            size="small"
            startIcon={<RestartAltIcon />}
            onClick={openResetConfirmation}
            sx={{
              borderColor: theme.red,
              color: theme.red,
              height: 36,
              display: { xs: "flex", md: "none" },
            }}
          >
            Reset
          </Button>
        </Box>

        {/* Questions List */}
        <Box sx={{ flex: 1, overflow: "auto", p: 2 }}>
          {isLoading ? (
            [...Array(6)].map((_, idx) => (
              <Box
                key={idx}
                sx={{
                  mb: 1,
                  p: 2,
                  borderRadius: theme.borderRadius,
                  boxShadow: theme.shadow,
                  bgcolor: theme.white,
                  height: 60,
                  opacity: 0.7,
                  animation: "pulse 1.5s infinite ease-in-out",
                  "@keyframes pulse": {
                    "0%": { opacity: 0.6 },
                    "50%": { opacity: 0.8 },
                    "100%": { opacity: 0.6 },
                  },
                }}
              />
            ))
          ) : sortedData.length === 0 ? (
            <EmptyState />
          ) : (
            <>
              {/* Status Summary */}
              <Box
                sx={{
                  mb: 2,
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography variant="body2" sx={{ color: theme.gray }}>
                  {timePeriod === "all"
                    ? `Showing ${paginatedData.length} of ${sortedData.length} questions`
                    : `${
                        timePeriod === "weekly" ? "Weekly" : currentMonth
                      } practice: ${sortedData.length} questions`}
                </Typography>

                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography
                    variant="body2"
                    sx={{ color: theme.primary, fontWeight: 500 }}
                  >
                    Done {practicedCount} / {totalQuestions}
                  </Typography>
                </Box>
              </Box>

              {/* Question Items */}
              {paginatedData.map((item) => (
                <QuestionRow key={item.questionId} item={item} />
              ))}

              {/* Pagination */}
              {totalPages > 1 && (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    mt: 2,
                    pb: 1,
                  }}
                >
                  <Button
                    variant="outlined"
                    onClick={handlePreviousPage}
                    disabled={currentPage <= 1}
                    sx={{
                      minWidth: 40,
                      height: 40,
                      p: 0,
                      mx: 0.5,
                      borderColor: theme.lightGray,
                      color: theme.gray,
                      "&:hover:not(:disabled)": {
                        borderColor: theme.primary,
                        color: theme.primary,
                      },
                      "&:disabled": {
                        borderColor: theme.lightGray,
                        color: "rgba(100, 116, 139, 0.4)",
                      },
                    }}
                  >
                    <NavigateBeforeIcon />
                  </Button>

                  <Typography
                    variant="body2"
                    sx={{
                      px: 2,
                      color: theme.darkGray,
                      fontWeight: 500,
                    }}
                  >
                    {currentPage} of {totalPages}
                  </Typography>

                  <Button
                    variant="outlined"
                    onClick={handleNextPage}
                    disabled={currentPage >= totalPages}
                    sx={{
                      minWidth: 40,
                      height: 40,
                      p: 0,
                      mx: 0.5,
                      borderColor: theme.lightGray,
                      color: theme.gray,
                      "&:hover:not(:disabled)": {
                        borderColor: theme.primary,
                        color: theme.primary,
                      },
                      "&:disabled": {
                        borderColor: theme.lightGray,
                        color: "rgba(100, 116, 139, 0.4)",
                      },
                    }}
                  >
                    <NavigateNextIcon />
                  </Button>
                </Box>
              )}
            </>
          )}
        </Box>

        {/* Reset Dialog */}
        <Dialog
          open={showConfirmDialog}
          onClose={closeResetConfirmation}
          sx={{
            "& .MuiDialog-paper": {
              borderRadius: theme.borderRadius,
              boxShadow: "0 8px 24px rgba(0,0,0,0.15)",
            },
          }}
        >
          <DialogTitle sx={{ color: theme.primary, fontWeight: 600 }}>
            Reset Practice Status
          </DialogTitle>
          <DialogContent>
            <DialogContentText>
              This will reset the practice status for all {totalQuestions}{" "}
              questions to "Undone". Practice counts will be reset to zero. This
              action cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ px: 3, pb: 2 }}>
            <Button
              onClick={closeResetConfirmation}
              sx={{ color: theme.primary }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleResetPracStatus}
              variant="contained"
              sx={{
                bgcolor: theme.red,
                "&:hover": {
                  bgcolor: "#FF5C33",
                },
              }}
            >
              Reset All
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar Notifications */}
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={4000}
          onClose={handleSnackbarClose}
          anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
        >
          <Alert
            onClose={handleSnackbarClose}
            severity={snackbarSeverity}
            sx={{ width: "100%" }}
          >
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </Box>
    </Modal>
  );
};

export default SubCategoryModal;
