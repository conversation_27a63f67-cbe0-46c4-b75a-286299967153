import React from "react";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck } from '@fortawesome/free-solid-svg-icons';
import { Link } from "react-router-dom";
export default function About() {
  return (
    <>
      <section className="page-header -type-1">
        <div className="container">
          <div className="page-header__content">
            <div className="row justify-center text-center">
              <div className="col-auto">
                <div>
                  <h1 className="page-header__title">About Us</h1>
                </div>

                <div>
                  <p className="page-header__text">
                    We’re on a mission to deliver engaging, curated courses at a
                    reasonable price.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section className="layout-pt-md layout-pb-lg">
        <div className="container">
          <div className="row y-gap-50 justify-between items-center">
            <div className="col-lg-6 pr-50 sm:pr-15">
              <div className="composition -type-8">
                <div className="-el-1">
                  <img src={"/assets/img/home-3/masthead/meeting.jpeg"} alt="image" />
                </div>
                <div className="-el-2">
                  <img src={"/assets/img/home-3/masthead/laptop.jpeg"} alt="image" />
                </div>
                <div className="-el-3">
                  <img src={"/assets/img/home-3/masthead/liveClass.jpeg"} alt="image" />
                </div>
              </div>
            </div>

            <div className="col-lg-5">
              <h2 className="text-30 lh-16">
                Why Deep Insight Academy
              </h2>
              <p className="text-dark-1 mt-30">
                At  Deep Insight Academy, you can be assured that you will be learning from the best in the industry. We give a touch of care and personal attention to every student, which is what differentiates us from the rest.
              </p>
              <p className="pr-50 lg:pr-0 mt-25">
                We are so confident in our strategies that we offer a FREE Trial Face-2-Face Class to everyone – so you can judge for yourself! Please contact us to book your FREE Face-2-Face Trial Class now!
              </p>
              <div className="d-inline-block">
                <Link
                  to="/signup"
                  className="button -md -purple-1 text-white mt-30"
                >
                  Start Learning For Free
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
      <div>
        <section className="layout-pt-md layout-pb-lg">
          <div className="container">
            <div className="row y-gap-50 justify-between items-center">
              <h2 className="text-30 lh-16">
                Our Success is Defined by Our Student’s Success. The recipe of their success:
              </h2>
              <table className="mt-10">
                <tbody>
                  <tr>
                    <td className="text-left bg-red-500">
                      <ul className="list-disc">
                        <li className="text-20 lh-16 text-black flex items-center">
                          <FontAwesomeIcon icon={faCheck} className="mr-4" color="#140342" />
                          {"   "} Regular Personal Feedback
                        </li>
                        <li className="text-20 lh-16 text-black flex items-center">
                          <FontAwesomeIcon icon={faCheck} className="mr-4" color="#140342" />
                          {"   "} Learn effective techniques and strategies to crack your test
                        </li>
                        <li className="text-20 lh-16 text-black flex items-center">
                          <FontAwesomeIcon icon={faCheck} className="mr-4" color="#140342" />
                          {"   "} Mock tests under exam conditions
                        </li>
                        <li className="text-20 lh-16 text-black flex items-center">
                          <FontAwesomeIcon icon={faCheck} className="mr-4" color="#140342" />
                          {"   "} Access to our resources and practice materials here
                        </li>
                      </ul>
                    </td>
                  </tr>
                </tbody>
              </table>


            </div>
          </div>
        </section>
      </div>
    </>
  );
}
