import React, { createContext, useContext, useEffect, useState } from "react";
import useAudioCompression from "../../hooks/useAudioCompression";

const AudioCompressionContext = createContext();

export const useAudioCompressionContext = () => {
  const context = useContext(AudioCompressionContext);
  if (!context) {
    throw new Error(
      "useAudioCompressionContext must be used within AudioCompressionProvider"
    );
  }
  return context;
};

const AudioCompressionProvider = ({ children }) => {
  const {
    compressAudio,
    smartCompress,
    initFFmpeg,
    isLoading,
    progress,
    error,
    isFFmpegLoaded,
  } = useAudioCompression();

  const [initializationStarted, setInitializationStarted] = useState(false);

  // Pre-initialize FFmpeg when the app loads (optional - can be lazy loaded)
  useEffect(() => {
    const shouldPreload = localStorage.getItem("ffmpeg-preload") !== "false";

    if (shouldPreload && !initializationStarted && !isFFmpegLoaded) {
      setInitializationStarted(true);
      console.log("🚀 Pre-initializing FFmpeg for audio compression...");
      initFFmpeg().catch((err) => {
        console.warn("FFmpeg pre-initialization failed:", err);
        // Allow manual initialization later
        setInitializationStarted(false);
      });
    }
  }, [initFFmpeg, isFFmpegLoaded, initializationStarted]);

  const value = {
    compressAudio,
    smartCompress,
    initFFmpeg,
    isLoading,
    progress,
    error,
    isFFmpegLoaded,
    initializationStarted,
  };

  return (
    <AudioCompressionContext.Provider value={value}>
      {children}
    </AudioCompressionContext.Provider>
  );
};

export default AudioCompressionProvider;
