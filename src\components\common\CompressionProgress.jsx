import React from "react";

const CompressionProgress = ({
  isVisible,
  progress,
  isLoading,
  stage = "compressing",
  compressionStats = null,
}) => {
  if (!isVisible) return null;

  const getStageText = () => {
    switch (stage) {
      case "initializing":
        return "Initializing audio processor...";
      case "compressing":
        return "Compressing audio...";
      case "uploading":
        return "Uploading compressed audio...";
      case "completed":
        return "Compression completed!";
      default:
        return "Processing audio...";
    }
  };

  return (
    <div
      style={{
        position: "fixed",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        background: "rgba(20, 3, 66, 0.95)",
        color: "white",
        padding: "30px",
        borderRadius: "12px",
        boxShadow: "0 10px 30px rgba(0,0,0,0.3)",
        zIndex: 10000,
        minWidth: "300px",
        textAlign: "center",
        backdropFilter: "blur(10px)",
      }}
    >
      {/* Header */}
      <div style={{ marginBottom: "20px" }}>
        <div style={{ fontSize: "24px", marginBottom: "8px" }}>🎵</div>
        <div
          style={{ fontSize: "16px", fontWeight: "600", marginBottom: "5px" }}
        >
          {getStageText()}
        </div>
        {stage === "initializing" && (
          <div style={{ fontSize: "12px", color: "#ccc" }}>
            Loading FFmpeg... (first time only)
          </div>
        )}
      </div>

      {/* Progress Bar */}
      <div
        style={{
          width: "100%",
          height: "8px",
          backgroundColor: "rgba(255,255,255,0.2)",
          borderRadius: "4px",
          overflow: "hidden",
          marginBottom: "15px",
        }}
      >
        <div
          style={{
            width: `${progress}%`,
            height: "100%",
            backgroundColor: "#4CAF50",
            borderRadius: "4px",
            transition: "width 0.3s ease",
            background: "linear-gradient(90deg, #4CAF50, #66BB6A)",
          }}
        />
      </div>

      {/* Progress Text */}
      <div style={{ fontSize: "14px", marginBottom: "15px" }}>
        {progress}% complete
      </div>

      {/* Compression Stats */}
      {compressionStats && stage === "completed" && (
        <div
          style={{
            backgroundColor: "rgba(255,255,255,0.1)",
            padding: "15px",
            borderRadius: "8px",
            fontSize: "12px",
            textAlign: "left",
          }}
        >
          <div
            style={{
              fontWeight: "600",
              marginBottom: "8px",
              textAlign: "center",
            }}
          >
            Compression Results
          </div>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              marginBottom: "4px",
            }}
          >
            <span>Original Size:</span>
            <span>
              {(compressionStats.originalSize / 1024 / 1024).toFixed(2)} MB
            </span>
          </div>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              marginBottom: "4px",
            }}
          >
            <span>Compressed Size:</span>
            <span>
              {(compressionStats.compressedSize / 1024 / 1024).toFixed(2)} MB
            </span>
          </div>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              marginBottom: "4px",
            }}
          >
            <span>Saved:</span>
            <span style={{ color: "#4CAF50", fontWeight: "600" }}>
              {compressionStats.compressionRatio}%
            </span>
          </div>
        </div>
      )}

      {/* Loading Spinner */}
      {isLoading && stage !== "completed" && (
        <div
          style={{
            display: "inline-block",
            width: "20px",
            height: "20px",
            border: "2px solid rgba(255,255,255,0.3)",
            borderTop: "2px solid white",
            borderRadius: "50%",
            animation: "spin 1s linear infinite",
          }}
        />
      )}

      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default CompressionProgress;
