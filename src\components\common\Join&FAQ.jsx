import { useState } from "react";
import { motion } from "framer-motion";

function JoinAndMission() {
  const [activeAccordion, setActiveAccordion] = useState(0);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const formVariants = {
    hidden: { x: 50, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  // Join section stats data
  const statsData = [
    {
      icon: "/assets/img/home-1/hero/j-1.png",
      title: "25+ Years of experience",
    },
    {
      icon: "/assets/img/home-1/hero/j-2.png",
      title: "1500+ University options",
    },
    {
      icon: "/assets/img/home-1/hero/j-3.png",
      title: "10K+ Students Counseled",
    },
    {
      icon: "/assets/img/home-1/hero/j-4.png",
      title: "100% Satisfied Students",
    },
  ];

  // FAQ data
  const faqData = [
    {
      question: "Expert Instructors:",
      answer:
        "Our experienced and certified instructors ER MANDEEP SINGH & ER ARSHDEEP KAUR provide personalized guidance and strategies tailored to each student's strengths and weaknesses.",
    },
    {
      question: "State-of-the-Art Resources",
      answer:
        "Access to cutting-edge learning materials and practice tests that mirror real exam conditions.",
    },
    {
      question: "Flexible Learning Options",
      answer:
        "Choose from various learning formats including online, offline, and hybrid classes to fit your schedule.",
    },
    {
      question: "Proven Track Record",
      answer:
        "Join thousands of successful students who have achieved their target scores with our proven methodology.",
    },
    {
      question: "Personalized Study Plans",
      answer:
        "Customized study plans designed to maximize your learning efficiency and score improvement.",
    },
  ];

  const toggleAccordion = (index) => {
    setActiveAccordion(activeAccordion === index ? -1 : index);
  };

  return (
    <>
      {/* Join Section */}
      <motion.section
        className="layout-pt-lg layout-pb-lg"
        style={{
          background: "linear-gradient(135deg, #140342 0%, #1e0a5a 100%)",
          position: "relative",
          overflow: "hidden",
        }}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={containerVariants}
      >
        {/* Background Pattern */}
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D5B363' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            opacity: 0.1,
          }}
        />
        <div className="container">
          <motion.div className="row" variants={containerVariants}>
            {/* Header */}
            <motion.div
              className="col-12 text-center mb-40"
              variants={itemVariants}
            >
              <h2
                className="text-white fw-700 mb-20"
                style={{
                  fontSize: "36px",
                  fontFamily: "Poppins, sans-serif",
                  lineHeight: "1.3",
                  marginBottom: "20px",
                }}
              >
                Join Global <span style={{ color: "#D5B363" }}>No.1</span>
                <br />
                <span style={{ color: "#D5B363" }}>
                  PTE, NAATI & IELTS
                </span>{" "}
                Institute
              </h2>
              <p
                className="text-white mb-40"
                style={{
                  fontSize: "16px",
                  opacity: "0.9",
                  marginBottom: "40px",
                }}
              >
                Join 5,000+ satisfied students in achieving their desired test
                scores!
              </p>
            </motion.div>

            {/* Left Side - Stats */}
            <motion.div className="col-lg-5" variants={itemVariants}>
              <div className="pr-40 lg:pr-0">
                {/* Stats */}
                <motion.div className="y-gap-30" variants={containerVariants}>
                  {statsData.map((stat, index) => (
                    <motion.div
                      key={index}
                      variants={itemVariants}
                      whileHover={{ x: 10 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <div className="d-flex items-center mb-30">
                        <motion.div
                          className="mr-20"
                          style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            flexShrink: 0,
                          }}
                          whileHover={{ scale: 1.1, rotate: 5 }}
                          transition={{ type: "spring", stiffness: 300 }}
                        >
                          <img
                            src={stat.icon}
                            alt={stat.title}
                            style={{
                              width: "60px",
                              height: "60px",
                              objectFit: "contain",
                            }}
                          />
                        </motion.div>
                        <h4
                          className="text-white fw-500"
                          style={{
                            fontSize: "18px",
                            margin: 0,
                            fontFamily: "Poppins, sans-serif",
                          }}
                        >
                          {stat.title}
                        </h4>
                      </div>
                      {/* Divider - only show if not the last item */}
                      {index < statsData.length - 1 && (
                        <motion.hr
                          style={{
                            border: "none",
                            height: "2px",
                            backgroundColor: "#FFFFFF",
                            margin: "20px 0",
                          }}
                          initial={{ scaleX: 0 }}
                          whileInView={{ scaleX: 1 }}
                          transition={{ delay: 0.5 + index * 0.1 }}
                        />
                      )}
                    </motion.div>
                  ))}
                </motion.div>
              </div>
            </motion.div>

            {/* Right Side - Form */}
            <motion.div className="col-lg-7" variants={formVariants}>
              <div
                className="p-40"
                style={{
                  background: "rgba(255, 255, 255, 0.95)",
                  borderRadius: "20px",
                  backdropFilter: "blur(15px)",
                  border: "2px solid rgba(213, 179, 99, 0.3)",
                  boxShadow: "0 20px 40px rgba(0, 0, 0, 0.1)",
                  position: "relative",
                  overflow: "hidden",
                  padding: "20px",
                }}
              >
                {/* Gold accent border */}
                <div
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    height: "4px",
                    background:
                      "linear-gradient(90deg, #D5B363 0%, #f4d574 100%)",
                    borderRadius: "20px 20px 0 0",
                  }}
                />
                <form>
                  <div className="row y-gap-20">
                    {/* Name Field */}
                    <div className="col-12">
                      <input
                        type="text"
                        placeholder="Name"
                        style={{
                          width: "100%",
                          padding: "15px 20px",
                          border: "1px solid rgba(213, 179, 99, 0.3)",
                          borderRadius: "12px",
                          fontSize: "16px",
                          backgroundColor: "rgba(255, 255, 255, 0.8)",
                          backdropFilter: "blur(5px)",
                          outline: "none",
                          transition: "all 0.3s ease",
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = "#D5B363";
                          e.target.style.boxShadow =
                            "0 0 0 3px rgba(213, 179, 99, 0.1)";
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor =
                            "rgba(213, 179, 99, 0.3)";
                          e.target.style.boxShadow = "none";
                        }}
                      />
                    </div>

                    {/* Phone and Email */}
                    <div className="col-lg-6">
                      <input
                        type="tel"
                        placeholder="Phone"
                        style={{
                          width: "100%",
                          padding: "15px 20px",
                          border: "1px solid rgba(213, 179, 99, 0.3)",
                          borderRadius: "12px",
                          fontSize: "16px",
                          backgroundColor: "rgba(255, 255, 255, 0.8)",
                          backdropFilter: "blur(5px)",
                          outline: "none",
                          transition: "all 0.3s ease",
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = "#D5B363";
                          e.target.style.boxShadow =
                            "0 0 0 3px rgba(213, 179, 99, 0.1)";
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor =
                            "rgba(213, 179, 99, 0.3)";
                          e.target.style.boxShadow = "none";
                        }}
                      />
                    </div>
                    <div className="col-lg-6">
                      <input
                        type="email"
                        placeholder="Email"
                        style={{
                          width: "100%",
                          padding: "15px 20px",
                          border: "1px solid rgba(213, 179, 99, 0.3)",
                          borderRadius: "12px",
                          fontSize: "16px",
                          backgroundColor: "rgba(255, 255, 255, 0.8)",
                          backdropFilter: "blur(5px)",
                          outline: "none",
                          transition: "all 0.3s ease",
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = "#D5B363";
                          e.target.style.boxShadow =
                            "0 0 0 3px rgba(213, 179, 99, 0.1)";
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor =
                            "rgba(213, 179, 99, 0.3)";
                          e.target.style.boxShadow = "none";
                        }}
                      />
                    </div>

                    {/* PTE Scores */}
                    <div className="col-lg-6">
                      <input
                        type="text"
                        placeholder="Previous PTE Score"
                        style={{
                          width: "100%",
                          padding: "15px 20px",
                          border: "1px solid rgba(213, 179, 99, 0.3)",
                          borderRadius: "12px",
                          fontSize: "16px",
                          backgroundColor: "rgba(255, 255, 255, 0.8)",
                          backdropFilter: "blur(5px)",
                          outline: "none",
                          transition: "all 0.3s ease",
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = "#D5B363";
                          e.target.style.boxShadow =
                            "0 0 0 3px rgba(213, 179, 99, 0.1)";
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor =
                            "rgba(213, 179, 99, 0.3)";
                          e.target.style.boxShadow = "none";
                        }}
                      />
                    </div>
                    <div className="col-lg-6">
                      <input
                        type="text"
                        placeholder="Required PTE Score"
                        style={{
                          width: "100%",
                          padding: "15px 20px",
                          border: "1px solid rgba(213, 179, 99, 0.3)",
                          borderRadius: "12px",
                          fontSize: "16px",
                          backgroundColor: "rgba(255, 255, 255, 0.8)",
                          backdropFilter: "blur(5px)",
                          outline: "none",
                          transition: "all 0.3s ease",
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = "#D5B363";
                          e.target.style.boxShadow =
                            "0 0 0 3px rgba(213, 179, 99, 0.1)";
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor =
                            "rgba(213, 179, 99, 0.3)";
                          e.target.style.boxShadow = "none";
                        }}
                      />
                    </div>

                    {/* Country Selection */}
                    <div className="col-12">
                      <select
                        style={{
                          width: "100%",
                          padding: "15px 20px",
                          border: "1px solid rgba(213, 179, 99, 0.3)",
                          borderRadius: "12px",
                          fontSize: "16px",
                          backgroundColor: "rgba(255, 255, 255, 0.8)",
                          backdropFilter: "blur(5px)",
                          outline: "none",
                          color: "#666",
                          transition: "all 0.3s ease",
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = "#D5B363";
                          e.target.style.boxShadow =
                            "0 0 0 3px rgba(213, 179, 99, 0.1)";
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor =
                            "rgba(213, 179, 99, 0.3)";
                          e.target.style.boxShadow = "none";
                        }}
                      >
                        <option>Choose A Country</option>
                        <option>Australia</option>
                        <option>Canada</option>
                        <option>New Zealand</option>
                        <option>United Kingdom</option>
                      </select>
                    </div>

                    {/* Message */}
                    <div className="col-12">
                      <textarea
                        placeholder="Message"
                        rows="4"
                        style={{
                          width: "100%",
                          padding: "15px 20px",
                          border: "1px solid rgba(213, 179, 99, 0.3)",
                          borderRadius: "12px",
                          fontSize: "16px",
                          backgroundColor: "rgba(255, 255, 255, 0.8)",
                          backdropFilter: "blur(5px)",
                          outline: "none",
                          resize: "vertical",
                          transition: "all 0.3s ease",
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = "#D5B363";
                          e.target.style.boxShadow =
                            "0 0 0 3px rgba(213, 179, 99, 0.1)";
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor =
                            "rgba(213, 179, 99, 0.3)";
                          e.target.style.boxShadow = "none";
                        }}
                      ></textarea>
                    </div>

                    {/* Submit Button */}
                    <div className="col-12">
                      <motion.button
                        type="submit"
                        style={{
                          width: "100%",
                          padding: "15px",
                          background:
                            "linear-gradient(135deg, #D5B363 0%, #f4d574 100%)",
                          color: "#140342",
                          border: "none",
                          borderRadius: "12px",
                          fontSize: "16px",
                          fontWeight: "700",
                          cursor: "pointer",
                          boxShadow: "0 8px 25px rgba(213, 179, 99, 0.4)",
                        }}
                        whileHover={{
                          scale: 1.02,
                          boxShadow: "0 12px 35px rgba(213, 179, 99, 0.5)",
                        }}
                        whileTap={{ scale: 0.98 }}
                        transition={{ type: "spring", stiffness: 400 }}
                      >
                        SEND MESSAGE
                      </motion.button>
                    </div>
                  </div>
                </form>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </motion.section>

      <motion.section
        className="layout-pt-lg layout-pb-lg"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={containerVariants}
      >
        <div className="container">
          {/* FAQ Section - Bottom */}
          <motion.div className="row" variants={containerVariants}>
            <div className="col-12">
              <div
                className="p-80"
                style={{
                  // border: "2px solid #140342",
                  borderRadius: "25px",
                  color: "#140342",
                  padding: "80px 60px",
                }}
              >
                <h3
                  className="text-[#140342] fw-700 mb-20 text-center"
                  style={{
                    fontSize: "clamp(28px, 5vw, 42px)",
                    fontFamily: "Poppins, sans-serif",
                  }}
                >
                  Why Choose <span style={{ color: "#D5B363" }}>Us?</span>
                </h3>
                <p
                  className="text-[#1c1c1c] mb-50 text-center"
                  style={{
                    fontSize: "16px",
                    opacity: "0.9",
                    lineHeight: "1.6",
                    maxWidth: "600px",
                    margin: "0 auto 50px",
                  }}
                >
                  Here are some compelling reasons to choose Deep Insight
                  Academy for PTE (Pearson Test of English) preparation:
                </p>

                {/* FAQ Accordion */}
                <motion.div
                  className="row justify-center"
                  variants={containerVariants}
                >
                  <div className="col-lg-10">
                    <div className="y-gap-25">
                      {faqData.map((faq, index) => (
                        <motion.div
                          key={index}
                          style={{
                            background:
                              "linear-gradient(135deg, #140342 0%, #1e0a5a 100%)",
                            backdropFilter: "blur(15px)",
                            border: "1px solid rgba(213, 179, 99, 0.3)",
                            borderRadius: "20px",
                            padding: "25px",
                            marginBottom: "20px",
                            color: "#ffffff",
                            boxShadow: "0 10px 30px rgba(0, 0, 0, 0.1)",
                          }}
                          variants={itemVariants}
                          whileHover={{
                            background: "#140342",
                            scale: 1.01,
                          }}
                          transition={{ type: "spring", stiffness: 300 }}
                        >
                          <motion.button
                            onClick={() => toggleAccordion(index)}
                            style={{
                              width: "100%",
                              textAlign: "left",
                              background: "none",
                              border: "none",
                              color: "#ffffff",
                              fontSize: "18px",
                              fontWeight: "600",
                              cursor: "pointer",
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                              padding: "0",
                            }}
                            whileHover={{ color: "#D5B363" }}
                            transition={{ duration: 0.2 }}
                          >
                            {faq.question}
                            <motion.span
                              style={{
                                fontSize: "16px",
                                marginLeft: "20px",
                              }}
                              animate={{
                                rotate: activeAccordion === index ? 180 : 0,
                              }}
                              transition={{ duration: 0.3 }}
                            >
                              ▼
                            </motion.span>
                          </motion.button>
                          {activeAccordion === index && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: "auto" }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.3 }}
                              style={{
                                paddingTop: "20px",
                                fontSize: "16px",
                                opacity: "0.9",
                                lineHeight: "1.7",
                              }}
                            >
                              {faq.answer}
                            </motion.div>
                          )}
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.section>

      {/* Modern Address & Map Section */}
      <motion.section
        className="layout-pt-lg layout-pb-lg"
        style={{ backgroundColor: "#f8f9fa" }}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={containerVariants}
      >
        <div className="container">
          <motion.div className="row" variants={containerVariants}>
            {/* Left Side - Address Info */}
            <motion.div className="col-lg-6" variants={itemVariants}>
              <div
                style={{
                  background:
                    "linear-gradient(135deg, #140342 0%, #1e0a5a 100%)",
                  borderRadius: "25px",
                  padding: "60px 40px",
                  height: "100%",
                  minHeight: "500px",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  position: "relative",
                  overflow: "hidden",
                }}
              >
                {/* Background Pattern */}
                <div
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                    opacity: 0.1,
                  }}
                />

                {/* Content */}
                <div style={{ position: "relative", zIndex: 2 }}>
                  {/* Icon */}
                  <motion.div
                    className="mb-40"
                    variants={itemVariants}
                    whileHover={{ scale: 1.1, rotate: 10 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div
                      style={{
                        width: "100px",
                        height: "100px",
                        background:
                          "linear-gradient(135deg, #D5B363 0%, #f4d574 100%)",
                        borderRadius: "50%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        boxShadow: "0 20px 40px rgba(213, 179, 99, 0.3)",
                      }}
                    >
                      <svg
                        width="40"
                        height="40"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"
                          fill="#140342"
                        />
                      </svg>
                    </div>
                  </motion.div>

                  {/* Title */}
                  <motion.h2
                    className="text-white fw-700 mb-20"
                    variants={itemVariants}
                    style={{
                      fontSize: "clamp(28px, 5vw, 42px)",
                      fontFamily: "Poppins, sans-serif",
                      lineHeight: "1.2",
                      marginBottom: "20px",
                    }}
                  >
                    Visit Our
                    <br />
                    <span style={{ color: "#D5B363" }}>Modern Campus</span>
                  </motion.h2>

                  {/* Description */}
                  <motion.p
                    className="text-white mb-40"
                    variants={itemVariants}
                    style={{
                      fontSize: "16px",
                      opacity: "0.9",
                      lineHeight: "1.7",
                      marginBottom: "40px",
                    }}
                  >
                    Experience world-class education at our state-of-the-art
                    facility. Located in the heart of Truganina, our campus
                    offers the perfect learning environment for your success.
                  </motion.p>

                  {/* Address Details */}
                  <motion.div className="mb-40" variants={itemVariants}>
                    <motion.div
                      className="d-flex align-items-start mb-20"
                      whileHover={{ x: 10 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <div
                        style={{
                          width: "50px",
                          height: "50px",
                          backgroundColor: "rgba(255, 255, 255, 0.1)",
                          borderRadius: "12px",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          marginRight: "15px",
                          flexShrink: 0,
                        }}
                      >
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"
                            fill="#D5B363"
                          />
                        </svg>
                      </div>
                      <div>
                        <h5
                          className="text-white fw-600 mb-5"
                          style={{ fontSize: "18px", marginBottom: "5px" }}
                        >
                          Deep Insight Academy
                        </h5>
                        <p
                          className="text-white mb-0"
                          style={{ opacity: "0.8", fontSize: "14px" }}
                        >
                          8 Rugby Cres, Truganina VIC 3029, Australia
                        </p>
                      </div>
                    </motion.div>

                    <motion.div
                      className="d-flex align-items-center mb-20"
                      whileHover={{ x: 10 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <div
                        style={{
                          width: "50px",
                          height: "50px",
                          backgroundColor: "rgba(255, 255, 255, 0.1)",
                          borderRadius: "12px",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          marginRight: "15px",
                          flexShrink: 0,
                        }}
                      >
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"
                            fill="#D5B363"
                          />
                        </svg>
                      </div>
                      <div>
                        <h6
                          className="text-white fw-600 mb-5"
                          style={{ fontSize: "16px", marginBottom: "5px" }}
                        >
                          Call Us
                        </h6>
                        <p
                          className="text-white mb-0"
                          style={{ opacity: "0.8", fontSize: "14px" }}
                        >
                          +61 478 730 730
                        </p>
                      </div>
                    </motion.div>

                    <motion.div
                      className="d-flex align-items-center mb-20"
                      whileHover={{ x: 10 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <div
                        style={{
                          width: "50px",
                          height: "50px",
                          backgroundColor: "rgba(255, 255, 255, 0.1)",
                          borderRadius: "12px",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          marginRight: "15px",
                          flexShrink: 0,
                        }}
                      >
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67V7z"
                            fill="#D5B363"
                          />
                        </svg>
                      </div>
                      <div>
                        <h6
                          className="text-white fw-600 mb-5"
                          style={{ fontSize: "16px", marginBottom: "5px" }}
                        >
                          Open Hours
                        </h6>
                        <p
                          className="text-white mb-0"
                          style={{ opacity: "0.8", fontSize: "14px" }}
                        >
                          Mon - Fri: 9:00 AM - 6:00 PM
                        </p>
                      </div>
                    </motion.div>
                  </motion.div>

                  {/* Action Buttons */}
                  <motion.div
                    className="d-flex flex-wrap x-gap-15 y-gap-15"
                    variants={itemVariants}
                  >
                    <motion.a
                      href="https://maps.google.com/?q=8+Rugby+Cres,+Truganina+VIC+3029,+Australia"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="button px-25 h-50 rounded-200 text-black"
                      style={{
                        background:
                          "linear-gradient(135deg, #D5B363 0%, #f4d574 100%)",
                        color: "#000000",
                        textDecoration: "none",
                        display: "flex",
                        alignItems: "center",
                        gap: "10px",
                        fontWeight: "600",
                        boxShadow: "0 10px 30px rgba(213, 179, 99, 0.3)",
                      }}
                      whileHover={{
                        scale: 1.05,
                        boxShadow: "0 15px 40px rgba(213, 179, 99, 0.4)",
                      }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ type: "spring", stiffness: 400 }}
                    >
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"
                          fill="currentColor"
                        />
                      </svg>
                      <span>Get Directions</span>
                    </motion.a>
                  </motion.div>
                </div>
              </div>
            </motion.div>

            {/* Right Side - Interactive Map */}
            <motion.div className="col-lg-6" variants={itemVariants}>
              <motion.div
                style={{
                  borderRadius: "25px",
                  overflow: "hidden",
                  height: "100%",
                  minHeight: "500px",
                  boxShadow: "0 25px 50px rgba(0, 0, 0, 0.15)",
                  position: "relative",
                }}
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                {/* Map Overlay for Styling */}
                <div
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background:
                      "linear-gradient(45deg, rgba(20, 3, 66, 0.1) 0%, rgba(213, 179, 99, 0.1) 100%)",
                    zIndex: 1,
                    pointerEvents: "none",
                  }}
                />

                {/* Google Maps Embed */}
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3151.019285796892!2d144.74089431531895!3d-37.82879397975171!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6ad6564c5b9b7b9f%3A0x5045675218ce6e0!2s8%20Rugby%20Cres%2C%20Truganina%20VIC%203029%2C%20Australia!5e0!3m2!1sen!2sus!4v1673000000000!5m2!1sen!2sus"
                  width="100%"
                  height="100%"
                  style={{
                    border: 0,
                    minHeight: "500px",
                    filter: "grayscale(20%) contrast(1.1)",
                  }}
                  allowFullScreen=""
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  title="Deep Insight Academy Location"
                />

                {/* Map Info Card */}
                <motion.div
                  style={{
                    position: "absolute",
                    bottom: "20px",
                    left: "20px",
                    right: "20px",
                    background: "rgba(255, 255, 255, 0.95)",
                    backdropFilter: "blur(10px)",
                    borderRadius: "15px",
                    padding: "20px",
                    zIndex: 2,
                    boxShadow: "0 10px 30px rgba(0, 0, 0, 0.2)",
                  }}
                  initial={{ y: 50, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.5, duration: 0.6 }}
                >
                  <div className="d-flex align-items-center">
                    <div
                      style={{
                        width: "50px",
                        height: "50px",
                        background:
                          "linear-gradient(135deg, #140342 0%, #1e0a5a 100%)",
                        borderRadius: "12px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        marginRight: "15px",
                      }}
                    >
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"
                          fill="#D5B363"
                        />
                      </svg>
                    </div>
                    <div>
                      <h6
                        style={{
                          margin: 0,
                          fontSize: "16px",
                          fontWeight: "600",
                          color: "#140342",
                        }}
                      >
                        Deep Insight Academy
                      </h6>
                      <p
                        style={{
                          margin: 0,
                          fontSize: "14px",
                          color: "#666",
                          opacity: "0.8",
                        }}
                      >
                        8 Rugby Cres, Truganina VIC 3029
                      </p>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </motion.section>
    </>
  );
}

export default JoinAndMission;
