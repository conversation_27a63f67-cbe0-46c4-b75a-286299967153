// components/Loader.js
import React from 'react';

const Loader = () => (
  <div className="loader">
    <div className="spinner"></div>
    <style jsx>{`
      .loader {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100px;
      }
      .spinner {
        border: 8px solid #f3f3f3;
        border-top: 8px solid [#D7AB0F];
        border-radius: 50%;
        width: 50px;
        height: 50px;
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `}</style>
  </div>
);

export default Loader;
