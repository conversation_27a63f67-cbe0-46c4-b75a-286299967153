import { motion } from "framer-motion";

function Mission() {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <motion.section
      className="layout-pt-md layout-pt-lg-lg layout-pb-md layout-pb-lg-lg"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.3 }}
      variants={containerVariants}
    >
      <div className="container">
        {/* Leadership Team Section */}
        <motion.div className="row mb-4 mb-md-5" variants={containerVariants}>
          <div className="col-12">
            <div
              className="px-3 px-md-5 py-4 py-md-5"
              style={{
                backgroundColor: "",
                borderRadius: "25px",
                color: "white",
              }}
            >
              <h2
                className="text-[#140342] fw-700 mb-8 text-center "
                style={{
                  fontSize: "clamp(24px, 5vw, 48px)",
                  fontFamily: "Poppins, sans-serif",
                  lineHeight: "1.3",
                  marginBottom: "20px",
                }}
              >
                Meet Our{" "}
                <span style={{ color: "#D5B363" }}>Leadership Team</span>
              </h2>

              {/* Leadership Team Cards */}
              <div className="row justify-center y-gap-20 y-gap-md-30 mb-4 mb-md-5 mt-4">
                {/* Founder Card */}
                <div className="col-12 col-sm-10 col-md-8 col-lg-5">
                  <motion.div
                    className="text-center position-relative"
                    style={{
                      background:
                        "linear-gradient(135deg, #140342 0%, #1e0a5a 100%)",
                      backdropFilter: "blur(20px)",
                      borderRadius: "clamp(15px, 3vw, 30px)",
                      padding: "clamp(20px, 5vw, 50px) clamp(15px, 4vw, 40px)",
                      border: "2px solid rgba(213, 179, 99, 0.3)",
                      boxShadow: "0 25px 50px rgba(0, 0, 0, 0.3)",
                      overflow: "hidden",
                    }}
                    whileHover={{
                      scale: 1.02,
                      y: -15,
                      boxShadow: "0 35px 70px rgba(0, 0, 0, 0.4)",
                    }}
                    transition={{ type: "spring", stiffness: 200 }}
                  >
                    {/* Decorative Elements */}
                    <div
                      style={{
                        position: "absolute",
                        top: "-60px",
                        right: "-60px",
                        width: "120px",
                        height: "120px",
                        background:
                          "linear-gradient(45deg, rgba(213, 179, 99, 0.2), rgba(244, 213, 116, 0.1))",
                        borderRadius: "50%",
                        zIndex: 0,
                      }}
                    />

                    <div style={{ position: "relative", zIndex: 1 }}>
                      <motion.div
                        style={{
                          position: "relative",
                          display: "inline-block",
                          marginBottom: "15px",
                        }}
                        whileHover={{ scale: 1.08 }}
                        transition={{ type: "spring", stiffness: 400 }}
                      >
                        <img
                          src="/assets/img/home-1/hero/fd.png"
                          alt="Founder"
                          style={{
                            width: "clamp(150px, 25vw, 220px)",
                            height: "clamp(150px, 25vw, 220px)",
                            objectFit: "contain",
                            borderRadius: "50%",
                            border:
                              "clamp(4px, 1vw, 8px) solid rgba(213, 179, 99, 0.8)",
                            boxShadow: "0 25px 60px rgba(0, 0, 0, 0.5)",
                          }}
                        />
                        <div
                          style={{
                            position: "absolute",
                            bottom: "8px",
                            right: "8px",
                            width: "45px",
                            height: "45px",
                            background:
                              "linear-gradient(45deg, #D5B363, #f4d574)",
                            borderRadius: "50%",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            border: "4px solid white",
                            fontSize: "18px",
                            color: "#140342",
                            fontWeight: "bold",
                          }}
                        >
                          ✓
                        </div>
                      </motion.div>

                      <h4
                        className="text-white fw-700 mb-10"
                        style={{
                          fontSize: "clamp(20px, 4vw, 32px)",
                          fontFamily: "Poppins, sans-serif",
                          letterSpacing: "-0.5px",
                        }}
                      >
                        Mandeep Singh
                      </h4>

                      <div
                        style={{
                          background: "rgba(213, 179, 99, 0.2)",
                          backdropFilter: "blur(10px)",
                          border: "1px solid rgba(213, 179, 99, 0.3)",
                          borderRadius: "25px",
                          padding: "12px 25px",
                          display: "inline-block",
                          marginBottom: "15px",
                        }}
                      >
                        <p
                          className="mb-0"
                          style={{
                            fontSize: "16px",
                            fontWeight: "700",
                            textTransform: "uppercase",
                            letterSpacing: "1px",
                            color: "#D5B363",
                          }}
                        >
                          Founder & CEO
                        </p>
                      </div>

                      <p
                        className="text-white"
                        style={{
                          opacity: "0.95",
                          fontSize: "clamp(14px, 2vw, 16px)",
                          lineHeight: "1.6",
                          margin: 0,
                          fontWeight: "400",
                          maxWidth: "280px",
                          marginLeft: "auto",
                          marginRight: "auto",
                        }}
                      >
                        Visionary leader with years of experience in education
                        and immigration services, helping thousands achieve
                        their dreams.
                      </p>
                    </div>
                  </motion.div>
                </div>

                {/* Director Card */}
                <div className="col-12 col-sm-10 col-md-8 col-lg-5">
                  <motion.div
                    className="text-center position-relative"
                    style={{
                      background: "rgba(255, 255, 255, 0.95)",
                      backdropFilter: "blur(10px)",
                      border: "2px solid rgba(213, 179, 99, 0.3)",
                      borderRadius: "clamp(15px, 3vw, 30px)",
                      padding: "clamp(20px, 5vw, 50px) clamp(15px, 4vw, 40px)",
                      boxShadow: "0 15px 35px rgba(0, 0, 0, 0.1)",
                      overflow: "hidden",
                    }}
                    whileHover={{
                      scale: 1.02,
                      y: -15,
                      boxShadow: "0 35px 70px rgba(0, 0, 0, 0.4)",
                    }}
                    transition={{ type: "spring", stiffness: 200 }}
                  >
                    {/* Decorative Elements */}
                    <div
                      style={{
                        position: "absolute",
                        top: "-60px",
                        left: "-60px",
                        width: "120px",
                        height: "120px",
                        background:
                          "linear-gradient(45deg, rgba(213, 179, 99, 0.15), rgba(244, 213, 116, 0.08))",
                        borderRadius: "50%",
                        zIndex: 0,
                      }}
                    />

                    <div style={{ position: "relative", zIndex: 1 }}>
                      <motion.div
                        style={{
                          position: "relative",
                          display: "inline-block",
                          marginBottom: "15px",
                        }}
                        whileHover={{ scale: 1.08 }}
                        transition={{ type: "spring", stiffness: 400 }}
                      >
                        <img
                          src="/assets/img/home-1/hero/fd1.JPG"
                          alt="Director"
                          style={{
                            width: "clamp(150px, 25vw, 220px)",
                            height: "clamp(150px, 25vw, 220px)",
                            objectFit: "contain",
                            borderRadius: "50%",
                            border:
                              "clamp(4px, 1vw, 8px) solid rgba(213, 179, 99, 0.8)",
                            boxShadow: "0 25px 60px rgba(0, 0, 0, 0.5)",
                          }}
                        />
                        <div
                          style={{
                            position: "absolute",
                            bottom: "8px",
                            right: "8px",
                            width: "45px",
                            height: "45px",
                            background:
                              "linear-gradient(45deg, #D5B363, #f4d574)",
                            borderRadius: "50%",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            border: "4px solid white",
                            fontSize: "18px",
                            color: "#140342",
                            fontWeight: "bold",
                          }}
                        >
                          ★
                        </div>
                      </motion.div>

                      <h4
                        className="text-[#140342] fw-700 mb-10"
                        style={{
                          fontSize: "clamp(20px, 4vw, 32px)",
                          fontFamily: "Poppins, sans-serif",
                          letterSpacing: "-0.5px",
                        }}
                      >
                        Arshdeep Kaur
                      </h4>

                      <div
                        style={{
                          background: "rgba(213, 179, 99, 0.2)",
                          backdropFilter: "blur(10px)",
                          border: "1px solid rgba(213, 179, 99, 0.3)",
                          borderRadius: "25px",
                          padding: "12px 25px",
                          display: "inline-block",
                          marginBottom: "15px",
                        }}
                      >
                        <p
                          className="mb-0"
                          style={{
                            fontSize: "16px",
                            fontWeight: "700",
                            textTransform: "uppercase",
                            letterSpacing: "1px",
                            color: "#D5B363",
                          }}
                        >
                          Director & Co-Founder
                        </p>
                      </div>

                      <p
                        className="text-[#140342]"
                        style={{
                          opacity: "0.95",
                          fontSize: "clamp(14px, 2vw, 16px)",
                          lineHeight: "1.6",
                          margin: 0,
                          fontWeight: "400",
                          maxWidth: "280px",
                          marginLeft: "auto",
                          marginRight: "auto",
                          color: "#140342",
                        }}
                      >
                        Expert instructor specializing in PTE, IELTS, and NAATI
                        preparation with proven success record and innovative
                        teaching methods.
                      </p>
                    </div>
                  </motion.div>
                </div>
              </div>

              {/* Mission Statement */}
              <motion.div
                className="text-center"
                style={{
                  background: "rgba(255, 255, 255, 0.9)",
                  border: "2px solid rgba(213, 179, 99, 0.3)",
                  borderRadius: "clamp(15px, 3vw, 25px)",
                  padding: "clamp(20px, 4vw, 40px) clamp(15px, 3vw, 30px)",
                  backdropFilter: "blur(10px)",
                  boxShadow: "0 15px 35px rgba(0, 0, 0, 0.1)",
                }}
                variants={itemVariants}
                whileHover={{
                  scale: 1.01,
                  boxShadow: "0 20px 45px rgba(0, 0, 0, 0.3)",
                }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div
                  style={{
                    width: "80px",
                    height: "5px",
                    background: "linear-gradient(90deg, #D5B363, #f4d574)",
                    borderRadius: "3px",
                    margin: "0 auto 20px",
                  }}
                  initial={{ width: 0 }}
                  whileInView={{ width: "80px" }}
                  transition={{ duration: 1.2, delay: 0.3 }}
                />

                <h3
                  className="text-[#140342] fw-700 mb-3"
                  style={{
                    fontSize: "clamp(20px, 4vw, 32px)",
                    fontFamily: "Poppins, sans-serif",
                    lineHeight: "1.3",
                    letterSpacing: "-0.5px",
                  }}
                >
                  Empowering{" "}
                  <span style={{ color: "#D5B363" }}>Global Journeys</span>
                </h3>
                <p
                  className="text-[#1c1c1c]"
                  style={{
                    opacity: "0.9",
                    fontSize: "clamp(14px, 2.5vw, 18px)",
                    lineHeight: "1.7",
                    margin: 0,
                    maxWidth: "800px",
                    marginLeft: "auto",
                    marginRight: "auto",
                    color: "#1c1c1c",
                  }}
                >
                  Together, we&apos;re committed to helping you achieve your
                  dreams of studying and living abroad through comprehensive
                  test preparation, personalized guidance, and unwavering
                  support throughout your journey.
                </p>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.section>
  );
}

export default Mission;
