import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import PropTypes from "prop-types";
import { server } from "@/api/services/server";
import { useAuth } from "../others/AuthContext";

export default function SidebarToggle({
  isVisible = false,
  activeCategory = null,
  selectedTab = "PTE Core",
  currentQuestionType = null,
  onQuestionSelect,
}) {
  const { user } = useAuth();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [questionTypeQuestions, setQuestionTypeQuestions] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [questionsPerPage] = useState(10); // Number of questions per page
  const [totalQuestions, setTotalQuestions] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTimeout, setSearchTimeout] = useState(null);

  // Categories data matching Menu.jsx
  const categories = [
    {
      name: "speaking",
      icon: "🎤",
      color: "#E8F5E8",
      borderColor: "#4CAF50",
      data: [
        {
          categoryId: "6787cc2b486e6c04269a34e1",
          name: "Read Aloud",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "67838fcf2e266d08ba713eed",
          name: "Repeat Sentence",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "678391852e266d08ba713eee",
          name: "Describe Image",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "67e5acdd73e32a15ba3d88c8",
          name: "Re-tell Lecture",
          isAiBased: true,
          activeInCore: false,
          activeInAcademic: true,
        },
        {
          categoryId: "67b34f839ab23dd6196d1a91",
          name: "Respond to a situation",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: false,
        },
        {
          categoryId: "6783923483cd4009d9cddafa",
          name: "Answer Short Question",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: true,
        },
      ],
    },
    {
      name: "writing",
      icon: "✍️",
      color: "#FFF3E0",
      borderColor: "#FF9800",
      data: [
        {
          categoryId: "6783926b83cd4009d9cddafb",
          name: "Summarize Written Text",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: false,
        },
        {
          categoryId: "67d893d673e32a15ba3d88c1",
          name: "Write Essay",
          isAiBased: true,
          activeInCore: false,
          activeInAcademic: true,
        },
        {
          categoryId: "67e5af2b73e32a15ba3d88c9",
          name: "Summarize Written Text (A)",
          isAiBased: true,
          activeInCore: false,
          activeInAcademic: true,
        },
        {
          categoryId: "678392a883cd4009d9cddafc",
          name: "Write Email",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: false,
        },
      ],
    },
    {
      name: "reading",
      icon: "📖",
      color: "#E3F2FD",
      borderColor: "#2196F3",
      data: [
        {
          categoryId: "67801456e0dfdc154eff11ba",
          name: "Reading & Writing: Fill in the blanks",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "6780113ae0dfdc154eff11b6",
          name: "Multiple Choice (Multiple)",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "678011ade0dfdc154eff11b8",
          name: "Re-order Paragraphs",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "67894ed2102a6d6548ceec90",
          name: "Reading: Fill in the Blanks",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "67800fc7d4e7d9147dd9525d",
          name: "Multiple Choice (Single)",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
      ],
    },
    {
      name: "listening",
      icon: "🎧",
      color: "#F3E5F5",
      borderColor: "#9C27B0",
      data: [
        {
          categoryId: "68556cb5d0001a608d091719",
          name: "Summarize Spoken Text",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "67801bda382bce18e30d11ca",
          name: "Multiple Choice (Multiple)",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "68556cb8d0001a608d09171b",
          name: "Fill in the Blanks",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "68556cb6d0001a608d09171a",
          name: "Highlight Correct Summary",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "67801ec5382bce18e30d11ce",
          name: "Multiple Choice (Single)",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "678022c1382bce18e30d11d0",
          name: "Select Missing Word",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "678023f5382bce18e30d11d2",
          name: "Highlight Incorrect Words",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "678024ac382bce18e30d11d3",
          name: "Write from Dictation",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
      ],
    },
  ];

  const getFilteredQuestions = (category) => {
    if (!category) return [];
    return category.data.filter((item) =>
      selectedTab === "PTE Core" ? item.activeInCore : item.activeInAcademic
    );
  };

  const getCurrentCategory = () => {
    if (!activeCategory) return null;
    return categories.find((cat) => cat.name === activeCategory);
  };

  // Get question type name from categoryId
  const getQuestionTypeName = (categoryId) => {
    for (const category of categories) {
      const question = category.data.find((q) => q.categoryId === categoryId);
      if (question) return question.name;
    }
    return "Unknown Question Type";
  };

  // Fetch questions with server-side pagination and client-side search
  const fetchQuestionTypeQuestions = async (
    categoryId,
    searchTerm = "",
    page = 1
  ) => {
    if (!categoryId) return;

    setIsLoading(true);

    try {
      // Get userId from AuthContext or localStorage
      const userId = user?.id || localStorage.getItem("isUserId");

      // Build filter query with user-specific questionmetas
      const baseFilter = {
        where: { categoryId: categoryId },
        order: "questionNumber ASC",
        include: userId
          ? [
              {
                relation: "questionmetas",
                scope: {
                  where: { userId: userId },
                },
              },
            ]
          : [],
      };

      // For search, we need to fetch more data to filter client-side
      // For no search, we can use server-side pagination
      let questionsUrl, countUrl;

      if (searchTerm && searchTerm.trim()) {
        // When searching, fetch more data (or all) to filter client-side
        const searchFilter = {
          ...baseFilter,
          limit: 1000,
        };
        questionsUrl = `${server.uri}questions?filter=${encodeURIComponent(
          JSON.stringify(searchFilter)
        )}`;
        countUrl = `${server.uri}questions/count?where[categoryId]=${categoryId}`;
      } else {
        // Normal pagination without search
        const paginatedFilter = {
          ...baseFilter,
          limit: questionsPerPage,
          skip: (page - 1) * questionsPerPage,
        };
        questionsUrl = `${server.uri}questions?filter=${encodeURIComponent(
          JSON.stringify(paginatedFilter)
        )}`;
        countUrl = `${server.uri}questions/count?where[categoryId]=${categoryId}`;
      }

      const [questionsResponse, countResponse] = await Promise.all([
        fetch(questionsUrl),
        fetch(countUrl),
      ]);

      if (questionsResponse.ok && countResponse.ok) {
        const questionsData = await questionsResponse.json();
        const countData = await countResponse.json();

        let filteredQuestions = questionsData || [];
        let totalCount = countData.count || 0;

        // Sort questions numerically by questionNumber
        filteredQuestions.sort((a, b) => {
          const numA = parseInt(a.questionNumber) || 0;
          const numB = parseInt(b.questionNumber) || 0;
          return numA - numB;
        });

        // Apply client-side search filter if search term exists
        if (searchTerm && searchTerm.trim()) {
          const searchLower = searchTerm.toLowerCase();
          filteredQuestions = filteredQuestions.filter(
            (q) =>
              (q.prompt && q.prompt.toLowerCase().includes(searchLower)) ||
              (q.questionName &&
                q.questionName.toLowerCase().includes(searchLower)) ||
              (q.questionNumber &&
                q.questionNumber.toString().toLowerCase().includes(searchLower))
          );

          // For search results, apply client-side pagination
          totalCount = filteredQuestions.length;
          const startIndex = (page - 1) * questionsPerPage;
          filteredQuestions = filteredQuestions.slice(
            startIndex,
            startIndex + questionsPerPage
          );
        }

        setQuestionTypeQuestions(filteredQuestions);
        setTotalQuestions(totalCount);
      } else {
        console.error(
          "API Error:",
          questionsResponse.status,
          countResponse.status
        );
        setQuestionTypeQuestions([]);
        setTotalQuestions(0);
      }
    } catch (error) {
      console.error("Error fetching questions:", error);
      setQuestionTypeQuestions([]);
      setTotalQuestions(0);
    } finally {
      setIsLoading(false);
    }
  };

  // Debounced search to avoid too many API calls
  const debouncedSearch = (categoryId, searchTerm, page) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      fetchQuestionTypeQuestions(categoryId, searchTerm, page);
    }, 300); // 300ms delay

    setSearchTimeout(timeout);
  };

  // Initial load when question type changes
  useEffect(() => {
    if (currentQuestionType) {
      setSearchTerm("");
      setCurrentPage(1);
      fetchQuestionTypeQuestions(currentQuestionType, "", 1);
    }
  }, [currentQuestionType]);

  // Search term changes
  useEffect(() => {
    if (currentQuestionType) {
      setCurrentPage(1);
      debouncedSearch(currentQuestionType, searchTerm, 1);
    }
  }, [searchTerm]);

  // Page changes
  useEffect(() => {
    if (currentQuestionType && currentPage > 1) {
      fetchQuestionTypeQuestions(currentQuestionType, searchTerm, currentPage);
    }
  }, [currentPage]);

  // Reset search and pagination when sidebar opens/closes
  useEffect(() => {
    if (!isSidebarOpen) {
      setSearchTerm("");
      setCurrentPage(1);
    }
  }, [isSidebarOpen]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);

  const currentCategory = getCurrentCategory();
  const allQuestions = currentQuestionType
    ? questionTypeQuestions
    : getFilteredQuestions(currentCategory);

  // For question types (not individual questions), still use client-side filtering
  const filteredQuestions = currentQuestionType
    ? questionTypeQuestions // Already filtered by API
    : allQuestions.filter((question) => {
        if (!searchTerm) return true;
        const searchLower = searchTerm.toLowerCase();
        return question.name.toLowerCase().includes(searchLower);
      });

  // Calculate pagination
  const totalPages = currentQuestionType
    ? Math.ceil(totalQuestions / questionsPerPage)
    : Math.ceil(filteredQuestions.length / questionsPerPage);

  const startIndex = currentQuestionType
    ? (currentPage - 1) * questionsPerPage
    : (currentPage - 1) * questionsPerPage;

  const endIndex = currentQuestionType
    ? Math.min(startIndex + questionsPerPage, totalQuestions)
    : startIndex + questionsPerPage;

  const paginatedQuestions = currentQuestionType
    ? questionTypeQuestions // Already paginated by API
    : filteredQuestions.slice(startIndex, endIndex);

  const displayTotalCount = currentQuestionType
    ? totalQuestions
    : filteredQuestions.length;

  // Handle page change
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  // Close sidebar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isSidebarOpen && !event.target.closest(".sidebar-toggle-container")) {
        setIsSidebarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [isSidebarOpen]);

  if (!isVisible || (!currentCategory && !currentQuestionType)) return null;

  return (
    <>
      {/* Floating Toggle Button */}
      <motion.div
        initial={{ x: -100, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        exit={{ x: -100, opacity: 0 }}
        transition={{ type: "spring", damping: 25, stiffness: 200 }}
        className="sidebar-toggle-container"
        style={{
          position: "fixed",
          top: "50%",
          left: 0,
          transform: "translateY(-50%)",
          zIndex: 999,
          display: "flex",
          alignItems: "center",
        }}
      >
        {/* Toggle Button */}
        <motion.button
          onClick={() => setIsSidebarOpen(!isSidebarOpen)}
          style={{
            background: "linear-gradient(135deg, #140342 0%, #1d0659 100%)",
            border: "none",
            color: "#fff",
            padding: "8px 6px",
            borderRadius: "0 8px 8px 0",
            cursor: "pointer",
            boxShadow: "2px 0 10px rgba(20, 3, 66, 0.3)",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: "4px",
            minWidth: "50px",
            transition: "all 0.3s ease",
          }}
          whileHover={{
            scale: 1.05,
            boxShadow: "3px 0 15px rgba(20, 3, 66, 0.4)",
          }}
          whileTap={{ scale: 0.95 }}
        >
          <span style={{ fontSize: "20px" }}>
            {currentCategory?.icon || (currentQuestionType ? "🗣️" : "❓")}
          </span>
        </motion.button>
      </motion.div>

      {/* Sidebar */}
      <AnimatePresence>
        {isSidebarOpen && (
          <>
            {/* Overlay */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              style={{
                position: "fixed",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: "rgba(0,0,0,0.5)",
                zIndex: 998,
                backdropFilter: "blur(2px)",
              }}
              onClick={() => setIsSidebarOpen(false)}
            />

            {/* Sidebar Panel */}
            <motion.div
              initial={{ x: -400 }}
              animate={{ x: 0 }}
              exit={{ x: -400 }}
              transition={{ type: "spring", damping: 25, stiffness: 200 }}
              className="sidebar-toggle-container"
              style={{
                position: "fixed",
                top: 0,
                left: 0,
                width: "min(90vw, 450px)",
                height: "100vh",
                background: "#fff",
                zIndex: 999,
                boxShadow: "4px 0 20px rgba(0,0,0,0.15)",
                display: "flex",
                flexDirection: "column",
                overflow: "hidden",
              }}
            >
              {/* Sidebar Header */}
              <div
                style={{
                  padding: "clamp(16px, 4vw, 24px)",
                  background:
                    "linear-gradient(135deg, #140342 0%, #1d0659 100%)",
                  color: "#fff",
                  borderBottom: "1px solid rgba(255,255,255,0.1)",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <div>
                    <h3
                      style={{
                        margin: 0,
                        fontSize: "clamp(16px, 3vw, 18px)",
                        fontWeight: "600",
                        fontFamily: "Poppins, sans-serif",
                        display: "flex",
                        alignItems: "center",
                        gap: "8px",
                        color: "#fff",
                      }}
                    >
                      {currentCategory?.icon}{" "}
                      {currentQuestionType
                        ? getQuestionTypeName(currentQuestionType)
                        : currentCategory?.name.toUpperCase()}
                    </h3>
                    <p
                      style={{
                        margin: "4px 0 0 0",
                        fontSize: "clamp(12px, 2.5vw, 14px)",
                        opacity: 0.8,
                        fontFamily: "Poppins, sans-serif",
                      }}
                    >
                      {selectedTab} • {displayTotalCount} Questions
                      {isLoading && " (Loading...)"}
                    </p>
                  </div>
                  <button
                    onClick={() => setIsSidebarOpen(false)}
                    style={{
                      background: "rgba(255,255,255,0.1)",
                      border: "none",
                      color: "#fff",
                      width: "32px",
                      height: "32px",
                      borderRadius: "50%",
                      cursor: "pointer",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      fontSize: "18px",
                      transition: "background 0.2s ease",
                    }}
                    onMouseOver={(e) =>
                      (e.target.style.background = "rgba(255,255,255,0.2)")
                    }
                    onMouseOut={(e) =>
                      (e.target.style.background = "rgba(255,255,255,0.1)")
                    }
                  >
                    ×
                  </button>
                </div>
              </div>

              {/* Search Bar */}
              <div
                style={{
                  padding: "16px 20px",
                  borderBottom: "1px solid #E5E7EB",
                  background: "#fff",
                }}
              >
                <div style={{ position: "relative" }}>
                  <input
                    type="text"
                    placeholder={
                      currentQuestionType
                        ? "Search questions..."
                        : "Search question types..."
                    }
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    style={{
                      width: "100%",
                      padding: "10px 40px 10px 12px",
                      border: "1px solid #D1D5DB",
                      borderRadius: "8px",
                      fontSize: "14px",
                      fontFamily: "Poppins, sans-serif",
                      outline: "none",
                      transition: "border-color 0.2s ease",
                      boxSizing: "border-box",
                    }}
                    onFocus={(e) => (e.target.style.borderColor = "#140342")}
                    onBlur={(e) => (e.target.style.borderColor = "#D1D5DB")}
                    disabled={isLoading}
                  />
                  <div
                    style={{
                      position: "absolute",
                      right: "12px",
                      top: "50%",
                      transform: "translateY(-50%)",
                      color: isLoading ? "#9CA3AF" : "#6B7280",
                      fontSize: "16px",
                      pointerEvents: "none",
                    }}
                  >
                    {isLoading ? "⏳" : "🔍"}
                  </div>
                  {searchTerm && !isLoading && (
                    <button
                      onClick={() => setSearchTerm("")}
                      style={{
                        position: "absolute",
                        right: "35px",
                        top: "50%",
                        transform: "translateY(-50%)",
                        background: "none",
                        border: "none",
                        color: "#6B7280",
                        cursor: "pointer",
                        fontSize: "18px",
                        padding: "0",
                        width: "20px",
                        height: "20px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      ×
                    </button>
                  )}
                </div>
              </div>

              {/* Questions List */}
              <div
                style={{
                  flex: 1,
                  padding: "clamp(12px, 3vw, 20px)",
                  overflowY: "auto",
                  background: "#f8f9fa",
                }}
              >
                <div style={{ marginBottom: "16px" }}>
                  <h4
                    style={{
                      margin: 0,
                      fontSize: "14px",
                      fontWeight: "600",
                      color: "#140342",
                      fontFamily: "Poppins, sans-serif",
                      textTransform: "uppercase",
                      letterSpacing: "0.5px",
                    }}
                  >
                    {searchTerm
                      ? `Search Results (${displayTotalCount})`
                      : "Available Questions"}
                  </h4>
                  {totalPages > 1 && (
                    <p
                      style={{
                        margin: "4px 0 0 0",
                        fontSize: "12px",
                        color: "#6B7280",
                        fontFamily: "Poppins, sans-serif",
                      }}
                    >
                      Page {currentPage} of {totalPages} • Showing{" "}
                      {startIndex + 1}-{endIndex} of {displayTotalCount}
                    </p>
                  )}
                </div>

                {isLoading ? (
                  <div
                    style={{
                      textAlign: "center",
                      padding: "40px 20px",
                      color: "#666",
                      fontSize: "14px",
                    }}
                  >
                    <div
                      style={{
                        display: "inline-block",
                        width: "20px",
                        height: "20px",
                        border: "2px solid #140342",
                        borderTop: "2px solid transparent",
                        borderRadius: "50%",
                        animation: "spin 1s linear infinite",
                        marginBottom: "10px",
                      }}
                    />
                    <div>Loading questions...</div>
                    <style>
                      {`
                        @keyframes spin {
                          0% { transform: rotate(0deg); }
                          100% { transform: rotate(360deg); }
                        }
                      `}
                    </style>
                  </div>
                ) : (
                  paginatedQuestions.map((question, index) => (
                    <motion.div
                      key={
                        currentQuestionType ? question.id : question.categoryId
                      }
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05, duration: 0.3 }}
                      style={{
                        background: "#fff",
                        border: "1px solid #E5E7EB",
                        borderRadius: "12px",
                        padding: "clamp(14px, 3vw, 18px)",
                        marginBottom: "clamp(10px, 2vw, 14px)",
                        cursor: "pointer",
                        transition: "all 0.2s ease",
                        position: "relative",
                        overflow: "hidden",
                      }}
                      whileHover={{
                        scale: 1.02,
                        boxShadow: "0 8px 25px rgba(20, 3, 66, 0.1)",
                        borderColor: currentCategory?.borderColor || "#140342",
                      }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => {
                        if (currentQuestionType) {
                          // Navigate to specific question - use questionId if available, otherwise id
                          const questionId = question.questionId || question.id;
                          onQuestionSelect(questionId, currentQuestionType);
                        } else {
                          // Navigate to question type
                          onQuestionSelect(question.categoryId);
                        }
                        setIsSidebarOpen(false);
                      }}
                    >
                      {/* Background accent */}
                      <div
                        style={{
                          position: "absolute",
                          top: 0,
                          right: 0,
                          width: "60px",
                          height: "60px",
                          background: currentCategory?.color || "#140342",
                          borderRadius: "50%",
                          transform: "translate(20px, -20px)",
                          opacity: 0.3,
                        }}
                      />

                      <div style={{ position: "relative", zIndex: 2 }}>
                        <div
                          style={{
                            display: "flex",
                            flexDirection: "column",
                            gap: "8px",
                          }}
                        >
                          <div
                            style={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "flex-start",
                              gap: "8px",
                            }}
                          >
                            <h5
                              style={{
                                margin: 0,
                                fontSize: "clamp(14px, 2.5vw, 16px)",
                                fontWeight: "600",
                                color: "#140342",
                                fontFamily: "Poppins, sans-serif",
                                lineHeight: "1.3",
                                flex: 1,
                              }}
                            >
                              {currentQuestionType
                                ? `Question ${
                                    question.questionNumber ||
                                    startIndex + index + 1
                                  }`
                                : question.name}
                            </h5>
                            <div
                              style={{
                                display: "flex",
                                gap: "4px",
                                flexShrink: 0,
                                flexWrap: "wrap",
                              }}
                            >
                              {!currentQuestionType && question.isAiBased && (
                                <span
                                  style={{
                                    background: "rgba(20, 3, 66, 0.1)",
                                    color: "#140342",
                                    padding: "4px 8px",
                                    borderRadius: "6px",
                                    fontSize: "10px",
                                    fontWeight: "600",
                                  }}
                                >
                                  AI Score
                                </span>
                              )}
                              {currentQuestionType && question.difficulty && (
                                <span
                                  style={{
                                    background:
                                      question.difficulty === "easy"
                                        ? "rgba(34, 197, 94, 0.1)"
                                        : question.difficulty === "medium"
                                        ? "rgba(251, 146, 60, 0.1)"
                                        : question.difficulty === "hard"
                                        ? "rgba(239, 68, 68, 0.1)"
                                        : "rgba(107, 114, 128, 0.1)",
                                    color:
                                      question.difficulty === "easy"
                                        ? "#16a34a"
                                        : question.difficulty === "medium"
                                        ? "#ea580c"
                                        : question.difficulty === "hard"
                                        ? "#dc2626"
                                        : "#374151",
                                    padding: "4px 8px",
                                    borderRadius: "6px",
                                    fontSize: "10px",
                                    fontWeight: "600",
                                    textTransform: "capitalize",
                                  }}
                                >
                                  {question.difficulty}
                                </span>
                              )}
                            </div>
                          </div>

                          {/* Practice Status and Count */}
                          {currentQuestionType && (
                            <div
                              style={{
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                                gap: "8px",
                                paddingTop: "4px",
                              }}
                            >
                              <div
                                style={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: "8px",
                                }}
                              >
                                {(() => {
                                  // Get user-specific practice data from questionmetas
                                  const userMeta =
                                    question.questionmetas &&
                                    question.questionmetas.length > 0
                                      ? question.questionmetas[0]
                                      : null;
                                  const practiceStatus =
                                    userMeta?.practiceStatus || "Undone";
                                  const practiceCount = parseInt(
                                    userMeta?.practiceCount || 0
                                  );

                                  return (
                                    <>
                                      <span
                                        style={{
                                          background:
                                            practiceStatus === "Done"
                                              ? "rgba(34, 197, 94, 0.1)"
                                              : practiceStatus === "InProgress"
                                              ? "rgba(251, 146, 60, 0.1)"
                                              : "rgba(107, 114, 128, 0.1)",
                                          color:
                                            practiceStatus === "Done"
                                              ? "#16a34a"
                                              : practiceStatus === "InProgress"
                                              ? "#ea580c"
                                              : "#6b7280",
                                          padding: "3px 8px",
                                          borderRadius: "4px",
                                          fontSize: "9px",
                                          fontWeight: "600",
                                          textTransform: "capitalize",
                                        }}
                                      >
                                        {practiceStatus === "Undone"
                                          ? "Not Started"
                                          : practiceStatus === "InProgress"
                                          ? "In Progress"
                                          : practiceStatus}
                                      </span>
                                      <span
                                        style={{
                                          fontSize: "9px",
                                          color: "#6b7280",
                                          fontWeight: "500",
                                          display: "flex",
                                          alignItems: "center",
                                          gap: "2px",
                                        }}
                                      >
                                        🔄 {practiceCount} attempt
                                        {practiceCount !== 1 ? "s" : ""}
                                      </span>
                                    </>
                                  );
                                })()}
                              </div>

                              {/* Bookmark Status */}
                              {(() => {
                                // Get user-specific bookmark data from questionmetas
                                const userMeta =
                                  question.questionmetas &&
                                  question.questionmetas.length > 0
                                    ? question.questionmetas[0]
                                    : null;
                                const markLabel = userMeta?.markLabel || "";

                                // Get bookmark option details
                                const getBookmarkOption = (label) => {
                                  const bookmarkOptions = [
                                    {
                                      label: "Review",
                                      icon: "🔄",
                                      color: "#FFA500",
                                    },
                                    {
                                      label: "Important",
                                      icon: "⭐",
                                      color: "#FF0000",
                                    },
                                    {
                                      label: "Challenging",
                                      icon: "🔥",
                                      color: "#800080",
                                    },
                                    {
                                      label: "Favorite",
                                      icon: "❤️",
                                      color: "#008000",
                                    },
                                    {
                                      label: "Later",
                                      icon: "⏰",
                                      color: "#607D8B",
                                    },
                                  ];
                                  return (
                                    bookmarkOptions.find(
                                      (option) => option.label === label
                                    ) || null
                                  );
                                };

                                const bookmarkOption =
                                  getBookmarkOption(markLabel);

                                return bookmarkOption ? (
                                  <div
                                    style={{
                                      display: "flex",
                                      alignItems: "center",
                                      gap: "4px",
                                      marginTop: "4px",
                                    }}
                                  >
                                    <span
                                      style={{
                                        background: `${bookmarkOption.color}20`,
                                        color: bookmarkOption.color,
                                        padding: "2px 6px",
                                        borderRadius: "4px",
                                        fontSize: "8px",
                                        fontWeight: "600",
                                        display: "flex",
                                        alignItems: "center",
                                        gap: "2px",
                                      }}
                                    >
                                      <span style={{ fontSize: "8px" }}>
                                        {bookmarkOption.icon}
                                      </span>
                                      {bookmarkOption.label}
                                    </span>
                                  </div>
                                ) : null;
                              })()}
                            </div>
                          )}

                          {currentQuestionType && question.prompt && (
                            <p
                              style={{
                                margin: 0,
                                fontSize: "clamp(11px, 2vw, 13px)",
                                color: "#666",
                                lineHeight: "1.4",
                                display: "-webkit-box",
                                WebkitLineClamp: 3,
                                WebkitBoxOrient: "vertical",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                              }}
                            >
                              {question.prompt.length > 120
                                ? `${question.prompt.substring(0, 120)}...`
                                : question.prompt}
                            </p>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))
                )}

                {!isLoading && paginatedQuestions.length === 0 && (
                  <div
                    style={{
                      textAlign: "center",
                      padding: "40px 20px",
                      color: "#666",
                      fontSize: "14px",
                    }}
                  >
                    {searchTerm
                      ? `No questions found for "${searchTerm}"`
                      : `No questions available for ${selectedTab}`}
                  </div>
                )}
              </div>

              {/* Pagination */}
              {totalPages > 1 && !isLoading && (
                <div
                  style={{
                    padding: "16px 20px",
                    borderTop: "1px solid #E5E7EB",
                    background: "#fff",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    gap: "8px",
                  }}
                >
                  <button
                    onClick={() =>
                      handlePageChange(Math.max(1, currentPage - 1))
                    }
                    disabled={currentPage === 1}
                    style={{
                      background: currentPage === 1 ? "#F3F4F6" : "#140342",
                      color: currentPage === 1 ? "#9CA3AF" : "#fff",
                      border: "none",
                      padding: "8px 12px",
                      borderRadius: "6px",
                      cursor: currentPage === 1 ? "default" : "pointer",
                      fontSize: "12px",
                      fontWeight: "500",
                      fontFamily: "Poppins, sans-serif",
                      transition: "all 0.2s ease",
                    }}
                  >
                    ← Prev
                  </button>

                  <div
                    style={{
                      display: "flex",
                      gap: "4px",
                      alignItems: "center",
                    }}
                  >
                    {Array.from({ length: totalPages }, (_, i) => i + 1)
                      .filter(
                        (page) =>
                          page === 1 ||
                          page === totalPages ||
                          Math.abs(page - currentPage) <= 1
                      )
                      .map((page, index, array) => (
                        <div key={page} style={{ display: "flex", gap: "4px" }}>
                          {index > 0 && array[index - 1] !== page - 1 && (
                            <span
                              style={{
                                color: "#9CA3AF",
                                fontSize: "12px",
                                padding: "8px 4px",
                              }}
                            >
                              ...
                            </span>
                          )}
                          <button
                            onClick={() => handlePageChange(page)}
                            style={{
                              background:
                                currentPage === page
                                  ? "#140342"
                                  : "transparent",
                              color: currentPage === page ? "#fff" : "#6B7280",
                              border:
                                currentPage === page
                                  ? "none"
                                  : "1px solid #E5E7EB",
                              padding: "8px 10px",
                              borderRadius: "6px",
                              cursor: "pointer",
                              fontSize: "12px",
                              fontWeight: "500",
                              fontFamily: "Poppins, sans-serif",
                              minWidth: "32px",
                              transition: "all 0.2s ease",
                            }}
                            onMouseOver={(e) => {
                              if (currentPage !== page) {
                                e.target.style.background = "#F3F4F6";
                              }
                            }}
                            onMouseOut={(e) => {
                              if (currentPage !== page) {
                                e.target.style.background = "transparent";
                              }
                            }}
                          >
                            {page}
                          </button>
                        </div>
                      ))}
                  </div>

                  <button
                    onClick={() =>
                      handlePageChange(Math.min(totalPages, currentPage + 1))
                    }
                    disabled={currentPage === totalPages}
                    style={{
                      background:
                        currentPage === totalPages ? "#F3F4F6" : "#140342",
                      color: currentPage === totalPages ? "#9CA3AF" : "#fff",
                      border: "none",
                      padding: "8px 12px",
                      borderRadius: "6px",
                      cursor:
                        currentPage === totalPages ? "default" : "pointer",
                      fontSize: "12px",
                      fontWeight: "500",
                      fontFamily: "Poppins, sans-serif",
                      transition: "all 0.2s ease",
                    }}
                  >
                    Next →
                  </button>
                </div>
              )}

              {/* Footer */}
              <div
                style={{
                  padding: "clamp(12px, 3vw, 16px) clamp(16px, 4vw, 20px)",
                  borderTop: "1px solid #E5E7EB",
                  background: "#fff",
                }}
              >
                <div
                  style={{
                    fontSize: "clamp(11px, 2vw, 12px)",
                    color: "#666",
                    textAlign: "center",
                    fontFamily: "Poppins, sans-serif",
                  }}
                >
                  Quick access to{" "}
                  {currentQuestionType
                    ? getQuestionTypeName(currentQuestionType)
                    : currentCategory?.name || "practice"}{" "}
                  questions
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
}

SidebarToggle.propTypes = {
  isVisible: PropTypes.bool,
  activeCategory: PropTypes.string,
  selectedTab: PropTypes.string,
  currentQuestionType: PropTypes.string,
  onQuestionSelect: PropTypes.func.isRequired,
};
