import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Navigation, Pagination, Autoplay } from "swiper";
import { Swiper, SwiperSlide } from "swiper/react";
// import 'swiper/swiper.min.css';
import { testimonials } from "../../data/tesimonials";

export default function TestimonialsOne() {
  const [showSlider, setShowSlider] = useState(false);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.2,
        staggerChildren: 0.15,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const imageVariants = {
    hidden: { scale: 0.8, opacity: 0, y: 20 },
    visible: {
      scale: 1,
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  // PTE Exam YouTube video ID
  const pteVideoId = "ioQKgdkSTtM"; // PTE Academic preparation guide video

  useEffect(() => {
    setShowSlider(true);
  }, []);

  return (
    <>
      {/* Wall of Fame Section */}
      <motion.section
        className="layout-pt-lg layout-pb-lg"
        style={{
          background: "linear-gradient(135deg, #140342 0%, #1e0a5a 100%)",
          position: "relative",
          overflow: "hidden",
        }}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={containerVariants}
      >
        {/* Background Pattern */}
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D5B363' fill-opacity='0.04'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            opacity: 0.1,
          }}
        />
        <div className="container" style={{ position: "relative", zIndex: 2 }}>
          <motion.div
            className="row justify-center text-center mb-60"
            variants={itemVariants}
          >
            <div className="col-lg-8">
              <h2
                className="text-white fw-600 mb-20"
                style={{
                  fontSize: "42px",
                  fontFamily: "Poppins, sans-serif",
                  lineHeight: "1.2",
                }}
              >
                Wall of <span style={{ color: "#D5B363" }}>Fame</span>
              </h2>
              <p
                className="text-white"
                style={{
                  fontSize: "18px",
                  opacity: "0.9",
                  maxWidth: "600px",
                  margin: "0 auto",
                }}
              >
                Unlocking Borders, Fulfilling Dreams: Our Visa Success Stories
              </p>
            </div>
          </motion.div>

          {/* PTE Exam Video Display */}
          <motion.div
            className="row justify-center"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <motion.div
              className="col-lg-8 col-md-10"
              variants={imageVariants}
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 200 }}
            >
              <div
                style={{
                  position: "relative",
                  width: "100%",
                  height: "0",
                  paddingBottom: "56.25%", // 16:9 aspect ratio
                  borderRadius: "20px",
                  overflow: "hidden",
                  boxShadow: "0 20px 40px rgba(0,0,0,0.3)",
                  background: "rgba(255, 255, 255, 0.1)",
                  backdropFilter: "blur(10px)",
                  border: "2px solid rgba(213, 179, 99, 0.3)",
                }}
              >
                <iframe
                  src={`https://www.youtube.com/embed/${pteVideoId}?rel=0&modestbranding=1&showinfo=0`}
                  title="PTE Exam Guide"
                  style={{
                    position: "absolute",
                    top: "0",
                    left: "0",
                    width: "100%",
                    height: "100%",
                    border: "none",
                    borderRadius: "20px",
                  }}
                  allowFullScreen
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                />
              </div>

              {/* Video Info Card */}
              <motion.div
                className="text-center mt-30"
                variants={itemVariants}
                style={{
                  background: "rgba(255, 255, 255, 0.95)",
                  backdropFilter: "blur(15px)",
                  borderRadius: "15px",
                  padding: "20px",
                  border: "1px solid rgba(213, 179, 99, 0.3)",
                  boxShadow: "0 10px 30px rgba(0, 0, 0, 0.1)",
                }}
              >
                <h4
                  style={{
                    color: "#140342",
                    fontSize: "20px",
                    fontWeight: "600",
                    marginBottom: "8px",
                    fontFamily: "Poppins, sans-serif",
                  }}
                >
                  Master <span style={{ color: "#D5B363" }}>PTE Academic</span>
                </h4>
                <p
                  style={{
                    color: "#666",
                    fontSize: "14px",
                    margin: "0",
                    lineHeight: "1.5",
                  }}
                >
                  Complete guide to achieving your target PTE score
                </p>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </motion.section>

      {/* Voices of Trust Section */}
      <motion.section
        className="layout-pt-lg layout-pb-lg"
        style={{
          background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
          position: "relative",
          overflow: "hidden",
        }}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={containerVariants}
      >
        {/* Subtle background pattern */}
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23D5B363' fill-opacity='0.02'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E")`,
            opacity: 0.5,
          }}
        />
        <div className="">
          <motion.div
            className="row justify-center text-center mb-60"
            variants={itemVariants}
          >
            <div className="col-lg-8">
              <h2
                className="fw-700 mb-20"
                style={{
                  fontSize: "42px",
                  fontFamily: "Poppins, sans-serif",
                  lineHeight: "1.2",
                  color: "#140342",
                  fontWeight: "700",
                }}
              >
                Voices of <span style={{ color: "#D5B363" }}>Trust</span>
              </h2>
              <p
                style={{
                  fontSize: "16px",
                  color: "#333333",
                  maxWidth: "1000px",
                  margin: "0 auto",
                }}
              >
                Discover what our clients are saying about us through their
                valuable feedback and reviews. We prioritize transparency and
                excellence in our services, and our clients opinions help us
                continuously refine and improve our offerings.
              </p>
            </div>
          </motion.div>

          <motion.div className="js-section-slider" variants={itemVariants}>
            {showSlider && (
              <Swiper
                className="overflow-visible"
                modules={[Navigation, Pagination, Autoplay]}
                autoplay={{
                  delay: 4000,
                  disableOnInteraction: false,
                }}
                loop={true}
                spaceBetween={30}
                slidesPerView={1}
                centeredSlides={true}
              >
                {testimonials.map((elm, i) => (
                  <SwiperSlide key={i}>
                    <div
                      style={{
                        backgroundImage:
                          "url('/assets/img/home-1/hero/v-1.png')",
                        backgroundSize: "contain",
                        backgroundPosition: "center",
                        backgroundRepeat: "no-repeat",
                        borderRadius: "20px",
                        color: "white",
                        textAlign: "center",
                        maxWidth: "800px",
                        margin: "0 auto",
                        position: "relative",
                        minHeight: "200px",
                        marginBottom: "20px",
                      }}
                    >
                      {/* Gold accent */}
                      <div
                        style={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          height: "4px",
                          background:
                            "linear-gradient(90deg, #D5B363 0%, #f4d574 100%)",
                          borderRadius: "25px 25px 0 0",
                        }}
                      />
                      {/* Testimonial Text - Absolutely centered */}
                      <p
                        style={{
                          fontSize: "16px",
                          lineHeight: "1.6",
                          fontStyle: "italic",
                          position: "absolute",
                          top: "50%",
                          left: "50%",
                          transform: "translate(-50%, -50%)",
                          width: "80%",
                          margin: 0,
                          padding: "10px",
                        }}
                        title={elm.description} // Shows full text on hover
                      >
                        {elm.description.length > 250
                          ? elm.description.substring(0, 250) + "..."
                          : elm.description}
                      </p>

                      {/* Avatar at bottom border - 50% above, 50% below */}
                      <div
                        style={{
                          position: "absolute",
                          bottom: "-10px", // Half of avatar height (60px / 2)
                          left: "50%",
                          transform: "translateX(-50%)",
                          textAlign: "center",
                        }}
                      >
                        <img
                          src={elm.imageSrc}
                          alt={elm.name}
                          style={{
                            width: "60px",
                            height: "60px",
                            borderRadius: "20%",
                            objectFit: "cover",
                            border: "3px solid #D5B363",
                            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
                          }}
                        />
                      </div>
                    </div>

                    {/* User Info below avatar */}
                    <div style={{ textAlign: "center", marginTop: "0px" }}>
                      <h5
                        style={{
                          margin: 0,
                          fontSize: "18px",
                          fontWeight: "600",
                          marginBottom: "5px",
                          color: "#1C1C1C",
                        }}
                      >
                        {elm.name}
                      </h5>
                      <p style={{ margin: 0, fontSize: "14px", color: "#666" }}>
                        {elm.position}
                      </p>
                    </div>
                  </SwiperSlide>
                ))}
              </Swiper>
            )}
          </motion.div>

          {/* Counter Section */}
          {/* <div className="row y-gap-30 counter__row pt-80">
            {counters.map((elm, i) => (
              <div
                key={i}
                className="col-lg-3 col-sm-6"
                data-aos="fade-left"
                data-aos-duration={(i + 1) * 350}
              >
                <div className="counter -type-1 text-center">
                  <div
                    className="counter__number"
                    style={{
                      color: "#3B1E6B",
                      fontSize: "48px",
                      fontWeight: "700",
                    }}
                  >
                    {elm.number}
                  </div>
                  <div
                    className="counter__title"
                    style={{ color: "#666", fontSize: "16px" }}
                  >
                    {elm.title}
                  </div>
                </div>
              </div>
            ))}
          </div> */}
        </div>
      </motion.section>
    </>
  );
}
