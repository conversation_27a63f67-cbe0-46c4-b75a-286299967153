import React, { useState, useEffect } from "react";

import { contactData } from "@/data/contactLinks";

import MapComponent from "./Map";

export default function ContactOne() {
  const handleSubmit = (e) => {
    e.preventDefault();
  };

  return (
    <>
      <section className="">
        <MapComponent />
      </section>
      <section className="layout-pt-md layout-pb-lg">
        <div className="container">
          <div className="row y-gap-50 justify-between">
            <div className="col-lg-4">
              <h3 className="text-24 fw-500">Ask Us Anything</h3>
              <p className="mt-25">
                Feel free to contact us if you need some help, consultation or
                you have some other questions.
              </p>

              <div className="y-gap-30 pt-60 lg:pt-40">
                {contactData.map((elm, i) => (
                  <div key={i} className="d-flex items-center">
                    <div className="d-flex justify-center items-center size-60 rounded-full bg-light-7">
                      <img src={elm.icon} alt="icon" />
                    </div>
                    <div className="ml-20">
                      {elm.address
                        ? `${elm.address
                          .split(" ")
                          .slice(0, 4)
                          .join(" ")} \n ${elm.address
                            .split(" ")
                            .slice(4, -1)
                            .join(" ")}`
                        : elm.email || elm.phoneNumber}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="col-lg-7">
              <h3 className="text-24 fw-500">Don’t be Stranger</h3>
              <p className="mt-25">
                NeqFeel free to contact us if you need some help, consultation or
                <br />you have some other questions.
              </p>

              <form
                className="contact-form row y-gap-30 pt-60 lg:pt-40"
                onSubmit={handleSubmit}
              >
                <div className="col-md-6">
                  <label className="text-16 lh-1 fw-500 text-dark-1 mb-10">
                    Name
                  </label>
                  <input
                    required
                    type="text"
                    name="title"
                    placeholder="Name..."
                  />
                </div>
                <div className="col-md-6">
                  <label className="text-16 lh-1 fw-500 text-dark-1 mb-10">
                    Email Address
                  </label>
                  <input
                    required
                    type="text"
                    name="title"
                    placeholder="Email..."
                  />
                </div>
                <div className="col-md-6">
                  <label className="text-16 lh-1 fw-500 text-dark-1 mb-10">
                    Phone Number
                  </label>
                  <div className="d-flex">
                    
                    <select
                      name="countryCode"
                      required
                      className="form-control"
                      style={{ width: "40%", marginRight: "5px",padding:0 }}
                    >
                      <option value="+1">+1 (USA)</option>
                      <option value="+91">+91 (India)</option>
                      <option value="+44">+44 (UK)</option>
                      <option value="+61">+61 (Australia)</option>
                      <option value="+81">+81 (Japan)</option>
                     
                    </select>

                    
                    <input
                    required
                    type="text"
                    name="phoneNumber"
                    placeholder="Phone Number..."
                  />
                  </div>
                  </div>
                  <div className="col-12">
                    <label className="text-16 lh-1 fw-500 text-dark-1 mb-10">
                      Message...
                    </label>
                    <textarea
                      required
                      name="comment"
                      placeholder="Message"
                      rows="8"
                    ></textarea>
                  </div>
                  <div className="col-12">
                    <button
                      type="submit"
                      name="submit"
                      id="submit"
                      className="button -md -purple-1 text-white"
                    >
                      Send Message
                    </button>
                  </div>
              </form>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
