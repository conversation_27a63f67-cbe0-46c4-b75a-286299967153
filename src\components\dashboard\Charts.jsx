import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ResponsiveContaine<PERSON>,
} from "recharts";

const data = [
  { name: "<PERSON>", value: 148 },
  { name: "<PERSON>", value: 100 },
  { name: "<PERSON>", value: 205 },
  { name: "April", value: 110 },
  { name: "May", value: 165 },
  { name: "<PERSON>", value: 145 },
  { name: "July", value: 180 },
  { name: "<PERSON>gus<PERSON>", value: 156 },
  { name: "Sept", value: 148 },
  { name: "Oct", value: 220 },
  { name: "Now", value: 180 },
  { name: "Dec", value: 245 },
];

const Charts = () => {
  const chart = (interval) => (
    <ResponsiveContainer height={250} width="100%">
      <LineChart data={data}>
        <CartesianGrid strokeDasharray="" />
        <XAxis tick={{ fontSize: 12 }} dataKey="name" interval={interval} />
        <YAxis
          tick={{ fontSize: 12 }}
          domain={[0, 300]}
          tickCount={7}
          interval={interval}
        />
        <Tooltip />
        <Line
          type="monotone"
          dataKey="value"
          strokeWidth={2}
          stroke="#336CFB"
          fill="#336CFB"
          activeDot={{ r: 8 }}
        />
        {/* <Line type="monotone" dataKey="uv" stroke="#82ca9d" /> */}
      </LineChart>
    </ResponsiveContainer>
  );

  return (
    <>
      {chart("preserveEnd")}
      {/* {chart('preserveStart')}
      {chart('preserveStartEnd')}
      {chart('equidistantPreserveStart')}
      {chart(1)} */}
    </>
  );
};

export default Charts;
