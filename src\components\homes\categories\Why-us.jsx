import { motion } from "framer-motion";

const CategoriesHomeOne = () => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const cardVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  // PTE Framework data
  const pteFramework = [
    { id: 1, title: "SPEAKING", image: "/assets/img/home-1/hero/w-1.png" },
    { id: 2, title: "WRITING", image: "/assets/img/home-1/hero/w-2.png" },
    { id: 3, title: "READING", image: "/assets/img/home-1/hero/w-3.png" },
    { id: 4, title: "LISTENING", image: "/assets/img/home-1/hero/w-4.png" },
  ];

  // Why we are best data
  const whyBestData = [
    {
      id: 1,
      icon: "/assets/img/home-1/hero/w-5.png",
      title: "Digital Platform",
      description:
        "We provide seamless mock tests for reading, listening, speaking and writing on our digital platform.",
    },
    {
      id: 2,
      icon: "/assets/img/home-1/hero/w-6.png",
      title: "Admission Guidance",
      description:
        "Our elite team of academic experts simplifies the study abroad journey by handling the heavy lifting for you.",
    },
    {
      id: 3,
      icon: "/assets/img/home-1/hero/w-7.png",
      title: "Finding you the best fit!",
      description:
        "We meticulously analyze your profile and preferences to arrive precise alignment with the optimal program maximising your graduation prospects.",
    },
    {
      id: 4,
      icon: "/assets/img/home-1/hero/w-8.png",
      title: "Scholarship Guidance",
      description:
        "Recognizing the importance of financial support offered our studnets dream of studying abroad.",
    },
    {
      id: 5,
      icon: "/assets/img/home-1/hero/w-9.png",
      title: "24X7 Online Support",
      description:
        "Our experts are directly reachable help the students through.",
    },
    {
      id: 6,
      icon: "/assets/img/home-1/hero/w-10.png",
      title: "100% Success Rate",
      description: "Students of our respected success with the right.",
    },
  ];

  return (
    <>
      {/* PTE Framework Section */}
      <motion.section
        className="layout-pt-lg layout-pb-lg"
        style={{
          background: "linear-gradient(135deg, #140342 0%, #1e0a5a 100%)",
          position: "relative",
          padding: "clamp(40px, 8vw, 100px) clamp(15px, 5vw, 60px)",
          overflow: "hidden",
        }}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={containerVariants}
      >
        {/* Background Pattern */}
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D5B363' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            opacity: 0.1,
          }}
        />
        <div
          className="container"
          style={{
            position: "relative",
            zIndex: 2,
            padding: "0 clamp(15px, 3vw, 30px)",
          }}
        >
          <motion.div
            className="row justify-center text-center"
            variants={itemVariants}
            style={{ marginBottom: "clamp(30px, 6vw, 60px)" }}
          >
            <div className="col-auto">
              <h2
                className="text-white fw-600"
                style={{
                  fontSize: "clamp(20px, 4vw, 32px)",
                  fontFamily: "Poppins, sans-serif",
                  letterSpacing: "clamp(0.5px, 0.1vw, 1px)",
                  lineHeight: "1.3",
                  textAlign: "center",
                  maxWidth: "100%",
                  wordBreak: "break-word",
                }}
              >
                PTE ACADEMIC: UNVEILING ITS{" "}
                <span style={{ color: "#D5B363" }}>STRUCTURAL FRAMEWORK</span>
              </h2>
            </div>
          </motion.div>

          <motion.div className="row justify-center" variants={itemVariants}>
            <div className="col-12">
              <div
                className="d-flex justify-center align-items-center flex-wrap"
                style={{
                  gap: "clamp(10px, 2vw, 20px)",
                  padding: "0 clamp(10px, 2vw, 20px)",
                }}
              >
                {pteFramework.map((item, index) => (
                  <motion.div
                    key={item.id}
                    className="d-flex align-items-center flex-wrap justify-center"
                    variants={cardVariants}
                    whileHover={{ y: -10, scale: 1.05 }}
                    transition={{ type: "spring", stiffness: 300 }}
                    style={{
                      margin: "clamp(5px, 1vw, 10px)",
                    }}
                  >
                    {/* Card */}
                    <div
                      className="position-relative"
                      style={{
                        width: "clamp(120px, 20vw, 180px)",
                        height: "clamp(80px, 12vw, 120px)",
                        borderRadius: "16px",
                        overflow: "hidden",
                        flexShrink: 0,
                        background: "rgba(255, 255, 255, 0.1)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(213, 179, 99, 0.3)",
                        boxShadow: "0 8px 32px rgba(0, 0, 0, 0.3)",
                      }}
                    >
                      <img
                        src={item.image}
                        alt={item.title}
                        style={{
                          width: "100%",
                          height: "100%",
                          objectFit: "cover",
                        }}
                      />
                      {/* Gold overlay */}
                      <div
                        style={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          background:
                            "linear-gradient(45deg, rgba(213, 179, 99, 0.1) 0%, rgba(244, 213, 116, 0.1) 100%)",
                          pointerEvents: "none",
                        }}
                      />
                    </div>

                    {/* Vertical Divider - Hidden on mobile */}
                    {index < pteFramework.length - 1 && (
                      <motion.div
                        className="d-none d-md-block"
                        style={{
                          width: "2px",
                          height: "clamp(40px, 8vw, 60px)",
                          background:
                            "linear-gradient(180deg, #D5B363 0%, rgba(213, 179, 99, 0.3) 100%)",
                          margin: "0 clamp(10px, 2vw, 20px)",
                          flexShrink: 0,
                          borderRadius: "1px",
                        }}
                        initial={{ scaleY: 0 }}
                        whileInView={{ scaleY: 1 }}
                        transition={{ delay: 0.5 + index * 0.1, duration: 0.5 }}
                      ></motion.div>
                    )}
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </motion.section>

      {/* Why We Are Best Section */}
      <motion.section
        className="layout-pt-lg layout-pb-lg"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        variants={containerVariants}
        style={{
          padding: "clamp(40px, 8vw, 100px) clamp(15px, 3vw, 30px)",
        }}
      >
        <div className="container">
          <motion.div
            className="row justify-center text-center"
            variants={itemVariants}
            style={{ marginBottom: "clamp(30px, 6vw, 60px)" }}
          >
            <div className="col-lg-8 col-md-10 col-12">
              <div className="sectionTitle">
                <h2
                  className="sectionTitle__title"
                  style={{
                    fontSize: "clamp(28px, 6vw, 52px)",
                    fontFamily: "Poppins, sans-serif",
                    color: "#140342",
                    marginBottom: "clamp(15px, 3vw, 20px)",
                    lineHeight: "1.2",
                    textAlign: "center",
                  }}
                >
                  Why we are{" "}
                  <span style={{ color: "#D5B363", fontWeight: "600" }}>
                    best from others?
                  </span>
                </h2>
                <p
                  className="sectionTitle__text"
                  style={{
                    fontSize: "clamp(14px, 2vw, 16px)",
                    color: "#333333",
                    lineHeight: "1.6",
                    maxWidth: "100%",
                    margin: "0 auto",
                    padding: "0 clamp(10px, 2vw, 20px)",
                    textAlign: "center",
                  }}
                >
                  Our academy offers comprehensive training and guidance led by
                  seasoned professionals, ensuring that every consultant is
                  equipped with the knowledge and skills needed to excel in the
                  field. It provides tailored programs designed to meet your
                  needs. We provide the top-rated PTE training to the candidates
                  to move their career journey smooth.
                </p>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="row justify-center"
            style={{
              padding: "0 clamp(10px, 2vw, 20px)",
            }}
            variants={itemVariants}
          >
            <div className="col-12">
              <motion.div
                className="row y-gap-30 justify-center"
                variants={containerVariants}
                style={{
                  gap: "clamp(20px, 3vw, 30px) 0",
                }}
              >
                {whyBestData.map((item, index) => (
                  <motion.div
                    key={item.id}
                    className="col-lg-4 col-md-6 col-sm-8 col-10"
                    variants={cardVariants}
                    whileHover={{
                      y: -10,
                      scale: 1.03,
                      boxShadow: "0 20px 40px rgba(0,0,0,0.15)",
                    }}
                    transition={{ type: "spring", stiffness: 300 }}
                    style={{
                      padding: "0 clamp(10px, 1.5vw, 15px)",
                      marginBottom: "clamp(20px, 3vw, 30px)",
                    }}
                  >
                    <div
                      className="text-center h-100"
                      style={{
                        padding:
                          "clamp(20px, 4vw, 30px) clamp(15px, 3vw, 20px)",
                        borderRadius: "20px",
                        cursor: "pointer",
                        background: "rgba(255, 255, 255, 0.9)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(213, 179, 99, 0.2)",
                        boxShadow: "0 15px 35px rgba(0,0,0,0.1)",
                        display: "flex",
                        flexDirection: "column",
                        justifyContent: "flex-start",
                        alignItems: "center",
                        minHeight: "clamp(280px, 35vw, 320px)",
                        position: "relative",
                        overflow: "hidden",
                      }}
                    >
                      {/* Gold accent */}
                      <div
                        style={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          height: "4px",
                          background:
                            "linear-gradient(90deg, #D5B363 0%, #f4d574 100%)",
                        }}
                      />
                      {/* Icon */}
                      <motion.div
                        style={{
                          width: "clamp(60px, 12vw, 100px)",
                          height: "clamp(60px, 12vw, 100px)",
                          margin: "0 auto clamp(15px, 3vw, 25px) auto",
                          overflow: "hidden",
                          flexShrink: 0,
                        }}
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        transition={{ type: "spring", stiffness: 300 }}
                      >
                        <img
                          src={item.icon}
                          alt={item.title}
                          style={{
                            width: "100%",
                            height: "100%",
                            objectFit: "cover",
                          }}
                        />
                      </motion.div>

                      {/* Title */}
                      <motion.h5
                        className="fw-600"
                        style={{
                          fontSize: "clamp(16px, 3vw, 20px)",
                          color: "#1C1C1C",
                          fontFamily: "Poppins, sans-serif",
                          marginBottom: "clamp(10px, 2vw, 15px)",
                          textAlign: "center",
                          lineHeight: "1.3",
                        }}
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ delay: 0.2 + index * 0.1 }}
                      >
                        {item.title}
                      </motion.h5>

                      {/* Description */}
                      <motion.p
                        style={{
                          fontSize: "clamp(12px, 2vw, 14px)",
                          color: "#4D4D4D",
                          lineHeight: "1.6",
                          margin: "0 auto",
                          textAlign: "center",
                          flex: 1,
                        }}
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ delay: 0.3 + index * 0.1 }}
                      >
                        {item.description}
                      </motion.p>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </div>
          </motion.div>
        </div>
      </motion.section>
    </>
  );
};

export default CategoriesHomeOne;
