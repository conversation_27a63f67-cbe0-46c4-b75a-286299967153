import { motion } from "framer-motion";

export default function TopExperience() {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const cardVariants = {
    hidden: { y: 50, opacity: 0, scale: 0.9 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  // Top-tier coaching data
  const coachingData = [
    {
      id: 1,
      image: "/assets/img/home-1/hero/exp-1.png",
      title: "PTE Coaching",
      description:
        "Our PTE coaching program offers personalized guidance and structured preparation to help you excel in the exam.",
      buttonText: "BOOK A FREE TRAIL",
      buttonColor: "#140342",
    },
    {
      id: 3,
      image: "/assets/img/home-1/hero/exp-3.png",
      title: "NAATI Coaching",
      description:
        "Join our NAATI coaching program, designed to instill confidence and readiness for the certification exam.",
      buttonText: "ENROLL NOW",
      buttonColor: "#140342",
    },
  ];

  return (
    <>
      {/* Top-tier PTE Coaching Section */}
      <motion.section
        className="layout-pt-lg layout-pb-lg"
        style={{
          background: "linear-gradient(135deg, #140342 0%, #1e0a5a 100%)",
          position: "relative",
          overflow: "hidden",
        }}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={containerVariants}
      >
        {/* Background Pattern */}
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23D5B363' fill-opacity='0.03'%3E%3Cpath d='M20 20c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8zm0-20c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8zm20 0c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8zm0 20c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8z'/%3E%3C/g%3E%3C/svg%3E")`,
            opacity: 0.1,
          }}
        />
        <div
          className="container"
          style={{ position: "relative", zIndex: 2, padding: "0 20px" }}
        >
          {/* Header */}
          <motion.div
            className="row justify-center text-center mb-40 mb-md-60"
            variants={itemVariants}
          >
            <div className="col-lg-8 col-md-10 px-3 px-md-4">
              <h2
                className="text-white fw-600 mb-15 mb-md-20"
                style={{
                  fontSize: "clamp(28px, 5vw, 42px)",
                  fontFamily: "Poppins, sans-serif",
                  lineHeight: "1.2",
                }}
              >
                Experience top-tier{" "}
                <span style={{ color: "#D5B363", fontWeight: "600" }}>
                  PTE Coaching in Australia
                </span>
              </h2>
              <p
                className="text-white"
                style={{
                  fontSize: "clamp(14px, 3vw, 18px)",
                  opacity: "0.9",
                  maxWidth: "600px",
                  margin: "0 auto",
                  padding: "0 15px",
                }}
              >
                Global Reach, Tailored PTE Solutions: Wherever Your Journey
                Leads
              </p>
            </div>
          </motion.div>

          {/* Cards */}
          <motion.div
            className="row justify-center y-gap-30 y-gap-md-40 min-h-[500px]"
            variants={itemVariants}
            style={{ padding: "0px 15px" }}
          >
            {coachingData.map((item) => (
              <motion.div
                key={item.id}
                className="col-lg-5 col-md-8 col-sm-10"
                variants={cardVariants}
                style={{ padding: "10px 15px" }}
                whileHover={{
                  y: -15,
                  scale: 1.03,
                  boxShadow: "0 25px 50px rgba(0,0,0,0.25)",
                }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div
                  className="d-flex flex-column"
                  style={{
                    background: "rgba(255, 255, 255, 0.95)",
                    backdropFilter: "blur(15px)",
                    border: "1px solid rgba(213, 179, 99, 0.2)",
                    borderRadius: "20px",
                    boxShadow: "0 20px 40px rgba(0,0,0,0.15)",
                    cursor: "pointer",
                    height: "100%",
                    overflow: "hidden",
                    position: "relative",
                  }}
                >
                  {/* Gold accent border */}
                  <div
                    style={{
                      position: "absolute",
                      top: 0,
                      left: 0,
                      right: 0,
                      height: "4px",
                      background:
                        "linear-gradient(90deg, #D5B363 0%, #f4d574 100%)",
                      borderRadius: "20px 20px 0 0",
                    }}
                  />
                  {/* Card Image */}
                  <div style={{ height: "260px", overflow: "hidden" }}>
                    <img
                      src={item.image}
                      alt={item.title}
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                      }}
                    />
                  </div>

                  {/* Card Content */}
                  <div
                    style={{
                      padding:
                        "clamp(20px, 4vw, 30px) clamp(15px, 3vw, 25px) 100px",
                      flex: "1 0 auto",
                    }}
                  >
                    <h4
                      className="fw-600 mb-15"
                      style={{
                        fontSize: "clamp(20px, 4vw, 24px)",
                        color: "#1C1C1C",
                        fontFamily: "Poppins, sans-serif",
                        textAlign: "center",
                      }}
                    >
                      {item.title}
                    </h4>
                    <p
                      style={{
                        fontSize: "clamp(12px, 2.5vw, 14px)",
                        color: "#666",
                        lineHeight: "1.6",
                        textAlign: "center",
                        margin: 0,
                        padding: "0 10px",
                      }}
                    >
                      {item.description}
                    </p>
                  </div>

                  {/* Action Button - Positioned at bottom center */}
                  <div
                    style={{
                      textAlign: "center",
                      padding:
                        "0 clamp(15px, 3vw, 25px) clamp(20px, 4vw, 30px)",
                      marginTop: "-70px", // Pull button up to overlap content area
                    }}
                  >
                    <motion.button
                      className="fw-600"
                      style={{
                        background: "linear-gradient(135deg, #D5B363, #f4e4a1)",
                        color: "#140342",
                        border: "2px solid #D5B363",
                        borderRadius: "12px",
                        padding:
                          "clamp(12px, 2vw, 14px) clamp(18px, 3.5vw, 24px)",
                        fontSize: "clamp(12px, 2.5vw, 14px)",
                        fontFamily: "Poppins, sans-serif",
                        fontWeight: "700",
                        cursor: "pointer",
                        width: "100%",
                        maxWidth: "200px",
                        boxShadow: "0 8px 32px rgba(213, 179, 99, 0.3)",
                        transition: "all 0.3s ease",
                        position: "relative",
                        overflow: "hidden",
                      }}
                      whileHover={{
                        scale: 1.05,
                        background: "rgba(213, 179, 99, 0.15)",
                        backdropFilter: "blur(10px)",
                        color: "#D5B363",
                        border: "2px solid rgba(213, 179, 99, 0.4)",
                        boxShadow: "0 12px 40px rgba(213, 179, 99, 0.2)",
                      }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ type: "spring", stiffness: 400 }}
                    >
                      {item.buttonText}
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </motion.section>

      {/* About Us Section */}
      <motion.section
        className="layout-pt-lg layout-pb-lg"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={containerVariants}
      >
        <div className="container" style={{ padding: "0 20px" }}>
          <motion.div
            className="row align-items-center"
            variants={containerVariants}
          >
            {/* Left Content - 40% */}
            <motion.div
              className="col-lg-5 col-md-6 mb-40 mb-md-0"
              variants={itemVariants}
            >
              <div className="pr-60 lg:pr-0" style={{ padding: "0 15px" }}>
                <motion.div className="mb-20" variants={itemVariants}>
                  <span
                    className="text-14 fw-800"
                    style={{
                      color: "#1c1c1c",
                      textTransform: "uppercase",
                      letterSpacing: "1px",
                    }}
                  >
                    About Deep Insight Academy
                  </span>
                </motion.div>

                <motion.h2
                  className="mb-20 mb-md-30 fw-700"
                  style={{
                    fontSize: "clamp(24px, 5vw, 36px)",
                    fontFamily: "Poppins, sans-serif",
                    color: "#1C1C1C",
                    lineHeight: "1.3",
                    fontWeight: "bold",
                  }}
                  variants={itemVariants}
                >
                  <span style={{ color: "#D5B363" }}>Global</span> Reputation
                </motion.h2>

                <motion.h3
                  className="mb-20 mb-md-30"
                  style={{
                    fontSize: "clamp(32px, 7vw, 52px)",
                    fontFamily: "Poppins, sans-serif",
                    color: "#140342",
                    lineHeight: "1.3",
                    fontWeight: "700",
                  }}
                  variants={itemVariants}
                >
                  <span style={{ color: "#D5B363" }}>No.1</span> Institute for{" "}
                  <span style={{ color: "#D5B363" }}>Online/Offline</span>{" "}
                  Coaching
                </motion.h3>

                <motion.p
                  className="mb-30 mb-md-40"
                  style={{
                    fontSize: "clamp(14px, 2.5vw, 16px)",
                    color: "#666",
                    lineHeight: "1.7",
                    padding: "0 10px 0 0",
                  }}
                  variants={itemVariants}
                >
                  Welcome to our DEEP INSIGHT ACADEMY, where expertise meets
                  excellence in navigating the intricacies of visa processes.
                  Our academy offers comprehensive training and guidance led by
                  seasoned professionals, ensuring that every consultant is
                  equipped with the knowledge and skills needed to excel in the
                  field. Our academy provides tailored programs designed to meet
                  your needs.
                </motion.p>

                <motion.button
                  className="fw-600"
                  style={{
                    background: "linear-gradient(135deg, #D5B363, #f4e4a1)",
                    color: "#140342",
                    border: "2px solid #D5B363",
                    borderRadius: "8px",
                    padding: "clamp(12px, 2.5vw, 15px) clamp(20px, 4vw, 30px)",
                    fontSize: "clamp(14px, 2.5vw, 16px)",
                    fontFamily: "Poppins, sans-serif",
                    cursor: "pointer",
                    width: "100%",
                    maxWidth: "200px",
                    boxShadow: "0 4px 15px rgba(213, 179, 99, 0.3)",
                    transition: "all 0.3s ease",
                  }}
                  variants={itemVariants}
                  whileHover={{
                    scale: 1.05,
                    boxShadow: "0 6px 20px rgba(213, 179, 99, 0.4)",
                  }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: "spring", stiffness: 400 }}
                >
                  READ MORE
                </motion.button>
              </div>
            </motion.div>

            {/* Right Image - 60% */}
            <motion.div className="col-lg-7 col-md-6" variants={itemVariants}>
              <motion.div
                className="position-relative"
                style={{
                  padding: "0 15px",
                  background: "rgba(255, 255, 255, 0.1)",
                  backdropFilter: "blur(10px)",
                  borderRadius: "clamp(12px, 2vw, 20px)",
                  border: "1px solid rgba(213, 179, 99, 0.2)",
                  boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
                }}
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div
                  style={{
                    padding: "15px",
                    borderRadius: "clamp(12px, 2vw, 20px)",
                    background:
                      "linear-gradient(135deg, rgba(213, 179, 99, 0.1), rgba(255, 255, 255, 0.05))",

                    borderTopWidth: "6px",
                  }}
                >
                  <img
                    src="/assets/img/home-1/hero/abt.png"
                    alt="About Deep Insight Academy"
                    style={{
                      width: "100%",
                      height: "auto",
                      borderRadius: "clamp(8px, 1.5vw, 16px)",
                      objectFit: "cover",
                      maxHeight: "500px",
                    }}
                  />
                </div>

                {/* Gold accent badge */}
                <div
                  style={{
                    position: "absolute",
                    top: "25px",
                    right: "25px",
                    background: "linear-gradient(135deg, #D5B363, #f4e4a1)",
                    color: "#140342",
                    padding: "8px 16px",
                    borderRadius: "20px",
                    fontSize: "12px",
                    fontWeight: "600",
                    boxShadow: "0 4px 15px rgba(213, 179, 99, 0.3)",
                    border: "2px solid rgba(255, 255, 255, 0.2)",
                  }}
                >
                  CERTIFIED
                </div>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </motion.section>
    </>
  );
}
