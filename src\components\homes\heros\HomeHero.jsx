import gsap from "gsap";
import { Link, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { server } from "@/api/services/server";
import { getRequest } from "@/api/services/controller";
import axios from "axios";
import SubscriptionModal from "@/components/others/SubscriptionModal";
import mocktestService from "@/api/services/mocktestService";
import { toast } from "react-toastify";

// Custom scrollbar styles
const scrollbarStyles = `
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #D5B363 0%, #f4d574 100%);
    border-radius: 10px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #c9a556 0%, #e8c967 100%);
  }
`;

const hero_content = {
  title: "Online Learning you can access anywhere easily!",
  text_underline: "",
  info_hero: <>Your Trusted Immigration Partner</>,
  subtitle: "DEEP INSIGHT ACADEMY",
  starts: [
    "icon-star text-yellow-1 text-11",
    "icon-star text-yellow-1 text-11",
    "icon-star text-yellow-1 text-11",
    "icon-star text-yellow-1 text-11",
    "icon-star text-yellow-1 text-11",
  ],
};

const HomeHero = () => {
  const navigate = useNavigate();
  const [mockTestData, setmockTestData] = useState([]);
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [showMockTestModal, setShowMockTestModal] = useState(false);
  const [subscriptionEligibility, setSubscriptionEligibility] = useState(null);
  // COMMENTED OUT RESUME FUNCTIONALITY FOR TESTING
  // const [incompleteTests, setIncompleteTests] = useState({});

  // COMMENTED OUT RESUME FUNCTIONALITY FOR TESTING
  // Check for incomplete tests for each mocktest
  // const checkIncompleteTests = async (testId) => {
  //   try {
  //     const response = await fetch(
  //       `${server.uri}logs?filter=${encodeURIComponent(
  //         JSON.stringify({
  //           where: {
  //             mocktestId: testId,
  //             userId: user?.id,
  //           },
  //           order: "attemptNumber DESC",
  //         })
  //       )}`
  //     );
  //     const logs = await response.json();
  //     return logs;
  //   } catch (error) {
  //     console.error("Error checking incomplete tests:", error);
  //     return [];
  //   }
  // };

  useEffect(() => {
    (async () => {
      const uri = server.uri + `mocktests`;

      const data = await getRequest(uri);
      if (data) {
        setmockTestData(data);

        // Check incomplete tests for each mocktest
        const userId = localStorage.getItem("isUserId");
        if (userId) {
          // COMMENTED OUT RESUME FUNCTIONALITY FOR TESTING
          // const incompleteData = {};
          // for (const test of data) {
          //   const testStatus = await checkIncompleteTests(test.testId);
          //   if (testStatus) {
          //     incompleteData[test.testId] = testStatus;
          //   }
          // }
          // setIncompleteTests(incompleteData);
        }
      }
    })();
  }, []);

  useEffect(() => {
    // Inject custom scrollbar styles
    const styleElement = document.createElement("style");
    styleElement.innerHTML = scrollbarStyles;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  useEffect(() => {
    const parallaxIt = () => {
      const target = document.querySelectorAll(".js-mouse-move-container");

      target.forEach((container) => {
        const targets = container.querySelectorAll(".js-mouse-move");

        targets.forEach((el) => {
          const movement = el.getAttribute("data-move");

          document.addEventListener("mousemove", (e) => {
            const relX = e.pageX - container.offsetLeft;
            const relY = e.pageY - container.offsetTop;

            gsap.to(el, {
              x:
                ((relX - container.offsetWidth / 2) / container.offsetWidth) *
                Number(movement),
              y:
                ((relY - container.offsetHeight / 2) / container.offsetHeight) *
                Number(movement),
              duration: 0.2,
            });
          });
        });
      });
    };

    parallaxIt();
  }, []);

  const checkSubscriptionAndNavigate = async (testId) => {
    try {
      const userId = localStorage.getItem("isUserId");
      if (!userId) {
        setSubscriptionEligibility({
          canSubmit: false,
          canViewAnswer: false,
          reason: "not_logged_in",
          userData: null,
          answersRemaining: 0,
        });
        setShowSubscriptionModal(true);
        return;
      }

      const response = await axios.get(`${server.uri}users/${userId}`);
      const userData = response.data;

      // Check if user has active subscription
      if (userData && userData.membershipStatus === "active") {
        navigate(`mockTest/${testId}`);
        return;
      } else if (
        userData &&
        userData.membershipStatus === "cancelled" &&
        new Date(userData.membershipEndAt) > new Date()
      ) {
        navigate(`mockTest/${testId}`);
        return;
      }

      // User doesn't have active subscription - check for free trial eligibility
      try {
        const hasAttempts = await mocktestService.hasAnyMocktestAttempts(
          userId
        );

        if (!hasAttempts) {
          // User has never attempted any mocktest - allow free trial
          toast.info(
            "🎉 Starting your FREE trial mocktest! You can take one complete test without subscription."
          );
          navigate(`mockTest/${testId}`);
          return;
        } else {
          // User has already used their free trial
          setSubscriptionEligibility({
            canSubmit: false,
            canViewAnswer: false,
            reason: "free_trial_used",
            userData: userData,
            answersRemaining: 0,
          });
          setShowSubscriptionModal(true);
        }
      } catch (attemptCheckError) {
        console.error("Error checking user attempts:", attemptCheckError);
        // Fallback - show subscription modal if we can't determine attempt history
        setSubscriptionEligibility({
          canSubmit: false,
          canViewAnswer: false,
          reason: "subscription_required",
          userData: userData,
          answersRemaining: 0,
        });
        setShowSubscriptionModal(true);
      }
    } catch (error) {
      console.error("Error checking subscription:", error);
      setSubscriptionEligibility({
        canSubmit: false,
        canViewAnswer: false,
        reason: "error",
        userData: null,
        answersRemaining: 0,
      });
      setShowSubscriptionModal(true);
    }
  };

  const handleCloseSubscriptionModal = () => {
    setShowSubscriptionModal(false);
    setSubscriptionEligibility(null);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const imageVariants = {
    hidden: { x: 50, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <>
      <section
        className="masthead -type-1"
        style={{
          background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
          position: "relative",
          overflow: "hidden",
        }}
      >
        {/* Background Pattern */}
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23D5B363' fill-opacity='0.03'%3E%3Cpath d='M20 20c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8zm0-20c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8zm20 0c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8zm0 20c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8z'/%3E%3C/g%3E%3C/svg%3E")`,
            opacity: 0.1,
          }}
        />
        <div className="container">
          <motion.div
            className="row min-vh-100 align-items-center"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {/* Left Side - Content */}
            <div className="col-lg-6 col-md-12">
              <motion.div
                className="masthead__content pr-60 lg:pr-0"
                style={{ fontFamily: "Poppins, sans-serif" }}
                variants={itemVariants}
              >
                <motion.div
                  className="mb-20"
                  variants={itemVariants}
                  style={{
                    display: "inline-block",
                    background:
                      "linear-gradient(135deg, #D5B363 0%, #f4d574 100%)",
                    border: "2px solid #D5B363",
                    borderRadius: "12px",
                    padding: "12px 20px",
                    boxShadow: "0 12px 40px rgba(213, 179, 99, 0.3)",
                    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                  }}
                  whileHover={{
                    background: "rgba(213, 179, 99, 0.1)",
                    backdropFilter: "blur(15px)",
                    border: "2px solid rgba(213, 179, 99, 0.3)",
                    boxShadow: "0 8px 32px rgba(213, 179, 99, 0.2)",
                    scale: 1.02,
                  }}
                >
                  <h6
                    className="text-16 fw-800 mb-0"
                    style={{
                      color: "#140342",
                      transition: "color 0.3s ease",
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.color = "#140342";
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.color = "#140342";
                    }}
                  >
                    {hero_content.subtitle}
                  </h6>
                </motion.div>

                <motion.h1
                  className="masthead__title mb-30 fw-400"
                  style={{ color: "#1C1C1C" }}
                  variants={itemVariants}
                >
                  Online{" "}
                  <span
                    className=" fw-900 "
                    style={{ color: "#140342", marginRight: "10px" }}
                  >
                    Learning
                  </span>
                  <br />
                  <span className=" fw-900" style={{ color: "#140342" }}>
                    you can access{" "}
                    <span className="fw-400" style={{ color: "#1C1C1C" }}>
                      any
                    </span>
                  </span>
                  <br />
                  <span style={{ color: "#1C1C1C" }}>where easily!</span>
                </motion.h1>

                <motion.div
                  className="mb-40 py-10 px-20 d-inline-block"
                  style={{
                    background:
                      "linear-gradient(135deg, #140342 0%, #1e0a5a 100%)",
                    color: "white",
                    backdropFilter: "blur(10px)",
                    border: "1px solid rgba(213, 179, 99, 0.3)",
                    borderRadius: "0 16px 0 16px",
                    display: "inline-block",
                    overflow: "hidden",
                    fontSize: "28px",
                    boxShadow: "0 10px 30px rgba(0, 0, 0, 0.15)",
                  }}
                  variants={itemVariants}
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <span className="fw-500">{hero_content.info_hero}</span>
                </motion.div>

                <motion.div
                  className="masthead__buttons row x-gap-20 y-gap-20 mb-40"
                  variants={itemVariants}
                >
                  <div className="col-auto">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{
                        type: "spring",
                        stiffness: 400,
                        damping: 17,
                      }}
                    >
                      <Link
                        to="/signup"
                        className="button -md px-40 py-15 fw-800 cursor-pointer text-black"
                        style={{
                          background:
                            "linear-gradient(135deg, #D5B363 0%, #f4d574 100%)",
                          color: "#1c1c1c",
                          border: "2px solid #D5B363",
                          borderRadius: "16px",
                          fontWeight: "700",
                          boxShadow: "0 8px 25px rgba(213, 179, 99, 0.4)",
                          cursor: "pointer",
                          transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                          position: "relative",
                          overflow: "hidden",
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.background = "rgba(213, 179, 99, 0.2)";
                          e.target.style.backdropFilter = "blur(15px)";
                          e.target.style.color = "#D5B363";
                          e.target.style.border =
                            "2px solid rgba(213, 179, 99, 0.5)";
                          e.target.style.boxShadow =
                            "0 12px 40px rgba(213, 179, 99, 0.3)";
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.background =
                            "linear-gradient(135deg, #D5B363 0%, #f4d574 100%)";
                          e.target.style.backdropFilter = "none";
                          e.target.style.color = "#1c1c1c";
                          e.target.style.border = "2px solid #D5B363";
                          e.target.style.boxShadow =
                            "0 8px 25px rgba(213, 179, 99, 0.4)";
                        }}
                      >
                        GET A FREE TRAIL NOW
                      </Link>
                    </motion.div>
                  </div>

                  <div className="col-auto">
                    <motion.button
                      className="button -md px-30 py-15 d-flex align-items-center"
                      style={{
                        background: "rgba(255, 255, 255, 0.1)",
                        backdropFilter: "blur(15px)",
                        border: "2px solid rgba(213, 179, 99, 0.3)",
                        borderRadius: "16px",
                        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                        boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
                      }}
                      whileHover={{
                        scale: 1.02,
                        background: "rgba(213, 179, 99, 0.1)",
                        border: "2px solid rgba(213, 179, 99, 0.5)",
                        boxShadow: "0 12px 40px rgba(213, 179, 99, 0.2)",
                      }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <motion.span
                        style={{
                          background:
                            "linear-gradient(135deg, #140342 0%, #1e0a5a 100%)",
                          color: "white",
                          borderRadius: "999px",
                          padding: "16px",
                          marginRight: "10px",
                          boxShadow: "0 4px 15px rgba(20, 3, 66, 0.3)",
                        }}
                        whileHover={{
                          rotate: 360,
                          background:
                            "linear-gradient(135deg, #D5B363 0%, #f4d574 100%)",
                          color: "#140342",
                        }}
                        transition={{ duration: 0.6 }}
                      >
                        {" "}
                        <i className="icon-play  "></i>
                      </motion.span>
                      <span
                        className="text-2xl fw-600"
                        style={{
                          color: "#140342",
                          transition: "color 0.3s ease",
                        }}
                      >
                        See how it works?
                      </span>
                    </motion.button>
                  </div>
                </motion.div>
              </motion.div>
            </div>

            {/* Right Side - Hero Image with Mock Test Button */}
            <motion.div
              className="col-lg-6 col-md-12"
              variants={imageVariants}
              style={{ position: "relative", padding: "10px" }}
            >
              {/* Hero Image Container */}
              <motion.div
                style={{
                  position: "relative",
                  height: "500px",
                  borderRadius: "20px",
                  overflow: "hidden",
                  boxShadow: "0 20px 40px rgba(0, 0, 0, 0.1)",
                }}
                whileHover={{ scale: 1.02, y: -3 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                {/* Hero Image */}
                <img
                  src="/assets/img/home-1/hero/hero-img.png"
                  alt="PTE Learning Platform"
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "contain",
                    borderRadius: "20px",
                  }}
                />

                {/* Overlay Gradient */}
                <div
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    border: "2px solid #D5B363",
                    borderRadius: "20px",
                  }}
                />

                {/* Mock Test Button - Simple Floating */}
                <motion.button
                  onClick={() => setShowMockTestModal(true)}
                  style={{
                    position: "absolute",
                    bottom: "30px",
                    left: "50%",
                    transform: "translateX(-50%)",
                    background:
                      "linear-gradient(135deg, #D5B363 0%, #f4d574 100%)",
                    color: "#140342",
                    border: "2px solid #D5B363",
                    borderRadius: "16px",
                    padding: "16px 32px",
                    fontSize: "16px",
                    fontWeight: "700",
                    cursor: "pointer",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: "10px",
                    boxShadow: "0 12px 40px rgba(213, 179, 99, 0.5)",
                    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                    textTransform: "uppercase",
                    letterSpacing: "0.5px",
                    backdropFilter: "blur(10px)",
                    minWidth: "220px",
                    zIndex: 10,
                  }}
                  whileHover={{
                    scale: 1.05,
                    background: "rgba(255, 255, 255, 0.95)",
                    backdropFilter: "blur(15px)",
                    color: "#140342",
                    border: "2px solid #D5B363",
                    boxShadow: "0 16px 50px rgba(213, 179, 99, 0.6)",
                    y: -5,
                  }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 1.0 }}
                >
                  🎯 Start Mock Test
                </motion.button>

                {/* Stats Badge - Top Right */}
                <motion.div
                  style={{
                    position: "absolute",
                    top: "20px",
                    right: "20px",
                    background: "rgba(255, 255, 255, 0.9)",
                    backdropFilter: "blur(10px)",
                    borderRadius: "12px",
                    padding: "12px 16px",
                    boxShadow: "0 8px 20px rgba(0, 0, 0, 0.1)",
                    border: "1px solid #D5B363",
                  }}
                  initial={{ x: 30, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 1.2 }}
                >
                  <div style={{ textAlign: "center" }}>
                    <div
                      style={{
                        fontSize: "18px",
                        fontWeight: "700",
                        color: "#140342",
                        marginBottom: "2px",
                      }}
                    >
                      {mockTestData?.length || 0}+
                    </div>
                    <div
                      style={{
                        fontSize: "11px",
                        color: "#64748b",
                        fontWeight: "600",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                      }}
                    >
                      Tests
                    </div>
                  </div>
                </motion.div>

                {/* Success Rate Badge - Top Left */}
                <motion.div
                  style={{
                    position: "absolute",
                    top: "20px",
                    left: "20px",
                    background:
                      "linear-gradient(135deg, #140342 0%, #1e0a5a 100%)",
                    color: "white",
                    borderRadius: "12px",
                    padding: "12px 16px",
                    boxShadow: "0 8px 20px rgba(20, 3, 66, 0.3)",
                    border: "1px solid rgba(213, 179, 99, 0.3)",
                  }}
                  initial={{ x: -30, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 1.4 }}
                >
                  <div style={{ textAlign: "center" }}>
                    <div
                      style={{
                        fontSize: "18px",
                        fontWeight: "700",
                        marginBottom: "2px",
                      }}
                    >
                      98%
                    </div>
                    <div
                      style={{
                        fontSize: "11px",
                        opacity: 0.9,
                        fontWeight: "600",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                      }}
                    >
                      Success
                    </div>
                  </div>
                </motion.div>

                {/* NAATI Badge - Bottom Right Corner */}
                <motion.div
                  style={{
                    position: "absolute",
                    bottom: "100px",
                    right: "20px",
                    background: "rgba(255, 255, 255, 0.95)",
                    backdropFilter: "blur(10px)",
                    color: "#140342",
                    borderRadius: "50px",
                    padding: "8px 16px",
                    fontSize: "12px",
                    fontWeight: "700",
                    textTransform: "uppercase",
                    letterSpacing: "1px",
                    boxShadow: "0 4px 15px rgba(0, 0, 0, 0.1)",
                    border: "1px solid rgba(213, 179, 99, 0.4)",
                    display: "flex",
                    alignItems: "center",
                    gap: "6px",
                  }}
                  initial={{ x: 30, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 1.6 }}
                  whileHover={{
                    scale: 1.05,
                    background:
                      "linear-gradient(135deg, #D5B363 0%, #f4d574 100%)",
                    color: "#140342",
                  }}
                >
                  <span style={{ fontSize: "14px" }}>🏆</span>
                  NAATI
                </motion.div>
              </motion.div>

              {/* Statistics Cards Row */}
              {/* <div
                style={{ display: "flex", gap: "10px", marginBottom: "15px" }}
              >
                <motion.div
                  style={{
                    flex: 1,
                    backgroundColor: "#140342",
                    borderRadius: "12px",
                    padding: "15px 10px",
                    textAlign: "center",
                    boxShadow: "0 6px 15px rgba(0, 0, 0, 0.1)",
                    border: "2px solid #140342",
                  }}
                  whileHover={{ scale: 1.05, y: -2 }}
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 1.6 }}
                >
                  <h3
                    style={{
                      fontSize: "20px",
                      fontWeight: "700",
                      color: "#FFFFFF",
                      marginBottom: "4px",
                    }}
                  >
                    {mockTestData?.length || 0}+
                  </h3>
                  <p
                    style={{
                      fontSize: "12px",
                      color: "#FFFFFF",
                      fontWeight: "500",
                    }}
                  >
                    Mock Tests
                  </p>
                </motion.div>

                <motion.div
                  style={{
                    flex: 1,
                    backgroundColor: "white",
                    borderRadius: "12px",
                    padding: "15px 10px",
                    textAlign: "center",
                    boxShadow: "0 6px 15px rgba(0, 0, 0, 0.1)",
                    border: "2px solid #140342",
                  }}
                  whileHover={{ scale: 1.05, y: -2 }}
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 1.7 }}
                >
                  <h3
                    style={{
                      fontSize: "20px",
                      fontWeight: "700",
                      color: "#140342",
                      marginBottom: "4px",
                    }}
                  >
                    98%
                  </h3>
                  <p
                    style={{
                      fontSize: "12px",
                      color: "#666",
                      fontWeight: "500",
                    }}
                  >
                    Success Rate
                  </p>
                </motion.div>

                <motion.div
                  style={{
                    flex: 1,
                    backgroundColor: "#140342",
                    borderRadius: "12px",
                    padding: "15px 10px",
                    textAlign: "center",
                    boxShadow: "0 6px 15px rgba(0, 0, 0, 0.1)",
                    border: "1px solid #f0f0f0",
                  }}
                  whileHover={{ scale: 1.05, y: -2 }}
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 1.8 }}
                >
                  <h3
                    style={{
                      fontSize: "20px",
                      fontWeight: "700",
                      color: "#FFFFFF",
                      marginBottom: "4px",
                    }}
                  >
                    24/7
                  </h3>
                  <p
                    style={{
                      fontSize: "12px",
                      color: "#FFFFFF",
                      fontWeight: "500",
                    }}
                  >
                    Support
                  </p>
                </motion.div>
              </div> */}

              {/* Quick Access Links - Compact */}
              {/* <motion.div
                style={{
                  backgroundColor: "#f8f9fa",
                  borderRadius: "12px",
                  padding: "15px",
                }}
                initial={{ y: 30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 1.9 }}
              >
                <h4
                  style={{
                    fontSize: "14px",
                    fontWeight: "600",
                    color: "#140342",
                    marginBottom: "12px",
                    textAlign: "center",
                  }}
                >
                  Quick Access
                </h4>
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-around",
                    gap: "8px",
                  }}
                >
                  <motion.button
                    onClick={() => navigate("/speaking")}
                    style={{
                      backgroundColor: "transparent",
                      border: "none",
                      cursor: "pointer",
                      textAlign: "center",
                      padding: "8px",
                      borderRadius: "10px",
                      flex: 1,
                    }}
                    whileHover={{ backgroundColor: "rgba(20, 3, 66, 0.1)" }}
                  >
                    <div style={{ fontSize: "18px", marginBottom: "4px" }}>
                      🎤
                    </div>
                    <p
                      style={{
                        fontSize: "11px",
                        color: "#140342",
                        fontWeight: "500",
                        margin: 0,
                      }}
                    >
                      Speaking
                    </p>
                  </motion.button>

                  <motion.button
                    onClick={() => navigate("/writing")}
                    style={{
                      backgroundColor: "transparent",
                      border: "none",
                      cursor: "pointer",
                      textAlign: "center",
                      padding: "8px",
                      borderRadius: "10px",
                      flex: 1,
                    }}
                    whileHover={{ backgroundColor: "rgba(20, 3, 66, 0.1)" }}
                  >
                    <div style={{ fontSize: "18px", marginBottom: "4px" }}>
                      ✍️
                    </div>
                    <p
                      style={{
                        fontSize: "11px",
                        color: "#140342",
                        fontWeight: "500",
                        margin: 0,
                      }}
                    >
                      Writing
                    </p>
                  </motion.button>

                  <motion.button
                    onClick={() => navigate("/listening")}
                    style={{
                      backgroundColor: "transparent",
                      border: "none",
                      cursor: "pointer",
                      textAlign: "center",
                      padding: "8px",
                      borderRadius: "10px",
                      flex: 1,
                    }}
                    whileHover={{ backgroundColor: "rgba(20, 3, 66, 0.1)" }}
                  >
                    <div style={{ fontSize: "18px", marginBottom: "4px" }}>
                      👂
                    </div>
                    <p
                      style={{
                        fontSize: "11px",
                        color: "#140342",
                        fontWeight: "500",
                        margin: 0,
                      }}
                    >
                      Listening
                    </p>
                  </motion.button>

                  <motion.button
                    onClick={() => navigate("/reading")}
                    style={{
                      backgroundColor: "transparent",
                      border: "none",
                      cursor: "pointer",
                      textAlign: "center",
                      padding: "8px",
                      borderRadius: "10px",
                      flex: 1,
                    }}
                    whileHover={{ backgroundColor: "rgba(20, 3, 66, 0.1)" }}
                  >
                    <div style={{ fontSize: "18px", marginBottom: "4px" }}>
                      📖
                    </div>
                    <p
                      style={{
                        fontSize: "11px",
                        color: "#140342",
                        fontWeight: "500",
                        margin: 0,
                      }}
                    >
                      Reading
                    </p>
                  </motion.button>
                </div>
              </motion.div> */}
            </motion.div>
          </motion.div>
        </div>

        {/* animated shape start */}
        {/* <ShapeRendering /> */}
        {/* animated shape end */}
      </section>

      {/* Mock Test Modal */}
      {showMockTestModal && (
        <div
          className="modal"
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            backgroundColor: "rgba(20, 3, 66, 0.4)",
            backdropFilter: "blur(8px)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 1000,
          }}
          onClick={() => setShowMockTestModal(false)}
        >
          <div
            className="modal-content"
            style={{
              backgroundColor: "#f4f0ff",
              borderRadius: "20px",
              padding: "32px",
              maxWidth: "650px",
              width: "95%",
              maxHeight: "85vh",
              boxShadow: "0 25px 50px rgba(20, 3, 66, 0.25)",
              border: "1px solid rgba(20, 3, 66, 0.1)",
              position: "relative",
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button */}
            <button
              onClick={() => setShowMockTestModal(false)}
              style={{
                position: "absolute",
                top: "20px",
                right: "20px",
                background: "white",
                border: "1px solid rgba(20, 3, 66, 0.1)",
                borderRadius: "50%",
                width: "40px",
                height: "40px",
                fontSize: "20px",
                cursor: "pointer",
                color: "#140342",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                boxShadow: "0 4px 12px rgba(20, 3, 66, 0.1)",
                transition: "all 0.2s ease",
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = "#140342";
                e.target.style.color = "white";
                e.target.style.transform = "scale(1.05)";
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = "white";
                e.target.style.color = "#140342";
                e.target.style.transform = "scale(1)";
              }}
            >
              ×
            </button>

            {/* Header with modern styling */}
            <div style={{ textAlign: "center", marginBottom: "32px" }}>
              <div
                style={{
                  display: "inline-flex",
                  alignItems: "center",
                  gap: "12px",
                  backgroundColor: "white",
                  padding: "16px 24px",
                  borderRadius: "16px",
                  boxShadow: "0 8px 20px rgba(20, 3, 66, 0.1)",
                  marginBottom: "16px",
                  border: "1px solid rgba(20, 3, 66, 0.05)",
                }}
              >
                <span style={{ fontSize: "28px" }}>🎯</span>
                <h2
                  style={{
                    fontSize: "28px",
                    fontWeight: "700",
                    color: "#140342",
                    margin: 0,
                    letterSpacing: "-0.5px",
                  }}
                >
                  Mock Tests
                </h2>
              </div>
              <p
                style={{
                  color: "#64748b",
                  fontSize: "16px",
                  margin: 0,
                  lineHeight: "1.5",
                  fontWeight: "400",
                }}
              >
                Choose a test to practice and improve your PTE skills
              </p>
            </div>

            <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: "16px",
                maxHeight: "400px",
                overflowY: "auto",
                paddingRight: "8px",
              }}
              className="custom-scrollbar"
            >
              {mockTestData?.map((item, index) => {
                // COMMENTED OUT RESUME FUNCTIONALITY FOR TESTING
                // const testStatus = incompleteTests[item?.testId];
                // const hasIncomplete = testStatus?.hasIncomplete;
                // const buttonText = hasIncomplete ? "Resume" : "Start";
                const buttonText = "Start";
                // COMMENTED OUT RESUME FUNCTIONALITY FOR TESTING
                // const sectionNames = ["Speaking", "Reading", "Listening"];

                return (
                  <div
                    key={index}
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      backgroundColor: "white",
                      padding: "20px 24px",
                      borderRadius: "16px",
                      // COMMENTED OUT RESUME FUNCTIONALITY FOR TESTING
                      // boxShadow: hasIncomplete
                      //   ? "0 12px 24px rgba(255, 165, 0, 0.15)"
                      //   : "0 8px 20px rgba(20, 3, 66, 0.08)",
                      // border: hasIncomplete
                      //   ? "2px solid #ffa500"
                      //   : "1px solid rgba(20, 3, 66, 0.08)",
                      boxShadow: "0 8px 20px rgba(20, 3, 66, 0.08)",
                      border: "1px solid rgba(20, 3, 66, 0.08)",
                      minHeight: "80px",
                      position: "relative",
                      transition: "all 0.3s ease",
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.transform = "translateY(-2px)";
                      // COMMENTED OUT RESUME FUNCTIONALITY FOR TESTING
                      // e.target.style.boxShadow = hasIncomplete
                      //   ? "0 16px 32px rgba(255, 165, 0, 0.2)"
                      //   : "0 12px 28px rgba(20, 3, 66, 0.12)";
                      e.target.style.boxShadow =
                        "0 12px 28px rgba(20, 3, 66, 0.12)";
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.transform = "translateY(0)";
                      // COMMENTED OUT RESUME FUNCTIONALITY FOR TESTING
                      // e.target.style.boxShadow = hasIncomplete
                      //   ? "0 12px 24px rgba(255, 165, 0, 0.15)"
                      //   : "0 8px 20px rgba(20, 3, 66, 0.08)";
                      e.target.style.boxShadow =
                        "0 8px 20px rgba(20, 3, 66, 0.08)";
                    }}
                  >
                    {/* COMMENTED OUT RESUME FUNCTIONALITY FOR TESTING */}
                    {/* Incomplete test indicator - moved to top-left to avoid overlap */}
                    {/* {hasIncomplete && (
                      <div
                        style={{
                          position: "absolute",
                          top: "12px",
                          left: "12px",
                          background:
                            "linear-gradient(135deg, #ffa500 0%, #ff8c00 100%)",
                          color: "white",
                          fontSize: "10px",
                          padding: "4px 10px",
                          borderRadius: "20px",
                          fontWeight: "700",
                          letterSpacing: "0.5px",
                          boxShadow: "0 4px 12px rgba(255, 165, 0, 0.3)",
                          textTransform: "uppercase",
                          zIndex: 2,
                        }}
                      >
                        In Progress
                      </div>
                    )} */}

                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "16px",
                        flex: 1,
                        // COMMENTED OUT RESUME FUNCTIONALITY FOR TESTING
                        // paddingTop: hasIncomplete ? "20px" : "0px",
                        paddingTop: "0px",
                      }}
                    >
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          backgroundColor: "#140342",
                          color: "white",
                          fontSize: "16px",
                          fontWeight: "700",
                          width: "40px",
                          height: "40px",
                          borderRadius: "12px",
                          marginRight: "16px",
                          minWidth: "40px",
                        }}
                      >
                        {index + 1}
                      </div>
                      <div style={{ flex: 1 }}>
                        <span
                          style={{
                            fontSize: "18px",
                            fontWeight: "600",
                            color: "#140342",
                            display: "block",
                            marginBottom: "6px",
                          }}
                        >
                          {item?.testName}
                        </span>
                        {/* Show attempt info */}
                        {/* {testStatus && (
                          <div
                            style={{
                              fontSize: "13px",
                              color: "#64748b",
                              marginTop: "4px",
                              fontWeight: "500",
                            }}
                          >
                            {hasIncomplete ? (
                              <>
                                Resume Attempt {testStatus.attemptNumber} •
                                Currently in{" "}
                                {sectionNames[testStatus.currentSection]}{" "}
                                section
                              </>
                            ) : testStatus.totalAttempts === 0 ? (
                              <>Ready for your first attempt</>
                            ) : (
                              <>
                                {testStatus.totalAttempts} attempt
                                {testStatus.totalAttempts > 1 ? "s" : ""}{" "}
                                completed • Ready for attempt{" "}
                                {testStatus.totalAttempts + 1}
                              </>
                            )}
                          </div>
                        )} */}
                      </div>
                      <div
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "flex-end",
                          gap: "8px",
                        }}
                      >
                        <span
                          style={{
                            fontSize: "12px",
                            fontWeight: "600",
                            color: "#140342",
                            backgroundColor: "#f4f0ff",
                            padding: "6px 12px",
                            borderRadius: "20px",
                            textTransform: "uppercase",
                            letterSpacing: "0.5px",
                          }}
                        >
                          {item?.difficultyLevel}
                        </span>
                      </div>
                    </div>
                    <button
                      onClick={() => {
                        setShowMockTestModal(false);
                        checkSubscriptionAndNavigate(item?.testId);
                      }}
                      style={{
                        fontSize: "14px",
                        fontWeight: "700",
                        borderRadius: "12px",
                        background:
                          "linear-gradient(135deg, #140342 0%, #1e0a5c 100%)",
                        color: "white",
                        padding: "12px 24px",
                        border: "none",
                        cursor: "pointer",
                        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                        marginLeft: "16px",
                        boxShadow: "0 8px 20px rgba(20, 3, 66, 0.25)",
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                        minWidth: "100px",
                      }}
                      onMouseEnter={(e) => {
                        e.target.style.transform =
                          "translateY(-2px) scale(1.02)";
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.transform = "translateY(0) scale(1)";
                      }}
                    >
                      {buttonText}
                    </button>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={handleCloseSubscriptionModal}
        eligibility={subscriptionEligibility}
        title="Subscription Required for Mock Tests"
        message="Access to mock tests is available only to our subscribed members. Upgrade to practice with full-length mock exams."
      />
    </>
  );
};

export default HomeHero;
