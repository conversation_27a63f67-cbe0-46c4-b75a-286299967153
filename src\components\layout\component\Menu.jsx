import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import MobileFooter from "./MobileFooter";
import { menuList } from "@/data/menu";
import { useLocation } from "react-router-dom";
import { getRequest } from "@/api/services/controller";
import { server } from "@/api/services/server";
import PropTypes from "prop-types";

export default function Menu({
  allClasses,
  headerPosition,
  onSubCategoryOpen,
}) {
  const navigate = useNavigate();
  const [menuItem, setMenuItem] = useState("");
  const { pathname } = useLocation();
  const [category, setCategory] = useState([]);
  const [selectedTab, setSelectedTab] = useState("PTE Academic"); // Changed default to PTE Academic
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [pendingSubCategory, setPendingSubCategory] = useState(null);

  // Shared categories with active/inactive status for both PTE Core and PTE Academic
  const sharedCategories = [
    {
      name: "speaking",
      icon: "🎤",
      color: "#E8F5E8",
      borderColor: "#4CAF50",
      data: [
        {
          categoryId: "6787cc2b486e6c04269a34e1",
          name: "Read Aloud",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "67838fcf2e266d08ba713eed",
          name: "Repeat Sentence",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "678391852e266d08ba713eee",
          name: "Describe Image",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "67e5acdd73e32a15ba3d88c8",
          name: "Re-tell Lecture",
          isAiBased: true,
          activeInCore: false,
          activeInAcademic: true,
        },
        {
          categoryId: "67b34f839ab23dd6196d1a91",
          name: "Respond to a situation",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: false,
        },
        {
          categoryId: "6783923483cd4009d9cddafa",
          name: "Answer Short Question",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: true,
        },
      ],
    },
    {
      name: "writing",
      icon: "✍️",
      color: "#FFF3E0",
      borderColor: "#FF9800",
      data: [
        {
          categoryId: "6783926b83cd4009d9cddafb",
          name: "Summarize Written Text",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: false,
        },
        {
          categoryId: "67e5af2b73e32a15ba3d88c9",
          name: "Summarize Written Text (A)",
          isAiBased: true,
          activeInCore: false,
          activeInAcademic: true,
        },
        {
          categoryId: "67d893d673e32a15ba3d88c1",
          name: "Write Essay",
          isAiBased: true,
          activeInCore: false,
          activeInAcademic: true,
        },
        {
          categoryId: "678392a883cd4009d9cddafc",
          name: "Write Email",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: false,
        },
      ],
    },
    {
      name: "reading",
      icon: "📖",
      color: "#E3F2FD",
      borderColor: "#2196F3",
      data: [
        {
          categoryId: "67801456e0dfdc154eff11ba",
          name: "Reading & Writing: Fill in the blanks",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "6780113ae0dfdc154eff11b6",
          name: "Multiple Choice (Multiple)",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "678011ade0dfdc154eff11b8",
          name: "Re-order Paragraphs",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "67894ed2102a6d6548ceec90",
          name: "Reading: Fill in the Blanks",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "67800fc7d4e7d9147dd9525d",
          name: "Multiple Choice (Single)",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
      ],
    },
    {
      name: "listening",
      icon: "🎧",
      color: "#F3E5F5",
      borderColor: "#9C27B0",
      data: [
        {
          categoryId: "68556cb5d0001a608d091719",
          name: "Summarize Spoken Text",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "67801bda382bce18e30d11ca",
          name: "Multiple Choice (Multiple)",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "68556cb8d0001a608d09171b",
          name: "Fill in the Blanks",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "68556cb6d0001a608d09171a",
          name: "Highlight Correct Summary",
          isAiBased: false,
          activeInCore: false,
          activeInAcademic: true,
        },
        {
          categoryId: "67801ec5382bce18e30d11ce",
          name: "Multiple Choice (Single)",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "678022c1382bce18e30d11d0",
          name: "Select Missing Word",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "678023f5382bce18e30d11d2",
          name: "Highlight Incorrect Words",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "678024ac382bce18e30d11d3",
          name: "Write from Dictation",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
      ],
    },
  ];

  const handleSubCategoryClick = async (
    subCategoryId,
    sectionName,
    isActive
  ) => {
    if (isActive) {
      // Directly open questions - subscription check will happen in question components
      onSubCategoryOpen(subCategoryId);
    } else {
      // If the category is inactive, store the pending category info and open the dialog
      setPendingSubCategory({
        id: subCategoryId,
        section: sectionName,
      });
      setIsModalOpen(true);
    }
  };

  const handleTabChange = () => {
    // Always open the modal when switching tabs
    setIsModalOpen(true);
  };

  const handleModalConfirm = (selectedTabValue) => {
    // Update the selected tab
    setSelectedTab(selectedTabValue);
    setIsModalOpen(false);

    // If there was a pending subcategory and it's now active in the selected tab, open it
    if (pendingSubCategory) {
      const isNowActive =
        selectedTabValue === "PTE Core"
          ? findSubcategoryActiveStatus(pendingSubCategory.id, true)
          : findSubcategoryActiveStatus(pendingSubCategory.id, false);

      if (isNowActive) {
        onSubCategoryOpen(pendingSubCategory.id);
      }
      setPendingSubCategory(null);
    }
  };

  // Helper function to find if a subcategory is active in a specific tab
  const findSubcategoryActiveStatus = (categoryId, isCore) => {
    for (const section of sharedCategories) {
      const item = section.data.find((item) => item.categoryId === categoryId);
      if (item) {
        return isCore ? item.activeInCore : item.activeInAcademic;
      }
    }
    return false;
  };

  useEffect(() => {
    const fetchCategory = async () => {
      try {
        const uri = server.uri + "categories/header";
        const data = await getRequest(uri);
        if (data) {
          // Process data if needed, but for now use sharedCategories for consistency
          setCategory(sharedCategories);
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
        // Fallback to sharedCategories if API fails
        setCategory(sharedCategories);
      }
    };
    fetchCategory();
  }, []);

  useEffect(() => {
    menuList.forEach((elm) => {
      elm?.links?.forEach((elm2) => {
        if (elm2.href?.split("/")[1] === pathname.split("/")[1]) {
          setMenuItem(elm.title);
        } else {
          elm2?.links?.map((elm3) => {
            if (elm3.href?.split("/")[1] === pathname.split("/")[1]) {
              setMenuItem(elm.title);
            }
          });
        }
      });
    });
  }, [pathname]);

  // Prevent body scrolling when modal is open
  useEffect(() => {
    if (isModalOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isModalOpen]);

  // Save selectedTab to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem("selectedPteTab", selectedTab);
  }, [selectedTab]);

  // Load selectedTab from localStorage on component mount
  useEffect(() => {
    const savedTab = localStorage.getItem("selectedPteTab");
    if (savedTab) {
      setSelectedTab(savedTab);
    }
  }, []);

  // Handle navigation for protected routes
  const handleProtectedNavigation = (path) => {
    const isAuthenticated = localStorage.getItem("isAuthenticated");
    if (!isAuthenticated) {
      navigate("/login");
    } else {
      navigate(path);
    }
  };

  return (
    <>
      {/* Hidden Scrollbar Styles */}
      <style>
        {`
          .custom-scrollbar::-webkit-scrollbar {
            width: 0px;
            background: transparent;
          }
          
          .custom-scrollbar::-webkit-scrollbar-track {
            background: transparent;
          }
          
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: transparent;
          }
          
          /* Firefox scrollbar - hide it */
          .custom-scrollbar {
            scrollbar-width: none;
            -ms-overflow-style: none;
          }
          
          /* Smooth scrolling */
          .custom-scrollbar {
            scroll-behavior: smooth;
          }
          
          /* Two column layout for listening */
          .listening-two-columns {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
          }
        `}
      </style>

      {/* Modern Modal for selecting exam type - only shows for inactive subcategories */}
      {isModalOpen && (
        <div
          className="modal"
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: "rgba(20, 3, 66, 0.4)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 1000,
            backdropFilter: "blur(8px)",
          }}
          onClick={() => setIsModalOpen(false)}
        >
          <div
            style={{
              background: "rgba(255, 255, 255, 0.95)",
              backdropFilter: "blur(20px)",
              padding: "32px",
              borderRadius: "20px",
              width: "520px",
              maxWidth: "90%",
              boxShadow: "0 25px 50px rgba(20, 3, 66, 0.2)",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              position: "relative",
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <button
              onClick={() => setIsModalOpen(false)}
              style={{
                position: "absolute",
                right: "20px",
                top: "20px",
                background: "rgba(20, 3, 66, 0.1)",
                border: "none",
                fontSize: "20px",
                cursor: "pointer",
                color: "#140342",
                width: "40px",
                height: "40px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                borderRadius: "50%",
                transition: "all 0.2s ease",
              }}
              onMouseOver={(e) => {
                e.target.style.background = "rgba(20, 3, 66, 0.2)";
                e.target.style.transform = "scale(1.1)";
              }}
              onMouseOut={(e) => {
                e.target.style.background = "rgba(20, 3, 66, 0.1)";
                e.target.style.transform = "scale(1)";
              }}
            >
              ×
            </button>
            <h3
              style={{
                color: "#140342",
                fontWeight: "700",
                marginBottom: "24px",
                fontSize: "24px",
                fontFamily: "Poppins, sans-serif",
                textAlign: "center",
              }}
            >
              Choose Your Exam Type
            </h3>
            <div
              style={{
                padding: "20px",
                borderRadius: "16px",
                background: "rgba(20, 3, 66, 0.05)",
                marginBottom: "16px",
                border: "1px solid rgba(20, 3, 66, 0.1)",
              }}
            >
              <p
                style={{
                  color: "#140342",
                  marginBottom: "0",
                  fontSize: "15px",
                  lineHeight: "1.6",
                }}
              >
                <strong>PTE Academic / UKVI:</strong> Used for global university
                applications, various Australian and New Zealand visa
                applications (e.g., immigration or work visas), and UK work or
                student visa applications.
              </p>
            </div>
            <div
              style={{
                padding: "20px",
                borderRadius: "16px",
                background: "rgba(20, 3, 66, 0.05)",
                marginBottom: "24px",
                border: "1px solid rgba(20, 3, 66, 0.1)",
              }}
            >
              <p
                style={{
                  color: "#140342",
                  marginBottom: "0",
                  fontSize: "15px",
                  lineHeight: "1.6",
                }}
              >
                <strong>PTE Core:</strong> Used for Canadian immigration or work
                visa applications.
              </p>
            </div>
            <p
              style={{
                color: "#140342",
                marginBottom: "32px",
                textAlign: "center",
                fontSize: "14px",
              }}
            >
              Not sure which one to choose?{" "}
              <a
                href="#"
                style={{
                  color: "#140342",
                  fontWeight: "600",
                  textDecoration: "underline",
                }}
              >
                Learn more about the differences
              </a>
            </p>
            <div
              style={{ display: "flex", gap: "16px", justifyContent: "center" }}
            >
              <button
                onClick={() => handleModalConfirm("PTE Academic")}
                style={{
                  background:
                    "linear-gradient(135deg, #140342 0%, #1d0659 100%)",
                  color: "#fff",
                  padding: "16px 32px",
                  border: "none",
                  borderRadius: "12px",
                  cursor: "pointer",
                  fontWeight: "600",
                  fontSize: "15px",
                  fontFamily: "Poppins, sans-serif",
                  boxShadow: "0 8px 20px rgba(20, 3, 66, 0.3)",
                  transition: "all 0.3s ease",
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.transform = "translateY(-2px)";
                  e.currentTarget.style.boxShadow =
                    "0 12px 30px rgba(20, 3, 66, 0.4)";
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.transform = "translateY(0)";
                  e.currentTarget.style.boxShadow =
                    "0 8px 20px rgba(20, 3, 66, 0.3)";
                }}
              >
                PTE Academic / UKVI
              </button>
              <button
                onClick={() => handleModalConfirm("PTE Core")}
                style={{
                  background:
                    "linear-gradient(135deg, #1C1C1C 0%, #2a2a2a 100%)",
                  color: "#fff",
                  padding: "16px 32px",
                  border: "none",
                  borderRadius: "12px",
                  cursor: "pointer",
                  fontWeight: "600",
                  fontSize: "15px",
                  fontFamily: "Poppins, sans-serif",
                  boxShadow: "0 8px 20px rgba(28, 28, 28, 0.3)",
                  transition: "all 0.3s ease",
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.transform = "translateY(-2px)";
                  e.currentTarget.style.boxShadow =
                    "0 12px 30px rgba(28, 28, 28, 0.4)";
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.transform = "translateY(0)";
                  e.currentTarget.style.boxShadow =
                    "0 8px 20px rgba(28, 28, 28, 0.3)";
                }}
              >
                PTE Core
              </button>
            </div>
          </div>
        </div>
      )}

      <div
        className={`header-menu js-mobile-menu-toggle ${
          headerPosition ? headerPosition : ""
        }`}
      >
        <div className="header-menu__content">
          <div className="mobile-bg js-mobile-bg"></div>

          <div className="d-none xl:d-flex items-center px-20 py-20 border-bottom-light">
            <Link to="/login" className="text-dark-1">
              Log in
            </Link>
            <Link to="/signup" className="text-dark-1 ml-30">
              Sign Up
            </Link>
          </div>

          <div className="menu js-navList">
            <ul className={`${allClasses ? allClasses : ""}`}>
              <li className="menu-item-has-children">
                <Link
                  data-barba
                  to="/"
                  className={menuItem === "Home" ? "activeMenu" : ""}
                >
                  Home
                </Link>
              </li>

              <li className="menu-item-has-children -has-mega-menu">
                <Link
                  data-barba
                  to="#"
                  className={menuItem === "Courses" ? "activeMenu" : ""}
                >
                  Courses <i className="icon-chevron-right text-13 ml-10"></i>
                </Link>

                <div
                  className="mega xl:d-none custom-scrollbar"
                  style={{
                    background: "rgba(255, 255, 255, 0.98)",
                    backdropFilter: "blur(20px)",
                    border: "1px solid rgba(20, 3, 66, 0.1)",
                    borderRadius: "20px",
                    boxShadow: "0 25px 50px rgba(20, 3, 66, 0.15)",
                    padding: "32px",
                    margin: "20px 0",
                    maxHeight: "80vh",
                    overflowY: "auto",
                  }}
                >
                  <div className="mega__menu">
                    <div className="flex-row x-gap-40">
                      <div className="flex-row">
                        {/* Modern Tabs for selecting between PTE Core and PTE Academic */}
                        <div
                          className="tabs"
                          style={{
                            display: "flex",
                            flexDirection: "row",
                            width: "full",
                            gap: "16px",
                            marginBottom: "32px",
                            padding: "8px",
                            background: "rgba(20, 3, 66, 0.05)",
                            borderRadius: "16px",
                            border: "1px solid rgba(20, 3, 66, 0.1)",
                          }}
                        >
                          <button
                            className={`tab ${
                              selectedTab === "PTE Core" ? "active" : ""
                            }`}
                            onClick={() => handleTabChange()}
                            style={{
                              fontWeight: "600",
                              background:
                                selectedTab === "PTE Core"
                                  ? "linear-gradient(135deg, #140342 0%, #1d0659 100%)"
                                  : "transparent",
                              padding: "12px 24px",
                              borderRadius: "12px",
                              color:
                                selectedTab === "PTE Core" ? "#fff" : "#140342",
                              border: "none",
                              boxShadow:
                                selectedTab === "PTE Core"
                                  ? "0 4px 15px rgba(20, 3, 66, 0.3)"
                                  : "none",
                              transition: "all 0.3s ease",
                              fontSize: "14px",
                              fontFamily: "Poppins, sans-serif",
                              cursor: "pointer",
                            }}
                          >
                            PTE Core
                          </button>
                          <button
                            className={`tab ${
                              selectedTab === "PTE Academic" ? "active" : ""
                            }`}
                            onClick={() => handleTabChange()}
                            style={{
                              fontWeight: "600",
                              background:
                                selectedTab === "PTE Academic"
                                  ? "linear-gradient(135deg, #1C1C1C 0%, #2a2a2a 100%)"
                                  : "transparent",
                              padding: "12px 24px",
                              borderRadius: "12px",
                              color:
                                selectedTab === "PTE Academic"
                                  ? "#fff"
                                  : "#140342",
                              border: "none",
                              boxShadow:
                                selectedTab === "PTE Academic"
                                  ? "0 4px 15px rgba(28, 28, 28, 0.3)"
                                  : "none",
                              transition: "all 0.3s ease",
                              fontSize: "14px",
                              fontFamily: "Poppins, sans-serif",
                              cursor: "pointer",
                            }}
                          >
                            PTE Academic / UKVI
                          </button>
                        </div>

                        {/* Modern Subcategory content based on selected tab */}
                        <div
                          style={{
                            display: "flex",
                            flexDirection: "row",
                            gap: "40px",
                          }}
                        >
                          {category.map((item) => {
                            // Sort items: active first, then inactive
                            const sortedItems = [...item.data].sort((a, b) => {
                              const isActiveA =
                                selectedTab === "PTE Core"
                                  ? a.activeInCore
                                  : a.activeInAcademic;
                              const isActiveB =
                                selectedTab === "PTE Core"
                                  ? b.activeInCore
                                  : b.activeInAcademic;
                              return isActiveA === isActiveB
                                ? 0
                                : isActiveA
                                ? -1
                                : 1;
                            });

                            // Split into active and inactive items
                            const activeItems = sortedItems.filter((elm) =>
                              selectedTab === "PTE Core"
                                ? elm.activeInCore
                                : elm.activeInAcademic
                            );
                            const inactiveItems = sortedItems.filter(
                              (elm) =>
                                !(selectedTab === "PTE Core"
                                  ? elm.activeInCore
                                  : elm.activeInAcademic)
                            );

                            const categoryColor =
                              item.name === "speaking"
                                ? "#4CAF50"
                                : item.name === "writing"
                                ? "#FF9800"
                                : item.name === "reading"
                                ? "#2196F3"
                                : "#9C27B0";

                            return (
                              <div
                                className="flex flex-col"
                                key={item.name}
                                style={{
                                  minWidth: "220px",
                                  padding: "20px",
                                  background: "rgba(255, 255, 255, 0.7)",
                                  borderRadius: "16px",
                                  border: `1px solid ${categoryColor}20`,
                                }}
                              >
                                <div
                                  style={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: "12px",
                                    marginBottom: "20px",
                                  }}
                                >
                                  <span style={{ fontSize: "24px" }}>
                                    {item.name === "speaking"
                                      ? "🎤"
                                      : item.name === "writing"
                                      ? "✍️"
                                      : item.name === "reading"
                                      ? "📖"
                                      : "🎧"}
                                  </span>
                                  <h4
                                    className="text-17 fw-600"
                                    style={{
                                      color: "#140342",
                                      margin: 0,
                                      fontFamily: "Poppins, sans-serif",
                                      textTransform: "uppercase",
                                      letterSpacing: "0.5px",
                                    }}
                                  >
                                    {item.name}
                                  </h4>
                                </div>
                                <ul
                                  className={`mega__list ${
                                    item.name === "listening"
                                      ? "listening-two-columns"
                                      : ""
                                  }`}
                                  style={{
                                    listStyle: "none",
                                    padding: 0,
                                    margin: 0,
                                  }}
                                >
                                  {/* Active Items */}
                                  {activeItems.map((elm) => (
                                    <div
                                      key={elm.categoryId}
                                      style={{
                                        display: "flex",
                                        flexDirection: "column",
                                        marginBottom: "12px",
                                      }}
                                    >
                                      <li
                                        className="cursor-pointer"
                                        onClick={() =>
                                          handleSubCategoryClick(
                                            elm?.categoryId,
                                            item?.name,
                                            true
                                          )
                                        }
                                        style={{
                                          color: "#140342",
                                          cursor: "pointer",
                                          transition: "all 0.2s ease",
                                          padding: "12px 16px",
                                          borderRadius: "12px",
                                          background:
                                            "rgba(255, 255, 255, 0.8)",
                                          border:
                                            "1px solid rgba(20, 3, 66, 0.1)",
                                          fontSize: "14px",
                                          fontWeight: "500",
                                          fontFamily: "Poppins, sans-serif",
                                          display: "flex",
                                          alignItems: "center",
                                          justifyContent: "space-between",
                                        }}
                                        onMouseOver={(e) => {
                                          e.target.style.transform =
                                            "translateX(4px)";
                                          e.target.style.background =
                                            "rgba(20, 3, 66, 0.05)";
                                          e.target.style.borderColor =
                                            categoryColor;
                                        }}
                                        onMouseOut={(e) => {
                                          e.target.style.transform =
                                            "translateX(0)";
                                          e.target.style.background =
                                            "rgba(255, 255, 255, 0.8)";
                                          e.target.style.borderColor =
                                            "rgba(20, 3, 66, 0.1)";
                                        }}
                                      >
                                        <span>{elm.name}</span>
                                        {elm?.isAiBased && (
                                          <span
                                            style={{
                                              color: "#140342",
                                              fontSize: "10px",
                                              fontWeight: "700",
                                              backgroundColor:
                                                "rgba(20, 3, 66, 0.1)",
                                              padding: "4px 8px",
                                              borderRadius: "6px",
                                            }}
                                          >
                                            AI
                                          </span>
                                        )}
                                      </li>
                                    </div>
                                  ))}

                                  {/* Divider if there are both active and inactive items */}
                                  {activeItems.length > 0 &&
                                    inactiveItems.length > 0 && (
                                      <li
                                        style={{
                                          borderTop:
                                            "1px solid rgba(20, 3, 66, 0.1)",
                                          margin: "16px 0",
                                        }}
                                      ></li>
                                    )}

                                  {/* Inactive Items - Fixed to use the separate handler */}
                                  {inactiveItems.map((elm) => (
                                    <div
                                      key={elm.categoryId}
                                      style={{
                                        display: "flex",
                                        flexDirection: "column",
                                        marginBottom: "12px",
                                      }}
                                    >
                                      <li
                                        className="cursor-pointer"
                                        onClick={() =>
                                          handleSubCategoryClick(
                                            elm?.categoryId,
                                            item?.name,
                                            false
                                          )
                                        }
                                        style={{
                                          color: "#A9A9A9",
                                          cursor: "pointer",
                                          padding: "12px 16px",
                                          borderRadius: "12px",
                                          background:
                                            "rgba(169, 169, 169, 0.1)",
                                          border:
                                            "1px solid rgba(169, 169, 169, 0.2)",
                                          fontSize: "14px",
                                          fontWeight: "500",
                                          fontFamily: "Poppins, sans-serif",
                                          transition: "all 0.2s ease",
                                          display: "flex",
                                          alignItems: "center",
                                          justifyContent: "space-between",
                                        }}
                                        onMouseOver={(e) => {
                                          e.target.style.background =
                                            "rgba(169, 169, 169, 0.15)";
                                        }}
                                        onMouseOut={(e) => {
                                          e.target.style.background =
                                            "rgba(169, 169, 169, 0.1)";
                                        }}
                                      >
                                        <span>{elm.name}</span>
                                        {elm?.isAiBased && (
                                          <span
                                            style={{
                                              color: "#A9A9A9",
                                              fontSize: "10px",
                                              fontWeight: "700",
                                              backgroundColor:
                                                "rgba(169, 169, 169, 0.2)",
                                              padding: "4px 8px",
                                              borderRadius: "6px",
                                            }}
                                          >
                                            AI
                                          </span>
                                        )}
                                      </li>
                                    </div>
                                  ))}
                                </ul>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </li>

              <li className="menu-item-has-children">
                <Link
                  data-barba
                  to="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handleProtectedNavigation("/dashboard");
                  }}
                  className={pathname === "/dashboard" ? "activeMenu" : ""}
                >
                  PTE Practice
                </Link>
              </li>

              <li className="menu-item-has-children">
                <Link
                  data-barba
                  to="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handleProtectedNavigation("/naati");
                  }}
                  className={pathname === "/naati" ? "activeMenu" : ""}
                >
                  PTE NAATI
                </Link>
              </li>
            </ul>
          </div>

          <MobileFooter />
        </div>

        <div
          className="header-menu-close"
          data-el-toggle=".js-mobile-menu-toggle"
        >
          <div
            className="size-40 d-flex items-center justify-center rounded-full bg-white"
            style={{
              boxShadow: "0 8px 25px rgba(20, 3, 66, 0.2)",
              transition: "all 0.3s ease",
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.transform = "scale(1.1)";
              e.currentTarget.style.boxShadow =
                "0 12px 35px rgba(20, 3, 66, 0.3)";
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.transform = "scale(1)";
              e.currentTarget.style.boxShadow =
                "0 8px 25px rgba(20, 3, 66, 0.2)";
            }}
          >
            <div className="icon-close text-dark-1 text-16"></div>
          </div>
        </div>

        <div className="header-menu-bg"></div>
      </div>
    </>
  );
}

Menu.propTypes = {
  allClasses: PropTypes.string,
  headerPosition: PropTypes.string,
  onSubCategoryOpen: PropTypes.func.isRequired,
};