import { Link } from "react-router-dom";
export default function MobileFooter() {
  const isLogin = localStorage.getItem("isAuthenticated");
  return (
    <>
      <div className="mobile-footer px-20 py-20 border-top-light js-mobile-footer">
        {isLogin && (
          <div className="d-flex justify-center mb-20">
            <Link
              to="/pricing"
              className="button -sm -purple-1 text-white w-100"
            >
              Upgrade Your Plan
            </Link>
          </div>
        )}
        <div className="mobile-footer__number">
          <div className="text-17 fw-500 text-dark-1">Call us</div>
          <div className="text-17 fw-500 text-purple-1">+**************</div>
        </div>

        <div className="lh-2 mt-10">
          <div>
            Address: 8 Rugby Cres,
            <br /> Truganina VIC 3029, Australia
          </div>
        </div>

        <div className="mobile-socials mt-10">
          <Link
            to="#"
            className="d-flex items-center justify-center rounded-full size-40"
          >
            <i className="fa fa-facebook"></i>
          </Link>

          <Link
            to="#"
            className="d-flex items-center justify-center rounded-full size-40"
          >
            <i className="fa fa-twitter"></i>
          </Link>

          <Link
            to="#"
            className="d-flex items-center justify-center rounded-full size-40"
          >
            <i className="fa fa-instagram"></i>
          </Link>

          <Link
            to="#"
            className="d-flex items-center justify-center rounded-full size-40"
          >
            <i className="fa fa-linkedin"></i>
          </Link>
        </div>
      </div>
    </>
  );
}
