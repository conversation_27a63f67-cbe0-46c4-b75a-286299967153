import MobileFooter from "./MobileFooter";
import { menuList } from "../../../data/menu";
import { Link, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { useAllCategory } from "@/api/hooks/response";
import Loader from "@/components/common/Loader";
import LoginModal from "@/components/Modal/LoginModal";
import PropTypes from "prop-types";

export default function MobileMenu({
  setActiveMobileMenu,
  activeMobileMenu,
  onSubCategoryOpen,
}) {
  const navigate = useNavigate();
  const [showMenu, setShowMenu] = useState(false);
  const [menuNesting, setMenuNesting] = useState([]);
  const [menuItem, setMenuItem] = useState("");
  const [submenu, setSubmenu] = useState("");
  const [coursesOpen, setCoursesOpen] = useState(false); // State for Courses dropdown
  const [subCategoryOpen, setSubCategoryOpen] = useState({}); // State for subcategories
  const [selectedSubCategoryId, setSelectedSubCategoryId] = useState(null); // State for selected subcategory
  const { category, loading } = useAllCategory();
  const { pathname } = useLocation();
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    menuList.forEach((elm) => {
      elm?.links?.forEach((elm2) => {
        if (elm2.href?.split("/")[1] === pathname?.split("/")[1]) {
          setMenuItem(elm.title);
        } else {
          elm2?.links?.forEach((elm3) => {
            if (elm3.href?.split("/")[1] === pathname?.split("/")[1]) {
              setMenuItem(elm.title);
              setSubmenu(elm2.title);
            }
          });
        }
      });
    });
  }, [pathname]);

  useEffect(() => {
    setShowMenu(true);
  }, []);

  useEffect(() => {
    if (selectedSubCategoryId) {
      const fetchCategory = async () => {
        console.log("Fetching subcategories for ID:", selectedSubCategoryId);
        onSubCategoryOpen(selectedSubCategoryId); // Call the function to fetch or open subcategory data
      };
      fetchCategory();
    }
  }, [selectedSubCategoryId, onSubCategoryOpen]);

  if (loading) return <Loader />;

  // Updated handleSubCategoryClick to directly open categories
  const handleSubCategoryClick = async (subCategoryId) => {
    const isAuthenticated = localStorage.getItem("isUserId");

    if (!isAuthenticated) {
      setIsModalOpen(true); // Show login modal if not authenticated
      return;
    }

    // User is authenticated, directly proceed to questions
    setSelectedSubCategoryId(subCategoryId);
  };

  // Handle navigation for protected routes
  const handleProtectedNavigation = (path) => {
    const isAuthenticated = localStorage.getItem("isUserId");
    if (!isAuthenticated) {
      navigate("/login");
    } else {
      navigate(path);
    }
    setActiveMobileMenu(false); // Close mobile menu after navigation
  };

  return (
    <div
      className={`header-menu js-mobile-menu-toggle ${
        activeMobileMenu ? "-is-el-visible" : ""
      }`}
    >
      <LoginModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
      <div className="header-menu__content">
        <div className="mobile-bg js-mobile-bg"></div>

        {showMenu && activeMobileMenu && (
          <div className="mobileMenu text-dark-1">
            {menuList.map((elm, i) => {
              if (elm.title) {
                return (
                  <div key={i} className="submenuOne">
                    <div
                      style={{ marginBottom: 10 }}
                      className="title"
                      onClick={() => {
                        setMenuNesting((pre) =>
                          pre[0] === elm.title ? [] : [elm.title]
                        );
                        if (elm.title === "Courses") {
                          setCoursesOpen((prev) => !prev); // Toggle Courses dropdown
                        }
                      }}
                    >
                      <span
                        className={
                          elm.title === menuItem ? "activeMenu" : "inActiveMenu"
                        }
                      >
                        {elm.title}
                      </span>
                      {elm.links.length > 0 && (
                        <i
                          className={
                            menuNesting[0] === elm.title
                              ? "icon-chevron-right text-13 ml-10 active"
                              : "icon-chevron-right text-13 ml-10"
                          }
                        ></i>
                      )}
                    </div>

                    {/* Handle Courses Dropdown */}
                    {elm.title === "Courses" && coursesOpen && (
                      <div>
                        {category.map((item) => (
                          <div className="flex flex-col" key={item.id}>
                            <div
                              style={{
                                display: "flex",
                                flexDirection: "row",
                                justifyContent: "space-between",
                                marginLeft: 10,
                                marginRight: 10,
                              }}
                              onClick={() =>
                                setSubCategoryOpen((prev) => ({
                                  ...prev,
                                  [item.id]: !prev[item.id], // Toggle the sub-category
                                }))
                              }
                            >
                              <h4 className="text-17 fw-500 mb-10 mt-10 ml-20">
                                {item.name}
                              </h4>
                              <i
                                className={
                                  subCategoryOpen[item.id]
                                    ? "icon-chevron-down"
                                    : "icon-chevron-right"
                                }
                              ></i>
                            </div>

                            {subCategoryOpen[item.id] && (
                              <ul
                                className="mega__list"
                                style={{ marginLeft: 40 }}
                              >
                                {item.sub_categories.map((subItem) => (
                                  <li key={subItem.id} className="activeMenu">
                                    <Link
                                      to="#"
                                      onClick={() =>
                                        handleSubCategoryClick(subItem.id)
                                      }
                                    >
                                      {subItem.name}
                                    </Link>
                                  </li>
                                ))}
                              </ul>
                            )}
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Handle Other Dropdown Items */}
                    {elm.title !== "Courses" && (
                      <>
                        {elm.links.map((itm, index) => (
                          <div
                            key={index}
                            className={
                              menuNesting[0] === elm.title
                                ? "toggle active"
                                : "toggle"
                            }
                          >
                            {itm.href && (
                              <Link
                                className={
                                  pathname?.split("/")[1] ===
                                  itm.href?.split("/")[1]
                                    ? "activeMenu link"
                                    : "link inActiveMenu"
                                }
                                to={itm.href}
                              >
                                {itm.label}
                              </Link>
                            )}
                            {itm.links && (
                              <div className="submenuTwo">
                                <div
                                  className="title"
                                  onClick={() =>
                                    setMenuNesting((pre) =>
                                      pre[1] === itm.title
                                        ? [pre[0]]
                                        : [pre[0], itm.title]
                                    )
                                  }
                                >
                                  <span
                                    className={
                                      itm.title === submenu
                                        ? "activeMenu"
                                        : "inActiveMenu"
                                    }
                                  >
                                    {itm.title && itm.title}
                                  </span>
                                  <i
                                    className={
                                      menuNesting[1] === itm.title
                                        ? "icon-chevron-right text-13 ml-10 active"
                                        : "icon-chevron-right text-13 ml-10"
                                    }
                                  ></i>
                                </div>
                                <div
                                  className={
                                    menuNesting[1] === itm.title
                                      ? "toggle active"
                                      : "toggle"
                                  }
                                >
                                  {itm.links.map((itm2, index3) => (
                                    <Link
                                      key={index3}
                                      className={
                                        pathname?.split("/")[1] ===
                                        itm2.href?.split("/")[1]
                                          ? "activeMenu link"
                                          : "link inActiveMenu"
                                      }
                                      to={itm2.href}
                                    >
                                      {itm2.label}
                                    </Link>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </>
                    )}
                  </div>
                );
              }
              return null; // Added return null for better clarity
            })}

            {/* Additional Navigation Links */}
            <div
              className="submenuOne"
              style={{
                borderTop: "1px solid #E0E0E0",
                paddingTop: "15px",
                marginTop: "15px",
              }}
            >
              <div
                className="title"
                onClick={() => handleProtectedNavigation("/dashboard")}
                style={{ marginBottom: 10, cursor: "pointer" }}
              >
                <span className="inActiveMenu">PTE Practice</span>
              </div>
              <div
                className="title"
                onClick={() => handleProtectedNavigation("/naati")}
                style={{ marginBottom: 10, cursor: "pointer" }}
              >
                <span className="inActiveMenu">PTE NAATI</span>
              </div>
            </div>
          </div>
        )}

        {/* Mobile Footer */}
        <MobileFooter />
      </div>

      {/* Close Button */}
      <div
        className="header-menu-close"
        onClick={() => setActiveMobileMenu(false)}
        data-el-toggle=".js-mobile-menu-toggle"
      >
        <div className="size-40 d-flex items-center justify-center rounded-full bg-white">
          <div className="icon-close text-dark-1 text-16"></div>
        </div>
      </div>

      <div
        className="header-menu-bg"
        onClick={() => setActiveMobileMenu(false)}
      ></div>
    </div>
  );
}

MobileMenu.propTypes = {
  setActiveMobileMenu: PropTypes.func.isRequired,
  activeMobileMenu: PropTypes.bool.isRequired,
  onSubCategoryOpen: PropTypes.func.isRequired,
};
