import { useState, useEffect } from "react";

import <PERSON><PERSON> from "../component/Menu";
import { Link } from "react-router-dom";
import MobileMenu from "../component/MobileMenu";
import PropTypes from "prop-types";
import axios from "axios";
import { server } from "@/api/services/server";

export default function Header({ onSubCategoryOpen }) {
  const [activeMobileMenu, setActiveMobileMenu] = useState(false);
  const [userSubscription, setUserSubscription] = useState(null);
  const [subscriptionLoading, setSubscriptionLoading] = useState(false);
  const isLogin = localStorage.getItem("isAuthenticated");

  // Fetch user subscription data when logged in
  useEffect(() => {
    const fetchUserSubscription = async () => {
      if (!isLogin) {
        setUserSubscription(null);
        return;
      }

      setSubscriptionLoading(true);
      try {
        const userId = localStorage.getItem("isUserId");
        if (userId) {
          const response = await axios.get(`${server.uri}users/${userId}`);
          const userData = response.data;

          console.log("User subscription data:", userData);

          // Check if user has active or cancelled but not expired subscription
          if (userData && userData.membershipStatus === "active") {
            setUserSubscription(userData);
          } else if (
            userData &&
            userData.membershipStatus === "cancelled" &&
            new Date(userData.membershipEndAt) > new Date()
          ) {
            setUserSubscription(userData);
          } else {
            setUserSubscription(null);
          }
        }
      } catch (error) {
        console.error("Error fetching user subscription:", error);
        setUserSubscription(null);
      } finally {
        setSubscriptionLoading(false);
      }
    };

    fetchUserSubscription();
  }, [isLogin]);

  // Helper function to get plan name based on payment amount
  const getPlanNameFromAmount = (amount) => {
    if (amount === 20) return "Basic";
    if (amount === 45) return "Standard";
    if (amount === 110) return "Premium";
    return "Premium"; // fallback
  };

  // Helper function to get plan name from user subscription
  const getUserPlanName = () => {
    if (!userSubscription) return "Premium";

    // Check if there's a membershipPlan object with planName
    if (userSubscription.membershipPlan?.planName) {
      return userSubscription.membershipPlan.planName;
    }

    // Otherwise, determine from payment amount
    const amount = userSubscription.paymentDetails?.amount;
    if (amount) {
      return getPlanNameFromAmount(amount);
    }

    return "Premium"; // fallback
  };

  // Helper function to check if user has valid subscription
  const hasValidSubscription = () => {
    if (!userSubscription) return false;

    if (userSubscription.membershipStatus === "active") return true;

    if (userSubscription.membershipStatus === "cancelled") {
      const endDate = new Date(userSubscription.membershipEndAt);
      const today = new Date();
      return endDate > today;
    }

    return false;
  };

  const onLogoutClick = () => {
    localStorage.removeItem("isAuthenticated");
    localStorage.removeItem("isUserId");
    localStorage.removeItem("user");
    localStorage.removeItem("userEmail");
    localStorage.removeItem("token");
    window.location.href = "/";
  };

  const handleSubCategoryOpen = (data) => {
    console.log(data, "..");
    if (data) {
      onSubCategoryOpen(data);
    }
  };

  return (
    <>
      <header className="header -type-1 ">
        <div className="header__container">
          <div className="row justify-between items-center">
            <div className="col-auto">
              <div className="header-left">
                <div className="header__logo ">
                  <Link to="/">
                    <img
                      src="/assets/img/general/logo.png"
                      alt="logo"
                      style={{ height: "40px", width: "auto" }}
                    />
                  </Link>
                </div>
                {/* <HeaderExplore
                  allClasses={
                    "header__explore text-green-1 ml-60 xl:ml-30 xl:d-none"
                  }
                /> */}
              </div>
            </div>

            <Menu
              allClasses={"menu__nav text-white -is-active"}
              onSubCategoryOpen={handleSubCategoryOpen}
            />
            <MobileMenu
              setActiveMobileMenu={setActiveMobileMenu}
              activeMobileMenu={activeMobileMenu}
              onSubCategoryOpen={handleSubCategoryOpen}
            />

            <div className="col-auto">
              <div className="header-right d-flex items-center">
                <div className="header-right__icons text-white d-flex items-center">
                  {/* <SearchToggle />
                  <CartToggle
                    parentClassess={"relative ml-30 xl:ml-20"}
                    allClasses={"d-flex items-center text-white"}
                  /> */}
                  <div className="d-none xl:d-block ml-20">
                    <button
                      onClick={() => setActiveMobileMenu(true)}
                      className="text-white items-center"
                    >
                      <i className="text-11 icon icon-mobile-menu"></i>
                    </button>
                  </div>
                </div>
                {isLogin ? (
                  <div className="header-right__buttons d-flex items-center ml-30 md:d-none">
                    {subscriptionLoading ? (
                      <div
                        className="button -sm -purple-1 text-white mr-30"
                        style={{ opacity: 0.7 }}
                      >
                        Loading...
                      </div>
                    ) : hasValidSubscription() ? (
                      <Link
                        to="/pricing"
                        className="button -sm -green-1 text-white mr-30"
                        style={{
                          background:
                            userSubscription?.membershipStatus === "cancelled"
                              ? "#F79009"
                              : "#027A48",
                          border: `1px solid ${
                            userSubscription?.membershipStatus === "cancelled"
                              ? "#F79009"
                              : "#027A48"
                          }`,
                        }}
                      >
                        {getUserPlanName()} Plan
                        {userSubscription?.membershipStatus === "cancelled" &&
                          " (Expiring)"}
                      </Link>
                    ) : (
                      <Link
                        to="/pricing"
                        className="button -sm -purple-1 text-white mr-30"
                      >
                        Upgrade
                      </Link>
                    )}
                    <Link
                      onClick={onLogoutClick}
                      className="button -underline text-white"
                    >
                      Logout
                    </Link>
                  </div>
                ) : (
                  <div className="header-right__buttons d-flex items-center ml-30 md:d-none">
                    <Link to="/login" className="button -underline text-white">
                      Log in
                    </Link>
                    <Link
                      to="/signup"
                      className="button -sm -white text-dark-1 ml-30"
                    >
                      Sign up
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </header>
    </>
  );
}

Header.propTypes = {
  onSubCategoryOpen: PropTypes.func,
};
