import { useState, useEffect, useRef } from "react";
import PropTypes from "prop-types";
import { Switch } from "@mui/material";
import AudioPlayer from "react-audio-player";
import SidebarToggle from "../common/SidebarToggle";
// Practice tracking imports
import {
  logPracticeAttempt,
  usePracticeInfo,
} from "../speech-ace-component/utils/practiceUtils";

const FillInTheBlanks = ({ question, onQuestionSelect }) => {
  const audioRef = useRef(null);

  const [userAnswers, setUserAnswers] = useState([]);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [showAnswer, setShowAnswer] = useState(false);
  const [timeLeft, setTimeLeft] = useState(7 * 60); // 7 minutes in seconds
  const [timerActive, setTimerActive] = useState(false);
  const [score, setScore] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const maxScore = question?.maxScore || question?.blanks?.length || 0;

  // Practice tracking
  const { setPracticeCount, setIsPracticed } = usePracticeInfo(
    question.questionId
  );

  // Reset state when question changes
  useEffect(() => {
    setUserAnswers(new Array(question?.blanks?.length || 0).fill(""));
    setIsSubmitted(false);
    setShowAnswer(false);
    setScore(0);
    setTimeLeft(7 * 60);
    setTimerActive(false);
    setIsPlaying(false);
  }, [question?.questionId]);

  // Initialize timer
  useEffect(() => {
    let timer;
    if (timerActive && timeLeft > 0) {
      timer = setInterval(() => {
        setTimeLeft((prev) => prev - 1);
      }, 1000);
    } else if (timeLeft === 0 && timerActive) {
      handleDone();
    }
    return () => clearInterval(timer);
  }, [timerActive, timeLeft]);

  // Format time as MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  useEffect(() => {
    const handleResize = () => setScreenWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Auto play audio when component mounts
  useEffect(() => {
    const playAudio = async () => {
      try {
        const audioUrl = question?.media?.url;
        if (!audioUrl) {
          console.warn("No audio URL provided for this question");
          return;
        }

        if (
          audioRef.current &&
          audioRef.current.audioEl &&
          audioRef.current.audioEl.current
        ) {
          audioRef.current.audioEl.current.load();
          await new Promise((resolve) => setTimeout(resolve, 1000));
          audioRef.current.audioEl.current.currentTime = 0;

          const playPromise = audioRef.current.audioEl.current.play();
          if (playPromise !== undefined) {
            playPromise
              .then(() => {
                console.log("Audio started playing automatically");
                setIsPlaying(true);
                if (!timerActive) {
                  setTimerActive(true);
                }
              })
              .catch((error) => {
                console.warn("Auto-play was prevented:", error);
              });
          }
        }
      } catch (error) {
        console.error("Error in audio playback:", error);
      }
    };

    playAudio();
  }, [question?.media?.url]);

  // Handle audio end
  const handleAudioEnd = () => {
    setIsPlaying(false);
  };

  const isSmallScreen = screenWidth <= 768;

  // Handle input change
  const handleInputChange = (index, value) => {
    const newAnswers = [...userAnswers];
    newAnswers[index] = value;
    setUserAnswers(newAnswers);
  };

  // Calculate score based on correct answers
  const calculateScore = () => {
    let correctCount = 0;
    userAnswers.forEach((answer, index) => {
      const correctAnswer = question?.blanks[index]?.correctAnswer;
      if (answer.trim().toLowerCase() === correctAnswer.trim().toLowerCase()) {
        correctCount++;
      }
    });
    return Math.round(
      (correctCount / (question?.blanks?.length || 1)) * maxScore
    );
  };

  // Check if answer is correct
  const isAnswerCorrect = (index) => {
    const userAnswer = userAnswers[index];
    const correctAnswer = question?.blanks[index]?.correctAnswer;
    return (
      userAnswer.trim().toLowerCase() === correctAnswer.trim().toLowerCase()
    );
  };

  const renderContentWithBlanks = () => {
    if (!question?.content) return null;

    const parts = question.content.split("_____");
    return parts.map((part, idx) => (
      <span key={idx} style={{ fontSize: "18px", lineHeight: "2.5" }}>
        {part}
        {idx < (question?.blanks?.length || 0) && (
          <span
            style={{
              display: "inline-flex",
              alignItems: "center",
              justifyContent: "center",
              minWidth: "120px",
              height: "45px",
              border: `2px ${userAnswers[idx] ? "solid" : "dashed"} ${
                isSubmitted || showAnswer
                  ? isAnswerCorrect(idx)
                    ? "#28a745"
                    : "#dc3545"
                  : userAnswers[idx]
                  ? "#140342"
                  : "#ccc"
              }`,
              borderRadius: "8px",
              margin: "0 6px",
              padding: "8px 12px",
              backgroundColor:
                isSubmitted || showAnswer
                  ? isAnswerCorrect(idx)
                    ? "#f8fff8"
                    : "#fff8f8"
                  : userAnswers[idx]
                  ? "#f8f9ff"
                  : "white",
              transition: "all 0.3s ease",
              verticalAlign: "middle",
              boxShadow: userAnswers[idx]
                ? "0 2px 8px rgba(20, 3, 66, 0.15)"
                : "0 1px 3px rgba(0,0,0,0.1)",
              position: "relative",
            }}
          >
            <input
              type="text"
              value={
                showAnswer
                  ? question?.blanks[idx]?.correctAnswer
                  : userAnswers[idx] || ""
              }
              onChange={(e) =>
                !showAnswer && handleInputChange(idx, e.target.value)
              }
              disabled={isSubmitted || showAnswer}
              placeholder=""
              style={{
                width: "100%",
                height: "100%",
                border: "none",
                background: "transparent",
                fontSize: "16px",
                fontWeight: "700",
                fontStyle: "italic",
                textAlign: "center",
                color:
                  isSubmitted || showAnswer
                    ? isAnswerCorrect(idx)
                      ? "#28a745"
                      : "#dc3545"
                    : "#140342",
                outline: "none",
                textDecoration:
                  isSubmitted || showAnswer
                    ? "none"
                    : userAnswers[idx]
                    ? "underline"
                    : "none",
                textDecorationColor: "#140342",
                textDecorationStyle: "solid",
                textUnderlineOffset: "2px",
              }}
            />
            {(isSubmitted || showAnswer) && (
              <span
                style={{
                  position: "absolute",
                  right: "-25px",
                  fontSize: "16px",
                  fontWeight: "bold",
                  color: isAnswerCorrect(idx) ? "#28a745" : "#dc3545",
                }}
              >
                {isAnswerCorrect(idx) ? "✓" : "✗"}
              </span>
            )}
          </span>
        )}
      </span>
    ));
  };

  const handleDone = async () => {
    setIsSubmitted(true);
    setTimerActive(false);
    const calculatedScore = calculateScore();
    setScore(calculatedScore);

    // Log practice attempt
    const result = await logPracticeAttempt(question.questionId);
    if (result.success) {
      setPracticeCount(result.practiceCount);
      setIsPracticed(true);
    }
  };

  const handleRedo = () => {
    setUserAnswers(new Array(question?.blanks?.length || 0).fill(""));
    setIsSubmitted(false);
    setShowAnswer(false);
    setScore(0);
    setTimeLeft(7 * 60); // Reset to 7 minutes
    setTimerActive(false);
    setIsPlaying(false);

    // Restart audio
    if (audioRef.current && audioRef.current.audioEl.current) {
      audioRef.current.audioEl.current.currentTime = 0;
      audioRef.current.audioEl.current.play().catch((e) => {
        console.log("Auto-play prevented due to browser policy:", e);
      });
    }
  };

  // Check if all blanks are filled
  const allBlanksFilled = () => {
    return userAnswers.every((answer) => answer.trim() !== "");
  };

  // Audio controls
  const toggleAudio = () => {
    if (audioRef.current && audioRef.current.audioEl.current) {
      if (isPlaying) {
        audioRef.current.audioEl.current.pause();
      } else {
        audioRef.current.audioEl.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  return (
    <div
      style={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: "20px",
        backgroundColor: "#f9f9f9",
        borderRadius: "8px",
        position: "relative",
      }}
    >
      {/* Header with timer and score */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          width: isSmallScreen ? "100%" : "90%",
          marginBottom: "15px",
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            backgroundColor: timeLeft < 60 ? "#ffe6e6" : "#f0f0f0",
            padding: "5px 15px",
            borderRadius: "20px",
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          }}
        >
          <span
            style={{
              fontSize: "18px",
              fontWeight: "bold",
              color: timeLeft < 60 ? "#cc0000" : "#333",
            }}
          >
            Time: {formatTime(timeLeft)}
          </span>
        </div>
        {isSubmitted && (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              backgroundColor: "#f0f0f0",
              padding: "5px 15px",
              borderRadius: "20px",
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
              marginLeft: "10px",
            }}
          >
            <span style={{ fontSize: "16px", fontWeight: "bold" }}>
              Score:{" "}
            </span>
            <span
              style={{
                fontSize: "18px",
                fontWeight: "bold",
                color: score > 0 ? "#008800" : "#333",
                marginLeft: "5px",
              }}
            >
              {score}/{maxScore}
            </span>
          </div>
        )}
      </div>

      {/* Main content card */}
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          width: isSmallScreen ? "100%" : "90%",
          backgroundColor: "white",
          padding: "25px",
          borderRadius: "8px",
          boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
          marginBottom: "20px",
        }}
      >
        {/* Audio player - styled for better visibility */}
        <div
          style={{
            marginBottom: "25px",
            padding: "15px",
            backgroundColor: "#f5f5f5",
            borderRadius: "8px",
            border: "1px solid #ddd",
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              marginBottom: "10px",
            }}
          >
            <span
              style={{
                backgroundColor: isPlaying ? "#522CFF" : "#f0f0f0",
                color: isPlaying ? "white" : "#333",
                padding: "4px 10px",
                borderRadius: "20px",
                fontSize: "14px",
                fontWeight: "500",
                marginRight: "10px",
                display: "inline-flex",
                alignItems: "center",
              }}
            >
              {isPlaying ? (
                <>
                  <span style={{ marginRight: "5px" }}>●</span> Now Playing
                </>
              ) : (
                "Audio"
              )}
            </span>
            <button
              onClick={toggleAudio}
              style={{
                border: "none",
                backgroundColor: "#140342",
                color: "white",
                padding: "5px 15px",
                borderRadius: "5px",
                cursor: "pointer",
                fontSize: "14px",
                display: "flex",
                alignItems: "center",
                gap: "5px",
              }}
            >
              {isPlaying ? (
                <>
                  <span style={{ fontSize: "16px" }}>⏸️</span> Pause
                </>
              ) : (
                <>
                  <span style={{ fontSize: "16px" }}>▶️</span> Play
                </>
              )}
            </button>
          </div>
          <AudioPlayer
            ref={audioRef}
            src={question?.media?.url || ""}
            controls
            autoPlay={true}
            onCanPlayThrough={() => {
              if (audioRef.current && audioRef.current.audioEl.current) {
                const playPromise = audioRef.current.audioEl.current.play();
                if (playPromise !== undefined) {
                  playPromise.then(() => setIsPlaying(true)).catch(() => {});
                }
              }
            }}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            onEnded={handleAudioEnd}
            style={{ width: "100%" }}
          />
        </div>

        {/* Prompt */}
        <p
          style={{
            fontSize: "16px",
            fontWeight: "bold",
            marginBottom: "10px",
          }}
        >
          {question?.prompt ||
            "You will hear a recording. Type the missing words in each blank."}
        </p>

        {/* Content with input blanks */}
        <div style={{ marginBottom: "30px", lineHeight: "2" }}>
          {renderContentWithBlanks()}
        </div>

        {/* Instructions */}
        <div
          style={{
            padding: "15px",
            backgroundColor: "#f0f8ff",
            borderRadius: "8px",
            border: "1px solid #ddd",
            marginTop: "20px",
          }}
        >
          <p
            style={{
              margin: "0",
              fontSize: "14px",
              color: "#666",
              textAlign: "center",
            }}
          >
            💡 <strong>Instructions:</strong> Listen to the audio carefully and
            type the missing words in the blanks. Make sure to spell the words
            correctly as they appear in the recording.
          </p>
        </div>
      </div>

      {/* Controls section */}
      <div
        style={{
          display: "flex",
          flexDirection: isSmallScreen ? "column" : "row",
          justifyContent: "space-between",
          alignItems: isSmallScreen ? "stretch" : "center",
          width: isSmallScreen ? "100%" : "90%",
          gap: "15px",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: "15px",
            flexWrap: isSmallScreen ? "wrap" : "nowrap",
            justifyContent: isSmallScreen ? "center" : "flex-start",
          }}
        >
          <button
            style={{
              backgroundColor: isSubmitted ? "#d0d0d0" : "#D9D9D9",
              borderWidth: 1,
              borderColor: "#140342",
              borderStyle: "solid",
              padding: "10px 20px",
              borderRadius: "5px",
              cursor: isSubmitted || !allBlanksFilled() ? "default" : "pointer",
              opacity: isSubmitted || !allBlanksFilled() ? 0.7 : 1,
            }}
            onClick={handleDone}
            disabled={isSubmitted || !allBlanksFilled()}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
              }}
            >
              Submit
            </p>
          </button>

          <button
            style={{
              backgroundColor: "#D9D9D9",
              borderWidth: 1,
              borderColor: "#140342",
              borderStyle: "solid",
              padding: "10px 20px",
              borderRadius: "5px",
              cursor: "pointer",
            }}
            onClick={handleRedo}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
              }}
            >
              Re-do
            </p>
          </button>

          <div
            style={{
              display: "flex",
              alignItems: "center",
              backgroundColor: "#f9f9f9",
              padding: "5px 10px",
              borderRadius: "5px",
              border: "1px solid #ddd",
            }}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
                marginRight: "5px",
              }}
            >
              {showAnswer ? "Hide Answer" : "Show Answer"}
            </p>
            <Switch
              onChange={(e, checked) => setShowAnswer(checked)}
              checked={showAnswer}
              size="small"
            />
          </div>
        </div>

        {/* Feedback message after submission */}
        {isSubmitted && !showAnswer && (
          <div
            style={{
              padding: "10px 15px",
              borderRadius: "5px",
              backgroundColor: score === maxScore ? "#e6ffe6" : "#ffe6e6",
              border: `1px solid ${score === maxScore ? "#008800" : "#cc0000"}`,
              textAlign: "center",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              minWidth: isSmallScreen ? "auto" : "250px",
            }}
          >
            <div>
              <p
                style={{
                  margin: 0,
                  fontWeight: "bold",
                  color: score === maxScore ? "#008800" : "#cc0000",
                }}
              >
                {score === maxScore
                  ? `Correct! You earned ${score} point${
                      score !== 1 ? "s" : ""
                    }.`
                  : score > 0
                  ? "Partially correct"
                  : "Incorrect - Try again"}
              </p>
            </div>

            {score > 0 && (
              <div
                style={{
                  backgroundColor: "#4CAF50",
                  color: "white",
                  padding: "5px 12px",
                  borderRadius: "20px",
                  fontSize: "16px",
                  fontWeight: "bold",
                  marginLeft: "10px",
                }}
              >
                +{score}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Sidebar Toggle */}
      <SidebarToggle
        isVisible={true}
        activeCategory="listening"
        currentQuestionType="68556cb8d0001a608d09171b"
        onQuestionSelect={onQuestionSelect}
      />
    </div>
  );
};

FillInTheBlanks.propTypes = {
  question: PropTypes.shape({
    questionId: PropTypes.string.isRequired,
    prompt: PropTypes.string,
    content: PropTypes.string,
    media: PropTypes.shape({ url: PropTypes.string }),
    blanks: PropTypes.arrayOf(
      PropTypes.shape({
        correctAnswer: PropTypes.string.isRequired,
      })
    ),
    maxScore: PropTypes.number,
  }).isRequired,
  onQuestionSelect: PropTypes.func.isRequired,
};

export default FillInTheBlanks;
