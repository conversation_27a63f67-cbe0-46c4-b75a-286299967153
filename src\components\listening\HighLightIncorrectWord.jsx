import React, { useState, useEffect, useRef } from "react";
import { Switch } from "@mui/material";
import AudioPlayer from "react-audio-player";
// Subscription imports
// import { useSubscriptionCheck } from "../../hooks/useSubscriptionCheck";
// import SubscriptionModal from "../others/SubscriptionModal";
// import SubscriptionIndicator from "../others/SubscriptionIndicator";
import PropTypes from "prop-types";
import SidebarToggle from "../common/SidebarToggle";
// Practice tracking imports
import {
  logPracticeAttempt,
  usePracticeInfo,
} from "../speech-ace-component/utils/practiceUtils";

const HighlightIncorrectWords = ({ question, onQuestionSelect }) => {
  // Practice tracking
  const { setPracticeCount, setIsPracticed } = usePracticeInfo(
    question.questionId
  );

  const [highlightedWords, setHighlightedWords] = useState(new Set());
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showAnswer, setShowAnswer] = useState(false);
  const [timeElapsed, setTimeElapsed] = useState(0); // Time elapsed in seconds
  const [timerActive, setTimerActive] = useState(false);
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [score, setScore] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef(null);
  // Calculate maxScore based on the number of incorrect words in the question
  const maxScore = question?.incorrectWords?.length || 0;

  // Subscription logic
  // const {
  //   eligibility,
  //   loading: subscriptionLoading,
  //   showSubscriptionModal,
  //   canViewAnswer,
  //   handleSubmitWithCheck,
  //   closeSubscriptionModal,
  //   showSubscriptionRequiredModal,
  // } = useSubscriptionCheck(question.questionId);

  // Reset state when question changes
  useEffect(() => {
    setHighlightedWords(new Set());
    setIsSubmitted(false);
    setShowAnswer(false);
    setScore(0);
    setTimeElapsed(0); // Reset elapsed time to 0
    setTimerActive(false);
    setIsPlaying(false);
  }, [question?.questionId]);

  // Auto play audio when component mounts
  useEffect(() => {
    const playAudio = async () => {
      try {
        const audioUrl = question?.media?.url;
        if (!audioUrl) {
          console.warn("No audio URL provided for this question");
          return;
        }

        if (
          audioRef.current &&
          audioRef.current.audioEl &&
          audioRef.current.audioEl.current
        ) {
          audioRef.current.audioEl.current.load();
          await new Promise((resolve) => setTimeout(resolve, 1000));
          audioRef.current.audioEl.current.currentTime = 0;

          const playPromise = audioRef.current.audioEl.current.play();
          if (playPromise !== undefined) {
            playPromise
              .then(() => {
                console.log("Audio started playing automatically");
                setIsPlaying(true);
                if (!timerActive) {
                  setTimerActive(true);
                }
              })
              .catch((error) => {
                console.warn("Auto-play was prevented:", error);
              });
          }
        }
      } catch (error) {
        console.error("Error in audio playback:", error);
      }
    };

    playAudio();
  }, [question?.media?.url]);

  // Initialize timer - infinite and counting upward
  useEffect(() => {
    let timer;
    if (timerActive) {
      timer = setInterval(() => {
        setTimeElapsed((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [timerActive]);

  // Format time as MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Update screen width on resize
  useEffect(() => {
    const handleResize = () => setScreenWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const isSmallScreen = screenWidth <= 768;

  // Handle word click for highlighting
  const handleWordClick = (word, wordIndex) => {
    if (isSubmitted && !showAnswer) return; // Prevent clicking after submission

    const wordKey = `${word}-${wordIndex}`; // Use word + index to handle duplicate words
    const updatedHighlightedWords = new Set(highlightedWords);
    if (updatedHighlightedWords.has(wordKey)) {
      updatedHighlightedWords.delete(wordKey);
    } else {
      updatedHighlightedWords.add(wordKey);
    }
    setHighlightedWords(updatedHighlightedWords);
  };

  // Handle show answer toggle
  useEffect(() => {
    if (showAnswer) {
      // Show all the incorrect words
      const incorrectWordKeys = new Set();
      const words = question?.incorrectTranscript?.split(" ") || [];

      words.forEach((word, index) => {
        const cleanWord = word
          .trim()
          .replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, "");
        const isIncorrectWord = question?.incorrectWords?.some(
          (item) => item.incorrect.toLowerCase() === cleanWord.toLowerCase()
        );

        if (isIncorrectWord) {
          incorrectWordKeys.add(`${cleanWord}-${index}`);
        }
      });

      setHighlightedWords(incorrectWordKeys);
    }
  }, [showAnswer, question?.incorrectWords, question?.incorrectTranscript]);

  // Calculate score based on correct highlights and incorrect highlights
  const calculateScore = () => {
    const words = question?.incorrectTranscript?.split(" ") || [];
    let correctHighlights = 0;
    let incorrectHighlights = 0;
    
    // Get all incorrect words that should be highlighted
    const incorrectWordSet = new Set();
    words.forEach((word, index) => {
      const cleanWord = word.trim().replace(/[.,/#!$%^&*;:{}=\-_`~()]/g, "");
      const isIncorrectWord = question?.incorrectWords?.some(
        (item) => item.incorrect.toLowerCase() === cleanWord.toLowerCase()
      );
      if (isIncorrectWord) {
        incorrectWordSet.add(`${cleanWord}-${index}`);
      }
    });

    // Check each highlighted word
    highlightedWords.forEach((wordKey) => {
      const [word, index] = wordKey.split("-");
      const actualWord = words[parseInt(index)];
      const cleanWord = actualWord
        ?.trim()
        .replace(/[.,/#!$%^&*;:{}=\-_`~()]/g, "");

      const isCorrectHighlight = question?.incorrectWords?.some(
        (item) => item.incorrect.toLowerCase() === cleanWord?.toLowerCase()
      );

      if (isCorrectHighlight) {
        correctHighlights++;
      } else {
        incorrectHighlights++;
      }
    });

    // Calculate score based on correct highlights minus penalties for incorrect highlights
    // Ensure score doesn't go below 0 and doesn't exceed the total number of incorrect words
    return Math.min(maxScore, Math.max(0, correctHighlights - incorrectHighlights));
  };

  const handleDone = async () => {
    // Check subscription before proceeding
    // const success = await handleSubmitWithCheck(async () => {
    setIsSubmitted(true);
    setTimerActive(false);
    setScore(calculateScore());
    // });

    // if (!success) {
    //   // If subscription check failed, ensure timer doesn't stop
    //   setTimerActive(true);
    // }

    // Log practice attempt
    const result = await logPracticeAttempt(question.questionId);
    if (result.success) {
      setPracticeCount(result.practiceCount);
      setIsPracticed(true);
    }
  };

  const handleRedo = () => {
    setHighlightedWords(new Set());
    setIsSubmitted(false);
    setShowAnswer(false);
    setScore(0);
    setTimeElapsed(0); // Reset elapsed time to 0
    setTimerActive(false);
    setIsPlaying(false);

    // Restart audio
    if (audioRef.current && audioRef.current.audioEl.current) {
      audioRef.current.audioEl.current.currentTime = 0;
      audioRef.current.audioEl.current.play().catch((e) => {
        console.log("Auto-play prevented due to browser policy:", e);
      });
    }
  };

  // Handle audio end
  const handleAudioEnd = () => {
    setIsPlaying(false);
  };

  // Check if all answers are correct
  const allCorrectlyHighlighted = () => {
    const words = question?.incorrectTranscript?.split(" ") || [];
    const correctWordKeys = new Set();

    // Get all incorrect word keys
    words.forEach((word, index) => {
      const cleanWord = word.trim().replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, "");
      const isIncorrectWord = question?.incorrectWords?.some(
        (item) => item.incorrect.toLowerCase() === cleanWord.toLowerCase()
      );

      if (isIncorrectWord) {
        correctWordKeys.add(`${cleanWord}-${index}`);
      }
    });

    // Check if all correct words are highlighted and no extra words are highlighted
    const highlightedArray = Array.from(highlightedWords);
    const correctArray = Array.from(correctWordKeys);

    return (
      correctArray.every((wordKey) => highlightedArray.includes(wordKey)) &&
      highlightedArray.every((wordKey) => correctArray.includes(wordKey))
    );
  };

  const renderTextWithHighlight = () => {
    const words = question?.incorrectTranscript?.split(" ") || [];

    return words.map((word, index) => {
      // Clean the word by removing punctuation
      const cleanWord = word.trim().replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, "");
      const wordKey = `${cleanWord}-${index}`;
      const isHighlighted = highlightedWords.has(wordKey);

      // Check if this word is in the incorrectWords array
      const incorrectWordItem = question?.incorrectWords?.find(
        (item) => item.incorrect.toLowerCase() === cleanWord.toLowerCase()
      );
      const isIncorrectWord = !!incorrectWordItem;

      // Determine styling based on state
      let backgroundColor = "transparent";
      let borderColor = "transparent";
      let textColor = "#333";

      if (isSubmitted || showAnswer) {
        // Show correct/incorrect status after submission
        if (isIncorrectWord) {
          backgroundColor = "#fff8e0"; // Light yellow for incorrect words
          borderColor = "#ffc107"; // Amber border
        }

        if (isHighlighted) {
          if (isIncorrectWord) {
            backgroundColor = "#d4edda"; // Green for correctly identified words
            borderColor = "#28a745"; // Green border
            textColor = "#155724";
          } else {
            backgroundColor = "#f8d7da"; // Red for incorrectly highlighted words
            borderColor = "#dc3545"; // Red border
            textColor = "#721c24";
          }
        }
      } else if (isHighlighted) {
        backgroundColor = "#e8f0fe"; // Light blue for user highlights before submission
        borderColor = "#140342"; // Purple border
        textColor = "#140342";
      }

      return (
        <span
          key={index}
          onClick={() => handleWordClick(cleanWord, index)}
          style={{
            cursor: isSubmitted && !showAnswer ? "default" : "pointer",
            padding: "4px 6px",
            margin: "0 2px",
            borderRadius: "4px",
            backgroundColor: backgroundColor,
            border: `1px solid ${borderColor}`,
            color: textColor,
            fontWeight: isHighlighted ? "600" : "400",
            fontSize: "18px",
            lineHeight: 2.2,
            display: "inline-block",
            position: "relative",
            transition: "all 0.2s ease",
          }}
        >
          {word}

          {/* Show correct word after submission or when Show Answer is toggled */}
          {(isSubmitted || showAnswer) && isIncorrectWord && (
            <span
              style={{
                display: "block",
                fontSize: "12px",
                color: "#28a745",
                fontWeight: "500",
                fontStyle: "italic",
                marginTop: "2px",
                textAlign: "center",
              }}
            >
              → {incorrectWordItem.correct}
            </span>
          )}
        </span>
      );
    });
  };

  // Handle show answer with subscription check
  const handleShowAnswer = async () => {
    // if (canViewAnswer) {
    setShowAnswer(!showAnswer);
    // } else {
    //   showSubscriptionRequiredModal();
    // }
  };

  // Audio controls
  const toggleAudio = () => {
    if (audioRef.current && audioRef.current.audioEl.current) {
      if (isPlaying) {
        audioRef.current.audioEl.current.pause();
      } else {
        audioRef.current.audioEl.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  return (
    <div
      style={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: "20px",
        backgroundColor: "#f9f9f9",
        borderRadius: "8px",
        position: "relative",
      }}
      className="question-container"
    >
      {/* Subscription Indicator */}
      {/* <SubscriptionIndicator
        eligibility={eligibility}
        loading={subscriptionLoading}
        position="top-right"
      /> */}

      {/* Header with timer and score */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          width: isSmallScreen ? "100%" : "90%",
          marginBottom: "15px",
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            backgroundColor: "#f0f0f0",
            padding: "5px 15px",
            borderRadius: "20px",
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          }}
        >
          <span
            style={{
              fontSize: "18px",
              fontWeight: "bold",
              color: "#333",
            }}
          >
            Time: {formatTime(timeElapsed)}
          </span>
        </div>
        {isSubmitted && (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              backgroundColor: "#f0f0f0",
              padding: "5px 15px",
              borderRadius: "20px",
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
              marginLeft: "10px",
            }}
          >
            <span style={{ fontSize: "16px", fontWeight: "bold" }}>
              Score:{" "}
            </span>
            <span
              style={{
                fontSize: "18px",
                fontWeight: "bold",
                color: score > 0 ? "#008800" : "#333",
                marginLeft: "5px",
              }}
            >
              {score}/{maxScore}
            </span>
          </div>
        )}
      </div>

      {/* Main content card */}
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          width: isSmallScreen ? "100%" : "90%",
          backgroundColor: "white",
          padding: "25px",
          borderRadius: "8px",
          boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
          marginBottom: "20px",
        }}
      >
        {/* Audio player - styled for better visibility */}
        <div
          style={{
            marginBottom: "25px",
            padding: "15px",
            backgroundColor: "#f5f5f5",
            borderRadius: "8px",
            border: "1px solid #ddd",
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              marginBottom: "10px",
            }}
          >
            <span
              style={{
                backgroundColor: isPlaying ? "#522CFF" : "#f0f0f0",
                color: isPlaying ? "white" : "#333",
                padding: "4px 10px",
                borderRadius: "20px",
                fontSize: "14px",
                fontWeight: "500",
                marginRight: "10px",
                display: "inline-flex",
                alignItems: "center",
              }}
            >
              {isPlaying ? (
                <>
                  <span style={{ marginRight: "5px" }}>●</span> Now Playing
                </>
              ) : (
                "Audio"
              )}
            </span>
            <button
              onClick={toggleAudio}
              style={{
                border: "none",
                backgroundColor: "#140342",
                color: "white",
                padding: "5px 15px",
                borderRadius: "5px",
                cursor: "pointer",
                fontSize: "14px",
                display: "flex",
                alignItems: "center",
                gap: "5px",
              }}
            >
              {isPlaying ? (
                <>
                  <span style={{ fontSize: "16px" }}>⏸️</span> Pause
                </>
              ) : (
                <>
                  <span style={{ fontSize: "16px" }}>▶️</span> Play
                </>
              )}
            </button>
          </div>
          <AudioPlayer
            ref={audioRef}
            src={question?.media?.url || ""}
            controls
            autoPlay={true}
            onCanPlayThrough={() => {
              if (audioRef.current && audioRef.current.audioEl.current) {
                const playPromise = audioRef.current.audioEl.current.play();
                if (playPromise !== undefined) {
                  playPromise.then(() => setIsPlaying(true)).catch(() => {});
                }
              }
            }}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            onEnded={handleAudioEnd}
            style={{ width: "100%" }}
          />
        </div>

        {/* Prompt */}
        <div style={{ marginBottom: "20px" }}>
          <p
            style={{
              color: "#333",
              margin: 0,
              fontSize: "16px",
              fontWeight: "500",
              lineHeight: 1.6,
            }}
          >
            {question?.prompt ||
              "Listen to the recording and click on the words that are different from what you hear."}
          </p>
        </div>

        {/* Text with highlightable words */}
        <div
          style={{
            padding: "20px",
            backgroundColor: "#f9f9f9",
            borderRadius: "8px",
            lineHeight: "2.5",
            marginBottom: "20px",
            border: "1px solid #eee",
            minHeight: "120px",
          }}
        >
          {renderTextWithHighlight()}
        </div>

        {/* Instructions footer */}
        <div
          style={{
            padding: "15px",
            backgroundColor: "#e8f0fe",
            borderRadius: "6px",
            border: "1px solid #140342",
          }}
        >
          <p
            style={{
              margin: 0,
              fontSize: "14px",
              color: "#140342",
              fontWeight: "500",
              textAlign: "center",
            }}
          >
            💡 Click on words that sound different from what you hear in the
            audio
          </p>
        </div>
      </div>

      {/* Controls section */}
      <div
        style={{
          display: "flex",
          flexDirection: isSmallScreen ? "column" : "row",
          justifyContent: "space-between",
          alignItems: isSmallScreen ? "stretch" : "center",
          width: isSmallScreen ? "100%" : "90%",
          gap: "15px",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: "15px",
            flexWrap: isSmallScreen ? "wrap" : "nowrap",
            justifyContent: isSmallScreen ? "center" : "flex-start",
          }}
        >
          <button
            style={{
              backgroundColor: isSubmitted ? "#d0d0d0" : "#D9D9D9",
              borderWidth: 1,
              borderColor: "#140342",
              borderStyle: "solid",
              padding: "10px 20px",
              borderRadius: "5px",
              cursor: isSubmitted ? "default" : "pointer",
              opacity: isSubmitted ? 0.7 : 1,
            }}
            onClick={handleDone}
            disabled={isSubmitted}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
              }}
            >
              Submit
            </p>
          </button>

          <button
            style={{
              backgroundColor: "#D9D9D9",
              borderWidth: 1,
              borderColor: "#140342",
              borderStyle: "solid",
              padding: "10px 20px",
              borderRadius: "5px",
              cursor: "pointer",
            }}
            onClick={handleRedo}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
              }}
            >
              Re-do
            </p>
          </button>

          <div
            style={{
              display: "flex",
              alignItems: "center",
              backgroundColor: "#f9f9f9",
              padding: "5px 10px",
              borderRadius: "5px",
              border: "1px solid #ddd",
            }}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
                marginRight: "5px",
              }}
            >
              {showAnswer ? "Hide Answer" : "Show Answer"}
            </p>
            <Switch
              onChange={() => handleShowAnswer()}
              checked={showAnswer}
              size="small"
            />
          </div>

          {/* <button
            style={{
              backgroundColor: "#D9D9D9",
              borderWidth: 1,
              borderColor: "#140342",
              borderStyle: "solid",
              padding: "10px 20px",
              borderRadius: "5px",
              display: "flex",
              alignItems: "center",
              gap: 10,
              cursor: "pointer",
            }}
          >
            <img
              src="/assets/img/translate.png"
              alt="translate"
              style={{ width: "20px", height: "20px" }}
            />
            <p
              style={{
                margin: 0,
                color: "#522CFF",
                fontSize: 14,
                fontWeight: "500",
              }}
            >
              Translation
            </p>
          </button> */}
        </div>

        {/* Feedback message after submission */}
        {isSubmitted && !showAnswer && (
          <div
            style={{
              padding: "10px 15px",
              borderRadius: "5px",
              backgroundColor: allCorrectlyHighlighted()
                ? "#e6ffe6"
                : "#ffe6e6",
              border: `1px solid ${
                allCorrectlyHighlighted() ? "#008800" : "#cc0000"
              }`,
              textAlign: "center",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              minWidth: isSmallScreen ? "auto" : "250px",
            }}
          >
            <div>
              <p
                style={{
                  margin: 0,
                  fontWeight: "bold",
                  color: allCorrectlyHighlighted() ? "#008800" : "#cc0000",
                }}
              >
                {allCorrectlyHighlighted()
                  ? `Correct! You earned ${score} out of ${maxScore} point${
                      maxScore !== 1 ? "s" : ""
                    }.`
                  : score > 0
                  ? `Partially correct - you got ${score} out of ${maxScore} points`
                  : "Incorrect - try again"}
              </p>
            </div>

            {score > 0 && (
              <div
                style={{
                  backgroundColor: "#4CAF50",
                  color: "white",
                  padding: "5px 12px",
                  borderRadius: "20px",
                  fontSize: "16px",
                  fontWeight: "bold",
                  marginLeft: "10px",
                }}
              >
                +{score}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Subscription Modal */}
      {/* <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={closeSubscriptionModal}
        eligibility={eligibility}
        title="Subscription Required"
        message="Continue practicing with detailed feedback and analysis."
      /> */}

      {/* Sidebar Toggle */}
      <SidebarToggle
        isVisible={true}
        activeCategory="listening"
        currentQuestionType="678023f5382bce18e30d11d2"
        onQuestionSelect={onQuestionSelect}
      />
    </div>
  );
};

HighlightIncorrectWords.propTypes = {
  question: PropTypes.shape({
    questionId: PropTypes.string,
    maxScore: PropTypes.number,
    prompt: PropTypes.string,
    incorrectWords: PropTypes.arrayOf(
      PropTypes.shape({
        incorrect: PropTypes.string,
        correct: PropTypes.string,
      })
    ),
    transcript: PropTypes.string,
    incorrectTranscript: PropTypes.string,
    media: PropTypes.shape({
      url: PropTypes.string,
    }),
  }).isRequired,
  onQuestionSelect: PropTypes.func.isRequired,
};

export default HighlightIncorrectWords;
