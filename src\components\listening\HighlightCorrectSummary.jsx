import React, { useState, useEffect, useRef } from "react";
import { Switch } from "@mui/material";
import AudioPlayer from "react-audio-player";
// Subscription imports
// import { useSubscriptionCheck } from "../../hooks/useSubscriptionCheck";
// import SubscriptionModal from "../others/SubscriptionModal";
// import SubscriptionIndicator from "../others/SubscriptionIndicator";
import PropTypes from "prop-types";
// Practice tracking imports
import {
  logPracticeAttempt,
  usePracticeInfo,
} from "../speech-ace-component/utils/practiceUtils";
import SidebarToggle from "../common/SidebarToggle";

const HighlightCorrectSummary = ({ question, onQuestionSelect }) => {
  // Practice tracking
  const { setPracticeCount, setIsPracticed } = usePracticeInfo(
    question.questionId
  );

  const [selectedOption, setSelectedOption] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showAnswer, setShowAnswer] = useState(false);
  const [timeLeft, setTimeLeft] = useState(3 * 60); // 3 minutes in seconds
  const [timerActive, setTimerActive] = useState(true);
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [score, setScore] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showTranscript, setShowTranscript] = useState(false);
  const audioRef = useRef(null);
  const maxScore = question?.maxScore || 1;

  // Subscription logic
  // const {
  //   eligibility,
  //   loading: subscriptionLoading,
  //   showSubscriptionModal,
  //   canViewAnswer,
  //   handleSubmitWithCheck,
  //   closeSubscriptionModal,
  //   showSubscriptionRequiredModal,
  // } = useSubscriptionCheck(question.questionId);

  // Auto play audio when component mounts
  useEffect(() => {
    const playAudio = async () => {
      try {
        if (
          audioRef.current &&
          audioRef.current.audioEl &&
          audioRef.current.audioEl.current
        ) {
          // Force load the audio first
          audioRef.current.audioEl.current.load();

          // Try to play after a short delay to ensure it's loaded
          await new Promise((resolve) => setTimeout(resolve, 1000));

          // Sometimes we need to explicitly set the current time to 0
          audioRef.current.audioEl.current.currentTime = 0;

          // Play with user interaction simulation (may help with some browsers)
          const playPromise = audioRef.current.audioEl.current.play();

          if (playPromise !== undefined) {
            playPromise
              .then(() => {
                console.log("Audio started playing automatically");
                setIsPlaying(true);
              })
              .catch((error) => {
                console.warn("Auto-play was prevented:", error);
                // If auto-play fails, don't update the playing state
              });
          }
        }
      } catch (error) {
        console.error("Error in audio playback:", error);
      }
    };

    playAudio();
  }, []);

  // Initialize timer
  useEffect(() => {
    let timer;
    if (timerActive && timeLeft > 0) {
      timer = setInterval(() => {
        setTimeLeft((prev) => prev - 1);
      }, 1000);
    } else if (timeLeft === 0) {
      handleDone();
    }
    return () => clearInterval(timer);
  }, [timerActive, timeLeft]);

  // Format time as MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Update screen width on resize
  useEffect(() => {
    const handleResize = () => setScreenWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const isSmallScreen = screenWidth <= 768;

  // Handle option selection
  const handleSelect = (optionText) => {
    if (isSubmitted && !showAnswer) return;
    setSelectedOption(optionText);
  };

  // Handle form submission with subscription check
  const handleDone = async () => {
    // Check subscription before proceeding
    // const success = await handleSubmitWithCheck(async () => {
    setIsSubmitted(true);
    setTimerActive(false);

    // Calculate score
    const correctOption = question.options?.find((option) => option.isCorrect);
    if (correctOption && selectedOption === correctOption.text) {
      setScore(maxScore);
    } else {
      setScore(0);
    }
    // });

    // Log practice attempt
    const result = await logPracticeAttempt(question.questionId);
    if (result.success) {
      setPracticeCount(result.practiceCount);
      setIsPracticed(true);
    }
  };

  // Handle show answer with subscription check
  const handleShowAnswer = async () => {
    // if (canViewAnswer) {
    setShowAnswer(!showAnswer);
    // } else {
    //   showSubscriptionRequiredModal();
    // }
  };

  // Handle redo
  const handleRedo = () => {
    setSelectedOption("");
    setIsSubmitted(false);
    setShowAnswer(false);
    setScore(0);
    setTimeLeft(3 * 60); // Reset to 3 minutes
    setTimerActive(true);
    setIsPlaying(false);

    // Restart audio
    if (audioRef.current && audioRef.current.audioEl.current) {
      audioRef.current.audioEl.current.currentTime = 0;
      audioRef.current.audioEl.current.play().catch((e) => {
        console.log("Auto-play prevented due to browser policy:", e);
      });
    }
  };

  // Handle show answer toggle
  useEffect(() => {
    if (showAnswer) {
      // Set the selected option to the correct one
      const correctOption = question.options?.find(
        (option) => option.isCorrect
      );
      if (correctOption) {
        setSelectedOption(correctOption.text);
      }
    } else if (isSubmitted && !showAnswer) {
      // If we're hiding the answer after submission, we don't need to do anything
      // as the user's selection is already stored
    }
  }, [showAnswer, question.options]);

  // Reset component when question changes
  useEffect(() => {
    setSelectedOption("");
    setIsSubmitted(false);
    setShowAnswer(false);
    setScore(0);
    setTimeLeft(3 * 60); // Reset to 3 minutes
    setTimerActive(true);
    setIsPlaying(false);

    // Set a short timeout to ensure the audio player has updated its source
    const timer = setTimeout(() => {
      if (
        audioRef.current &&
        audioRef.current.audioEl &&
        audioRef.current.audioEl.current
      ) {
        // Reset the audio to the beginning
        audioRef.current.audioEl.current.currentTime = 0;
        // Force a reload of the audio source
        audioRef.current.audioEl.current.load();
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [question.questionId]);

  // Get feedback for each option
  const getFeedback = (option) => {
    if (!isSubmitted || showAnswer) return null;

    const isCorrect = option.isCorrect;
    const isSelected = selectedOption === option.text;

    // Show feedback for selected option or correct option that wasn't selected
    if (isSelected || (isCorrect && !isSelected)) {
      return (
        <span
          style={{
            color: isCorrect ? "#008800" : "#cc0000",
            marginLeft: "10px",
            fontSize: "16px",
            fontWeight: "500",
          }}
        >
          {isCorrect ? "✓ Correct" : "✗ Incorrect"}
        </span>
      );
    }

    return null;
  };

  // Get option letter (A, B, C, D)
  const getOptionLetter = (index) => {
    return String.fromCharCode(65 + index); // A, B, C, D...
  };

  // Get correct answer letter
  const getCorrectAnswerLetter = () => {
    const correctIndex = question.options?.findIndex(
      (option) => option.isCorrect
    );
    return correctIndex !== -1 ? getOptionLetter(correctIndex) : "";
  };

  // Audio controls
  const toggleAudio = () => {
    if (audioRef.current && audioRef.current.audioEl.current) {
      if (isPlaying) {
        audioRef.current.audioEl.current.pause();
      } else {
        audioRef.current.audioEl.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };
  const handleAudioEnd = () => setIsPlaying(false);

  const toggleTranscript = () => {
    setShowTranscript(!showTranscript);
  };

  return (
    <div
      style={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: "20px",
        backgroundColor: "#f9f9f9",
        borderRadius: "8px",
        position: "relative",
      }}
      className="question-container"
    >
      {/* Subscription Indicator */}
      {/* <SubscriptionIndicator
        eligibility={eligibility}
        loading={subscriptionLoading}
        position="top-right"
      /> */}

      {/* Subscription Modal */}
      {/* <SubscriptionModal
        open={showSubscriptionModal}
        onClose={closeSubscriptionModal}
      /> */}

      <div
        style={{
          display: "flex",
          flexDirection: "column",
          width: isSmallScreen ? "100%" : "90%",
          marginTop: "20px",
          marginBottom: "40px",
        }}
      >
        {/* Header with timer and score */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            width: isSmallScreen ? "100%" : "90%",
            marginBottom: "15px",
          }}
        >
          {/* Timer display */}
          <div
            style={{
              display: "flex",
              alignItems: "center",
              backgroundColor: timeLeft < 60 ? "#ffe6e6" : "#f0f0f0",
              padding: "5px 15px",
              borderRadius: "20px",
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
            }}
          >
            <span
              style={{
                fontSize: "18px",
                fontWeight: "bold",
                color: timeLeft < 60 ? "#cc0000" : "#333",
              }}
            >
              Time: {formatTime(timeLeft)}
            </span>
          </div>
          {/* Score display - only shown after submission */}
          {isSubmitted && (
            <div
              style={{
                display: "flex",
                alignItems: "center",
                backgroundColor: "#f0f0f0",
                padding: "5px 15px",
                borderRadius: "20px",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                marginLeft: "10px",
              }}
            >
              <span style={{ fontSize: "16px", fontWeight: "bold" }}>
                Score:{" "}
              </span>
              <span
                style={{
                  fontSize: "18px",
                  fontWeight: "bold",
                  color: score > 0 ? "#008800" : "#333",
                  marginLeft: "5px",
                }}
              >
                {score}/{maxScore}
              </span>
            </div>
          )}
        </div>

        {/* Main content card */}
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            width: isSmallScreen ? "100%" : "90%",
            backgroundColor: "white",
            padding: "25px",
            borderRadius: "8px",
            boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
            marginBottom: "20px",
          }}
        >
          {/* Audio player - styled for better visibility */}
          <div
            style={{
              marginBottom: "25px",
              padding: "15px",
              backgroundColor: "#f5f5f5",
              borderRadius: "8px",
              border: "1px solid #ddd",
            }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                marginBottom: "10px",
              }}
            >
              <span
                style={{
                  backgroundColor: isPlaying ? "#522CFF" : "#f0f0f0",
                  color: isPlaying ? "white" : "#333",
                  padding: "4px 10px",
                  borderRadius: "20px",
                  fontSize: "14px",
                  fontWeight: "500",
                  marginRight: "10px",
                  display: "inline-flex",
                  alignItems: "center",
                }}
              >
                {isPlaying ? (
                  <>
                    <span style={{ marginRight: "5px" }}>●</span> Now Playing
                  </>
                ) : (
                  "Audio"
                )}
              </span>
              <button
                onClick={toggleAudio}
                style={{
                  border: "none",
                  backgroundColor: "#140342",
                  color: "white",
                  padding: "5px 15px",
                  borderRadius: "5px",
                  cursor: "pointer",
                  fontSize: "14px",
                  display: "flex",
                  alignItems: "center",
                  gap: "5px",
                }}
              >
                {isPlaying ? (
                  <>
                    <span style={{ fontSize: "16px" }}>⏸️</span> Pause
                  </>
                ) : (
                  <>
                    <span style={{ fontSize: "16px" }}>▶️</span> Play
                  </>
                )}
              </button>
            </div>
            <AudioPlayer
  ref={audioRef}
  src={question.media?.url || ""}
  controls
  autoPlay={true}
  onCanPlayThrough={() => {
    if (audioRef.current && audioRef.current.audioEl.current) {
      // Set playback rate to 1.5x
      audioRef.current.audioEl.current.playbackRate = 1.5;
      
      const playPromise = audioRef.current.audioEl.current.play();
      if (playPromise !== undefined) {
        playPromise.then(() => setIsPlaying(true)).catch(() => {});
      }
    }
  }}
  onPlay={() => setIsPlaying(true)}
  onPause={() => setIsPlaying(false)}
  onEnded={handleAudioEnd}
  style={{ width: "100%" }}
/>
          </div>

          {/* Options */}
          <div
            style={{ display: "flex", flexDirection: "column", gap: "15px" }}
          >
            {question.options?.map((option, index) => {
              const isSelected = selectedOption === option.text;
              const bgColor =
                isSubmitted || showAnswer
                  ? isSelected
                    ? option.isCorrect
                      ? "#e6ffe6"
                      : "#ffe6e6"
                    : option.isCorrect && showAnswer
                    ? "#e6ffe6"
                    : "white"
                  : isSelected
                  ? "#f0f0ff"
                  : "white";
              return (
                <div
                  key={index}
                  onClick={() => handleSelect(option.text)}
                  style={{
                    padding: "15px",
                    border: `2px solid ${isSelected ? "#1976d2" : "#ddd"}`,
                    borderRadius: "8px",
                    cursor: isSubmitted && !showAnswer ? "default" : "pointer",
                    backgroundColor: bgColor,
                    transition: "all 0.2s ease",
                    position: "relative",
                    display: "flex",
                    alignItems: "flex-start",
                    gap: "15px",
                  }}
                >
                  <span
                    style={{
                      minWidth: "30px",
                      height: "30px",
                      borderRadius: "50%",
                      border: `2px solid ${isSelected ? "#1976d2" : "#ddd"}`,
                      backgroundColor: isSelected ? "#1976d2" : "#fff",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      marginTop: "2px",
                      fontSize: "14px",
                      fontWeight: "600",
                      color: isSelected ? "#fff" : "#333",
                    }}
                  >
                    {getOptionLetter(index)}
                  </span>
                  <div style={{ flex: 1 }}>
                    <p
                      style={{
                        margin: 0,
                        color: "#333",
                        fontSize: "16px",
                        lineHeight: "1.5",
                        textAlign: "justify",
                      }}
                    >
                      {option.text}
                    </p>
                    {getFeedback(option)}
                  </div>
                </div>
              );
            })}
          </div>
          {/* Show transcript/correct answer when answer is visible */}
          {showAnswer && question.transcript && (
            <div
              style={{
                marginTop: "20px",
                padding: "15px",
                backgroundColor: "#e6ffe6",
                border: "1px solid #008800",
                borderRadius: "8px",
              }}
            >
              <p
                style={{
                  margin: "0 0 10px 0",
                  fontSize: "16px",
                  fontWeight: "bold",
                  color: "#008800",
                }}
              >
                ✓ Correct Answer: {getCorrectAnswerLetter()}
              </p>
              <p
                style={{
                  margin: 0,
                  fontSize: "16px",
                  color: "#333",
                  fontStyle: "italic",
                }}
              >
                {question.transcript}
              </p>
            </div>
          )}
        </div>
        {/* Controls section */}
        <div
          style={{
            display: "flex",
            flexDirection: isSmallScreen ? "column" : "row",
            justifyContent: "space-between",
            alignItems: isSmallScreen ? "stretch" : "center",
            width: isSmallScreen ? "100%" : "90%",
            gap: "15px",
          }}
        >
          <div
            style={{
              display: "flex",
              gap: "15px",
              flexWrap: isSmallScreen ? "wrap" : "nowrap",
              justifyContent: isSmallScreen ? "center" : "flex-start",
            }}
          >
            <button
              style={{
                backgroundColor: isSubmitted ? "#d0d0d0" : "#D9D9D9",
                borderWidth: 1,
                borderColor: "#140342",
                borderStyle: "solid",
                padding: "10px 20px",
                borderRadius: "5px",
                cursor: isSubmitted || !selectedOption ? "default" : "pointer",
                opacity: isSubmitted || !selectedOption ? 0.7 : 1,
              }}
              onClick={handleDone}
              disabled={isSubmitted || !selectedOption}
            >
              <p
                style={{
                  margin: 0,
                  color: "black",
                  fontSize: 14,
                  fontWeight: "500",
                }}
              >
                Submit
              </p>
            </button>
            <button
              style={{
                backgroundColor: "#D9D9D9",
                borderWidth: 1,
                borderColor: "#140342",
                borderStyle: "solid",
                padding: "10px 20px",
                borderRadius: "5px",
                cursor: "pointer",
              }}
              onClick={handleRedo}
            >
              <p
                style={{
                  margin: 0,
                  color: "black",
                  fontSize: 14,
                  fontWeight: "500",
                }}
              >
                Re-do
              </p>
            </button>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                backgroundColor: "#f9f9f9",
                padding: "5px 10px",
                borderRadius: "5px",
                border: "1px solid #ddd",
              }}
            >
              <p
                style={{
                  margin: 0,
                  color: "black",
                  fontSize: 14,
                  fontWeight: "500",
                  marginRight: "5px",
                }}
              >
                {showAnswer ? "Hide Answer" : "Show Answer"}
              </p>
              <Switch
                onChange={() => handleShowAnswer()}
                checked={showAnswer}
                size="small"
              />
            </div>
            <button
              onClick={toggleTranscript}
              style={{
                backgroundColor: "#9c27b0",
                padding: "10px 20px",
                borderRadius: "5px",
                border: "none",
                cursor: "pointer",
                fontSize: "14px",
                color: "white",
                textAlign: "center",
              }}
            >
              {showTranscript ? "Hide Transcript" : "Show Transcript"}
            </button>
          </div>
          {/* Feedback message after submission */}
          {isSubmitted && !showAnswer && (
            <div
              style={{
                padding: "10px 15px",
                borderRadius: "5px",
                backgroundColor: score > 0 ? "#e6ffe6" : "#ffe6e6",
                border: `1px solid ${score > 0 ? "#008800" : "#cc0000"}`,
                textAlign: "center",
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                minWidth: isSmallScreen ? "auto" : "250px",
              }}
            >
              <div>
                <p
                  style={{
                    margin: 0,
                    fontWeight: "bold",
                    color: score > 0 ? "#008800" : "#cc0000",
                  }}
                >
                  {score > 0
                    ? `Correct! You earned ${score} point${
                        score !== 1 ? "s" : ""
                      }.`
                    : "Incorrect - Try again"}
                </p>
              </div>
              {score > 0 && (
                <div
                  style={{
                    backgroundColor: "#4CAF50",
                    color: "white",
                    padding: "5px 12px",
                    borderRadius: "20px",
                    fontSize: "16px",
                    fontWeight: "bold",
                    marginLeft: "10px",
                  }}
                >
                  +{score}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Transcript Section - Below Action Buttons */}
      {showTranscript && question?.transcript && (
        <div
          style={{
            width: isSmallScreen ? "100%" : "90%",
            marginBottom: "20px",
            padding: "20px",
            backgroundColor: "#fff",
            borderRadius: "8px",
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
            border: "2px solid #e3f2fd",
            marginTop: "20px",
          }}
        >
          <h4
            style={{
              margin: "0 0 15px 0",
              color: "#1976d2",
              fontWeight: "600",
            }}
          >
            📄 Transcript
          </h4>
          <div
            style={{
              padding: "15px",
              backgroundColor: "#f8f9fa",
              borderRadius: "6px",
              border: "1px solid #e9ecef",
              fontSize: "15px",
              lineHeight: "1.6",
              color: "#333",
              fontFamily: "'Arial', sans-serif",
              maxHeight: "300px",
              overflowY: "auto",
            }}
          >
            {question.transcript}
          </div>
        </div>
      )}
      {/* Sidebar Toggle */}
      <SidebarToggle
        isVisible={true}
        activeCategory="listening"
        currentQuestionType="678024ac382bce18e30d11d3"
        onQuestionSelect={onQuestionSelect}
      />
    </div>
  );
};

HighlightCorrectSummary.propTypes = {
  question: PropTypes.shape({
    questionId: PropTypes.string.isRequired,
    questionNumber: PropTypes.string,
    questionName: PropTypes.string,
    prompt: PropTypes.string.isRequired,
    difficulty: PropTypes.string,
    media: PropTypes.shape({
      url: PropTypes.string,
    }),
    options: PropTypes.arrayOf(
      PropTypes.shape({
        text: PropTypes.string.isRequired,
        isCorrect: PropTypes.bool.isRequired,
      })
    ).isRequired,
    transcript: PropTypes.string,
    maxScore: PropTypes.number,
  }).isRequired,
  onQuestionSelect: PropTypes.func,
};

export default HighlightCorrectSummary;
