import React from "react";
import PropTypes from "prop-types";
import {
// Practice tracking imports
import {
  logPracticeAttempt,
  usePracticeInfo,
} from "../speech-ace-component/utils/practiceUtils";
  getCategoryName,
  formatScore,
  isAIBased,
} from "../../utils/listeningScoring";

const ScoreDisplay = ({
  categoryId,
  score,
  maxScore,
  feedback,
  breakdown,
  isVisible = true,
  showPercentage = true,
  aiScores = null,
}) => {
  if (!isVisible) return null;

  const categoryName = getCategoryName(categoryId);
  const isAI = isAIBased(categoryId);
  const percentage = maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;

  // Color coding based on score percentage
  const getScoreColor = (percentage) => {
    if (percentage >= 80) return "#4caf50"; // Green
    if (percentage >= 60) return "#ff9800"; // Orange
    return "#f44336"; // Red
  };

  const scoreColor = getScoreColor(percentage);

  return (
    <div
      style={{
        padding: "20px",
        backgroundColor: "#fff",
        borderRadius: "8px",
        marginTop: "20px",
        border: `2px solid ${scoreColor}`,
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
      }}
    >
      {/* Header */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "15px",
          paddingBottom: "10px",
          borderBottom: "1px solid #eee",
        }}
      >
        <h3 style={{ margin: 0, color: "#333" }}>{categoryName} Results</h3>
        {isAI && (
          <span
            style={{
              padding: "4px 8px",
              backgroundColor: "#e3f2fd",
              color: "#1976d2",
              borderRadius: "4px",
              fontSize: "12px",
              fontWeight: "500",
            }}
          >
            AI Scored
          </span>
        )}
      </div>

      {/* Main Score */}
      <div style={{ textAlign: "center", marginBottom: "20px" }}>
        <div
          style={{
            fontSize: "48px",
            fontWeight: "700",
            color: scoreColor,
            marginBottom: "5px",
          }}
        >
          {formatScore(score, maxScore, false)}
        </div>
        {showPercentage && (
          <div
            style={{
              fontSize: "24px",
              color: "#666",
              fontWeight: "500",
            }}
          >
            {percentage}%
          </div>
        )}
      </div>

      {/* AI Scoring Breakdown */}
      {isAI && aiScores && (
        <div style={{ marginBottom: "20px" }}>
          <h4 style={{ margin: "0 0 10px 0", color: "#333" }}>
            Scoring Breakdown
          </h4>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(150px, 1fr))",
              gap: "10px",
            }}
          >
            {["content", "form", "grammar", "vocabulary"].map(
              (criterion) =>
                aiScores[criterion] && (
                  <div
                    key={criterion}
                    style={{
                      padding: "10px",
                      backgroundColor: "#f5f5f5",
                      borderRadius: "6px",
                      textAlign: "center",
                    }}
                  >
                    <div
                      style={{
                        fontSize: "12px",
                        color: "#666",
                        marginBottom: "5px",
                        textTransform: "capitalize",
                      }}
                    >
                      {criterion}
                    </div>
                    <div
                      style={{
                        fontSize: "20px",
                        fontWeight: "600",
                        color: "#1976d2",
                      }}
                    >
                      {aiScores[criterion].score || 0}/
                      {aiScores[criterion].maxScore || 2}
                    </div>
                  </div>
                )
            )}
          </div>
        </div>
      )}

      {/* Breakdown for other question types */}
      {!isAI && breakdown && (
        <div style={{ marginBottom: "15px" }}>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
              gap: "10px",
            }}
          >
            {Object.entries(breakdown).map(([key, value]) => (
              <div
                key={key}
                style={{
                  padding: "8px 12px",
                  backgroundColor: "#f8f9fa",
                  borderRadius: "4px",
                  fontSize: "14px",
                }}
              >
                <strong>
                  {key
                    .replace(/([A-Z])/g, " $1")
                    .replace(/^./, (str) => str.toUpperCase())}
                  :
                </strong>{" "}
                {value}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Feedback */}
      {feedback && (
        <div
          style={{
            padding: "12px",
            backgroundColor: "#f8f9fa",
            borderRadius: "6px",
            borderLeft: `4px solid ${scoreColor}`,
          }}
        >
          <div
            style={{ fontSize: "14px", fontWeight: "500", marginBottom: "5px" }}
          >
            Feedback:
          </div>
          <div style={{ fontSize: "14px", color: "#555", lineHeight: "1.4" }}>
            {feedback}
          </div>
        </div>
      )}

      {/* Performance indicators */}
      <div
        style={{
          marginTop: "15px",
          display: "flex",
          gap: "15px",
          fontSize: "14px",
        }}
      >
        <div style={{ display: "flex", alignItems: "center", gap: "5px" }}>
          <div
            style={{
              width: "12px",
              height: "12px",
              borderRadius: "50%",
              backgroundColor: percentage >= 80 ? "#4caf50" : "#ddd",
            }}
          ></div>
          <span style={{ color: percentage >= 80 ? "#4caf50" : "#999" }}>
            Excellent (80%+)
          </span>
        </div>
        <div style={{ display: "flex", alignItems: "center", gap: "5px" }}>
          <div
            style={{
              width: "12px",
              height: "12px",
              borderRadius: "50%",
              backgroundColor:
                percentage >= 60 && percentage < 80 ? "#ff9800" : "#ddd",
            }}
          ></div>
          <span
            style={{
              color: percentage >= 60 && percentage < 80 ? "#ff9800" : "#999",
            }}
          >
            Good (60-79%)
          </span>
        </div>
        <div style={{ display: "flex", alignItems: "center", gap: "5px" }}>
          <div
            style={{
              width: "12px",
              height: "12px",
              borderRadius: "50%",
              backgroundColor: percentage < 60 ? "#f44336" : "#ddd",
            }}
          ></div>
          <span style={{ color: percentage < 60 ? "#f44336" : "#999" }}>
            Needs Improvement (&lt;60%)
          </span>
        </div>
      </div>
    </div>
  );
};

ScoreDisplay.propTypes = {
  categoryId: PropTypes.string.isRequired,
  score: PropTypes.number.isRequired,
  maxScore: PropTypes.number.isRequired,
  feedback: PropTypes.string,
  breakdown: PropTypes.object,
  isVisible: PropTypes.bool,
  showPercentage: PropTypes.bool,
  aiScores: PropTypes.object,
};

export default ScoreDisplay;
