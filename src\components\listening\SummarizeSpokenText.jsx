import { useState, useEffect, useRef } from "react";
import { Switch, CircularProgress } from "@mui/material";
import AudioPlayer from "react-audio-player";
import axios from "axios";
import { toast } from "react-toastify";
// Subscription imports
import { useSubscriptionCheck } from "../../hooks/useSubscriptionCheck";
import SubscriptionModal from "../others/SubscriptionModal";
import SubscriptionIndicator from "../others/SubscriptionIndicator";
import PropTypes from "prop-types";
// Listening helper utilities
import SidebarToggle from "../common/SidebarToggle";
// Practice tracking imports
import {
  logPracticeAttempt,
  usePracticeInfo,
} from "../speech-ace-component/utils/practiceUtils";
// Answer history and score display
import { server } from "@/api/services/server";
import { postRequests } from "@/api/services/controller";
import SummarizeScoreDialog from "../speech-ace-component/SummarizeScoreDialog";
import UserAnswersSummarize from "../speech-ace-component/UserAnswersSummarize";
import { useAuth } from "../others/AuthContext";

const SummarizeSpokenText = ({ question, onQuestionSelect }) => {
  // Practice tracking
  const { setPracticeCount, setIsPracticed } = usePracticeInfo(
    question.questionId
  );

  const [summary, setSummary] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showAnswer, setShowAnswer] = useState(false);
  const [timeLeft, setTimeLeft] = useState(10 * 60); // 10 minutes in seconds
  const [timerActive, setTimerActive] = useState(true);
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [scores, setScores] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [loading, setLoading] = useState(false); // Changed from isLoading to loading for consistency
  const [showScoreDialog, setShowScoreDialog] = useState(false);
  const [showPreviousAnswers, setShowPreviousAnswers] = useState(false);
  const [showTranscript, setShowTranscript] = useState(false);
  const audioRef = useRef(null);

  const { user } = useAuth();

  // Get user ID from localStorage
  const user_id = localStorage.getItem("isUserId");

  // Extract the actual user ID regardless of format
  const getUserId = () => {
    let userInfo = user?.id || user_id || "";
    if (typeof userInfo === "string") {
      try {
        const parsed = JSON.parse(userInfo);
        return parsed.id || parsed || userInfo;
      } catch (e) {
        return userInfo;
      }
    }
    return userInfo;
  };

  // Subscription logic
  const {
    eligibility,
    loading: subscriptionLoading,
    showSubscriptionModal,
    canViewAnswer,
    handleSubmitWithCheck,
    closeSubscriptionModal,
    showSubscriptionRequiredModal,
  } = useSubscriptionCheck(question.questionId);

  // Auto play audio when component mounts
  useEffect(() => {
    const playAudio = async () => {
      try {
        // Check if audio URL exists
        const audioUrl = question?.media?.url;
        if (!audioUrl) {
          return;
        }

        if (
          audioRef.current &&
          audioRef.current.audioEl &&
          audioRef.current.audioEl.current
        ) {
          // Force load the audio first
          audioRef.current.audioEl.current.load();

          // Try to play after a short delay to ensure it's loaded
          await new Promise((resolve) => setTimeout(resolve, 1000));

          // Sometimes we need to explicitly set the current time to 0
          audioRef.current.audioEl.current.currentTime = 0;

          // Play with user interaction simulation (may help with some browsers)
          const playPromise = audioRef.current.audioEl.current.play();

          if (playPromise !== undefined) {
            playPromise
              .then(() => {
                setIsPlaying(true);
              })
              .catch((error) => {
                // If auto-play fails, don't update the playing state
              });
          }
        }
      } catch (error) {
        // Handle audio playback errors silently
      }
    };

    playAudio();
  }, [question?.media?.url]);

  // Initialize timer
  useEffect(() => {
    let timer;
    if (timerActive && timeLeft > 0 && !isSubmitted) {
      timer = setInterval(() => {
        setTimeLeft((prev) => prev - 1);
      }, 1000);
    } else if (timeLeft === 0 && !isSubmitted) {
      handleDone();
    }
    return () => clearInterval(timer);
  }, [timerActive, timeLeft, isSubmitted]);

  // Format time as MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Update screen width on resize
  useEffect(() => {
    const handleResize = () => setScreenWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const isSmallScreen = screenWidth <= 768;

  // Handle summary input change
  const handleSummaryChange = (e) => {
    if (isSubmitted && !showAnswer) return;
    setSummary(e.target.value);
  };

  // Submit summary for AI scoring using new SST endpoint
  const submitForScoring = async (summaryText) => {
    try {
      const response = await axios.post(
        "https://deep-ai.up.railway.app/api/summarize-spoken-text-scoring",
        {
          text: summaryText,
          originalText:
            question?.originalText ||
            question?.transcript ||
            question?.audioScript ||
            "",
          prompt: question?.prompt || "Summarize the spoken text",
          user_id: getUserId(),
        }
      );

      if (response.data) {
        setScores(response.data);
        return response.data;
      }
    } catch (error) {
      throw error;
    }
  };

  // Handle form submission with subscription check
  const handleDone = async () => {
    if (summary.trim() === "") {
      toast.error("Please write a summary before submitting");
      return;
    }

    // Check subscription before proceeding
    const success = await handleSubmitWithCheck(async () => {
      setLoading(true); // Start loading

      try {
        // Submit for AI scoring using new SST endpoint
        const scoreResult = await submitForScoring(summary);

        // Log practice attempt
        const result = await logPracticeAttempt(question.questionId);
        if (result.success) {
          setPracticeCount(result.practiceCount);
          setIsPracticed(true);
        }

        // Get user info
        const userInfo = getUserId();

        // Ensure score and userId are strings
        const scoreAsString = String(
          scoreResult.score || scoreResult.totalScore || "0"
        );
        const userIdAsString = String(userInfo);

        // Save to answers API
        const uri = server.uri + "answers";
        const answersResponse = await postRequests(uri, {
          score: scoreAsString,
          answer: summary,
          questionId: question?.questionId || "",
          userId: userIdAsString,
          section: question?.section || "listening",
          additionalProps: {
            originalPayload: {
              text: summary,
              originalText:
                question?.originalText ||
                question?.transcript ||
                question?.audioScript ||
                "",
              prompt: question?.prompt || "Summarize the spoken text",
              user_id: userInfo,
              category_id: question?.categoryId || "",
              section: question?.section || "listening",
            },
            scoreResponse: scoreResult,
          },
        });

        // Check if the response indicates an error
        if (answersResponse.status >= 400) {
          const errorText =
            answersResponse.data?.error?.message ||
            answersResponse.statusText ||
            "Unknown error";
          throw new Error(`Error saving to answers API: ${errorText}`);
        }

        setIsSubmitted(true);
        setTimerActive(false);
        setShowScoreDialog(true); // Show the dialog immediately after getting score
        toast.success("Summary submitted and saved successfully");
      } catch (error) {
        toast.error(`Save error: ${error.message}`);
        // Still show score dialog even if saving fails
        setShowScoreDialog(true);
        setIsSubmitted(true);
        setTimerActive(false);
      } finally {
        setLoading(false); // End loading
      }
    });

    if (!success) {
      // If subscription check failed, ensure timer doesn't stop and loading stops
      setLoading(false);
    }
  };

  // Handle show answer with subscription check
  const handleShowAnswer = async () => {
    if (canViewAnswer) {
      setShowAnswer(!showAnswer);
      if (!showAnswer && question.sampleAnswer) {
        setSummary(question.sampleAnswer);
      }
    } else {
      showSubscriptionRequiredModal();
    }
  };

  // Handle redo
  const handleRedo = () => {
    setSummary("");
    setIsSubmitted(false);
    setShowAnswer(false);
    setScores(null);
    setTimeLeft(10 * 60); // Reset to 10 minutes
    setTimerActive(true);
    setShowScoreDialog(false);
    setLoading(false); // Reset loading state
    setShowTranscript(false); // Reset transcript view

    // Restart audio
    if (audioRef.current && audioRef.current.audioEl.current) {
      audioRef.current.audioEl.current.currentTime = 0;
      audioRef.current.audioEl.current.play().catch((e) => {
        // Auto-play prevented due to browser policy
      });
    }
  };

  // Reset component when question changes
  useEffect(() => {
    setSummary("");
    setIsSubmitted(false);
    setShowAnswer(false);
    setScores(null);
    setTimeLeft(10 * 60); // Reset to 10 minutes
    setTimerActive(true);
    setIsPlaying(false);
    setShowScoreDialog(false);
    setLoading(false); // Reset loading state
    setShowTranscript(false); // Reset transcript view

    // Set a short timeout to ensure the audio player has updated its source
    const timer = setTimeout(() => {
      if (
        audioRef.current &&
        audioRef.current.audioEl &&
        audioRef.current.audioEl.current
      ) {
        // Reset the audio to the beginning
        audioRef.current.audioEl.current.currentTime = 0;
        // Force a reload of the audio source
        audioRef.current.audioEl.current.load();
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [question.questionId]);

  // Handle audio end
  const handleAudioEnd = () => {
    setIsPlaying(false);
  };

  // Get word count
  const getWordCount = () => {
    return summary
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0).length;
  };

  // Check if word count is within range (50-70 words for SST)
  const isWordCountValid = () => {
    const count = getWordCount();
    return count >= 50 && count <= 70;
  };

  // Function to show score dialog
  const handleViewScore = () => {
    setShowScoreDialog(true);
  };

  // Function to close score dialog
  const handleCloseDialog = () => {
    setShowScoreDialog(false);
  };

  // Toggle previous answers view
  const togglePreviousAnswers = () => {
    setShowPreviousAnswers(!showPreviousAnswers);
  };

  // Toggle transcript view
  const toggleTranscript = () => {
    setShowTranscript(!showTranscript);
  };

  // Get word count status color
  const getWordCountColor = () => {
    if (getWordCount() < 50) return "#ff3c00"; // Red if below minimum
    if (getWordCount() > 70) return "#ff3c00"; // Red if above maximum
    return "#4caf50"; // Green if within range
  };

  // Audio controls
  const toggleAudio = () => {
    if (audioRef.current && audioRef.current.audioEl.current) {
      if (isPlaying) {
        audioRef.current.audioEl.current.pause();
      } else {
        audioRef.current.audioEl.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  return (
    <div
      style={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: "20px",
        backgroundColor: "#f9f9f9",
        borderRadius: "10px", // Changed from "8px" to match EssayWriting
        position: "relative",
      }}
      className="question-container"
    >
      {/* Subscription Indicator */}
      <SubscriptionIndicator
        eligibility={eligibility}
        loading={subscriptionLoading}
        position="top-right"
      />

      {/* Subscription Modal */}
      <SubscriptionModal
        open={showSubscriptionModal}
        onClose={closeSubscriptionModal}
      />

      {/* Score Dialog Component */}
      {scores && (
        <SummarizeScoreDialog
          aiScore={scores}
          isOpen={showScoreDialog}
          onClose={handleCloseDialog}
        />
      )}

      {/* Header with controls - Updated to match EssayWriting layout */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          width: isSmallScreen ? "100%" : "90%",
          marginBottom: "20px",
        }}
      >
        <div
          style={{
            backgroundColor: timeLeft <= 120 ? "#f44336" : "#ff9800", // Changed to match EssayWriting
            color: "white",
            padding: "5px 15px",
            borderRadius: "20px",
            fontWeight: "bold",
          }}
        >
          Remaining: {formatTime(timeLeft)}
        </div>

     
      </div>

      <div
        style={{
          display: "flex",
          flexDirection: "column",
          width: isSmallScreen ? "100%" : "90%",
          marginTop: "20px",
          marginBottom: "20px",
        }}
      >
        {/* Audio Player */}
        <div
          style={{
            marginBottom: "20px",
            padding: "20px",
            backgroundColor: "#fff",
            borderRadius: "8px",
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          }}
        >
          <h4 style={{ margin: "0 0 15px 0", color: "#333" }}>
            🎧 Listen to the audio
          </h4>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              marginBottom: "10px",
            }}
          >
            <span
              style={{
                backgroundColor: isPlaying ? "#522CFF" : "#f0f0f0",
                color: isPlaying ? "white" : "#333",
                padding: "4px 10px",
                borderRadius: "20px",
                fontSize: "14px",
                fontWeight: "500",
                marginRight: "10px",
                display: "inline-flex",
                alignItems: "center",
              }}
            >
              {isPlaying ? (
                <>
                  <span style={{ marginRight: "5px" }}>●</span> Now Playing
                </>
              ) : (
                "Audio"
              )}
            </span>
            <button
              onClick={toggleAudio}
              style={{
                border: "none",
                backgroundColor: "#140342",
                color: "white",
                padding: "5px 15px",
                borderRadius: "5px",
                cursor: "pointer",
                fontSize: "14px",
                display: "flex",
                alignItems: "center",
                gap: "5px",
              }}
            >
              {isPlaying ? (
                <>
                  <span style={{ fontSize: "16px" }}>⏸️</span> Pause
                </>
              ) : (
                <>
                  <span style={{ fontSize: "16px" }}>▶️</span> Play
                </>
              )}
            </button>
          </div>
          <AudioPlayer
            ref={audioRef}
            src={question?.media?.url || ""}
            controls
            autoPlay={true}
            onCanPlayThrough={() => {
              if (audioRef.current && audioRef.current.audioEl.current) {
                const playPromise = audioRef.current.audioEl.current.play();
                if (playPromise !== undefined) {
                  playPromise
                    .then(() => {
                      setIsPlaying(true);
                    })
                    .catch((e) => {
                      // Auto-play still prevented
                    });
                }
              }
            }}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            onEnded={handleAudioEnd}
            style={{ width: "100%" }}
          />
          {!question?.media?.url && (
            <div
              style={{
                marginTop: "10px",
                padding: "10px",
                backgroundColor: "#fff3cd",
                border: "1px solid #ffeaa7",
                borderRadius: "4px",
                textAlign: "center",
                color: "#856404",
                fontSize: "14px",
              }}
            >
              ⚠️ Audio URL not available for this question
            </div>
          )}
        </div>

        {/* Question prompt */}
        <p
          className="font-bold my-3"
          style={{
            color: "#333",
            fontWeight: "600",
            fontSize: "18px",
            lineHeight: 1.5,
            textAlign: "justify",
            marginBottom: "10px",
            padding: "15px",
            backgroundColor: "#f0f0f0",
            borderRadius: "5px",
            border: "1px solid #e0e0e0",
          }}
        >
          {question.prompt ||
            "You will hear a short report. Write a summary for a fellow student who was not present. You should write 50-70 words. You have 8 minutes to finish this task. Your response will be judged on the quality of your writing and on how well your response presents the key points presented in the report."}
          <div style={{ marginTop: "10px", fontSize: "14px", color: "#666" }}>
            Write 50-70 words in 10 minutes.
          </div>
        </p>

        {/* Summary Text Area */}
        <textarea
          value={summary}
          onChange={handleSummaryChange}
          disabled={isSubmitted || loading}
          placeholder="Write your summary here..."
          style={{
            minHeight: "40vh", // Changed to match EssayWriting
            borderRadius: 5,
            borderWidth: 2,
            borderColor: isSubmitted ? "#ccc" : "black",
            borderStyle: "solid",
            marginTop: 20,
            fontSize: 15,
            color: "black",
            padding: 15,
            fontWeight: "500",
            fontFamily: "Arial, sans-serif",
            resize: "vertical",
          }}
        />

        <div
          className="controls"
          style={{
            fontSize: "14px",
            marginTop: "10px",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <p style={{ color: getWordCountColor(), fontWeight: "bold" }}>
            Word Count: {getWordCount()}
            {getWordCount() < 50 && " (Minimum 50 words required)"}
            {getWordCount() > 70 && " (Maximum 70 words allowed)"}
          </p>
          <p>Characters: {summary.length}</p>
        </div>
      </div>

      {/* Action Buttons - Updated to match EssayWriting layout and behavior */}
      <div
        className="my-7"
        style={{
          display: "flex",
          flexDirection: isSmallScreen ? "column" : "row",
          justifyContent: "space-between",
          alignItems: isSmallScreen ? "stretch" : "center",
          width: isSmallScreen ? "100%" : "90%",
          marginTop: "20px",
          gap: isSmallScreen ? "10px" : "0",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: "12px",
            flexWrap: isSmallScreen ? "wrap" : "nowrap",
          }}
        >
          {!isSubmitted ? (
            <>
              <button
                style={{
                  backgroundColor: loading ? "#a5a5a5" : "#0078d4",
                  padding: "10px 20px",
                  borderRadius: "5px",
                  border: "none",
                  cursor: loading ? "default" : "pointer",
                  fontSize: "14px",
                  color: "white",
                  textAlign: "center",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  minWidth: "100px",
                }}
                onClick={handleDone}
                disabled={loading || !summary.trim()}
              >
                {loading ? (
                  <>
                    <span style={{ marginRight: "10px" }}>Submitting</span>
                    <CircularProgress
                      size={16}
                      thickness={4}
                      style={{ color: "white" }}
                    />
                  </>
                ) : (
                  "Submit"
                )}
              </button>
              <button
                onClick={toggleTranscript}
                style={{
                  backgroundColor: "#9c27b0",
                  padding: "10px 20px",
                  borderRadius: "5px",
                  border: "none",
                  cursor: "pointer",
                  fontSize: "14px",
                  color: "white",
                  textAlign: "center",
                }}
              >
                {showTranscript ? "Hide Transcript" : "Show Transcript"}
              </button>
              <button
                style={{
                  backgroundColor: "#f5f5f5",
                  padding: "10px 20px",
                  borderRadius: "5px",
                  border: "1px solid #140342",
                  cursor: "pointer",
                  fontSize: "14px",
                  color: "#140342",
                  textAlign: "center",
                }}
                onClick={handleRedo}
                disabled={loading}
              >
                Reset
              </button>
              <button
                onClick={togglePreviousAnswers}
                style={{
                  backgroundColor: "#f5f5f5",
                  padding: "10px 20px",
                  borderRadius: "5px",
                  border: "1px solid #140342",
                  cursor: "pointer",
                  fontSize: "14px",
                  color: "#140342",
                  textAlign: "center",
                }}
              >
                {showPreviousAnswers
                  ? "Hide Past Submissions"
                  : "Show Past Submissions"}
              </button>
            </>
          ) : (
            <>
              {/* Show "View Score" button after submission */}
              <button
                onClick={handleViewScore}
                style={{
                  backgroundColor: "#140342",
                  padding: "10px 20px",
                  borderRadius: "5px",
                  border: "none",
                  cursor: "pointer",
                  fontSize: "14px",
                  color: "white",
                  textAlign: "center",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  minWidth: "100px",
                }}
              >
                View Score
              </button>
              <button
                onClick={toggleTranscript}
                style={{
                  backgroundColor: "#9c27b0",
                  padding: "10px 20px",
                  borderRadius: "5px",
                  border: "none",
                  cursor: "pointer",
                  fontSize: "14px",
                  color: "white",
                  textAlign: "center",
                }}
              >
                {showTranscript ? "Hide Transcript" : "Show Transcript"}
              </button>
              <button
                style={{
                  backgroundColor: "#f5f5f5",
                  padding: "10px 20px",
                  borderRadius: "5px",
                  border: "1px solid #140342",
                  cursor: "pointer",
                  fontSize: "14px",
                  color: "#140342",
                  textAlign: "center",
                }}
                onClick={handleRedo}
              >
                Try Again
              </button>
            </>
          )}
        </div>
      </div>

      {/* Transcript Section - Below Action Buttons */}
      {showTranscript && question?.originalText && (
        <div
          style={{
            width: isSmallScreen ? "100%" : "90%",
            marginBottom: "20px",
            padding: "20px",
            backgroundColor: "#fff",
            borderRadius: "8px",
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
            border: "2px solid #e3f2fd",
            marginTop: "20px",
          }}
        >
          <h4
            style={{
              margin: "0 0 15px 0",
              color: "#1976d2",
              fontWeight: "600",
            }}
          >
            📄 Transcript
          </h4>
          <div
            style={{
              padding: "15px",
              backgroundColor: "#f8f9fa",
              borderRadius: "6px",
              border: "1px solid #e9ecef",
              fontSize: "15px",
              lineHeight: "1.6",
              color: "#333",
              fontFamily: "'Arial', sans-serif",
              maxHeight: "300px",
              overflowY: "auto",
            }}
          >
            {question.originalText}
          </div>
        </div>
      )}

      {/* Add keyframe animation for loading spinner */}
      <style>{`
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>

      {/* Sidebar Toggle */}
      <SidebarToggle
        isVisible={true}
        activeCategory="listening"
        currentQuestionType="68556cb5d0001a608d091719"
        onQuestionSelect={onQuestionSelect}
      />
      {/* Show previous answers if toggled */}
      {showPreviousAnswers && (
        <div
          style={{
            width: "100%",
            marginBottom: "20px",
          }}
        >
          <UserAnswersSummarize
            userId={getUserId()}
            questionId={question?.questionId || ""}
          />
        </div>
      )}
    </div>
  );
};

SummarizeSpokenText.propTypes = {
  question: PropTypes.shape({
    questionId: PropTypes.string.isRequired,
    prompt: PropTypes.string,
    media: PropTypes.shape({
      url: PropTypes.string,
    }),
    originalText: PropTypes.string,
    transcript: PropTypes.string,
    audioScript: PropTypes.string,
    sampleAnswer: PropTypes.string,
    maxScore: PropTypes.number,
    categoryId: PropTypes.string,
    section: PropTypes.string,
  }).isRequired,
  onQuestionSelect: PropTypes.func.isRequired,
};

export default SummarizeSpokenText;
