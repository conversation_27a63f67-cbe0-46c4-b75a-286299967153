import React, { useState, useEffect, useRef } from "react";
import { Switch } from "@mui/material";
import AudioPlayer from "react-audio-player";
import PropTypes from "prop-types";
import SidebarToggle from "../common/SidebarToggle";
import {
  logPracticeAttempt,
  usePracticeInfo,
} from "../speech-ace-component/utils/practiceUtils";

const WriteFromDiction = ({ question, onQuestionSelect }) => {
  // Practice tracking
  const { setPracticeCount, setIsPracticed } = usePracticeInfo(
    question.questionId
  );

  const [selectedAnswer, setSelectedAnswer] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showAnswer, setShowAnswer] = useState(false);
  const [timeLeft, setTimeLeft] = useState(3 * 60); // 3 minutes in seconds
  const [timerActive, setTimerActive] = useState(true);
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [score, setScore] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showTranscript, setShowTranscript] = useState(false);
  const [feedback, setFeedback] = useState("");
  const [wordAnalysis, setWordAnalysis] = useState(null);
  const audioRef = useRef(null);
  const maxScore = 10; // Changed to 10

  // Function to get feedback based on score
  const getFeedback = (score) => {
    if (score >= 9) return "Excellent";
    if (score >= 7) return "Good";
    if (score >= 5) return "Average";
    return "Needs Improvement";
  };

  // Function to get detailed marks feedback
  const getMarksFeedback = (score) => {
    if (score >= 9) return {
      grade: "A+",
      message: "Outstanding! You have excellent listening and writing skills.",
      tips: ["Keep up the excellent work!", "You're ready for more challenging content."]
    };
    if (score >= 8) return {
      grade: "A",
      message: "Excellent work! Your accuracy is very impressive.",
      tips: ["Great job on accuracy!", "Focus on any missed words for perfection."]
    };
    if (score >= 7) return {
      grade: "B+",
      message: "Good job! You understood most of the content clearly.",
      tips: ["Review the words you missed.", "Practice with similar audio content."]
    };
    if (score >= 6) return {
      grade: "B",
      message: "Good effort! You're on the right track.",
      tips: ["Listen more carefully to word endings.", "Practice spelling of common words."]
    };
    if (score >= 5) return {
      grade: "C+",
      message: "Average performance. There's room for improvement.",
      tips: ["Try listening to the audio multiple times.", "Focus on understanding each word clearly."]
    };
    if (score >= 4) return {
      grade: "C",
      message: "You got some words right, but need more practice.",
      tips: ["Break down sentences into smaller parts.", "Practice with slower audio first."]
    };
    if (score >= 3) return {
      grade: "D+",
      message: "Keep practicing! Focus on improving your listening skills.",
      tips: ["Listen to each word carefully.", "Practice with simpler content first."]
    };
    if (score >= 2) return {
      grade: "D",
      message: "More practice needed. Don't give up!",
      tips: ["Start with shorter sentences.", "Use transcript to understand better."]
    };
    return {
      grade: "F",
      message: "Keep trying! Practice makes perfect.",
      tips: ["Review the transcript first.", "Practice listening skills daily.", "Start with very simple content."]
    };
  };

  // Function to analyze words and create detailed comparison
  const analyzeWords = (userAnswer, correctAnswer) => {
    const userWords = userAnswer.toLowerCase().trim().split(/\s+/);
    const correctWords = correctAnswer.toLowerCase().trim().split(/\s+/);
    
    const analysis = {
      totalWords: correctWords.length,
      correctWords: 0,
      incorrectWords: 0,
      missingWords: 0,
      extraWords: 0,
      wordComparison: []
    };

    // Create word-by-word comparison
    const maxLength = Math.max(userWords.length, correctWords.length);
    
    for (let i = 0; i < maxLength; i++) {
      const userWord = userWords[i] || '';
      const correctWord = correctWords[i] || '';
      
      if (i < correctWords.length && i < userWords.length) {
        // Both words exist
        if (userWord === correctWord) {
          analysis.correctWords++;
          analysis.wordComparison.push({
            index: i,
            user: userWord,
            correct: correctWord,
            status: 'correct'
          });
        } else {
          analysis.incorrectWords++;
          analysis.wordComparison.push({
            index: i,
            user: userWord,
            correct: correctWord,
            status: 'incorrect'
          });
        }
      } else if (i < correctWords.length) {
        // Missing word
        analysis.missingWords++;
        analysis.wordComparison.push({
          index: i,
          user: '',
          correct: correctWord,
          status: 'missing'
        });
      } else {
        // Extra word
        analysis.extraWords++;
        analysis.wordComparison.push({
          index: i,
          user: userWord,
          correct: '',
          status: 'extra'
        });
      }
    }

    return analysis;
  };

  // Function to get feedback color
  const getFeedbackColor = (score) => {
    if (score >= 9) return "#4CAF50"; // Green for excellent
    if (score >= 7) return "#2196F3"; // Blue for good
    if (score >= 5) return "#FF9800"; // Orange for average
    return "#F44336"; // Red for needs improvement
  };

  // Function to calculate text similarity score (out of 10)
  const calculateScore = (userAnswer, correctAnswer) => {
    if (!userAnswer || !correctAnswer) return 0;

    const analysis = analyzeWords(userAnswer, correctAnswer);
    const accuracy = analysis.correctWords / analysis.totalWords;

    // Calculate score out of 10 based on accuracy
    const calculatedScore = Math.round(accuracy * 10);
    return Math.max(0, Math.min(10, calculatedScore));
  };

  // Function to play audio that can be reused
  useEffect(() => {
    const playAudio = async () => {
      try {
        if (
          audioRef.current &&
          audioRef.current.audioEl &&
          audioRef.current.audioEl.current
        ) {
          // Force load the audio first
          audioRef.current.audioEl.current.load();

          // Try to play after a short delay to ensure it's loaded
          await new Promise((resolve) => setTimeout(resolve, 1000));

          // Sometimes we need to explicitly set the current time to 0
          audioRef.current.audioEl.current.currentTime = 0;

          // Play with user interaction simulation (may help with some browsers)
          const playPromise = audioRef.current.audioEl.current.play();

          if (playPromise !== undefined) {
            playPromise
              .then(() => {
                console.log("Audio started playing automatically");
                setIsPlaying(true);
              })
              .catch((error) => {
                console.warn("Auto-play was prevented:", error);
                // If auto-play fails, don't update the playing state
              });
          }
        }
      } catch (error) {
        console.error("Error in audio playback:", error);
      }
    };

    playAudio();
  }, []);

  // Initialize timer
  useEffect(() => {
    let timer;
    if (timerActive && timeLeft > 0) {
      timer = setInterval(() => {
        setTimeLeft((prev) => prev - 1);
      }, 1000);
    } else if (timeLeft === 0) {
      handleDone();
    }
    return () => clearInterval(timer);
  }, [timerActive, timeLeft]);

  // Format time as MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Update screen width on resize
  useEffect(() => {
    const handleResize = () => setScreenWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const isSmallScreen = screenWidth <= 768;

  // Handle form submission
  const handleDone = async () => {
    setIsSubmitted(true);
    setTimerActive(false);

    // Calculate score based on text similarity (out of 10)
    const calculatedScore = calculateScore(selectedAnswer, question.answer);
    const analysis = analyzeWords(selectedAnswer, question.answer);
    
    setScore(calculatedScore);
    setFeedback(getFeedback(calculatedScore));
    setWordAnalysis(analysis);

    // Log practice attempt
    const result = await logPracticeAttempt(question.questionId);
    if (result.success) {
      setPracticeCount(result.practiceCount);
      setIsPracticed(true);
    }
  };

  // Handle show answer
  const handleShowAnswer = async () => {
    setShowAnswer(!showAnswer);
  };

  // Handle redo
  const handleRedo = () => {
    setSelectedAnswer("");
    setIsSubmitted(false);
    setShowAnswer(false);
    setScore(0);
    setFeedback("");
    setWordAnalysis(null);
    setTimeLeft(3 * 60); // Reset to 3 minutes
    setTimerActive(true);

    // Restart audio
    if (audioRef.current && audioRef.current.audioEl.current) {
      audioRef.current.audioEl.current.currentTime = 0;
      audioRef.current.audioEl.current.play().catch((e) => {
        console.log("Auto-play prevented due to browser policy:", e);
      });
    }
  };

  // Toggle audio play/pause
  const toggleAudio = () => {
    if (audioRef.current && audioRef.current.audioEl.current) {
      if (isPlaying) {
        audioRef.current.audioEl.current.pause();
      } else {
        audioRef.current.audioEl.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  // Handle audio end
  const handleAudioEnd = () => {
    setIsPlaying(false);
  };

  const toggleTranscript = () => {
    setShowTranscript(!showTranscript);
  };

  // Reset component when question changes
  useEffect(() => {
    setSelectedAnswer("");
    setIsSubmitted(false);
    setShowAnswer(false);
    setScore(0);
    setFeedback("");
    setWordAnalysis(null);
    setTimeLeft(3 * 60);
    setTimerActive(true);
    setIsPlaying(false);

    // Set a short timeout to ensure the audio player has updated its source
    const timer = setTimeout(() => {
      if (
        audioRef.current &&
        audioRef.current.audioEl &&
        audioRef.current.audioEl.current
      ) {
        audioRef.current.audioEl.current.currentTime = 0;
        audioRef.current.audioEl.current.load();
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [question.questionId]);

  return (
    <div
      style={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: "20px",
        backgroundColor: "#f9f9f9",
        borderRadius: "8px",
        position: "relative",
      }}
      className="question-container"
    >
      {/* Header with timer and category name */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          width: isSmallScreen ? "100%" : "90%",
          marginBottom: "15px",
        }}
      >
        {/* Timer display */}
        <div
          style={{
            display: "flex",
            alignItems: "center",
            backgroundColor: timeLeft < 60 ? "#ffe6e6" : "#f0f0f0",
            padding: "5px 15px",
            borderRadius: "20px",
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          }}
        >
          <span
            style={{
              fontSize: "18px",
              fontWeight: "bold",
              color: timeLeft < 60 ? "#cc0000" : "#333",
            }}
          >
            Time: {formatTime(timeLeft)}
          </span>
        </div>
      </div>

      {/* Score display - only shown after submission */}
      {isSubmitted && (
        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            width: isSmallScreen ? "100%" : "90%",
            marginBottom: "15px",
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              backgroundColor: "#f0f0f0",
              padding: "5px 15px",
              borderRadius: "20px",
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
            }}
          >
            <span style={{ fontSize: "16px", fontWeight: "bold" }}>
              Score:{" "}
            </span>
            <span
              style={{
                fontSize: "18px",
                fontWeight: "bold",
                color: score > 0 ? "#008800" : "#333",
                marginLeft: "5px",
              }}
            >
              {score}/{maxScore}
            </span>
          </div>
        </div>
      )}

      {/* Main content card */}
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          width: isSmallScreen ? "100%" : "90%",
          backgroundColor: "white",
          padding: "25px",
          borderRadius: "8px",
          boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
          marginBottom: "20px",
        }}
      >
        {/* Audio player - styled for better visibility */}
        <div
          style={{
            marginBottom: "25px",
            padding: "15px",
            backgroundColor: "#f5f5f5",
            borderRadius: "8px",
            border: "1px solid #ddd",
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              marginBottom: "10px",
            }}
          >
            <span
              style={{
                backgroundColor: isPlaying ? "#522CFF" : "#f0f0f0",
                color: isPlaying ? "white" : "#333",
                padding: "4px 10px",
                borderRadius: "20px",
                fontSize: "14px",
                fontWeight: "500",
                marginRight: "10px",
                display: "inline-flex",
                alignItems: "center",
              }}
            >
              {isPlaying ? (
                <>
                  <span style={{ marginRight: "5px" }}>●</span> Now Playing
                </>
              ) : (
                "Audio"
              )}
            </span>
            <button
              onClick={toggleAudio}
              style={{
                border: "none",
                backgroundColor: "#140342",
                color: "white",
                padding: "5px 15px",
                borderRadius: "5px",
                cursor: "pointer",
                fontSize: "14px",
                display: "flex",
                alignItems: "center",
                gap: "5px",
              }}
            >
              {isPlaying ? (
                <>
                  <span style={{ fontSize: "16px" }}>⏸️</span> Pause
                </>
              ) : (
                <>
                  <span style={{ fontSize: "16px" }}>▶️</span> Play
                </>
              )}
            </button>
          </div>

          <AudioPlayer
            ref={audioRef}
            src={question.media?.url || ""}
            controls
            autoPlay={true}
            onCanPlayThrough={() => {
              if (audioRef.current && audioRef.current.audioEl.current) {
                const playPromise = audioRef.current.audioEl.current.play();
                if (playPromise !== undefined) {
                  playPromise
                    .then(() => {
                      setIsPlaying(true);
                    })
                    .catch((e) => console.log("Auto-play still prevented:", e));
                }
              }
            }}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            onEnded={handleAudioEnd}
            style={{ width: "100%" }}
          />
        </div>

        {/* Question prompt */}
        <p
          style={{
            color: "#333",
            fontWeight: "500",
            fontSize: "20px",
            lineHeight: 1.6,
            marginTop: 0,
            marginBottom: "30px",
            textAlign: "justify",
          }}
        >
          {question.prompt}
        </p>

        {/* Text input area */}
        <div style={{ marginBottom: "20px" }}>
          <textarea
            value={selectedAnswer}
            onChange={(e) => setSelectedAnswer(e.target.value)}
            placeholder="Type the sentence exactly as you hear it..."
            disabled={isSubmitted && !showAnswer}
            style={{
              width: "100%",
              height: "120px",
              padding: "15px",
              borderRadius: "8px",
              border: "2px solid #ddd",
              fontSize: "16px",
              lineHeight: "1.5",
              resize: "vertical",
              fontFamily: "inherit",
              backgroundColor: isSubmitted && !showAnswer ? "#f5f5f5" : "white",
              color: "#333",
            }}
          />
          <div
            style={{
              marginTop: "8px",
              fontSize: "14px",
              color: "#666",
              textAlign: "right",
            }}
          >
            Word Count:{" "}
            {
              selectedAnswer
                .trim()
                .split(/\s+/)
                .filter((word) => word.length > 0).length
            }
          </div>
        </div>

        {/* Show correct answer when toggled or submitted */}
        {(showAnswer || (isSubmitted && showAnswer)) && (
          <div
            style={{
              marginTop: "20px",
              padding: "15px",
              backgroundColor: "#e6ffe6",
              border: "1px solid #008800",
              borderRadius: "8px",
            }}
          >
            <p
              style={{
                margin: "0 0 10px 0",
                fontSize: "16px",
                fontWeight: "bold",
                color: "#008800",
              }}
            >
              Correct Answer:
            </p>
            <p
              style={{
                margin: 0,
                fontSize: "16px",
                color: "#333",
                fontStyle: "italic",
              }}
            >
              "{question.answer}"
            </p>
          </div>
        )}

               {/* Enhanced feedback message after submission */}
               {isSubmitted && !showAnswer && (
          <div
            style={{
              padding: "20px",
              borderRadius: "12px",
              backgroundColor: "#ffffff",
              border: `3px solid ${getFeedbackColor(score)}`,
              textAlign: "left",
              minWidth: isSmallScreen ? "auto" : "320px",
              boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
            }}
          >
            {/* Header with grade and score */}
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                marginBottom: "15px",
                paddingBottom: "10px",
                borderBottom: `2px solid ${getFeedbackColor(score)}`,
              }}
            >
              <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
                <div
                  style={{
                    backgroundColor: getFeedbackColor(score),
                    color: "white",
                    padding: "8px 16px",
                    borderRadius: "25px",
                    fontSize: "18px",
                    fontWeight: "bold",
                  }}
                >
                  {getMarksFeedback(score).grade}
                </div>
                <span
                  style={{
                    fontSize: "20px",
                    fontWeight: "bold",
                    color: getFeedbackColor(score),
                  }}
                >
                  {score}/10
                </span>
              </div>
              <div
                style={{
                  backgroundColor: getFeedbackColor(score),
                  color: "white",
                  padding: "6px 14px",
                  borderRadius: "20px",
                  fontSize: "16px",
                  fontWeight: "600",
                }}
              >
                {feedback}
              </div>
            </div>

            {/* Feedback message */}
            <div style={{ marginBottom: "15px" }}>
              <p
                style={{
                  margin: "0 0 10px 0",
                  fontSize: "16px",
                  color: "#333",
                  fontWeight: "500",
                  lineHeight: "1.4",
                }}
              >
                {getMarksFeedback(score).message}
              </p>
            </div>

            {/* Tips section */}
            <div>
              <h5
                style={{
                  margin: "0 0 8px 0",
                  fontSize: "14px",
                  color: getFeedbackColor(score),
                  fontWeight: "600",
                  textTransform: "uppercase",
                  letterSpacing: "0.5px",
                }}
              >
                💡 Tips for Improvement:
              </h5>
              <ul
                style={{
                  margin: 0,
                  paddingLeft: "20px",
                  fontSize: "14px",
                  color: "#555",
                  lineHeight: "1.5",
                }}
              >
                {getMarksFeedback(score).tips.map((tip, index) => (
                  <li key={index} style={{ marginBottom: "4px" }}>
                    {tip}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {/* Word Analysis Section - only shown after submission */}
        {isSubmitted && wordAnalysis && (
          <div
            style={{
              marginTop: "20px",
              padding: "20px",
              backgroundColor: "#f8f9fa",
              border: "2px solid #e9ecef",
              borderRadius: "8px",
            }}
          >
            <h4
              style={{
                margin: "0 0 15px 0",
                color: "#495057",
                fontWeight: "600",
                fontSize: "18px",
              }}
            >
              📊 Word Analysis
            </h4>
            
            {/* Analysis Summary */}
            <div
              style={{
                display: "flex",
                gap: "20px",
                marginBottom: "20px",
                flexWrap: "wrap",
              }}
            >
              <div style={{ 
                backgroundColor: "#d4edda", 
                padding: "8px 12px", 
                borderRadius: "6px",
                border: "1px solid #c3e6cb"
              }}>
                <span style={{ fontWeight: "bold", color: "#155724" }}>
                  ✓ Correct: {wordAnalysis.correctWords}
                </span>
              </div>
              <div style={{ 
                backgroundColor: "#f8d7da", 
                padding: "8px 12px", 
                borderRadius: "6px",
                border: "1px solid #f5c6cb"
              }}>
                <span style={{ fontWeight: "bold", color: "#721c24" }}>
                  ✗ Incorrect: {wordAnalysis.incorrectWords}
                </span>
              </div>
              <div style={{ 
                backgroundColor: "#fff3cd", 
                padding: "8px 12px", 
                borderRadius: "6px",
                border: "1px solid #ffeeba"
              }}>
                <span style={{ fontWeight: "bold", color: "#856404" }}>
                  ⊘ Missing: {wordAnalysis.missingWords}
                </span>
              </div>
              {wordAnalysis.extraWords > 0 && (
                <div style={{ 
                  backgroundColor: "#e2e3e5", 
                  padding: "8px 12px", 
                  borderRadius: "6px",
                  border: "1px solid #d6d8db"
                }}>
                  <span style={{ fontWeight: "bold", color: "#383d41" }}>
                    + Extra: {wordAnalysis.extraWords}
                  </span>
                </div>
              )}
            </div>

            {/* Word by Word Comparison */}
            <div>
              <h5 style={{ 
                margin: "0 0 10px 0", 
                color: "#495057",
                fontSize: "16px"
              }}>
                Word-by-Word Comparison:
              </h5>
              <div
                style={{
                  display: "flex",
                  flexWrap: "wrap",
                  gap: "8px",
                  padding: "15px",
                  backgroundColor: "white",
                  borderRadius: "6px",
                  border: "1px solid #dee2e6",
                }}
              >
                {wordAnalysis.wordComparison.map((word, index) => (
                  <div
                    key={index}
                    style={{
                      display: "inline-flex",
                      flexDirection: "column",
                      alignItems: "center",
                      padding: "8px",
                      borderRadius: "6px",
                      backgroundColor: 
                        word.status === 'correct' ? '#d4edda' :
                        word.status === 'incorrect' ? '#f8d7da' :
                        word.status === 'missing' ? '#fff3cd' : '#e2e3e5',
                      border: `1px solid ${
                        word.status === 'correct' ? '#c3e6cb' :
                        word.status === 'incorrect' ? '#f5c6cb' :
                        word.status === 'missing' ? '#ffeeba' : '#d6d8db'
                      }`,
                      minWidth: "60px",
                      position: "relative",
                    }}
                  >
                    {/* Position indicator */}
                    <span
                      style={{
                        fontSize: "10px",
                        color: "#6c757d",
                        fontWeight: "bold",
                        marginBottom: "4px",
                      }}
                    >
                      {index + 1}
                    </span>
                    
                    {/* User's word */}
                    <span
                      style={{
                        fontSize: "14px",
                        fontWeight: "500",
                        color: word.status === 'missing' ? '#856404' : '#333',
                        marginBottom: "2px",
                        textAlign: "center",
                      }}
                    >
                      {word.user || '(missing)'}
                    </span>
                    
                    {/* Correct word if different */}
                    {word.status !== 'correct' && word.status !== 'extra' && (
                      <span
                        style={{
                          fontSize: "12px",
                          color: "#155724",
                          fontStyle: "italic",
                          textAlign: "center",
                        }}
                      >
                        → {word.correct}
                      </span>
                    )}
                    
                    {/* Status indicator for extra words */}
                    {word.status === 'extra' && (
                      <span
                        style={{
                          fontSize: "12px",
                          color: "#383d41",
                          fontStyle: "italic",
                          textAlign: "center",
                        }}
                      >
                        (extra)
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Controls section */}
      <div
        style={{
          display: "flex",
          flexDirection: isSmallScreen ? "column" : "row",
          justifyContent: "space-between",
          alignItems: isSmallScreen ? "stretch" : "center",
          width: isSmallScreen ? "100%" : "90%",
          gap: "15px",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: "15px",
            flexWrap: isSmallScreen ? "wrap" : "nowrap",
            justifyContent: isSmallScreen ? "center" : "flex-start",
          }}
        >
          <button
            style={{
              backgroundColor: isSubmitted ? "#d0d0d0" : "#D9D9D9",
              borderWidth: 1,
              borderColor: "#140342",
              borderStyle: "solid",
              padding: "10px 20px",
              borderRadius: "5px",
              cursor:
                isSubmitted || !selectedAnswer.trim() ? "default" : "pointer",
              opacity: isSubmitted || !selectedAnswer.trim() ? 0.7 : 1,
            }}
            onClick={handleDone}
            disabled={isSubmitted || !selectedAnswer.trim()}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
              }}
            >
              Submit
            </p>
          </button>

          <button
            style={{
              backgroundColor: "#D9D9D9",
              borderWidth: 1,
              borderColor: "#140342",
              borderStyle: "solid",
              padding: "10px 20px",
              borderRadius: "5px",
              cursor: "pointer",
            }}
            onClick={handleRedo}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
              }}
            >
              Re-do
            </p>
          </button>

          <div
            style={{
              display: "flex",
              alignItems: "center",
              backgroundColor: "#f9f9f9",
              padding: "5px 10px",
              borderRadius: "5px",
              border: "1px solid #ddd",
            }}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
                marginRight: "5px",
              }}
            >
              {showAnswer ? "Hide Answer" : "Show Answer"}
            </p>
            <Switch
              onChange={() => handleShowAnswer()}
              checked={showAnswer}
              size="small"
            />
          </div>

          <button
            onClick={toggleTranscript}
            style={{
              backgroundColor: "#9c27b0",
              padding: "10px 20px",
              borderRadius: "5px",
              border: "none",
              cursor: "pointer",
              fontSize: "14px",
              color: "white",
              textAlign: "center",
            }}
          >
            {showTranscript ? "Hide Transcript" : "Show Transcript"}
          </button>
        </div>

 
      </div>

      {/* Transcript Section - Below Action Buttons */}
      {showTranscript && question?.transcript && (
        <div
          style={{
            width: isSmallScreen ? "100%" : "90%",
            marginBottom: "20px",
            padding: "20px",
            backgroundColor: "#fff",
            borderRadius: "8px",
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
            border: "2px solid #e3f2fd",
            marginTop: "20px",
          }}
        >
          <h4
            style={{
              margin: "0 0 15px 0",
              color: "#1976d2",
              fontWeight: "600",
            }}
          >
            📄 Transcript
          </h4>
          <div
            style={{
              padding: "15px",
              backgroundColor: "#f8f9fa",
              borderRadius: "6px",
              border: "1px solid #e9ecef",
              fontSize: "15px",
              lineHeight: "1.6",
              color: "#333",
              fontFamily: "'Arial', sans-serif",
              maxHeight: "300px",
              overflowY: "auto",
            }}
          >
            {question.transcript}
          </div>
        </div>
      )}

      

      {/* Sidebar Toggle */}
      <SidebarToggle
        isVisible={true}
        activeCategory="listening"
        currentQuestionType="678024ac382bce18e30d11d3"
        onQuestionSelect={onQuestionSelect}
      />
    </div>
  );
};

WriteFromDiction.propTypes = {
  question: PropTypes.shape({
    questionId: PropTypes.string,
    prompt: PropTypes.string,
    maxScore: PropTypes.number,
    answer: PropTypes.string,
    media: PropTypes.shape({
      url: PropTypes.string,
      type: PropTypes.string,
    }),
    transcript: PropTypes.string,
  }).isRequired,
  onQuestionSelect: PropTypes.func.isRequired,
};

export default WriteFromDiction;