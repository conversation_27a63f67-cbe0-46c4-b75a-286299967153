import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  Box,
  Button,
  Typography,
  Paper,
  LinearProgress,
  Alert,
  Grid,
} from "@mui/material";
import MicIcon from "@mui/icons-material/Mic";
import StopIcon from "@mui/icons-material/Stop";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import VolumeUpIcon from "@mui/icons-material/VolumeUp";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import HeadphonesIcon from "@mui/icons-material/Headphones";

const AudioCheck = ({ onComplete, onNext }) => {
  // Audio states
  const [speakerStatus, setSpeakerStatus] = useState("idle"); // idle, testing, success, failed
  const [microphoneStatus, setMicrophoneStatus] = useState("idle"); // idle, requesting, success, failed

  // Recording states
  const [isRecording, setIsRecording] = useState(false);
  const [volumeLevel, setVolumeLevel] = useState(0);
  const [recordedAudio, setRecordedAudio] = useState(null);

  // Refs
  const audioRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const streamRef = useRef(null);
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const animationFrameRef = useRef(null);

  const testAudioUrl = "/assets/dummy1.mp3";

  // Derived state
  const canProceed =
    speakerStatus === "success" && microphoneStatus === "success";

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop());
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
      if (recordedAudio) {
        URL.revokeObjectURL(recordedAudio);
      }
    };
  }, [recordedAudio]);

  // Volume monitoring
  const monitorVolume = useCallback(() => {
    if (!analyserRef.current) return;

    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);

    const updateVolume = () => {
      if (analyserRef.current) {
        analyserRef.current.getByteFrequencyData(dataArray);
        const average =
          dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
        setVolumeLevel(Math.min(100, (average / 255) * 100));
        animationFrameRef.current = requestAnimationFrame(updateVolume);
      }
    };

    updateVolume();
  }, []);

  // Speaker test
  const testSpeakers = useCallback(async () => {
    if (!audioRef.current) return;

    setSpeakerStatus("testing");

    try {
      audioRef.current.currentTime = 0;
      await audioRef.current.play();
    } catch (error) {
      console.error("Speaker test failed:", error);
      setSpeakerStatus("failed");
    }
  }, []);

  const handleAudioEnd = useCallback(() => {
    setSpeakerStatus("success");
  }, []);

  const handleAudioError = useCallback(() => {
    setSpeakerStatus("failed");
  }, []);

  // Microphone test
  const testMicrophone = useCallback(async () => {
    setMicrophoneStatus("requesting");

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: 1,
          sampleRate: 16000,
          echoCancellation: true,
          noiseSuppression: true,
        },
      });

      streamRef.current = stream;

      // Setup audio context for volume monitoring
      const audioContext = new AudioContext({ sampleRate: 16000 });
      const sourceNode = audioContext.createMediaStreamSource(stream);
      const analyser = audioContext.createAnalyser();
      analyser.fftSize = 256;
      sourceNode.connect(analyser);

      audioContextRef.current = audioContext;
      analyserRef.current = analyser;

      // Setup media recorder
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      let audioChunks = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunks.push(event.data);
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(audioChunks, { type: "audio/wav" });
        const url = URL.createObjectURL(blob);
        setRecordedAudio(url);
        audioChunks = [];
      };

      setMicrophoneStatus("success");
      monitorVolume();
    } catch (error) {
      console.error("Microphone test failed:", error);
      setMicrophoneStatus("failed");
    }
  }, [monitorVolume]);

  // Recording controls
  const startRecording = useCallback(() => {
    if (mediaRecorderRef.current && microphoneStatus === "success") {
      mediaRecorderRef.current.start();
      setIsRecording(true);
    }
  }, [microphoneStatus]);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  }, [isRecording]);

  const playRecording = useCallback(() => {
    if (recordedAudio) {
      const audio = new Audio(recordedAudio);
      audio.play();
    }
  }, [recordedAudio]);

  // Status indicator component
  const StatusIndicator = ({ status, label }) => {
    const getConfig = () => {
      switch (status) {
        case "success":
          return {
            icon: CheckCircleIcon,
            color: "success.main",
            text: "Working",
          };
        case "failed":
          return { icon: ErrorIcon, color: "error.main", text: "Failed" };
        case "testing":
        case "requesting":
          return { icon: null, color: "primary.main", text: "Testing..." };
        default:
          return { icon: null, color: "grey.500", text: "Not tested" };
      }
    };

    const config = getConfig();
    const IconComponent = config.icon;

    return (
      <Box display="flex" alignItems="center" gap={1}>
        {IconComponent ? (
          <IconComponent sx={{ color: config.color }} />
        ) : (
          <Box
            sx={{
              width: 20,
              height: 20,
              borderRadius: "50%",
              backgroundColor: config.color,
              opacity:
                status === "testing" || status === "requesting" ? 0.7 : 1,
            }}
          />
        )}
        <Typography variant="body2" sx={{ color: config.color }}>
          {label}: {config.text}
        </Typography>
      </Box>
    );
  };

  return (
    <Box sx={{ maxWidth: 800, mx: "auto", p: 3 }}>
      <Paper elevation={2} sx={{ p: 4, backgroundColor: "#f4f0ff" }}>
        <Typography
          variant="h4"
          align="center"
          gutterBottom
          sx={{ color: "#140342", fontWeight: 600 }}
        >
          Audio Equipment Check
        </Typography>

        <Typography
          variant="body1"
          align="center"
          sx={{ mb: 4, color: "#666" }}
        >
          Before starting your PTE practice test, let's make sure your audio
          equipment is working properly.
        </Typography>

        <Grid container spacing={4}>
          {/* Speaker Test */}
          <Grid item xs={12} md={6}>
            <Paper
              elevation={2}
              sx={{
                p: 3,
                height: "100%",
                backgroundColor: "white",
                border: "2px solid #140342",
              }}
            >
              <Box display="flex" alignItems="center" gap={2} mb={2}>
                <VolumeUpIcon sx={{ color: "#140342" }} />
                <Typography
                  variant="h6"
                  sx={{ color: "#140342", fontWeight: 600 }}
                >
                  Speaker Test
                </Typography>
              </Box>

              <Typography variant="body2" sx={{ mb: 2, color: "#666" }}>
                Click the button below to test your speakers or headphones.
              </Typography>

              <Box mb={2}>
                <StatusIndicator status={speakerStatus} label="Speakers" />
              </Box>

              <Button
                variant="outlined"
                startIcon={<PlayArrowIcon />}
                onClick={testSpeakers}
                disabled={speakerStatus === "testing"}
                fullWidth
                sx={{
                  mb: 2,
                  color: "#140342",
                  borderColor: "#140342",
                  "&:hover": {
                    backgroundColor: "#f4f0ff",
                    borderColor: "#140342",
                  },
                }}
              >
                {speakerStatus === "testing"
                  ? "Playing Test Audio..."
                  : "Test Speakers"}
              </Button>

              <audio
                ref={audioRef}
                src={testAudioUrl}
                onEnded={handleAudioEnd}
                onError={handleAudioError}
                preload="metadata"
              />

              {speakerStatus === "success" && (
                <Alert severity="success" sx={{ mt: 1 }}>
                  <HeadphonesIcon sx={{ mr: 1 }} />
                  Audio is working! Make sure you can hear clearly.
                </Alert>
              )}

              {speakerStatus === "failed" && (
                <Alert severity="error" sx={{ mt: 1 }}>
                  <ErrorIcon sx={{ mr: 1 }} />
                  Audio test failed. Please check your speakers/headphones and
                  try again.
                </Alert>
              )}
            </Paper>
          </Grid>

          {/* Microphone Test */}
          <Grid item xs={12} md={6}>
            <Paper
              elevation={2}
              sx={{
                p: 3,
                height: "100%",
                backgroundColor: "white",
                border: "2px solid #140342",
              }}
            >
              <Box display="flex" alignItems="center" gap={2} mb={2}>
                <MicIcon sx={{ color: "#140342" }} />
                <Typography
                  variant="h6"
                  sx={{ color: "#140342", fontWeight: 600 }}
                >
                  Microphone Test
                </Typography>
              </Box>

              <Typography variant="body2" sx={{ mb: 2, color: "#666" }}>
                We need access to your microphone for speaking tasks.
              </Typography>

              <Box mb={2}>
                <StatusIndicator status={microphoneStatus} label="Microphone" />
              </Box>

              {microphoneStatus === "idle" && (
                <Button
                  variant="outlined"
                  startIcon={<MicIcon />}
                  onClick={testMicrophone}
                  fullWidth
                  sx={{
                    mb: 2,
                    color: "#140342",
                    borderColor: "#140342",
                    "&:hover": {
                      backgroundColor: "#f4f0ff",
                      borderColor: "#140342",
                    },
                  }}
                >
                  Test Microphone
                </Button>
              )}

              {microphoneStatus === "success" && (
                <>
                  <Box mb={2}>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      Microphone Level:
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={volumeLevel}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: "grey.200",
                        "& .MuiLinearProgress-bar": {
                          backgroundColor:
                            volumeLevel > 20 ? "success.main" : "warning.main",
                        },
                      }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      Speak normally to see the level indicator move
                    </Typography>
                  </Box>

                  <Box display="flex" gap={1} mb={2}>
                    <Button
                      variant={isRecording ? "contained" : "outlined"}
                      color={isRecording ? "error" : "primary"}
                      startIcon={isRecording ? <StopIcon /> : <MicIcon />}
                      onClick={isRecording ? stopRecording : startRecording}
                      size="small"
                    >
                      {isRecording ? "Stop" : "Record Test"}
                    </Button>

                    {recordedAudio && (
                      <Button
                        variant="outlined"
                        startIcon={<PlayArrowIcon />}
                        onClick={playRecording}
                        size="small"
                      >
                        Play Back
                      </Button>
                    )}
                  </Box>

                  <Alert severity="success">
                    Microphone is working! Make sure you speak clearly and at a
                    normal volume.
                  </Alert>
                </>
              )}

              {microphoneStatus === "failed" && (
                <Alert severity="error">
                  Microphone access failed. Please check your browser
                  permissions and try again.
                </Alert>
              )}
            </Paper>
          </Grid>
        </Grid>

        {/* Equipment Check Summary */}
        {canProceed && (
          <Box mt={4}>
            <Alert severity="success" sx={{ mb: 3 }}>
              <CheckCircleIcon sx={{ mr: 1 }} />
              Great! Your audio equipment is working properly. You're ready to
              proceed.
            </Alert>
          </Box>
        )}

        {/* Instructions */}
        <Box mt={4}>
          <Typography variant="h6" gutterBottom>
            Important Tips:
          </Typography>
          <Box component="ul" sx={{ pl: 2 }}>
            <Typography component="li" variant="body2" sx={{ mb: 1 }}>
              Use headphones or earphones for better audio quality
            </Typography>
            <Typography component="li" variant="body2" sx={{ mb: 1 }}>
              Find a quiet environment for your test
            </Typography>
            <Typography component="li" variant="body2" sx={{ mb: 1 }}>
              Position your microphone close to your mouth
            </Typography>
            <Typography component="li" variant="body2" sx={{ mb: 1 }}>
              Speak clearly and at a normal pace
            </Typography>
          </Box>
        </Box>

        {/* Navigation */}
        <Box display="flex" justifyContent="center" mt={4}>
          <Button
            variant="contained"
            size="large"
            onClick={onNext}
            disabled={!canProceed}
            sx={{
              px: 4,
              py: 1.5,
              fontSize: "1.1rem",
              backgroundColor: "#140342",
              color: "white",
              fontWeight: "700",
              "&:hover": {
                backgroundColor: "#1e0a5c",
              },
            }}
          >
            Continue to Personal Introduction
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default AudioCheck;
