import React, { useState } from "react";
import {
  Box,
  Stepper,
  Step,
  StepLabel,
  Typography,
  Paper,
  Button,
} from "@mui/material";
import TestInstructions from "./TestInstructions";
import AudioCheck from "./AudioCheck";
import PersonalIntroduction from "./PersonalIntroduction";

const CompleteIntroFlow = ({ onComplete }) => {
  const [activeStep, setActiveStep] = useState(0);

  const steps = [
    {
      label: "Test Instructions",
      description: "Review test format and guidelines",
    },
    {
      label: "Audio Check",
      description: "Test your microphone and speakers",
    },
    {
      label: "Personal Introduction",
      description: "Practice speaking for 1 minute",
    },
  ];

  const handleNext = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep((prev) => prev + 1);
    } else {
      // Complete the introduction flow
      onComplete();
    }
  };

  const handleBack = () => {
    setActiveStep((prev) => prev - 1);
  };

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <TestInstructions />
            <Box display="flex" justifyContent="center" mt={4}>
              <Button
                variant="contained"
                size="large"
                onClick={handleNext}
                sx={{
                  px: 4,
                  py: 1.5,
                  fontSize: "1.1rem",
                  backgroundColor: "#140342",
                  color: "white",
                  fontWeight: "700",
                  "&:hover": {
                    backgroundColor: "#1e0a5c",
                  },
                }}
              >
                Continue to Audio Check
              </Button>
            </Box>
          </Box>
        );
      case 1:
        return <AudioCheck onNext={handleNext} />;
      case 2:
        return <PersonalIntroduction onNext={handleNext} />;
      default:
        return null;
    }
  };

  return (
    <Box sx={{ minHeight: "100vh", backgroundColor: "#f4f0ff", py: 4 }}>
      <Box sx={{ maxWidth: 1200, mx: "auto", px: 3 }}>
        {/* Header */}
        <Box textAlign="center" mb={4}>
          <Typography
            variant="h3"
            sx={{ color: "#140342", fontWeight: 700, mb: 1 }}
          >
            PTE Practice Test Setup
          </Typography>
          <Typography variant="h6" sx={{ color: "#666" }}>
            Complete the setup process to begin your practice test
          </Typography>
        </Box>

        {/* Progress Stepper */}
        <Paper
          elevation={3}
          sx={{
            p: 3,
            mb: 4,
            backgroundColor: "white",
            border: "2px solid #140342",
          }}
        >
          <Stepper
            activeStep={activeStep}
            alternativeLabel
            sx={{
              "& .MuiStepLabel-root .Mui-completed": {
                color: "#140342",
              },
              "& .MuiStepLabel-root .Mui-active": {
                color: "#140342",
              },
              "& .MuiStepConnector-line": {
                borderColor: "#140342",
              },
            }}
          >
            {steps.map((step, index) => (
              <Step key={step.label}>
                <StepLabel>
                  <Typography variant="h6" sx={{ color: "#140342" }}>
                    {step.label}
                  </Typography>
                  <Typography variant="body2" sx={{ color: "#666" }}>
                    {step.description}
                  </Typography>
                </StepLabel>
              </Step>
            ))}
          </Stepper>

          {/* Navigation buttons for non-interactive steps */}
          {activeStep > 0 && (
            <Box display="flex" justifyContent="center" mt={3}>
              <Button
                onClick={handleBack}
                sx={{
                  mr: 1,
                  color: "#140342",
                  borderColor: "#140342",
                  "&:hover": {
                    backgroundColor: "#f4f0ff",
                  },
                }}
                variant="outlined"
              >
                Back
              </Button>
            </Box>
          )}
        </Paper>

        {/* Step Content */}
        {renderStepContent(activeStep)}
      </Box>
    </Box>
  );
};

export default CompleteIntroFlow;
