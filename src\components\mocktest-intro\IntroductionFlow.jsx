import React, { useState } from "react";
import {
  Box,
  Stepper,
  Step,
  StepLabel,
  Typography,
  Paper,
} from "@mui/material";
import AudioCheck from "./AudioCheck";
import PersonalIntroduction from "./PersonalIntroduction";

const IntroductionFlow = ({ onComplete }) => {
  const [activeStep, setActiveStep] = useState(0);

  const steps = [
    {
      label: "Audio Check",
      description: "Test your microphone and speakers",
    },
    {
      label: "Personal Introduction",
      description: "Practice speaking for 1 minute",
    },
  ];

  const handleNext = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep((prev) => prev + 1);
    } else {
      // Complete the introduction flow
      onComplete();
    }
  };

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return <AudioCheck onNext={handleNext} />;
      case 1:
        return <PersonalIntroduction onNext={handleNext} />;
      default:
        return null;
    }
  };

  return (
    <Box sx={{ minHeight: "100vh", backgroundColor: "#f5f5f5", py: 4 }}>
      <Box sx={{ maxWidth: 1000, mx: "auto", px: 3 }}>
        {/* Header */}
        <Box textAlign="center" mb={4}>
          <Typography
            variant="h3"
            sx={{ color: "#140342", fontWeight: 700, mb: 1 }}
          >
            PTE Practice Test Setup
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Let's make sure everything is ready for your practice test
          </Typography>
        </Box>

        {/* Progress Stepper */}
        <Paper elevation={1} sx={{ p: 3, mb: 4 }}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((step, index) => (
              <Step key={step.label}>
                <StepLabel>
                  <Typography variant="h6">{step.label}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {step.description}
                  </Typography>
                </StepLabel>
              </Step>
            ))}
          </Stepper>
        </Paper>

        {/* Step Content */}
        {renderStepContent(activeStep)}
      </Box>
    </Box>
  );
};

export default IntroductionFlow;
