import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  Button,
  Typography,
  Paper,
  LinearProgress,
  Alert,
  Chip,
} from "@mui/material";
import MicIcon from "@mui/icons-material/Mic";
import StopIcon from "@mui/icons-material/Stop";
import TimerIcon from "@mui/icons-material/Timer";
import PersonIcon from "@mui/icons-material/Person";
import InfoIcon from "@mui/icons-material/Info";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";

const PersonalIntroduction = ({ onNext, onComplete }) => {
  const [phase, setPhase] = useState("instruction"); // instruction, preparation, speaking, completed
  const [timer, setTimer] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [recordedBlob, setRecordedBlob] = useState(null);
  const [audioUrl, setAudioUrl] = useState("");
  const [canProceed, setCanProceed] = useState(false);

  const mediaRecorderRef = useRef(null);
  const timerIntervalRef = useRef(null);
  const streamRef = useRef(null);

  const preparationTime = 15; // 15 seconds to prepare
  const speakingTime = 60; // 60 seconds to speak

  useEffect(() => {
    return () => {
      // Cleanup
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop());
      }
    };
  }, []);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  };

  const startPreparation = async () => {
    setPhase("preparation");
    setTimer(preparationTime);

    // Initialize microphone
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: 1,
          sampleRate: 16000,
          echoCancellation: true,
          noiseSuppression: true,
        },
      });
      streamRef.current = stream;
    } catch (error) {
      console.error("Microphone access failed:", error);
      alert("Please allow microphone access to continue.");
      return;
    }

    // Start countdown timer
    timerIntervalRef.current = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timerIntervalRef.current);
          startSpeaking();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const startSpeaking = async () => {
    setPhase("speaking");
    setTimer(speakingTime);

    if (streamRef.current) {
      const recorder = new MediaRecorder(streamRef.current);
      let audioChunks = [];

      recorder.ondataavailable = (event) => {
        audioChunks.push(event.data);
      };

      recorder.onstop = () => {
        const blob = new Blob(audioChunks, { type: "audio/wav" });
        setRecordedBlob(blob);
        const url = URL.createObjectURL(blob);
        setAudioUrl(url);
        setPhase("completed");
        setCanProceed(true);
        audioChunks = [];
      };

      mediaRecorderRef.current = recorder;
      recorder.start();
      setIsRecording(true);

      // Start speaking timer
      timerIntervalRef.current = setInterval(() => {
        setTimer((prev) => {
          if (prev <= 1) {
            clearInterval(timerIntervalRef.current);
            stopRecording();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);

      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
      }

      setPhase("completed");
      setCanProceed(true);
    }
  };

  const renderInstructionPhase = () => (
    <Box>
      <Box display="flex" alignItems="center" gap={2} mb={3}>
        <PersonIcon color="primary" sx={{ fontSize: 32 }} />
        <Typography variant="h4" sx={{ color: "#140342", fontWeight: 600 }}>
          Personal Introduction
        </Typography>
      </Box>

      <Alert severity="info" sx={{ mb: 3 }}>
        This is a practice session and will not be scored. It's designed to help
        you get comfortable with the recording process.
      </Alert>

      <Typography variant="h6" gutterBottom sx={{ color: "#140342" }}>
        Instructions:
      </Typography>

      <Box component="ul" sx={{ pl: 2, mb: 3 }}>
        <Typography component="li" variant="body1" sx={{ mb: 1 }}>
          You will have <strong>15 seconds</strong> to prepare your response
        </Typography>
        <Typography component="li" variant="body1" sx={{ mb: 1 }}>
          Then you will have <strong>1 minute</strong> to speak
        </Typography>
        <Typography component="li" variant="body1" sx={{ mb: 1 }}>
          Speak clearly and naturally about yourself
        </Typography>
        <Typography component="li" variant="body1" sx={{ mb: 1 }}>
          You can stop early if you finish before the time is up
        </Typography>
      </Box>

      <Paper
        elevation={2}
        sx={{
          p: 3,
          mb: 3,
          backgroundColor: "white",
          border: "2px solid #140342",
        }}
      >
        <Typography variant="h6" gutterBottom sx={{ color: "#140342" }}>
          Sample Introduction Topics:
        </Typography>
        <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
          <Chip
            label="Your name and background"
            size="small"
            sx={{ backgroundColor: "#f4f0ff", color: "#140342" }}
          />
          <Chip
            label="Where you're from"
            size="small"
            sx={{ backgroundColor: "#f4f0ff", color: "#140342" }}
          />
          <Chip
            label="Your occupation or studies"
            size="small"
            sx={{ backgroundColor: "#f4f0ff", color: "#140342" }}
          />
          <Chip
            label="Your interests or hobbies"
            size="small"
            sx={{ backgroundColor: "#f4f0ff", color: "#140342" }}
          />
          <Chip
            label="Your goals for learning English"
            size="small"
            sx={{ backgroundColor: "#f4f0ff", color: "#140342" }}
          />
        </Box>
        <Typography variant="body2" sx={{ color: "#666" }}>
          <strong>Example:</strong> "Hello, my name is... I'm from... Currently,
          I work as... In my free time, I enjoy... I'm taking this test
          because..."
        </Typography>
      </Paper>

      <Box display="flex" justifyContent="center">
        <Button
          variant="contained"
          size="large"
          onClick={startPreparation}
          sx={{
            px: 4,
            py: 1.5,
            fontSize: "1.1rem",
            backgroundColor: "#140342",
            color: "white",
            fontWeight: "700",
            "&:hover": {
              backgroundColor: "#1e0a5c",
            },
          }}
        >
          Start Personal Introduction
        </Button>
      </Box>
    </Box>
  );

  const renderPreparationPhase = () => (
    <Box textAlign="center">
      <TimerIcon color="primary" sx={{ fontSize: 48, mb: 2 }} />

      <Typography variant="h4" gutterBottom sx={{ color: "#140342" }}>
        Preparation Time
      </Typography>

      <Typography
        variant="h2"
        sx={{
          color: "#140342",
          fontWeight: 700,
          mb: 2,
          fontFamily: "monospace",
        }}
      >
        {formatTime(timer)}
      </Typography>

      <LinearProgress
        variant="determinate"
        value={((preparationTime - timer) / preparationTime) * 100}
        sx={{
          height: 8,
          borderRadius: 4,
          mb: 3,
          backgroundColor: "grey.200",
          "& .MuiLinearProgress-bar": {
            backgroundColor: "#140342",
          },
        }}
      />

      <Typography variant="body1" color="text.secondary">
        Think about what you want to say. The recording will start automatically
        when the timer reaches zero.
      </Typography>

      <Paper elevation={1} sx={{ p: 2, mt: 3, backgroundColor: "#fff3e0" }}>
        <Typography variant="body2" sx={{ fontStyle: "italic" }}>
          💡 <strong>Tip:</strong> Organize your thoughts now. What key points
          do you want to mention about yourself?
        </Typography>
      </Paper>
    </Box>
  );

  const renderSpeakingPhase = () => (
    <Box textAlign="center">
      <Box
        display="flex"
        alignItems="center"
        justifyContent="center"
        gap={2}
        mb={3}
      >
        <MicIcon color="error" sx={{ fontSize: 48 }} />
        <Typography variant="h4" sx={{ color: "#d32f2f" }}>
          Recording Now
        </Typography>
      </Box>

      <Typography
        variant="h2"
        sx={{
          color: "#d32f2f",
          fontWeight: 700,
          mb: 2,
          fontFamily: "monospace",
        }}
      >
        {formatTime(timer)}
      </Typography>

      <LinearProgress
        variant="determinate"
        value={((speakingTime - timer) / speakingTime) * 100}
        sx={{
          height: 8,
          borderRadius: 4,
          mb: 3,
          backgroundColor: "grey.200",
          "& .MuiLinearProgress-bar": {
            backgroundColor: "#d32f2f",
          },
        }}
      />

      <Typography variant="body1" sx={{ mb: 3 }}>
        Speak clearly about yourself. You have up to 1 minute.
      </Typography>

      <Button
        variant="outlined"
        color="error"
        startIcon={<StopIcon />}
        onClick={stopRecording}
        size="large"
        sx={{ px: 3 }}
      >
        Finish Early
      </Button>

      <Paper elevation={1} sx={{ p: 2, mt: 3, backgroundColor: "#ffebee" }}>
        <Typography variant="body2">
          🎙️ <strong>Remember:</strong> Speak naturally and don't worry about
          making it perfect. This is just practice!
        </Typography>
      </Paper>
    </Box>
  );

  const renderCompletedPhase = () => (
    <Box textAlign="center">
      <CheckCircleIcon color="success" sx={{ fontSize: 64, mb: 2 }} />

      <Typography variant="h4" gutterBottom sx={{ color: "#2e7d32" }}>
        Recording Complete!
      </Typography>

      <Typography variant="body1" sx={{ mb: 3, color: "text.secondary" }}>
        Great job! You've completed your personal introduction practice.
      </Typography>

      {audioUrl && (
        <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Your Recording:
          </Typography>
          <audio
            controls
            src={audioUrl}
            style={{
              width: "100%",
              maxWidth: 400,
              borderRadius: "4px",
            }}
          />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            You can listen to your recording to hear how you sound
          </Typography>
        </Paper>
      )}

      <Alert severity="success" sx={{ mb: 3 }}>
        This was a practice session and was not scored. You're now ready to
        begin the actual PTE practice test!
      </Alert>

      <Box display="flex" gap={2} justifyContent="center">
        <Button
          variant="outlined"
          onClick={() => {
            setPhase("instruction");
            setTimer(0);
            setRecordedBlob(null);
            setAudioUrl("");
            setCanProceed(false);
            setIsRecording(false);
          }}
        >
          Try Again
        </Button>

        <Button
          variant="contained"
          size="large"
          onClick={onNext}
          sx={{
            px: 4,
            py: 1.5,
            fontSize: "1.1rem",
            backgroundColor: "#140342",
            color: "white",
            fontWeight: "700",
            "&:hover": {
              backgroundColor: "#1e0a5c",
            },
          }}
        >
          Continue to PTE Practice Test
        </Button>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ maxWidth: 800, mx: "auto", p: 3 }}>
      <Paper
        elevation={2}
        sx={{ p: 4, minHeight: 600, backgroundColor: "#f4f0ff" }}
      >
        {phase === "instruction" && renderInstructionPhase()}
        {phase === "preparation" && renderPreparationPhase()}
        {phase === "speaking" && renderSpeakingPhase()}
        {phase === "completed" && renderCompletedPhase()}
      </Paper>
    </Box>
  );
};

export default PersonalIntroduction;
