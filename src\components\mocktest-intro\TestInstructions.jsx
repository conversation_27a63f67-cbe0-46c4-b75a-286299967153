import React from "react";
import {
  Box,
  Typography,
  Paper,
  Alert,
  Grid,
  Card,
  CardContent,
} from "@mui/material";
import InfoIcon from "@mui/icons-material/Info";
import VolumeUpIcon from "@mui/icons-material/VolumeUp";
import MicIcon from "@mui/icons-material/Mic";
import RecordVoiceOverIcon from "@mui/icons-material/RecordVoiceOver";
import AutoStoriesIcon from "@mui/icons-material/AutoStories";

const TestInstructions = () => {
  const sections = [
    {
      title: "Personal Introduction",
      icon: <RecordVoiceOverIcon sx={{ color: "#140342" }} />,
      duration: "1 min",
      description:
        "Introduce yourself - Not scored, practice session to get comfortable with recording",
    },
    {
      title: "Speaking & Writing",
      icon: <MicIcon sx={{ color: "#140342" }} />,
      duration: "54-67 min",
      description:
        "Read aloud, Repeat sentence, Describe image, Re-tell lecture, Answer short questions, Summarize written text, Write essay",
    },
    {
      title: "Reading",
      icon: <AutoStoriesIcon sx={{ color: "#140342" }} />,
      duration: "29-30 min",
      description: "Multiple choice, Re-order paragraphs, Fill in the blanks",
    },
    {
      title: "Listening",
      icon: <VolumeUpIcon sx={{ color: "#140342" }} />,
      duration: "30-43 min",
      description:
        "Summarize spoken text, Multiple choice, Fill in blanks, Highlight summary, Select missing word",
    },
  ];

  return (
    <Box sx={{ maxWidth: 1000, mx: "auto", p: 3 }}>
      <Paper elevation={2} sx={{ p: 4, backgroundColor: "#f4f0ff" }}>
        <Typography
          variant="h3"
          align="center"
          gutterBottom
          sx={{ color: "#140342", fontWeight: 700 }}
        >
          PTE Practice Test Instructions
        </Typography>

        <Alert
          severity="info"
          sx={{ mb: 4, backgroundColor: "white", border: "1px solid #140342" }}
        >
          <Typography sx={{ color: "#140342" }}>
            This practice test will help you familiarize yourself with the PTE
            Academic format.
          </Typography>
        </Alert>

        <Typography variant="h5" gutterBottom sx={{ color: "#140342", mb: 3 }}>
          Test Sections:
        </Typography>

        <Grid container spacing={3} sx={{ mb: 4 }}>
          {sections.map((section, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card
                elevation={3}
                sx={{
                  height: "100%",
                  backgroundColor: "white",
                  border: "2px solid #140342",
                  transition: "all 0.3s ease",
                  "&:hover": {
                    transform: "translateY(-4px)",
                    boxShadow: "0 8px 25px rgba(20, 3, 66, 0.15)",
                  },
                }}
              >
                <CardContent sx={{ textAlign: "center", p: 3 }}>
                  <Box mb={2}>{section.icon}</Box>
                  <Typography
                    variant="h6"
                    gutterBottom
                    sx={{ color: "#140342", fontWeight: 600 }}
                  >
                    {section.title}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      mb: 2,
                      color: "#140342",
                      fontWeight: 600,
                      fontSize: "1rem",
                    }}
                  >
                    {section.duration}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{ color: "#666", lineHeight: 1.4 }}
                  >
                    {section.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Typography variant="h5" gutterBottom sx={{ color: "#140342" }}>
          Important Tips:
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Paper
              elevation={2}
              sx={{
                p: 3,
                backgroundColor: "white",
                border: "2px solid #140342",
              }}
            >
              <Typography
                variant="h6"
                gutterBottom
                sx={{ color: "#140342", fontWeight: 600 }}
              >
                ✅ Do:
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                <Typography
                  component="li"
                  variant="body2"
                  sx={{ mb: 1, color: "#666" }}
                >
                  Use headphones for better audio quality
                </Typography>
                <Typography
                  component="li"
                  variant="body2"
                  sx={{ mb: 1, color: "#666" }}
                >
                  Find a quiet environment
                </Typography>
                <Typography
                  component="li"
                  variant="body2"
                  sx={{ mb: 1, color: "#666" }}
                >
                  Speak clearly and naturally
                </Typography>
                <Typography
                  component="li"
                  variant="body2"
                  sx={{ mb: 1, color: "#666" }}
                >
                  Manage your time effectively
                </Typography>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper
              elevation={2}
              sx={{
                p: 3,
                backgroundColor: "white",
                border: "2px solid #FF3C00",
              }}
            >
              <Typography
                variant="h6"
                gutterBottom
                sx={{ color: "#FF3C00", fontWeight: 600 }}
              >
                ❌ Don't:
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                <Typography
                  component="li"
                  variant="body2"
                  sx={{ mb: 1, color: "#666" }}
                >
                  Leave any answers blank
                </Typography>
                <Typography
                  component="li"
                  variant="body2"
                  sx={{ mb: 1, color: "#666" }}
                >
                  Spend too much time on one question
                </Typography>
                <Typography
                  component="li"
                  variant="body2"
                  sx={{ mb: 1, color: "#666" }}
                >
                  Ignore the preparation time
                </Typography>
                <Typography
                  component="li"
                  variant="body2"
                  sx={{ mb: 1, color: "#666" }}
                >
                  Panic if you make mistakes
                </Typography>
              </Box>
            </Paper>
          </Grid>
        </Grid>

        <Alert
          severity="success"
          sx={{ mt: 4, backgroundColor: "white", border: "2px solid #140342" }}
        >
          <Typography variant="body1" sx={{ color: "#140342" }}>
            <strong>Ready to begin?</strong> Complete the audio check and
            personal introduction to start your practice test.
          </Typography>
        </Alert>
      </Paper>
    </Box>
  );
};

export default TestInstructions;
