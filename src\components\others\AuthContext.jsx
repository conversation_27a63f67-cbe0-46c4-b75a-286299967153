import React, { createContext, useContext, useState, useCallback } from "react";

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(() => {
    const storedUser = localStorage.getItem("user");
    const storedToken = localStorage.getItem("token");
    const storedUserId = localStorage.getItem("isUserId");
    const isAuthenticated = localStorage.getItem("isAuthenticated");

    if (storedUser && storedToken && isAuthenticated === "true") {
      return {
        name: storedUser,
        token: storedToken,
        id: storedUserId,
        isAuthenticated: true,
      };
    }
    return null;
  });

  const login = useCallback(async (userData) => {
    try {
      setUser(userData);
      localStorage.setItem("isAuthenticated", "true");
      localStorage.setItem("isUserId", userData.id);
      localStorage.setItem("user", userData.name);
      localStorage.setItem("token", userData.token);
      return true;
    } catch (error) {
      console.error("Error during login:", error);
      return false;
    }
  }, []);

  const logout = useCallback(() => {
    setUser(null);
    localStorage.removeItem("isAuthenticated");
    localStorage.removeItem("isUserId");
    localStorage.removeItem("user");
    localStorage.removeItem("userEmail");
    localStorage.removeItem("token");
  }, []);

  return (
    <AuthContext.Provider value={{ user, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
