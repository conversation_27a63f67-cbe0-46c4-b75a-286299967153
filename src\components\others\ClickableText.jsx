import React, { useState, } from "react";
import WordDefinitionModal from "./WordDefinitionModal";

const ClickableText = ({ text }) => {
  const [selectedWord, setSelectedWord] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [clickedWordIndex, setClickedWordIndex] = useState(-1);
  const [wordStatus, setWordStatus] = useState({});
  const [hoverWordIndex, setHoverWordIndex] = useState(-1);

  // Function to handle word click
  const handleWordClick = (word, index) => {
    // Clean the word (remove punctuation)
    const cleanWord = word.replace(/[.,!?;:"'()]/g, "").toLowerCase();

    // Skip if the word is too short or common prepositions/articles
    const commonWords = [
      "a",
      "an",
      "the",
      "in",
      "on",
      "at",
      "by",
      "for",
      "to",
      "of",
    ];
    if (cleanWord.length <= 1 || commonWords.includes(cleanWord)) {
      return;
    }

    setSelectedWord(cleanWord);
    setClickedWordIndex(index);
    setIsModalOpen(true);

    // Mark this word as looked up
    setWordStatus((prev) => ({
      ...prev,
      [cleanWord]: true,
    }));
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setClickedWordIndex(-1);
  };

  // Mouse hover handlers
  const handleMouseEnter = (index, word) => {
    const cleanWord = word.replace(/[.,!?;:"'()]/g, "").toLowerCase();
    const commonWords = [
      "a",
      "an",
      "the",
      "in",
      "on",
      "at",
      "by",
      "for",
      "to",
      "of",
    ];
    if (cleanWord.length <= 1 || commonWords.includes(cleanWord)) {
      return;
    }
    setHoverWordIndex(index);
  };

  const handleMouseLeave = () => {
    setHoverWordIndex(-1);
  };

  // Split text into words and preserve punctuation for display
  const processedText = React.useMemo(() => {
    // Match words, punctuation, and whitespace separately
    const tokens = text.match(/\b\w+\b|[.,!?;:"'()]|\s+/g) || [];

    // Group the tokens into words (which may include trailing punctuation)
    const words = [];
    let currentWord = "";

    tokens.forEach((token) => {
      // If it's whitespace, add the current word to the array and reset
      if (/^\s+$/.test(token)) {
        if (currentWord) {
          words.push(currentWord);
          currentWord = "";
        }
      } else {
        // Otherwise add to the current word
        currentWord += token;

        // If it's punctuation, add the current word to the array and reset
        if (/^[.,!?;:"'()]$/.test(token)) {
          words.push(currentWord);
          currentWord = "";
        }
      }
    });

    // Add any remaining word
    if (currentWord) {
      words.push(currentWord);
    }

    return words;
  }, [text]);

  return (
    <div>
      <span
        style={{
          color: "#333",
          fontWeight: "600",
          fontSize: "18px",
          lineHeight: 1.5,
          textAlign: "justify",
          marginBottom: "40px",
          userSelect: "none", // Prevent text selection for better UX
        }}
      >
        {processedText.map((word, index) => {
          // Get the base word without punctuation for status checking
          const baseWord = word.replace(/[.,!?;:"'()]/g, "").toLowerCase();
          const hasBeenLookedUp = wordStatus[baseWord];
          const isHovering = hoverWordIndex === index;
          const commonWords = [
            "a",
            "an",
            "the",
            "in",
            "on",
            "at",
            "by",
            "for",
            "to",
            "of",
          ];
          const isClickable =
            baseWord.length > 1 && !commonWords.includes(baseWord);

          return (
            <span
              key={index}
              onClick={() => handleWordClick(word, index)}
              onMouseEnter={() => handleMouseEnter(index, word)}
              onMouseLeave={handleMouseLeave}
              style={{
                cursor: isClickable ? "pointer" : "default",
                display: "inline-block",
                padding: "2px 4px",
                borderRadius: "4px",
                backgroundColor:
                  clickedWordIndex === index
                    ? "rgba(20, 3, 66, 0.1)"
                    : isHovering && isClickable
                    ? "rgba(20, 3, 66, 0.05)"
                    : "transparent",
                textDecoration: hasBeenLookedUp ? "underline" : "none",
                textDecorationColor: "#140342",
                textDecorationThickness: "2px",
                textUnderlineOffset: "4px",
                position: "relative",
                transition: "all 0.2s ease",
              }}
              className={isClickable ? "word-clickable" : ""}
            >
              {word}
              {isHovering && isClickable && (
                <div
                  style={{
                    position: "absolute",
                    bottom: "-26px",
                    left: "50%",
                    transform: "translateX(-50%)",
                    backgroundColor: "#140342",
                    color: "white",
                    padding: "3px 8px",
                    borderRadius: "4px",
                    fontSize: "12px",
                    whiteSpace: "nowrap",
                    zIndex: 5,
                    boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
                  }}
                >
                  <div
                    style={{
                      position: "absolute",
                      top: "-6px",
                      left: "50%",
                      marginLeft: "-6px",
                      borderWidth: "0 6px 6px 6px",
                      borderStyle: "solid",
                      borderColor:
                        "transparent transparent #140342 transparent",
                    }}
                  ></div>
                  Click for definition
                </div>
              )}
            </span>
          );
        })}
      </span>

      <style>
        {`
          .word-clickable:hover {
            background-color: rgba(20, 3, 66, 0.05);
          }
        `}
      </style>

      {/* Word definition modal */}
      <WordDefinitionModal
        word={selectedWord}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        autoPlay={true} // Enable auto-pronunciation
      />
    </div>
  );
};

export default ClickableText;
