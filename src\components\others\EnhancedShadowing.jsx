import React, { useState, useEffect, useRef } from "react";

const EnhancedShadowing = ({ text, questionId }) => {
  const [textAnalysis, setTextAnalysis] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedFeature, setSelectedFeature] = useState("all");
  const containerRef = useRef(null);

  // Static analysis data for demonstration
  // In a real application, this would come from an API call based on the questionId
  useEffect(() => {
    const analyzeText = () => {
      try {
        setLoading(true);

        // Mock analysis data structure
        // This would normally come from an API request
        const mockAnalysis = {
          words: text.split(/\s+/).map((word, index) => {
            // Assign linguistic features randomly for demonstration
            const features = {
              pause: Math.random() < 0.15,
              loss: Math.random() < 0.1,
              linking:
                Math.random() < 0.2 &&
                word.match(/[aeiou]$/i) &&
                index < text.split(/\s+/).length - 1,
              weak: [
                "a",
                "an",
                "the",
                "of",
                "to",
                "in",
                "for",
                "on",
                "by",
                "at",
                "and",
                "but",
              ].includes(word.toLowerCase()),
            };

            return {
              word,
              features,
              // Apply special styling to indicate feature classes
              className: Object.entries(features)
                .filter(([_, hasFeature]) => hasFeature)
                .map(([feature]) => feature)
                .join(" "),
            };
          }),
          // Add some analysis metadata
          metadata: {
            pacing: "Good pace with natural pauses",
            rhythm: "Consistent speech rhythm throughout",
            pronunciation: "Clear pronunciation with proper emphasis",
            suggestions: [
              "Pay attention to linking words for smoother delivery",
              "Reduce emphasis on weak words for more natural speech",
              "Add slight pauses after commas and periods",
            ],
          },
        };

        setTextAnalysis(mockAnalysis);
        setLoading(false);
      } catch (err) {
        setError("Failed to analyze text: " + err.message);
        setLoading(false);
      }
    };

    // Simulate API delay
    const timer = setTimeout(() => {
      analyzeText();
    }, 800);

    return () => clearTimeout(timer);
  }, [text, questionId]);

  const handleFeatureToggle = (feature) => {
    setSelectedFeature(feature);
  };

  if (loading) {
    return (
      <div
        style={{
          padding: "20px",
          backgroundColor: "white",
          borderRadius: "8px",
          boxShadow: "0 2px 5px rgba(0,0,0,0.05)",
          textAlign: "center",
        }}
      >
        <div
          style={{
            display: "inline-block",
            width: "24px",
            height: "24px",
            borderRadius: "50%",
            border: "3px solid #f3f3f3",
            borderTop: "3px solid #3498db",
            animation: "spin 1s linear infinite",
          }}
        ></div>
        <p>Analyzing text patterns...</p>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  if (error) {
    return (
      <div
        style={{
          padding: "20px",
          backgroundColor: "white",
          borderRadius: "8px",
          border: "1px solid #f5c6cb",
          color: "#721c24",
          boxShadow: "0 2px 5px rgba(0,0,0,0.05)",
        }}
      >
        <h4>Error</h4>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      style={{
        width: "100%",
        backgroundColor: "white",
        borderRadius: "10px",
        padding: "20px",
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
      }}
    >
      {/* Toggle buttons for features */}
      <div
        style={{
          display: "flex",
          flexWrap: "wrap",
          gap: "10px",
          marginBottom: "15px",
        }}
      >
        <FeatureButton
          feature="all"
          label="All Features"
          selected={selectedFeature === "all"}
          onClick={() => handleFeatureToggle("all")}
          color="#6c757d"
        />
        <FeatureButton
          feature="pause"
          label="Pause"
          selected={selectedFeature === "pause"}
          onClick={() => handleFeatureToggle("pause")}
          color="#dc3545"
        />
        <FeatureButton
          feature="loss"
          label="Loss"
          selected={selectedFeature === "loss"}
          onClick={() => handleFeatureToggle("loss")}
          color="#fd7e14"
        />
        <FeatureButton
          feature="linking"
          label="Linking"
          selected={selectedFeature === "linking"}
          onClick={() => handleFeatureToggle("linking")}
          color="#007bff"
        />
        <FeatureButton
          feature="weak"
          label="Weak"
          selected={selectedFeature === "weak"}
          onClick={() => handleFeatureToggle("weak")}
          color="#6f42c1"
        />
      </div>

      {/* Highlighted text */}
      <div
        style={{
          padding: "15px",
          backgroundColor: "#f8f9fa",
          borderRadius: "8px",
          marginBottom: "20px",
          fontSize: "18px",
          lineHeight: 1.6,
          fontFamily: "Arial, sans-serif",
        }}
      >
        {textAnalysis?.words.map((wordObj, index) => {
          const { word, features, className } = wordObj;
          const isActive =
            selectedFeature === "all" || features[selectedFeature];
          let highlightColor = "inherit";

          if (isActive) {
            if (selectedFeature === "all") {
              if (features.pause) highlightColor = "#dc3545"; // red
              else if (features.loss) highlightColor = "#fd7e14"; // orange
              else if (features.linking) highlightColor = "#007bff"; // blue
              else if (features.weak) highlightColor = "#6f42c1"; // purple
            } else {
              switch (selectedFeature) {
                case "pause":
                  highlightColor = "#dc3545";
                  break;
                case "loss":
                  highlightColor = "#fd7e14";
                  break;
                case "linking":
                  highlightColor = "#007bff";
                  break;
                case "weak":
                  highlightColor = "#6f42c1";
                  break;
                default:
                  highlightColor = "inherit";
              }
            }
          }

          return (
            <span
              key={index}
              style={{
                backgroundColor: isActive
                  ? `${highlightColor}20`
                  : "transparent",
                color: isActive ? highlightColor : "inherit",
                padding: "2px 2px",
                margin: "0 1px",
                borderRadius: "3px",
                fontWeight: isActive ? "500" : "normal",
                borderBottom: isActive ? `2px solid ${highlightColor}` : "none",
                transition: "all 0.2s ease",
              }}
              title={className.split(" ").join(", ")}
            >
              {word}{" "}
            </span>
          );
        })}
      </div>

      {/* Legend */}
      <div
        style={{
          display: "flex",
          flexWrap: "wrap",
          gap: "15px",
          marginBottom: "20px",
          padding: "15px",
          backgroundColor: "#f8f9fa",
          borderRadius: "8px",
        }}
      >
        <LegendItem
          color="#dc3545"
          label="Pause"
          description="Words where you should briefly pause"
        />
        <LegendItem
          color="#fd7e14"
          label="Loss"
          description="Words that might be lost or unclear"
        />
        <LegendItem
          color="#007bff"
          label="Linking"
          description="Words that should be linked with the next word"
        />
        <LegendItem
          color="#6f42c1"
          label="Weak"
          description="Words that should be de-emphasized"
        />
      </div>

      {/* Tips */}
      <div
        style={{
          padding: "15px",
          backgroundColor: "#e9ecef",
          borderRadius: "8px",
          marginTop: "20px",
        }}
      >
        <h4 style={{ marginTop: 0, fontSize: "16px", fontWeight: "600" }}>
          Pronunciation Tips
        </h4>
        <ul style={{ paddingLeft: "20px", margin: "10px 0 0" }}>
          {textAnalysis?.metadata.suggestions.map((tip, i) => (
            <li key={i} style={{ marginBottom: "5px" }}>
              {tip}
            </li>
          ))}
          <li>
            Practice with the audio at different speeds to improve your
            shadowing
          </li>
        </ul>
      </div>
    </div>
  );
};

// Component for feature selection buttons
const FeatureButton = ({ feature, label, selected, onClick, color }) => (
  <button
    onClick={onClick}
    style={{
      padding: "6px 12px",
      backgroundColor: selected ? color : "white",
      color: selected ? "white" : color,
      border: `1px solid ${color}`,
      borderRadius: "20px",
      cursor: "pointer",
      fontSize: "14px",
      fontWeight: selected ? "600" : "normal",
      transition: "all 0.2s ease",
    }}
  >
    {label}
  </button>
);

// Component for legend items
const LegendItem = ({ color, label, description }) => (
  <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
    <div
      style={{
        width: "16px",
        height: "16px",
        backgroundColor: color,
        borderRadius: "3px",
      }}
    ></div>
    <div>
      <strong style={{ fontSize: "14px" }}>{label}</strong>
      <span
        style={{
          fontSize: "12px",
          color: "#6c757d",
          marginLeft: "5px",
        }}
      >
        - {description}
      </span>
    </div>
  </div>
);

export default EnhancedShadowing;
