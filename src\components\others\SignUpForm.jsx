import { Link, useNavigate } from "react-router-dom";
import { useState } from "react";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import axios from "axios";

import { server } from "../../api/services/server";

const api = axios.create({
  baseURL: server.uri.slice(0, -1), // Remove trailing slash
});

export default function SignUpForm() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    mobileNumber: "",
    confirmPassword: "",
  });
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    // Validate password and confirm password
    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match.");
      setLoading(false);
      return;
    }

    try {
      // Create payload according to the new API requirements
      const payload = {
        emailId: formData.email,
        name: formData.name,
        mobileNumber: formData.mobileNumber,
        password: formData.password,
      };

      // Use the correct signup endpoint
      const response = await api.post("/signup", payload);

      if (response.data) {
        console.log("Registration response:", response.data);

        // Check if response indicates OTP was sent
        if (
          response.data.message &&
          response.data.message.includes("OTP sent")
        ) {
          toast.success(
            "OTP sent to your email. Please verify to complete registration."
          );

          // Store signup data for OTP verification
          localStorage.setItem("signupEmail", formData.email);
          localStorage.setItem(
            "signupData",
            JSON.stringify({
              name: formData.name,
              email: formData.email,
              mobileNumber: formData.mobileNumber,
              password: formData.password,
            })
          );

          // Navigate to signup OTP verification
          setTimeout(() => {
            navigate("/signup-verify-otp");
          }, 1000);
        }
        // Check if response has token and user structure (direct signup without OTP)
        else if (response.data.token && response.data.user) {
          const { token, user } = response.data;
          localStorage.setItem("isAuthenticated", "true");
          localStorage.setItem("isUserId", user.userId);
          localStorage.setItem("user", user.name);
          localStorage.setItem("userEmail", user.emailId);
          localStorage.setItem("token", token);

          toast.success("Registration successful!");
          // Navigate to home page if auto-logged in
          setTimeout(() => {
            navigate("/");
            window.location.reload();
          }, 1000);
        } else {
          // If signup doesn't auto-login, just navigate to login page
          toast.success("Registration successful! Please login to continue.");
          setTimeout(() => {
            navigate("/login");
          }, 1000);
        }
      } else {
        toast.error("Registration failed");
        throw new Error("Registration failed");
      }
    } catch (err) {
      toast.error(err.response?.data?.message || err.message);
      console.error(err);
      setError(err.response?.data?.message || err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="form-page__content lg:py-50">
      <ToastContainer />
      <div className="container">
        <div className="row justify-center items-center">
          <div className="col-xl-8 col-lg-9">
            <div className="px-50 py-50 md:px-25 md:py-25 bg-white shadow-1 rounded-16">
              <h3 className="text-30 lh-13">Sign Up</h3>
              <p className="mt-10">
                Already have an account?{" "}
                <Link to="/login" className="text-purple-1">
                  Log in
                </Link>
              </p>

              <form
                className="contact-form respondForm__form row y-gap-20 pt-30"
                onSubmit={handleSubmit}
              >
                {error && <p className="text-red-600">{error}</p>}

                <div className="col-lg-6">
                  <label className="text-16 lh-1 fw-500 text-dark-1 mb-10">
                    Name *
                  </label>
                  <input
                    required
                    type="text"
                    name="name"
                    placeholder="Name"
                    value={formData.name}
                    onChange={handleChange}
                  />
                </div>
                <div className="col-lg-6">
                  <label className="text-16 lh-1 fw-500 text-dark-1 mb-10">
                    Email address *
                  </label>
                  <input
                    required
                    type="email"
                    name="email"
                    placeholder="Email"
                    value={formData.email}
                    onChange={handleChange}
                  />
                </div>

                <div className="col-lg-6">
                  <label className="text-16 lh-1 fw-500 text-dark-1 mb-10">
                    Mobile Number *
                  </label>
                  <input
                    required
                    type="text"
                    name="mobileNumber"
                    placeholder="Mobile Number"
                    value={formData.mobileNumber}
                    onChange={handleChange}
                  />
                </div>
                <div className="col-lg-6">
                  <label className="text-16 lh-1 fw-500 text-dark-1 mb-10">
                    Password *
                  </label>
                  <input
                    required
                    type="password"
                    name="password"
                    placeholder="Password"
                    value={formData.password}
                    onChange={handleChange}
                  />
                </div>
                <div className="col-lg-6">
                  <label className="text-16 lh-1 fw-500 text-dark-1 mb-10">
                    Confirm Password *
                  </label>
                  <input
                    required
                    type="password"
                    name="confirmPassword"
                    placeholder="Confirm Password"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                  />
                </div>

                <div className="col-12">
                  <button
                    type="submit"
                    name="submit"
                    id="submit"
                    className="button -md -green-1 text-dark-1 fw-500 w-1/1"
                    disabled={loading}
                  >
                    {loading ? "Registering..." : "Register"}
                  </button>
                </div>
              </form>

              {/* <div className="lh-12 text-dark-1 fw-500 text-center mt-20">
                Or sign in using
              </div>

              <div className="d-flex x-gap-20 items-center justify-between pt-20">
                <div>
                  <button className="button -sm px-80 py-20 -outline-blue-3 text-blue-3 text-14">
                    Log In via Facebook
                  </button>
                </div>
                <div>
                  <button className="button -sm px-80 py-20 -outline-red-3 text-red-3 text-14">
                    Log In via Google+
                  </button>
                </div>
              </div> */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
