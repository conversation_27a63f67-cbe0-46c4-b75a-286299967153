import { Link, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import axios from "axios";

import { server } from "../../api/services/server";

const api = axios.create({
  baseURL: server.uri.slice(0, -1), // Remove trailing slash
});

export default function SignupVerifyOtpForm() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    email: "",
    otp: "",
  });
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [signupData, setSignupData] = useState(null);

  useEffect(() => {
    // Get signup data from localStorage
    const signupEmail = localStorage.getItem("signupEmail");
    const storedSignupData = localStorage.getItem("signupData");

    if (signupEmail && storedSignupData) {
      setFormData((prev) => ({ ...prev, email: signupEmail }));
      setSignupData(JSON.parse(storedSignupData));
    } else {
      // If no signup data found, redirect to signup
      toast.error("Please complete the signup process first.");
      navigate("/signup");
    }
  }, [navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    // Only allow numbers for OTP and limit to 6 digits
    if (name === "otp") {
      const numericValue = value.replace(/[^0-9]/g, "").slice(0, 6);
      setFormData((prev) => ({ ...prev, [name]: numericValue }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await api.post("/verify-otp", {
        emailId: formData.email,
        otp: formData.otp,
      });

      if (response.data) {
        toast.success("Email verified successfully! Registration completed.");

        // Clear signup data from localStorage
        localStorage.removeItem("signupEmail");
        localStorage.removeItem("signupData");

        // Check if the response includes auto-login data
        if (response.data.token && response.data.user) {
          const { token, user } = response.data;
          localStorage.setItem("isAuthenticated", "true");
          localStorage.setItem("isUserId", user.userId);
          localStorage.setItem("user", user.name);
          localStorage.setItem("userEmail", user.emailId);
          localStorage.setItem("token", token);

          // Navigate to home page after successful verification and auto-login
          setTimeout(() => {
            navigate("/");
            window.location.reload();
          }, 1500);
        } else {
          // Navigate to login page after successful verification
          setTimeout(() => {
            navigate("/login");
          }, 1500);
        }
      }
    } catch (error) {
      console.error("OTP verification error:", error);
      toast.error(
        error.response?.data?.message || "Invalid OTP. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  const handleResendOtp = async () => {
    if (!signupData) {
      toast.error("Signup data not found. Please signup again.");
      navigate("/signup");
      return;
    }

    setResendLoading(true);

    try {
      // Resend signup request to get new OTP
      const response = await api.post("/signup", {
        emailId: signupData.email,
        name: signupData.name,
        mobileNumber: signupData.mobileNumber,
        password: signupData.password,
      });

      if (response.data) {
        toast.success("New OTP sent to your email!");
      }
    } catch (error) {
      console.error("Resend OTP error:", error);
      toast.error(
        error.response?.data?.message ||
          "Failed to resend OTP. Please try again."
      );
    } finally {
      setResendLoading(false);
    }
  };

  return (
    <div className="form-page__content lg:py-50">
      <ToastContainer />
      <div className="container">
        <div className="row justify-center items-center">
          <div className="col-xl-6 col-lg-8">
            <div className="px-50 py-50 md:px-25 md:py-25 bg-white shadow-1 rounded-16">
              <h3 className="text-30 lh-13">Verify Your Email</h3>
              <p className="mt-10">
                We&apos;ve sent a 6-digit verification code to{" "}
                <span className="text-purple-1 fw-500">{formData.email}</span>
                <br />
                Please verify your email to complete registration.
              </p>

              <form
                className="contact-form respondForm__form row y-gap-20 pt-30"
                onSubmit={handleSubmit}
              >
                <div className="col-12">
                  <label className="text-16 lh-1 fw-500 text-dark-1 mb-10">
                    Enter 6-digit OTP
                  </label>
                  <input
                    required
                    type="text"
                    name="otp"
                    placeholder="Enter OTP"
                    value={formData.otp}
                    onChange={handleChange}
                    maxLength="6"
                    className="text-center text-20 fw-500 letter-spacing-2"
                    style={{ letterSpacing: "0.5em", textAlign: "center" }}
                  />
                  <div className="text-13 text-light-1 mt-5">
                    Please enter the 6-digit code sent to your email
                  </div>
                </div>

                <div className="col-12">
                  <button
                    type="submit"
                    className="button -md -green-1 text-dark-1 fw-500 w-1/1"
                    disabled={loading || formData.otp.length !== 6}
                  >
                    {loading
                      ? "Verifying..."
                      : "Verify & Complete Registration"}
                  </button>
                </div>
              </form>

              <div className="text-center mt-30">
                <p className="text-14 mb-15">
                  Didn&apos;t receive the code?{" "}
                  <button
                    type="button"
                    onClick={handleResendOtp}
                    disabled={resendLoading}
                    className="text-purple-1 bg-transparent border-0 text-14 fw-500"
                    style={{ cursor: "pointer" }}
                  >
                    {resendLoading ? "Sending..." : "Resend OTP"}
                  </button>
                </p>
                <p className="text-14">
                  <Link to="/signup" className="text-purple-1">
                    Change Registration Details
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
