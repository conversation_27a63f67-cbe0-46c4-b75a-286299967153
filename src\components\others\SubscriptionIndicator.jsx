import React from "react";
import { formatRemainingAnswersMessage } from "../../utils/subscriptionUtils";

const SubscriptionIndicator = ({
  eligibility,
  loading = false,
  position = "top-right",
  style = {},
}) => {
  // Don't render if loading or no eligibility data
  if (loading || !eligibility) {
    return null;
  }

  // Get the message and styling based on eligibility
  const getMessage = () => {
    if (eligibility.reason === "active_subscription") {
      return {
        text: "",
        icon: "⭐",
        bgColor: "#ECFDF3",
        textColor: "#027A48",
        borderColor: "#ABEFC6",
      };
    } else if (eligibility.reason === "within_free_limit") {
      return {
        text: `${eligibility.answersRemaining} free answers remaining`,
        icon: "🆓",
        bgColor: "#FFF4ED",
        textColor: "#F79009",
        borderColor: "#FDBF47",
      };
    } else if (eligibility.reason === "free_limit_exceeded") {
      return {
        text: "Free limit reached - Upgrade needed",
        icon: "🔒",
        bgColor: "#FEF3F2",
        textColor: "#B42318",
        borderColor: "#FDA29B",
      };
    } else {
      return {
        text: "Check subscription status",
        icon: "❓",
        bgColor: "#F9FAFB",
        textColor: "#6B7280",
        borderColor: "#D1D5DB",
      };
    }
  };

  const messageData = getMessage();

  // Position styles
  const getPositionStyles = () => {
    const baseStyles = {
      position: "absolute",
      zIndex: 10,
      fontSize: "12px",
      fontWeight: "600",
    };

    switch (position) {
      case "top-right":
        return { ...baseStyles, top: "10px", right: "10px" };
      case "top-left":
        return { ...baseStyles, top: "10px", left: "10px" };
      case "bottom-right":
        return { ...baseStyles, bottom: "10px", right: "10px" };
      case "bottom-left":
        return { ...baseStyles, bottom: "10px", left: "10px" };
      case "inline":
        return {
          ...baseStyles,
          position: "relative",
          top: "auto",
          right: "auto",
          margin: "0",
        };
      default:
        return { ...baseStyles, top: "10px", right: "10px" };
    }
  };

  return (
    <div
      style={{
        ...getPositionStyles(),
        backgroundColor: messageData.bgColor,
        color: messageData.textColor,
        border: `1px solid ${messageData.borderColor}`,
        borderRadius: "8px",
        padding: "6px 12px",
        display: "flex",
        alignItems: "center",
        gap: "6px",
        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        maxWidth: "250px",
        ...style,
      }}
    >
      <span style={{ fontSize: "14px" }}>{messageData.icon}</span>
      <span style={{ fontSize: "12px", fontWeight: "600" }}>
        {messageData.text}
      </span>
    </div>
  );
};

export default SubscriptionIndicator;
