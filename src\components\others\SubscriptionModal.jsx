import { useState } from "react";
import { useNavigate } from "react-router-dom";
import PropTypes from "prop-types";
import { formatRemainingAnswersMessage } from "../../utils/subscriptionUtils";

const SubscriptionModal = ({
  isOpen,
  onClose,
  eligibility = null,
  title = "Subscription Required",
  message = null,
}) => {
  const navigate = useNavigate();
  const [isClosing, setIsClosing] = useState(false);

  if (!isOpen) return null;

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      setIsClosing(false);
      onClose();
    }, 200);
  };

  const handleSubscribe = () => {
    handleClose();
    navigate("/pricing");
  };

  const handleSignIn = () => {
    handleClose();
    navigate("/login");
  };

  const getModalContent = () => {
    if (!eligibility) {
      return {
        title: title,
        message: message || "This feature requires a subscription to access.",
        icon: "warning",
      };
    }

    switch (eligibility.reason) {
      case "unauthenticated":
        return {
          title: "Sign In Required",
          message:
            "Please sign in to submit answers and access detailed feedback for practice questions.",
          icon: "auth",
        };
      case "free_limit_exceeded":
        return {
          title: "Free Practice Limit Reached",
          message: `You've completed your ${
            eligibility.currentAnswerCount || 0
          } free practice questions with detailed feedback. Upgrade to continue practicing with unlimited access to answers and explanations.`,
          icon: "limit",
        };
      case "within_free_limit":
        return {
          title: "Practice Questions Available",
          message: formatRemainingAnswersMessage(eligibility.answersRemaining),
          icon: "info",
        };
      case "free_trial_used":
        return {
          title: "Free Trial Used",
          message:
            "You've already used your free trial mocktest. Upgrade to unlimited mocktest access with detailed feedback and performance analytics.",
          icon: "limit",
        };
      default:
        return {
          title: title,
          message:
            "Subscribe to access unlimited practice questions with detailed feedback and explanations.",
          icon: "subscription",
        };
    }
  };

  const { title: modalTitle, message: modalMessage, icon } = getModalContent();

  const getIcon = () => {
    switch (icon) {
      case "auth":
        return (
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
            <path
              d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1ZM12 21C7.59 19.94 5 15.94 5 11V6.3L12 3.19L19 6.3V11C19 15.94 16.41 19.94 12 21ZM10 17L6 13L7.41 11.59L10 14.17L16.59 7.58L18 9L10 17Z"
              fill="#10B981"
            />
          </svg>
        );
      case "limit":
        return (
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
            <path
              d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM13 7H11V13H13V7ZM13 15H11V17H13V15Z"
              fill="#F59E0B"
            />
          </svg>
        );
      case "info":
        return (
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
            <path
              d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM11 7H13V9H11V7ZM11 11H13V17H11V11Z"
              fill="#3B82F6"
            />
          </svg>
        );
      case "subscription":
        return (
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
            <path
              d="M17 3H7C5.9 3 5 3.9 5 5V19L12 16L19 19V5C19 3.9 18.1 3 17 3ZM17 15L12 13L7 15V5H17V15Z"
              fill="#8B5CF6"
            />
          </svg>
        );
      default:
        return (
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
            <path
              d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM11 7H13V13H11V7ZM11 15H13V17H11V15Z"
              fill="#6A5AF9"
            />
          </svg>
        );
    }
  };

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100vw",
        height: "100vh",
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 9999,
        opacity: isClosing ? 0 : 1,
        transition: "opacity 0.2s ease-out",
      }}
      onClick={handleClose}
    >
      <div
        style={{
          backgroundColor: "white",
          borderRadius: "16px",
          padding: "32px",
          maxWidth: "520px",
          width: "90%",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.12)",
          textAlign: "center",
          transform: isClosing ? "scale(0.95)" : "scale(1)",
          transition: "transform 0.2s ease-out",
          position: "relative",
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Button */}
        <button
          onClick={handleClose}
          style={{
            position: "absolute",
            top: "16px",
            right: "16px",
            background: "none",
            border: "none",
            fontSize: "24px",
            color: "#9CA3AF",
            cursor: "pointer",
            padding: "4px",
            borderRadius: "4px",
            transition: "color 0.2s",
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.color = "#374151";
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.color = "#9CA3AF";
          }}
        >
          ×
        </button>

        {/* Icon */}
        <div
          style={{
            marginBottom: "24px",
            width: "80px",
            height: "80px",
            borderRadius: "50%",
            backgroundColor: "#F4F0FF",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            margin: "0 auto 24px",
          }}
        >
          {getIcon()}
        </div>

        {/* Title */}
        <h2
          style={{
            fontSize: "24px",
            fontWeight: "700",
            color: "#140342",
            marginBottom: "16px",
            lineHeight: "1.3",
          }}
        >
          {modalTitle}
        </h2>

        {/* Message */}
        <p
          style={{
            fontSize: "16px",
            lineHeight: "1.6",
            color: "#4B5563",
            marginBottom: "24px",
            maxWidth: "400px",
            margin: "0 auto 24px",
          }}
        >
          {modalMessage}
        </p>

        {/* Subscription Benefits */}
        {eligibility?.reason === "free_limit_exceeded" && (
          <div
            style={{
              backgroundColor: "#F4F0FF",
              padding: "20px",
              borderRadius: "12px",
              marginBottom: "24px",
              textAlign: "left",
            }}
          >
            <h3
              style={{
                fontSize: "16px",
                fontWeight: "600",
                color: "#140342",
                marginBottom: "12px",
                textAlign: "center",
              }}
            >
              🚀 Upgrade to Premium and get:
            </h3>
            <ul
              style={{
                listStyle: "none",
                padding: "0",
                margin: "0",
                display: "grid",
                gap: "8px",
              }}
            >
              {[
                "Unlimited practice questions with detailed feedback",
                "Advanced AI-powered scoring and analysis",
                "Comprehensive performance tracking",
                "Access to all PTE question types",
                "Priority customer support",
              ].map((benefit, index) => (
                <li
                  key={index}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    fontSize: "14px",
                    color: "#374151",
                  }}
                >
                  <span
                    style={{
                      display: "inline-flex",
                      alignItems: "center",
                      justifyContent: "center",
                      width: "20px",
                      height: "20px",
                      backgroundColor: "#10B981",
                      borderRadius: "50%",
                      marginRight: "12px",
                      flexShrink: 0,
                    }}
                  >
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                      <path
                        d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z"
                        fill="white"
                      />
                    </svg>
                  </span>
                  {benefit}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Progress Bar for Free Users */}
        {eligibility?.answersRemaining !== -1 &&
          eligibility?.currentAnswerCount >= 0 && (
            <div
              style={{
                backgroundColor: "#F9FAFB",
                padding: "16px",
                borderRadius: "8px",
                marginBottom: "24px",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: "8px",
                }}
              >
                <span style={{ fontSize: "14px", color: "#374151" }}>
                  Free Questions Used
                </span>
                <span
                  style={{
                    fontSize: "14px",
                    fontWeight: "500",
                    color: "#140342",
                  }}
                >
                  {eligibility.currentAnswerCount || 0} / 5
                </span>
              </div>
              <div
                style={{
                  width: "100%",
                  height: "8px",
                  backgroundColor: "#E5E7EB",
                  borderRadius: "4px",
                  overflow: "hidden",
                }}
              >
                <div
                  style={{
                    width: `${
                      ((eligibility.currentAnswerCount || 0) / 5) * 100
                    }%`,
                    height: "100%",
                    backgroundColor:
                      eligibility.currentAnswerCount >= 5
                        ? "#EF4444"
                        : "#10B981",
                    transition: "width 0.3s ease",
                  }}
                ></div>
              </div>
            </div>
          )}

        {/* Action Buttons */}
        <div
          style={{
            display: "flex",
            gap: "12px",
            justifyContent: "center",
          }}
        >
          {eligibility?.reason === "unauthenticated" ? (
            <>
              <button
                onClick={handleSignIn}
                style={{
                  padding: "14px 28px",
                  backgroundColor: "#140342",
                  color: "white",
                  border: "none",
                  borderRadius: "8px",
                  fontSize: "16px",
                  fontWeight: "600",
                  cursor: "pointer",
                  transition: "all 0.3s ease",
                  boxShadow: "0 4px 12px rgba(20, 3, 66, 0.15)",
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = "#1e065e";
                  e.currentTarget.style.transform = "translateY(-1px)";
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = "#140342";
                  e.currentTarget.style.transform = "translateY(0)";
                }}
              >
                Sign In
              </button>
              <button
                onClick={handleClose}
                style={{
                  padding: "14px 28px",
                  backgroundColor: "white",
                  color: "#374151",
                  border: "1px solid #D1D5DB",
                  borderRadius: "8px",
                  fontSize: "16px",
                  fontWeight: "600",
                  cursor: "pointer",
                  transition: "all 0.3s ease",
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = "#F9FAFB";
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = "white";
                }}
              >
                Continue Browsing
              </button>
            </>
          ) : eligibility?.reason === "free_limit_exceeded" ? (
            <>
              <button
                onClick={handleSubscribe}
                style={{
                  padding: "14px 28px",
                  backgroundColor: "#140342",
                  color: "white",
                  border: "none",
                  borderRadius: "8px",
                  fontSize: "16px",
                  fontWeight: "600",
                  cursor: "pointer",
                  transition: "all 0.3s ease",
                  boxShadow: "0 4px 12px rgba(20, 3, 66, 0.15)",
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = "#1e065e";
                  e.currentTarget.style.transform = "translateY(-1px)";
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = "#140342";
                  e.currentTarget.style.transform = "translateY(0)";
                }}
              >
                Upgrade Now
              </button>
              <button
                onClick={handleClose}
                style={{
                  padding: "14px 28px",
                  backgroundColor: "white",
                  color: "#374151",
                  border: "1px solid #D1D5DB",
                  borderRadius: "8px",
                  fontSize: "16px",
                  fontWeight: "600",
                  cursor: "pointer",
                  transition: "all 0.3s ease",
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = "#F9FAFB";
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = "white";
                }}
              >
                Maybe Later
              </button>
            </>
          ) : (
            <>
              <button
                onClick={handleSubscribe}
                style={{
                  padding: "14px 28px",
                  backgroundColor: "#140342",
                  color: "white",
                  border: "none",
                  borderRadius: "8px",
                  fontSize: "16px",
                  fontWeight: "600",
                  cursor: "pointer",
                  transition: "all 0.3s ease",
                }}
              >
                View Plans
              </button>
              <button
                onClick={handleClose}
                style={{
                  padding: "14px 28px",
                  backgroundColor: "white",
                  color: "#140342",
                  border: "1px solid #D1D5DB",
                  borderRadius: "8px",
                  fontSize: "16px",
                  fontWeight: "600",
                  cursor: "pointer",
                  transition: "all 0.3s ease",
                }}
              >
                Continue Practicing
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

SubscriptionModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  eligibility: PropTypes.shape({
    canSubmit: PropTypes.bool,
    canViewAnswer: PropTypes.bool,
    reason: PropTypes.string,
    userData: PropTypes.object,
    answersRemaining: PropTypes.number,
    currentAnswerCount: PropTypes.number,
  }),
  title: PropTypes.string,
  message: PropTypes.string,
};

export default SubscriptionModal;
