import { useState, useEffect, useRef } from "react";
import ClickableText from "./ClickableText";

// Google Translate API configuration
const GOOGLE_TRANSLATE_API_URL =
  "https://translation.googleapis.com/language/translate/v2";
// Access environment variables safely
const GOOGLE_TRANSLATE_API_KEY =
  typeof window !== "undefined" &&
  window.env &&
  window.env.GOOGLE_TRANSLATE_API_KEY
    ? window.env.GOOGLE_TRANSLATE_API_KEY
    : (typeof process !== "undefined" &&
        process.env &&
        process.env.NEXT_PUBLIC_GOOGLE_TRANSLATE_API_KEY) ||
      "AIzaSyCqmNLDn0GwTJBNTBIXCOHG2uWgR7htoYQ"; // Fallback to your hardcoded key or empty string

const TranslationDialog = ({
  isOpen,
  onClose,
  transcript,
  questionName,
  questionNumber,
}) => {
  const [selectedLanguage, setSelectedLanguage] = useState("");
  const [translatedText, setTranslatedText] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [showLanguageSelector, setShowLanguageSelector] = useState(true);
  const modalRef = useRef(null);

  // Complete list of Google Translate supported languages
  const languages = [
    {
      code: "en",
      name: "I don't need to translate to other languages. (English)",
    },
    { code: "af", name: "Afrikaans" },
    { code: "sq", name: "Albanian" },
    { code: "am", name: "Amharic" },
    { code: "ar", name: "Arabic" },
    { code: "hy", name: "Armenian" },
    { code: "az", name: "Azerbaijani" },
    { code: "eu", name: "Basque" },
    { code: "be", name: "Belarusian" },
    { code: "bn", name: "Bengali" },
    { code: "bs", name: "Bosnian" },
    { code: "bg", name: "Bulgarian" },
    { code: "ca", name: "Catalan" },
    { code: "ceb", name: "Cebuano" },
    { code: "zh-CN", name: "Chinese (Simplified)" },
    { code: "zh-TW", name: "Chinese (Traditional)" },
    { code: "co", name: "Corsican" },
    { code: "hr", name: "Croatian" },
    { code: "cs", name: "Czech" },
    { code: "da", name: "Danish" },
    { code: "nl", name: "Dutch" },
    { code: "eo", name: "Esperanto" },
    { code: "et", name: "Estonian" },
    { code: "fi", name: "Finnish" },
    { code: "fr", name: "French" },
    { code: "fy", name: "Frisian" },
    { code: "gl", name: "Galician" },
    { code: "ka", name: "Georgian" },
    { code: "de", name: "German" },
    { code: "el", name: "Greek" },
    { code: "gu", name: "Gujarati" },
    { code: "ht", name: "Haitian Creole" },
    { code: "ha", name: "Hausa" },
    { code: "haw", name: "Hawaiian" },
    { code: "he", name: "Hebrew" },
    { code: "hi", name: "Hindi" },
    { code: "hmn", name: "Hmong" },
    { code: "hu", name: "Hungarian" },
    { code: "is", name: "Icelandic" },
    { code: "ig", name: "Igbo" },
    { code: "id", name: "Indonesian" },
    { code: "ga", name: "Irish" },
    { code: "it", name: "Italian" },
    { code: "ja", name: "Japanese" },
    { code: "jv", name: "Javanese" },
    { code: "kn", name: "Kannada" },
    { code: "kk", name: "Kazakh" },
    { code: "km", name: "Khmer" },
    { code: "rw", name: "Kinyarwanda" },
    { code: "ko", name: "Korean" },
    { code: "ku", name: "Kurdish" },
    { code: "ky", name: "Kyrgyz" },
    { code: "lo", name: "Lao" },
    { code: "la", name: "Latin" },
    { code: "lv", name: "Latvian" },
    { code: "lt", name: "Lithuanian" },
    { code: "lb", name: "Luxembourgish" },
    { code: "mk", name: "Macedonian" },
    { code: "mg", name: "Malagasy" },
    { code: "ms", name: "Malay" },
    { code: "ml", name: "Malayalam" },
    { code: "mt", name: "Maltese" },
    { code: "mi", name: "Maori" },
    { code: "mr", name: "Marathi" },
    { code: "mn", name: "Mongolian" },
    { code: "my", name: "Myanmar (Burmese)" },
    { code: "ne", name: "Nepali" },
    { code: "no", name: "Norwegian" },
    { code: "ny", name: "Nyanja (Chichewa)" },
    { code: "or", name: "Odia (Oriya)" },
    { code: "ps", name: "Pashto" },
    { code: "fa", name: "Persian" },
    { code: "pl", name: "Polish" },
    { code: "pt", name: "Portuguese" },
    { code: "pa", name: "Punjabi" },
    { code: "ro", name: "Romanian" },
    { code: "ru", name: "Russian" },
    { code: "sm", name: "Samoan" },
    { code: "gd", name: "Scots Gaelic" },
    { code: "sr", name: "Serbian" },
    { code: "st", name: "Sesotho" },
    { code: "sn", name: "Shona" },
    { code: "sd", name: "Sindhi" },
    { code: "si", name: "Sinhala" },
    { code: "sk", name: "Slovak" },
    { code: "sl", name: "Slovenian" },
    { code: "so", name: "Somali" },
    { code: "es", name: "Spanish" },
    { code: "su", name: "Sundanese" },
    { code: "sw", name: "Swahili" },
    { code: "sv", name: "Swedish" },
    { code: "tl", name: "Tagalog (Filipino)" },
    { code: "tg", name: "Tajik" },
    { code: "ta", name: "Tamil" },
    { code: "tt", name: "Tatar" },
    { code: "te", name: "Telugu" },
    { code: "th", name: "Thai" },
    { code: "tr", name: "Turkish" },
    { code: "tk", name: "Turkmen" },
    { code: "uk", name: "Ukrainian" },
    { code: "ur", name: "Urdu" },
    { code: "ug", name: "Uyghur" },
    { code: "uz", name: "Uzbek" },
    { code: "vi", name: "Vietnamese" },
    { code: "cy", name: "Welsh" },
    { code: "xh", name: "Xhosa" },
    { code: "yi", name: "Yiddish" },
    { code: "yo", name: "Yoruba" },
    { code: "zu", name: "Zulu" },
  ];

  // Split the transcript into sentences
  const sentences = transcript
    ? transcript.match(/[^\.!\?]+[\.!\?]+/g) || [transcript]
    : [];
  const [filteredLanguages, setFilteredLanguages] = useState(languages);

  useEffect(() => {
    if (selectedLanguage && selectedLanguage !== "en" && transcript) {
      translateText();
      // After selecting a language, switch to transcript view
      setShowLanguageSelector(false);
    }
  }, [selectedLanguage]);

  // Reset view when dialog opens
  useEffect(() => {
    if (isOpen) {
      // If no language selected yet, show language selector
      if (!selectedLanguage || selectedLanguage === "en") {
        setShowLanguageSelector(true);
      } else {
        // If language already selected, show transcript
        setShowLanguageSelector(false);
      }
    }
  }, [isOpen]);

  // Filter languages based on search query
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredLanguages(languages);
    } else {
      const filtered = languages.filter((lang) =>
        lang.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredLanguages(filtered);
    }
  }, [searchQuery]);

  // Handle clicks outside modal to close
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  const translateText = async () => {
    if (!selectedLanguage || selectedLanguage === "en") return;

    setLoading(true);

    try {
      // For each sentence, make a separate call to maintain the sentence structure
      const translatedSentences = await Promise.all(
        sentences.map(async (sentence) => {
          try {
            // If we don't have an API key, use a mock translation for demo
            if (
              !GOOGLE_TRANSLATE_API_KEY ||
              GOOGLE_TRANSLATE_API_KEY === "YOUR_API_KEY_HERE"
            ) {
              // Simple mock translation for demonstration purposes
              return {
                original: sentence.trim(),
                translated: `[Translation to ${
                  languages.find((l) => l.code === selectedLanguage).name
                } would appear here]`,
              };
            }

            // Google Translate API integration
            const response = await fetch(
              `${GOOGLE_TRANSLATE_API_URL}?key=${GOOGLE_TRANSLATE_API_KEY}`,
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  q: sentence.trim(),
                  target: selectedLanguage,
                  format: "text",
                }),
              }
            );

            if (!response.ok) {
              throw new Error(`Translation API error: ${response.status}`);
            }

            const data = await response.json();
            const translatedText =
              data.data?.translations?.[0]?.translatedText ||
              `Error: Could not translate to ${
                languages.find((l) => l.code === selectedLanguage).name
              }`;

            return {
              original: sentence.trim(),
              translated: translatedText,
            };
          } catch (error) {
            console.error("Error translating sentence:", error);
            return {
              original: sentence.trim(),
              translated: `Error translating to ${
                languages.find((l) => l.code === selectedLanguage).name
              }`,
            };
          }
        })
      );

      setTranslatedText(translatedSentences);
    } catch (error) {
      console.error("Translation error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleConfirm = () => {
    onClose();
  };

  const handleLanguageSelect = (code) => {
    setSelectedLanguage(code);
    // Only do translation if it's not English
    if (code !== "en") {
      setShowLanguageSelector(false);
    } else {
      onClose(); // Close dialog if English is selected
    }
  };

  const switchToLanguageSelector = () => {
    setShowLanguageSelector(true);
  };

  if (!isOpen) return null;

  const rtlLanguages = ["ar", "fa", "he", "ur", "ps", "sd", "yi"];
  const isRtl = rtlLanguages.includes(selectedLanguage);
  const selectedLanguageName =
    languages.find((l) => l.code === selectedLanguage)?.name || "";

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 1000,
        backdropFilter: "blur(3px)",
        transition: "all 0.2s ease-in-out",
      }}
    >
      <div
        ref={modalRef}
        style={{
          backgroundColor: "#f4f0ff", // Light purple background
          borderRadius: "12px",
          width: "90%",
          maxWidth: "600px",
          maxHeight: "85vh",
          overflowY: "auto",
          boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
          position: "relative",
          animation: "scaleIn 0.2s ease-out",
        }}
      >
        <style>
          {`
            @keyframes scaleIn {
              from { transform: scale(0.95); opacity: 0; }
              to { transform: scale(1); opacity: 1; }
            }

            .language-item {
              padding: 12px 16px;
              cursor: pointer;
              border-radius: 8px;
              margin-bottom: 4px;
              transition: all 0.2s ease;
            }

            .language-item:hover {
              background-color: rgba(20, 3, 66, 0.05);
            }

            .language-selected {
              background-color: rgba(20, 3, 66, 0.1);
            }

            .translation-card {
              background-color: white;
              border-radius: 10px;
              padding: 15px;
              margin-bottom: 15px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
              transition: all 0.2s ease;
            }

            .translation-card:hover {
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .translation-original {
              color: #333;
              margin-bottom: 10px;
              font-weight: 500;
            }

            .translation-text {
              color: #140342;
              font-weight: 400;
              line-height: 1.5;
            }

            .pulse-animation {
              animation: pulse 1.5s infinite;
            }

            @keyframes pulse {
              0% { opacity: 0.6; }
              50% { opacity: 1; }
              100% { opacity: 0.6; }
            }
            
            .slide-in {
              animation: slideIn 0.3s ease-out;
            }
            
            @keyframes slideIn {
              from { transform: translateY(10px); opacity: 0; }
              to { transform: translateY(0); opacity: 1; }
            }
            
            .header-divider {
              display: inline-block;
              width: 4px;
              height: 24px;
              background-color: #140342;
              margin: 0 12px;
              border-radius: 2px;
              vertical-align: middle;
            }
            
            .question-number {
              color: #140342;
              font-weight: 700;
              margin-right: 4px;
            }
            
            .question-name {
              color: #555;
              font-weight: 500;
            }
          `}
        </style>

        <div style={{ padding: "24px" }}>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "flex-start",
              marginBottom: "20px",
            }}
          >
            <div>
              {questionNumber && questionName && (
                <div style={{ marginBottom: "8px" }}>
                  <span className="question-number">#{questionNumber}</span>
                  <span className="question-name">{questionName}</span>
                  <div
                    style={{
                      height: "2px",
                      backgroundColor: "#e0e0e0",
											marginTop: "8px",
											marginBottom: "16px",
                      width: "100%",
                    }}
                  ></div>
                </div>
              )}
              <h2
                style={{
                  fontSize: "22px",
                  fontWeight: "600",
                  color: "#140342",
                  margin: 0,
                }}
              >
                {showLanguageSelector
                  ? "Choose Language"
                  : `Translation (${selectedLanguageName})`}
              </h2>
            </div>

            <button
              onClick={onClose}
              style={{
                background: "none",
                border: "none",
                fontSize: "26px",
                cursor: "pointer",
                color: "#140342",
                lineHeight: 1,
                marginTop: "0",
                padding: "0",
              }}
              aria-label="Close dialog"
            >
              ×
            </button>
          </div>

          {/* LANGUAGE SELECTOR VIEW */}
          {showLanguageSelector && (
            <div className="slide-in">
              <p
                style={{
                  color: "#666",
                  fontSize: "15px",
                  marginBottom: "20px",
                }}
              >
                Which language do you want to translate the text to?
              </p>

              <div
                style={{
                  padding: "12px",
                  backgroundColor: "white",
                  borderRadius: "10px",
                  marginBottom: "20px",
                  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.05)",
                }}
              >
                <input
                  type="text"
                  placeholder="Search languages..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  style={{
                    width: "100%",
                    padding: "12px 16px",
                    borderRadius: "8px",
                    border: "1px solid #e0e0e0",
                    fontSize: "15px",
                    backgroundColor: "#f9f9f9",
                    outline: "none",
                    transition: "all 0.2s ease",
                  }}
                />
              </div>

              <div
                style={{
                  maxHeight: "400px",
                  overflowY: "auto",
                  padding: "12px",
                  backgroundColor: "white",
                  borderRadius: "10px",
                  marginBottom: "20px",
                  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.05)",
                }}
              >
                {filteredLanguages.length > 0 ? (
                  filteredLanguages.map((lang) => (
                    <div
                      key={lang.code}
                      onClick={() => handleLanguageSelect(lang.code)}
                      className={`language-item ${
                        selectedLanguage === lang.code
                          ? "language-selected"
                          : ""
                      }`}
                    >
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                        }}
                      >
                        <span
                          style={{
                            fontWeight:
                              selectedLanguage === lang.code ? "600" : "400",
                            color:
                              selectedLanguage === lang.code
                                ? "#140342"
                                : "#333",
                          }}
                        >
                          {lang.name}
                        </span>
                        {selectedLanguage === lang.code && (
                          <span style={{ color: "#140342" }}>✓</span>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <div
                    style={{
                      textAlign: "center",
                      padding: "20px",
                      color: "#666",
                    }}
                  >
                    No languages match your search
                  </div>
                )}
              </div>
            </div>
          )}

          {/* TRANSCRIPT VIEW */}
          {!showLanguageSelector &&
            selectedLanguage &&
            selectedLanguage !== "en" && (
              <div className="slide-in">
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "12px",
                  }}
                >
                  {/* <h3
                    style={{
                      fontSize: "18px",
                      fontWeight: "600",
                      color: "#140342",
                      margin: 0,
                    }}
                  >
                    Translation Results
                  </h3> */}

                  {loading && (
                    <div
                      style={{
                        color: "#140342",
                        display: "flex",
                        alignItems: "center",
                        gap: "8px",
                      }}
                      className="pulse-animation"
                    >
                      <div
                        style={{
                          width: "14px",
                          height: "14px",
                          borderRadius: "50%",
                          border: "2px solid #f3f3f3",
                          borderTop: "2px solid #140342",
                          animation: "spin 1s linear infinite",
                        }}
                      ></div>
                      Translating...
                    </div>
                  )}
                </div>

                {loading
                  ? // Skeleton loading placeholders
                    Array(3)
                      .fill(0)
                      .map((_, index) => (
                        <div
                          key={index}
                          className="translation-card pulse-animation"
                          style={{
                            opacity: 0.7 - index * 0.15,
                          }}
                        >
                          <div
                            style={{
                              height: "16px",
                              width: "80%",
                              backgroundColor: "#eee",
                              marginBottom: "12px",
                              borderRadius: "4px",
                            }}
                          ></div>
                          <div
                            style={{
                              height: "16px",
                              width: "90%",
                              backgroundColor: "#eee",
                              borderRadius: "4px",
                            }}
                          ></div>
                        </div>
                      ))
                  : translatedText.map((item, index) => (
                      <div key={index} className="translation-card">
                        <span className="translation-original">
                          {<ClickableText text={item.original} />}
                        </span>
                        <p
                          className="translation-text"
                          style={{
                            direction: isRtl ? "rtl" : "ltr",
                            textAlign: isRtl ? "right" : "left",
                            fontSize: "18px",
                          }}
                        >
                          {item.translated}
                        </p>
                      </div>
                    ))}

                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    marginTop: "20px",
                  }}
                >
                  <button
                    onClick={switchToLanguageSelector}
                    style={{
                      padding: "10px 20px",
                      backgroundColor: "white",
                      border: "1px solid #140342",
                      borderRadius: "8px",
                      color: "#140342",
                      cursor: "pointer",
                      fontSize: "15px",
                      fontWeight: "500",
                      transition: "all 0.2s ease",
                    }}
                  >
                    Choose Language
                  </button>
                  <button
                    onClick={handleConfirm}
                    style={{
                      padding: "10px 20px",
                      backgroundColor: "#140342",
                      border: "none",
                      borderRadius: "8px",
                      color: "white",
                      cursor: "pointer",
                      fontSize: "15px",
                      fontWeight: "500",
                      boxShadow: "0 2px 5px rgba(20, 3, 66, 0.2)",
                      transition: "all 0.2s ease",
                    }}
                  >
                    Close
                  </button>
                </div>
              </div>
            )}
        </div>
      </div>
    </div>
  );
};

export default TranslationDialog;
