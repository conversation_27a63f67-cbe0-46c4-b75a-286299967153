import DictionaryService from "@/api/dictionaryservice/DictionaryService";
import React, { useState, useEffect, useRef } from "react";

const WordDefinitionModal = ({ word, onClose, isOpen, autoPlay = true }) => {
  const [wordData, setWordData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [ukPronunciation, setUkPronunciation] = useState(null);
  const [usPronunciation, setUsPronunciation] = useState(null);
  const [ukPhonetic, setUkPhonetic] = useState("");
  const [usPhonetic, setUsPhonetic] = useState("");
  const [activeTab, setActiveTab] = useState("definitions");
  const modalRef = useRef(null);
  const audioRef = useRef(null);
  const [recentlyPlayed, setRecentlyPlayed] = useState(null); // Track which pronunciation was recently played
  const [webSpeechAvailable, setWebSpeechAvailable] = useState(false);

  // Check if Web Speech API is available
  useEffect(() => {
    if (typeof window !== "undefined") {
      setWebSpeechAvailable(
        "speechSynthesis" in window && "SpeechSynthesisUtterance" in window
      );
    }
  }, []);

  useEffect(() => {
    if (!isOpen || !word) return;

    const fetchWordDefinition = async () => {
      setLoading(true);
      try {
        // Use our dictionary service
        const data = await DictionaryService.getWordDefinition(word);
        setWordData(data[0]);

        // Get pronunciation URLs
        const { uk, us } = DictionaryService.getPronunciationUrls(data);
        setUkPronunciation(uk);
        setUsPronunciation(us);

        // Get phonetic transcriptions - try to find UK and US specific phonetics
        const phonetics = data[0]?.phonetics || [];

        // Look for UK phonetic
        const ukPhoneticData = phonetics.find(
          (p) =>
            p.audio && (p.audio.includes("uk") || p.audio.includes("british"))
        );

        // Look for US phonetic
        const usPhoneticData = phonetics.find(
          (p) =>
            p.audio && (p.audio.includes("us") || p.audio.includes("american"))
        );

        // Set phonetics with preference for accent-specific ones
        setUkPhonetic(
          ukPhoneticData?.text || phonetics[0]?.text || `/${word}/`
        );
        setUsPhonetic(
          usPhoneticData?.text || phonetics[0]?.text || `/${word}/`
        );

        // Auto-play pronunciation if enabled
        if (autoPlay && (uk || us || webSpeechAvailable)) {
          setTimeout(() => {
            if (uk) {
              playPronunciation(uk, "uk");
            } else if (webSpeechAvailable) {
              speakWord(word, "uk");
            }
          }, 300);
        }
      } catch (err) {
        setError(err.message);
        const fallbackData = DictionaryService.getFallbackDefinition(word);
        setWordData(fallbackData[0]);
      } finally {
        setLoading(false);
      }
    };

    fetchWordDefinition();
  }, [isOpen, word, autoPlay, webSpeechAvailable]);

  // Handle clicks outside modal to close
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscapeKey = (e) => {
      if (e.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscapeKey);
    }

    return () => {
      document.removeEventListener("keydown", handleEscapeKey);
    };
  }, [isOpen, onClose]);

  // Function to use Web Speech API for pronunciation
  const speakWord = (text, accent = "us") => {
    if (!webSpeechAvailable) return;

    // Stop any currently playing audio
    if (audioRef.current) {
      audioRef.current.pause();
    }

    if (window.speechSynthesis.speaking) {
      window.speechSynthesis.cancel();
    }

    setRecentlyPlayed(accent);

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = accent === "uk" ? "en-GB" : "en-US";

    // Optional: Set a specific voice if available
    const voices = window.speechSynthesis.getVoices();
    const voiceName =
      accent === "uk" ? "Google UK English Female" : "Google US English Female";
    const voice = voices.find((v) => v.name === voiceName);
    if (voice) {
      utterance.voice = voice;
    }

    utterance.onend = () => {
      setRecentlyPlayed(null);
    };

    window.speechSynthesis.speak(utterance);
  };

  const playPronunciation = (audioUrl, type = null) => {
    // If no audio URL but Web Speech API is available, use it instead
    if (!audioUrl && webSpeechAvailable) {
      speakWord(wordData.word, type);
      return;
    }

    if (audioUrl) {
      if (audioRef.current) {
        audioRef.current.pause();
      }

      // Cancel any speech synthesis that might be playing
      if (webSpeechAvailable && window.speechSynthesis.speaking) {
        window.speechSynthesis.cancel();
      }

      setRecentlyPlayed(type); // Track which pronunciation is playing

      audioRef.current = new Audio(audioUrl);
      audioRef.current.onended = () => {
        setRecentlyPlayed(null); // Reset when audio ends
      };

      audioRef.current.play().catch((error) => {
        console.error("Failed to play audio:", error);
        setRecentlyPlayed(null);

        // If the audio source fails and Web Speech API is available, fall back to it
        if (webSpeechAvailable) {
          speakWord(wordData.word, type);
        }
      });
    }
  };

  // Function to handle pronunciation button click
  const handlePronounce = (type) => {
    if (type === "uk") {
      if (ukPronunciation) {
        playPronunciation(ukPronunciation, "uk");
      } else if (webSpeechAvailable) {
        speakWord(wordData.word, "uk");
      }
    } else {
      if (usPronunciation) {
        playPronunciation(usPronunciation, "us");
      } else if (webSpeechAvailable) {
        speakWord(wordData.word, "us");
      }
    }
  };

  // Function to add a word to the vocabulary list (you can implement this)
  const addToVocab = () => {
    // Implement your vocabulary list functionality here
    console.log("Adding to vocabulary:", wordData.word);
    // Example: localStorage.setItem('vocabList', JSON.stringify([...currentList, wordData]));

    // Show a confirmation toast or message
    if (typeof window !== "undefined" && window.toast) {
      window.toast.success(`"${wordData.word}" added to vocabulary list`);
    }
  };

  if (!isOpen) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 1000,
        backdropFilter: "blur(3px)",
        transition: "all 0.2s ease-in-out",
      }}
    >
      <div
        ref={modalRef}
        style={{
          backgroundColor: "#f4f0ff", // Matching the speech analysis dialog background
          borderRadius: "12px",
          width: "90%",
          maxWidth: "600px",
          maxHeight: "85vh",
          overflowY: "auto",
          boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
          position: "relative",
          animation: "scaleIn 0.2s ease-out",
          padding: "24px",
        }}
      >
        <style>
          {`
            @keyframes scaleIn {
              from { transform: scale(0.95); opacity: 0; }
              to { transform: scale(1); opacity: 1; }
            }

            .tab-button {
              background: none;
              border: none;
              padding: 12px 20px;
              font-size: 16px;
              cursor: pointer;
              transition: all 0.2s;
              position: relative;
              color: #666;
              font-weight: 500;
            }

            .tab-button.active {
              color: #140342;
              font-weight: 600;
            }

            .tab-button.active::after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 20px;
              right: 20px;
              height: 3px;
              background-color: #140342;
              border-radius: 2px;
            }

            .definition-card {
              background-color: white;
              border-radius: 10px;
              padding: 15px;
              margin-bottom: 10px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
              transition: all 0.2s ease;
            }

            .definition-card:hover {
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .example-card {
              background-color: white;
              border-radius: 10px;
              padding: 15px;
              margin-bottom: 10px;
              border-left: 4px solid #140342;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
              transition: all 0.2s ease;
            }

            .example-card:hover {
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .tag {
              display: inline-block;
              background-color: #e9e4f5;
              color: #140342;
              padding: 4px 10px;
              border-radius: 30px;
              font-size: 13px;
              margin-right: 8px;
              margin-bottom: 8px;
            }

            .pronunciation-button {
              background: none;
              border: none;
              cursor: pointer;
              color: #140342;
              font-size: 20px;
              display: flex;
              align-items: center;
              padding: 5px 10px;
              border-radius: 4px;
              transition: all 0.2s ease;
            }

            .pronunciation-button:hover {
              background-color: rgba(20, 3, 66, 0.05);
            }

            .pronunciation-button:disabled {
              opacity: 0.5;
              cursor: default;
            }

            @keyframes pulse {
              0% { opacity: 0.6; transform: scale(1); }
              50% { opacity: 1; transform: scale(1.05); }
              100% { opacity: 0.6; transform: scale(1); }
            }

            .playing {
              animation: pulse 1s infinite;
              background-color: rgba(20, 3, 66, 0.1);
            }
          `}
        </style>

        <button
          onClick={onClose}
          style={{
            position: "absolute",
            right: "16px",
            top: "16px",
            background: "none",
            border: "none",
            fontSize: "24px",
            cursor: "pointer",
            color: "#140342",
            zIndex: 10,
          }}
        >
          ×
        </button>

        {loading ? (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              minHeight: "200px",
            }}
          >
            <div
              style={{
                width: "40px",
                height: "40px",
                borderRadius: "50%",
                border: "3px solid #f3f3f3",
                borderTop: "3px solid #140342",
                animation: "spin 1s linear infinite",
              }}
            ></div>
            <style>{`
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            `}</style>
          </div>
        ) : (
          <>
            <h2
              style={{
                fontSize: "28px",
                fontWeight: "600",
                color: "#140342",
                marginBottom: "8px",
              }}
            >
              {wordData.word}
            </h2>

            {/* Pronunciation section with UK and US options */}
            <div
              style={{
                display: "flex",
                flexWrap: "wrap",
                gap: "15px",
                marginBottom: "15px",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "5px",
                  background: "white",
                  padding: "8px 12px",
                  borderRadius: "8px",
                  boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
                }}
              >
                <span style={{ fontWeight: "600", color: "#555" }}>UK</span>
                <span style={{ color: "#666" }}>{ukPhonetic}</span>
                <button
                  className={`pronunciation-button ${
                    recentlyPlayed === "uk" ? "playing" : ""
                  }`}
                  onClick={() => handlePronounce("uk")}
                  aria-label="Play UK pronunciation"
                >
                  🔊
                </button>
              </div>

              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "5px",
                  background: "white",
                  padding: "8px 12px",
                  borderRadius: "8px",
                  boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
                }}
              >
                <span style={{ fontWeight: "600", color: "#555" }}>US</span>
                <span style={{ color: "#666" }}>{usPhonetic}</span>
                <button
                  className={`pronunciation-button ${
                    recentlyPlayed === "us" ? "playing" : ""
                  }`}
                  onClick={() => handlePronounce("us")}
                  aria-label="Play US pronunciation"
                >
                  🔊
                </button>
              </div>
            </div>

            <div
              style={{
                display: "flex",
                gap: "8px",
                flexWrap: "wrap",
                marginBottom: "16px",
              }}
            >
              {wordData?.meanings?.slice(0, 3).map((meaning, idx) => (
                <span key={idx} className="tag">
                  {meaning.partOfSpeech}
                </span>
              ))}
            </div>

            <div
              style={{
                display: "flex",
                borderBottom: "1px solid #ddd",
                marginBottom: "20px",
              }}
            >
              <button
                className={`tab-button ${
                  activeTab === "definitions" ? "active" : ""
                }`}
                onClick={() => setActiveTab("definitions")}
              >
                Definitions
              </button>
              <button
                className={`tab-button ${
                  activeTab === "examples" ? "active" : ""
                }`}
                onClick={() => setActiveTab("examples")}
              >
                Examples
              </button>
            </div>

            <div style={{ marginBottom: "30px" }}>
              {activeTab === "definitions" && (
                <div>
                  {wordData?.meanings?.map((meaning, index) => (
                    <div key={index} className="definition-card">
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                          marginBottom: "10px",
                        }}
                      >
                        <h4
                          style={{
                            margin: 0,
                            color: "#140342",
                            fontSize: "16px",
                            fontWeight: "600",
                          }}
                        >
                          {meaning.partOfSpeech}
                        </h4>
                      </div>

                      {meaning.definitions.slice(0, 3).map((def, idx) => (
                        <div
                          key={idx}
                          style={{
                            marginBottom:
                              idx < meaning.definitions.length - 1 ? "12px" : 0,
                            paddingBottom:
                              idx < meaning.definitions.length - 1 ? "12px" : 0,
                            borderBottom:
                              idx < meaning.definitions.length - 1
                                ? "1px solid #eee"
                                : "none",
                          }}
                        >
                          <p
                            style={{
                              margin: 0,
                              lineHeight: 1.6,
                              fontSize: "15px",
                            }}
                          >
                            {def.definition}
                          </p>
                          {def.example && (
                            <p
                              style={{
                                margin: "8px 0 0 0",
                                color: "#666",
                                fontSize: "14px",
                                fontStyle: "italic",
                              }}
                            >
                              "{def.example}"
                            </p>
                          )}
                        </div>
                      ))}
                    </div>
                  ))}
                </div>
              )}

              {activeTab === "examples" && (
                <div>
                  {wordData?.meanings?.some((meaning) =>
                    meaning.definitions.some((def) => def.example)
                  ) ? (
                    wordData.meanings.flatMap((meaning) =>
                      meaning.definitions
                        .filter((def) => def.example)
                        .slice(0, 3)
                        .map((def, idx) => (
                          <div key={idx} className="example-card">
                            <p
                              style={{
                                margin: 0,
                                fontStyle: "italic",
                                color: "#333",
                                fontSize: "15px",
                                lineHeight: 1.5,
                              }}
                            >
                              "{def.example}"
                            </p>
                            <p
                              style={{
                                margin: "8px 0 0 0",
                                fontSize: "13px",
                                color: "#666",
                              }}
                            >
                              {meaning.partOfSpeech}
                            </p>
                          </div>
                        ))
                    )
                  ) : (
                    // Fallback examples
                    <>
                      <div className="example-card">
                        <p
                          style={{
                            margin: 0,
                            fontStyle: "italic",
                            color: "#333",
                            fontSize: "15px",
                          }}
                        >
                          "The room was littered with discarded newspapers."
                        </p>
                      </div>
                      <div className="example-card">
                        <p
                          style={{
                            margin: 0,
                            fontStyle: "italic",
                            color: "#333",
                            fontSize: "15px",
                          }}
                        >
                          "He had discarded his jacket because of the heat."
                        </p>
                      </div>
                      <div className="example-card">
                        <p
                          style={{
                            margin: 0,
                            fontStyle: "italic",
                            color: "#333",
                            fontSize: "15px",
                          }}
                        >
                          "10% of the data was discarded as unreliable."
                        </p>
                      </div>
                    </>
                  )}
                </div>
              )}
            </div>

            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                borderTop: "1px solid #ddd",
                paddingTop: "20px",
              }}
            >
              <button
                onClick={onClose}
                style={{
                  padding: "10px 20px",
                  backgroundColor: "white",
                  border: "1px solid #ddd",
                  borderRadius: "8px",
                  color: "#333",
                  cursor: "pointer",
                  fontSize: "14px",
                  fontWeight: "500",
                  transition: "all 0.2s",
                }}
              >
                Close
              </button>
              {/* <button
                onClick={addToVocab}
                style={{
                  padding: "10px 20px",
                  backgroundColor: "#140342",
                  border: "none",
                  borderRadius: "8px",
                  color: "white",
                  cursor: "pointer",
                  fontSize: "14px",
                  fontWeight: "500",
                  boxShadow: "0 2px 5px rgba(20, 3, 66, 0.2)",
                  transition: "all 0.2s",
                }}
              >
                Add to Vocab List
              </button> */}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default WordDefinitionModal;
