import { useState, useEffect } from "react";
import { Switch } from "@mui/material";
// Subscription imports
// import { useSubscriptionCheck } from "../../hooks/useSubscriptionCheck";
// import SubscriptionModal from "../others/SubscriptionModal";
// import SubscriptionIndicator from "../others/SubscriptionIndicator";
import PropTypes from "prop-types";
import SidebarToggle from "../common/SidebarToggle";
// Practice tracking imports
import {
  logPracticeAttempt,
  usePracticeInfo,
} from "../speech-ace-component/utils/practiceUtils";

const MultipleChoiceMultiple = ({ question, onQuestionSelect }) => {
  // Practice tracking
  const { setPracticeCount, setIsPracticed } = usePracticeInfo(
    question.questionId
  );

  const [selectedAnswers, setSelectedAnswers] = useState([]);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showAnswer, setShowAnswer] = useState(false);
  const [timeElapsed, setTimeElapsed] = useState(0); // Time elapsed in seconds
  const [timerActive, setTimerActive] = useState(true);
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [score, setScore] = useState(0);
  const maxScore = question?.maxScore || 1;

  // Subscription logic
  // const {
  //   eligibility,
  //   loading: subscriptionLoading,
  //   showSubscriptionModal,
  //   canViewAnswer,
  //   handleSubmitWithCheck,
  //   closeSubscriptionModal,
  //   showSubscriptionRequiredModal,
  // } = useSubscriptionCheck(question.questionId);

  // Initialize timer - infinite and counting upward
  useEffect(() => {
    let timer;
    if (timerActive) {
      timer = setInterval(() => {
        setTimeElapsed((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [timerActive]);

  // Format time as MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Update screen width on resize
  useEffect(() => {
    const handleResize = () => setScreenWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const isSmallScreen = screenWidth <= 768;

  // Handle answer selection
  const handleSelect = (value, e) => {
    if (e) {
      e.stopPropagation(); // Stop event propagation only when event is provided
    }

    if (isSubmitted && !showAnswer) return;

    setSelectedAnswers((prev) => {
      if (prev.includes(value)) {
        return prev.filter((item) => item !== value);
      } else {
        return [...prev, value];
      }
    });
  };

  // Calculate score based on correct answers - with partial scoring
  const calculateScore = () => {
    // Get all the correct options from the question
    const correctOptions = question.options.filter(
      (option) => option.isCorrect
    );
    const correctAnswers = correctOptions.map(
      (option) => option.text || option.optionText
    );

    // Count correct selections
    const selectedCorrectAnswers = selectedAnswers.filter((answer) =>
      correctAnswers.includes(answer)
    );

    // Count incorrect selections
    const selectedIncorrectAnswers = selectedAnswers.filter(
      (answer) => !correctAnswers.includes(answer)
    );

    // If no correct answers selected, score is 0
    if (selectedCorrectAnswers.length === 0) {
      return 0;
    }

    // Calculate base score for correct selections
    const pointsPerCorrect = maxScore / correctAnswers.length;
    let score = selectedCorrectAnswers.length * pointsPerCorrect;

    // Penalize for incorrect selections (subtract points for wrong answers)
    const penaltyPerIncorrect = pointsPerCorrect * 0.5; // 50% penalty per incorrect answer
    score -= selectedIncorrectAnswers.length * penaltyPerIncorrect;

    // Ensure score doesn't go below 0
    score = Math.max(0, score);

    // Round to nearest integer
    return Math.round(score);
  };

  // Handle form submission with subscription check
  const handleDone = async () => {
    // Check subscription before proceeding
    // const success = await handleSubmitWithCheck(async () => {
    setIsSubmitted(true);
    setTimerActive(false);
    setScore(calculateScore());
    // });

    // if (!success) {
    //   // If subscription check failed, ensure timer doesn't stop
    //   setTimerActive(true);
    // }

    // Log practice attempt
    const result = await logPracticeAttempt(question.questionId);
    if (result.success) {
      setPracticeCount(result.practiceCount);
      setIsPracticed(true);
    }
  };

  // Handle show answer with subscription check
  const handleShowAnswer = async () => {
    // if (canViewAnswer) {
    setShowAnswer(!showAnswer);
    // } else {
    //   showSubscriptionRequiredModal();
    // }
  };

  // Handle redo
  const handleRedo = () => {
    setSelectedAnswers([]);
    setIsSubmitted(false);
    setShowAnswer(false);
    setTimeElapsed(0); // Reset timer
    setTimerActive(true);
    setScore(0);
  };

  // Handle show answer toggle
  useEffect(() => {
    if (showAnswer) {
      // Set selected answers to all correct ones
      const correctAnswers = question.options
        .filter((option) => option.isCorrect)
        .map((option) => option.text || option.optionText);
      setSelectedAnswers(correctAnswers);
    } else if (isSubmitted && !showAnswer) {
      // If we're hiding the answer after submission, we don't reset the selections
    }
  }, [showAnswer, question.options]);

  // Get feedback for each option
  const getFeedback = (option) => {
    if (!isSubmitted || showAnswer) return null;

    const isCorrect = option.isCorrect;
    const isSelected = selectedAnswers.includes(
      option.text || option.optionText
    );

    // Show feedback for selected options or correct options that weren't selected
    if (isSelected || (isCorrect && !isSelected)) {
      return (
        <span
          style={{
            color: isCorrect ? "#008800" : "#cc0000",
            marginLeft: "10px",
            fontSize: "16px",
            fontWeight: "500",
          }}
        >
          {isCorrect ? "✓ Correct" : "✗ Incorrect"}
        </span>
      );
    }

    return null;
  };

  // Get detailed feedback about user's answers
  const getDetailedFeedback = () => {
    const correctOptions = question.options.filter(
      (option) => option.isCorrect
    );
    const correctAnswers = correctOptions.map(
      (option) => option.text || option.optionText
    );

    const selectedCorrectAnswers = selectedAnswers.filter((answer) =>
      correctAnswers.includes(answer)
    );
    const selectedIncorrectAnswers = selectedAnswers.filter(
      (answer) => !correctAnswers.includes(answer)
    );

    const missedCorrectAnswers = correctAnswers.filter(
      (answer) => !selectedAnswers.includes(answer)
    );

    return {
      selectedCorrectCount: selectedCorrectAnswers.length,
      selectedIncorrectCount: selectedIncorrectAnswers.length,
      missedCorrectCount: missedCorrectAnswers.length,
      totalCorrectCount: correctAnswers.length,
      isAllCorrect:
        selectedCorrectAnswers.length === correctAnswers.length &&
        selectedIncorrectAnswers.length === 0,
      hasIncorrectSelections: selectedIncorrectAnswers.length > 0,
      hasMissedCorrect: missedCorrectAnswers.length > 0,
    };
  };

  // Check if answers are all correct
  const checkAllCorrect = () => {
    const feedback = getDetailedFeedback();
    return feedback.isAllCorrect;
  };

  return (
    <div
      style={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: "20px",
        backgroundColor: "#f9f9f9",
        borderRadius: "8px",
        position: "relative",
      }}
      className="question-container"
    >
      {/* Subscription Indicator */}
      {/* <SubscriptionIndicator
        eligibility={eligibility}
        loading={subscriptionLoading}
        position="top-right"
      /> */}

      {/* Header with timer and score */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          width: isSmallScreen ? "100%" : "90%",
          marginBottom: "15px",
        }}
      >
        {/* Timer display */}
        <div
          style={{
            display: "flex",
            alignItems: "center",
            backgroundColor: "#f0f0f0",
            padding: "5px 15px",
            borderRadius: "20px",
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          }}
        >
          <span
            style={{
              fontSize: "18px",
              fontWeight: "bold",
              color: "#333",
            }}
          >
            Time: {formatTime(timeElapsed)}
          </span>
        </div>

        {/* Score display - only shown after submission */}
        {isSubmitted && (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              backgroundColor: "#f0f0f0",
              padding: "5px 15px",
              borderRadius: "20px",
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
            }}
          >
            <span style={{ fontSize: "16px", fontWeight: "bold" }}>
              Score:{" "}
            </span>
            <span
              style={{
                fontSize: "18px",
                fontWeight: "bold",
                color: score > 0 ? "#008800" : "#333",
                marginLeft: "5px",
              }}
            >
              {score}/{maxScore}
            </span>
          </div>
        )}
      </div>

      {/* Main content card */}
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          width: isSmallScreen ? "100%" : "90%",
          backgroundColor: "white",
          padding: "25px",
          borderRadius: "8px",
          boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
          marginBottom: "20px",
        }}
      >
        {/* Question prompt */}
        <p
          style={{
            color: "#333",
            fontWeight: "500",
            fontSize: "20px",
            lineHeight: 1.6,
            marginTop: 0,
            marginBottom: "30px",
            textAlign: "justify",
          }}
        >
          {question.prompt}
        </p>

        {/* Options */}
        <div className="options">
          {question.options?.map((option, idx) => {
            const isSelected = selectedAnswers.includes(
              option.text || option.optionText
            );
            const bgColor = isSubmitted
              ? isSelected
                ? option.isCorrect
                  ? "#e6ffe6" // Selected and correct
                  : "#ffe6e6" // Selected and incorrect
                : option.isCorrect && showAnswer
                ? "#e6ffe6" // Show answer mode, highlight correct
                : "white"
              : isSelected
              ? "#f0f0ff" // Selected but not submitted
              : "white"; // Not selected

            return (
              <div
                key={idx}
                style={{
                  marginBottom: "15px",
                  padding: "12px 15px",
                  borderRadius: "8px",
                  border: "1px solid #ddd",
                  backgroundColor: bgColor,
                  display: "flex",
                  alignItems: "center",
                }}
                // Removed the onClick from the container div
              >
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    fontSize: "18px",
                    color: "#333",
                    width: "100%",
                  }}
                >
                  <input
                    type="checkbox"
                    name="multiple-choice"
                    value={option.text || option.optionText}
                    checked={selectedAnswers.includes(
                      option.text || option.optionText
                    )}
                    onChange={(e) =>
                      handleSelect(option.text || option.optionText, e)
                    }
                    disabled={isSubmitted && !showAnswer}
                    style={{
                      marginRight: "15px",
                      width: "20px",
                      height: "20px",
                      cursor: "pointer",
                    }}
                    id={`checkbox-${idx}`}
                  />
                  <label
                    htmlFor={`checkbox-${idx}`}
                    style={{
                      cursor: "pointer",
                      flex: 1,
                      // This lets the label text be clickable as well to toggle the checkbox
                    }}
                    onClick={(e) => {
                      if (!(isSubmitted && !showAnswer)) {
                        handleSelect(option.text || option.optionText, e);
                      }
                    }}
                  >
                    {option.text || option.optionText}
                  </label>
                </div>
                {getFeedback(option)}
              </div>
            );
          })}
        </div>
      </div>

      {/* Feedback message after submission */}
      {isSubmitted && !showAnswer && (
        <div
          style={{
            padding: "10px 15px",
            borderRadius: "5px",
            backgroundColor: checkAllCorrect() ? "#e6ffe6" : "#ffe6e6",
            border: `1px solid ${checkAllCorrect() ? "#008800" : "#cc0000"}`,
            textAlign: "center",
            display: "flex",
            flexDirection: isSmallScreen ? "column" : "row",
            justifyContent: "space-between",
            alignItems: "center",
            width: isSmallScreen ? "100%" : "90%",
            marginBottom: "15px",
            gap: isSmallScreen ? "10px" : "0px",
          }}
        >
          <div style={{ flex: isSmallScreen ? "none" : 1 }}>
            <p
              style={{
                margin: 0,
                fontWeight: "bold",
                color: checkAllCorrect() ? "#008800" : "#cc0000",
                fontSize: isSmallScreen ? "14px" : "16px",
              }}
            >
              {(() => {
                const feedback = getDetailedFeedback();

                if (feedback.isAllCorrect) {
                  return `Correct! You earned ${score} point${
                    score !== 1 ? "s" : ""
                  }.`;
                }

                if (feedback.selectedCorrectCount === 0) {
                  return "Incorrect - None of your selections are correct.";
                }

                if (
                  feedback.hasIncorrectSelections &&
                  feedback.selectedCorrectCount > 0
                ) {
                  return `Partially correct - You selected ${
                    feedback.selectedCorrectCount
                  } correct answer${
                    feedback.selectedCorrectCount !== 1 ? "s" : ""
                  } but also selected ${
                    feedback.selectedIncorrectCount
                  } incorrect answer${
                    feedback.selectedIncorrectCount !== 1 ? "s" : ""
                  }.`;
                }

                if (
                  feedback.hasMissedCorrect &&
                  !feedback.hasIncorrectSelections
                ) {
                  return `Partially correct - You selected ${
                    feedback.selectedCorrectCount
                  } correct answer${
                    feedback.selectedCorrectCount !== 1 ? "s" : ""
                  } but missed ${feedback.missedCorrectCount} correct answer${
                    feedback.missedCorrectCount !== 1 ? "s" : ""
                  }.`;
                }

                return "Incorrect - Try again.";
              })()}
            </p>
          </div>

          {score > 0 && (
            <div
              style={{
                backgroundColor: "#4CAF50",
                color: "white",
                padding: "5px 12px",
                borderRadius: "20px",
                fontSize: isSmallScreen ? "14px" : "16px",
                fontWeight: "bold",
                marginLeft: isSmallScreen ? "0px" : "10px",
              }}
            >
              +{score}
            </div>
          )}
        </div>
      )}

      {/* Controls section */}
      <div
        style={{
          display: "flex",
          flexDirection: isSmallScreen ? "column" : "row",
          justifyContent: "space-between",
          alignItems: isSmallScreen ? "stretch" : "center",
          width: isSmallScreen ? "100%" : "90%",
          gap: "15px",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: "15px",
            flexWrap: isSmallScreen ? "wrap" : "nowrap",
            justifyContent: isSmallScreen ? "center" : "flex-start",
          }}
        >
          <button
            style={{
              backgroundColor: isSubmitted ? "#d0d0d0" : "#D9D9D9",
              borderWidth: 1,
              borderColor: "#140342",
              borderStyle: "solid",
              padding: "10px 20px",
              borderRadius: "5px",
              cursor:
                isSubmitted || selectedAnswers.length === 0
                  ? "default"
                  : "pointer",
              opacity: isSubmitted || selectedAnswers.length === 0 ? 0.7 : 1,
            }}
            onClick={handleDone}
            disabled={isSubmitted || selectedAnswers.length === 0}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
              }}
            >
              Submit
            </p>
          </button>

          <button
            style={{
              backgroundColor: "#D9D9D9",
              borderWidth: 1,
              borderColor: "#140342",
              borderStyle: "solid",
              padding: "10px 20px",
              borderRadius: "5px",
              cursor: "pointer",
            }}
            onClick={handleRedo}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
              }}
            >
              Re-do
            </p>
          </button>

          <div
            style={{
              display: "flex",
              alignItems: "center",
              backgroundColor: "#f9f9f9",
              padding: "5px 10px",
              borderRadius: "5px",
              border: "1px solid #ddd",
            }}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
                marginRight: "5px",
              }}
            >
              {showAnswer ? "Hide Answer" : "Show Answer"}
            </p>
            <Switch
              onChange={() => handleShowAnswer()}
              checked={showAnswer}
              size="small"
            />
          </div>


        </div>
      </div>

      {/* Subscription Modal */}
      {/* <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={closeSubscriptionModal}
        eligibility={eligibility}
        title="Subscription Required"
        message="Continue practicing with detailed feedback and analysis."
      /> */}

      {/* Sidebar Toggle */}
      <SidebarToggle
        isVisible={true}
        activeCategory="reading"
        currentQuestionType="6780113ae0dfdc154eff11b6"
        onQuestionSelect={onQuestionSelect}
      />
    </div>
  );
};

MultipleChoiceMultiple.propTypes = {
  question: PropTypes.shape({
    questionId: PropTypes.string,
    maxScore: PropTypes.number,
    prompt: PropTypes.string,
    duration: PropTypes.number,
    options: PropTypes.arrayOf(
      PropTypes.shape({
        text: PropTypes.string,
        isCorrect: PropTypes.bool,
      })
    ),
  }).isRequired,
  onQuestionSelect: PropTypes.func.isRequired,
};

export default MultipleChoiceMultiple;
