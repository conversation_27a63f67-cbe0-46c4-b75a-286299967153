import { useState, useEffect } from "react";
import { Switch } from "@mui/material";
// Subscription imports
// import { useSubscriptionCheck } from "../../hooks/useSubscriptionCheck";
// import SubscriptionModal from "../others/SubscriptionModal";
// import SubscriptionIndicator from "../others/SubscriptionIndicator";
import PropTypes from "prop-types";
import SidebarToggle from "../common/SidebarToggle";
// Practice tracking imports
import {
  logPracticeAttempt,
  usePracticeInfo,
} from "../speech-ace-component/utils/practiceUtils";

const MultipleChoiceSingle = ({ question, onQuestionSelect }) => {
  const [selectedAnswer, setSelectedAnswer] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showAnswer, setShowAnswer] = useState(false);
  const [timeElapsed, setTimeElapsed] = useState(0); // Time elapsed in seconds
  const [timerActive, setTimerActive] = useState(true);
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [score, setScore] = useState(0);
  const maxScore = question?.maxScore || 1;

  // Practice tracking
  const { setPracticeCount, setIsPracticed } = usePracticeInfo(
    question.questionId
  );

  // Subscription logic
  // const {
  //   eligibility,
  //   loading: subscriptionLoading,
  //   showSubscriptionModal,
  //   canViewAnswer,
  //   handleSubmitWithCheck,
  //   closeSubscriptionModal,
  //   showSubscriptionRequiredModal,
  // } = useSubscriptionCheck(question.questionId);

  // Reset component when question changes
  useEffect(() => {
    setSelectedAnswer("");
    setIsSubmitted(false);
    setShowAnswer(false);
    setScore(0);
    setTimeElapsed(0); // Reset elapsed time to 0
    setTimerActive(true);
  }, [question?.questionId, question?.prompt]); // Reset when question ID or prompt changes

  // Initialize timer - infinite and counting upward
  useEffect(() => {
    let timer;
    if (timerActive) {
      timer = setInterval(() => {
        setTimeElapsed((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [timerActive]);

  // Format time as MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  // Update screen width on resize
  useEffect(() => {
    const handleResize = () => setScreenWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const isSmallScreen = screenWidth <= 768;

  // Handle answer selection
  const handleSelect = (value) => {
    if (isSubmitted && !showAnswer) return;
    setSelectedAnswer(value);
  };

  // Handle form submission with subscription check
  const handleDone = async () => {
    // Check subscription before proceeding
    // const success = await handleSubmitWithCheck(async () => {
    setIsSubmitted(true);
    // Timer is infinite, no need to control activity

    // Calculate score - handle both isCorrect and correct properties for backward compatibility
    const correctOption = question.options.find(
      (option) => option.isCorrect === true || option.correct === true
    );
    const isAnswerCorrect =
      correctOption && selectedAnswer === correctOption.text;

    // Also check against correctAnswer field if available
    const isCorrectByField =
      question.correctAnswer && selectedAnswer === question.correctAnswer;

    if (isAnswerCorrect || isCorrectByField) {
      setScore(maxScore);
    } else {
      setScore(0);
    }

    // Log practice attempt
    const result = await logPracticeAttempt(question.questionId);
    if (result.success) {
      setPracticeCount(result.practiceCount);
      setIsPracticed(true);
    }
    // });

    // if (!success) {
    //   // If subscription check failed, ensure timer doesn't stop
    //   setTimerActive(true);
    // }
  };

  // Handle show answer with subscription check
  const handleShowAnswer = async () => {
    // if (canViewAnswer) {
    setShowAnswer(!showAnswer);
    // } else {
    //   showSubscriptionRequiredModal();
    // }
  };

  // Handle redo
  const handleRedo = () => {
    setSelectedAnswer("");
    setIsSubmitted(false);
    setShowAnswer(false);
    setScore(0);
    setTimeElapsed(0); // Reset elapsed time to 0
    setTimerActive(true);
  };

  // Handle show answer toggle
  useEffect(() => {
    if (showAnswer) {
      // Set the selected answer to the correct one - handle both API structures
      const correctOption = question.options.find(
        (option) => option.isCorrect === true || option.correct === true
      );
      if (correctOption) {
        setSelectedAnswer(correctOption.text || correctOption.optionText);
      } else if (question.correctAnswer) {
        // Fallback to correctAnswer field
        setSelectedAnswer(question.correctAnswer);
      }
    } else if (isSubmitted && !showAnswer) {
      // If we're hiding the answer after submission, we don't need to do anything
      // as the user's selection is already stored
    }
  }, [showAnswer, question.options, question.correctAnswer, isSubmitted]);

  // Get feedback for each option
  const getFeedback = (option) => {
    if (!isSubmitted || showAnswer) return null;

    const isCorrect = option.isCorrect === true || option.correct === true; // Handle both API structures
    const isSelected = selectedAnswer === (option.text || option.optionText);

    // Only show feedback for the selected option or the correct one
    if (!isSelected && !isCorrect) return null;

    return (
      <span
        style={{
          color: isCorrect ? "#008800" : "#cc0000",
          marginLeft: "10px",
          fontSize: "16px",
          fontWeight: "500",
        }}
      >
        {isCorrect ? "✓ Correct" : "✗ Incorrect"}
      </span>
    );
  };

  return (
    <div
      style={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: "20px",
        backgroundColor: "#f9f9f9",
        borderRadius: "8px",
        position: "relative",
      }}
      className="question-container"
    >
      {/* Subscription Indicator */}
      {/* <SubscriptionIndicator
        eligibility={eligibility}
        loading={subscriptionLoading}
        position="top-right"
      /> */}

      {/* Header with timer and category name */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          width: isSmallScreen ? "100%" : "90%",
          marginBottom: "15px",
        }}
      >
        {/* Timer display */}
        <div
          style={{
            display: "flex",
            alignItems: "center",
            backgroundColor: "#f0f0f0",
            padding: "5px 15px",
            borderRadius: "20px",
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          }}
        >
          <span
            style={{
              fontSize: "18px",
              fontWeight: "bold",
              color: "#333",
            }}
          >
            Time:
          </span>
          <span
            style={{
              fontSize: "18px",
              fontWeight: "bold",
              color: "#333",
              marginLeft: "5px",
            }}
          >
            {formatTime(timeElapsed)}
          </span>
        </div>
        {/* Score display - only shown after submission */}
        {isSubmitted && (
          <div
            style={{
              display: "flex",
              justifyContent: "flex-end",
              width: isSmallScreen ? "100%" : "90%",
              marginBottom: "15px",
            }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                backgroundColor: "#f0f0f0",
                padding: "5px 15px",
                borderRadius: "20px",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
              }}
            >
              <span style={{ fontSize: "16px", fontWeight: "bold" }}>
                Score:{" "}
              </span>
              <span
                style={{
                  fontSize: "18px",
                  fontWeight: "bold",
                  color: score > 0 ? "#008800" : "#333",
                  marginLeft: "5px",
                }}
              >
                {score}/{maxScore}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Main content card */}
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          width: isSmallScreen ? "100%" : "90%",
          backgroundColor: "white",
          padding: "25px",
          borderRadius: "8px",
          boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
          marginBottom: "20px",
        }}
      >
        {/* Question prompt */}
        <p
          style={{
            color: "#333",
            fontWeight: "600",
            fontSize: "22px",
            lineHeight: 1.5,
            marginTop: 0,
            marginBottom: "30px",
          }}
        >
          {question.prompt}
        </p>

        {/* Options */}
        <div className="options">
          {question.options?.map((option, idx) => (
            <div
              key={idx}
              style={{
                marginBottom: "15px",
                padding: "12px 15px",
                borderRadius: "8px",
                border: "1px solid #ddd",
                backgroundColor:
                  selectedAnswer === (option.text || option.optionText)
                    ? isSubmitted
                      ? option.isCorrect === true || option.correct === true
                        ? "#e6ffe6" // Correct and selected
                        : "#ffe6e6" // Incorrect and selected
                      : "#f0f0ff" // Selected but not submitted
                    : isSubmitted &&
                      (option.isCorrect === true || option.correct === true) &&
                      showAnswer
                    ? "#e6ffe6" // Correct answer on show answer
                    : "white", // Not selected
                display: "flex",
                alignItems: "center",
                cursor: isSubmitted && !showAnswer ? "default" : "pointer",
              }}
              onClick={() =>
                !isSubmitted && handleSelect(option.text || option.optionText)
              }
            >
              <label
                style={{
                  display: "flex",
                  alignItems: "center",
                  fontSize: "18px",
                  color: "#333",
                  width: "100%",
                  cursor: "pointer",
                }}
              >
                <input
                  type="radio"
                  name="multiple-choice"
                  value={option.text || option.optionText}
                  checked={selectedAnswer === (option.text || option.optionText)}
                  onChange={() =>
                    handleSelect(option.text || option.optionText)
                  }
                  disabled={isSubmitted && !showAnswer}
                  style={{
                    marginRight: "15px",
                    width: "20px",
                    height: "20px",
                  }}
                />
                <span style={{ flex: 1 }}>
                  {option.text || option.optionText}
                </span>
              </label>
              {getFeedback(option)}
            </div>
          ))}
        </div>
      </div>

      {/* Controls section */}
      <div
        style={{
          display: "flex",
          flexDirection: isSmallScreen ? "column" : "row",
          justifyContent: "space-between",
          alignItems: isSmallScreen ? "stretch" : "center",
          width: isSmallScreen ? "100%" : "90%",
          gap: "15px",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: "15px",
            flexWrap: isSmallScreen ? "wrap" : "nowrap",
            justifyContent: isSmallScreen ? "center" : "flex-start",
          }}
        >
          <button
            style={{
              backgroundColor: isSubmitted ? "#d0d0d0" : "#D9D9D9",
              borderWidth: 1,
              borderColor: "#140342",
              borderStyle: "solid",
              padding: "10px 20px",
              borderRadius: "5px",
              cursor: isSubmitted || !selectedAnswer ? "default" : "pointer",
              opacity: isSubmitted || !selectedAnswer ? 0.7 : 1,
            }}
            onClick={handleDone}
            disabled={isSubmitted || !selectedAnswer}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
              }}
            >
              Submit
            </p>
          </button>

          <button
            style={{
              backgroundColor: "#D9D9D9",
              borderWidth: 1,
              borderColor: "#140342",
              borderStyle: "solid",
              padding: "10px 20px",
              borderRadius: "5px",
              cursor: "pointer",
            }}
            onClick={handleRedo}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
              }}
            >
              Re-do
            </p>
          </button>

          <div
            style={{
              display: "flex",
              alignItems: "center",
              backgroundColor: "#f9f9f9",
              padding: "5px 10px",
              borderRadius: "5px",
              border: "1px solid #ddd",
            }}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
                marginRight: "5px",
              }}
            >
              {showAnswer ? "Hide Answer" : "Show Answer"}
            </p>
            <Switch
              onChange={() => handleShowAnswer()}
              checked={showAnswer}
              size="small"
            />
          </div>

          <button
            style={{
              backgroundColor: "#D9D9D9",
              borderWidth: 1,
              borderColor: "#140342",
              borderStyle: "solid",
              padding: "10px 20px",
              borderRadius: "5px",
              display: "flex",
              alignItems: "center",
              gap: 10,
              cursor: "pointer",
            }}
          >
            <img
              src="/assets/img/translate.png"
              alt="translate"
              style={{ width: "20px", height: "20px" }}
            />
            <p
              style={{
                margin: 0,
                color: "#522CFF",
                fontSize: 14,
                fontWeight: "500",
              }}
            >
              Translation
            </p>
          </button>
        </div>

        {/* Feedback message after submission */}
        {isSubmitted && !showAnswer && (
          <div
            style={{
              padding: "10px 15px",
              borderRadius: "5px",
              backgroundColor: score > 0 ? "#e6ffe6" : "#ffe6e6",
              border: `1px solid ${score > 0 ? "#008800" : "#cc0000"}`,
              textAlign: "center",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              minWidth: isSmallScreen ? "auto" : "250px",
            }}
          >
            <div>
              <p
                style={{
                  margin: 0,
                  fontWeight: "bold",
                  color: score > 0 ? "#008800" : "#cc0000",
                }}
              >
                {score > 0
                  ? `Correct! You earned ${score} point${
                      score !== 1 ? "s" : ""
                    }.`
                  : "Incorrect - Try again"}
              </p>
            </div>

            {score > 0 && (
              <div
                style={{
                  backgroundColor: "#4CAF50",
                  color: "white",
                  padding: "5px 12px",
                  borderRadius: "20px",
                  fontSize: "16px",
                  fontWeight: "bold",
                  marginLeft: "10px",
                }}
              >
                +{score}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Subscription Modal */}
      {/* <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={closeSubscriptionModal}
        eligibility={eligibility}
        title="Subscription Required"
        message="Continue practicing with detailed feedback and analysis."
      /> */}

      {/* Sidebar Toggle */}
      <SidebarToggle
        isVisible={true}
        activeCategory="reading"
        currentQuestionType="67800fc7d4e7d9147dd9525d"
        onQuestionSelect={onQuestionSelect}
      />
    </div>
  );
};

MultipleChoiceSingle.propTypes = {
  question: PropTypes.shape({
    questionId: PropTypes.string,
    questionNumber: PropTypes.string,
    prompt: PropTypes.string.isRequired,
    section: PropTypes.string,
    difficulty: PropTypes.string,
    maxScore: PropTypes.number,
    isExplained: PropTypes.string,
    isExplainedByVideo: PropTypes.string,
    options: PropTypes.arrayOf(
      PropTypes.shape({
        text: PropTypes.string.isRequired,
        isCorrect: PropTypes.bool, // New API structure
        correct: PropTypes.bool, // Old API structure for backward compatibility
      })
    ).isRequired,
    correctAnswer: PropTypes.string, // New API field
    categoryId: PropTypes.string,
    mocktestId: PropTypes.string,
    type: PropTypes.string,
    duration: PropTypes.number,
    additionalProp1: PropTypes.shape({
      questionName: PropTypes.string,
      practiceCount: PropTypes.number,
      practiceStatus: PropTypes.string,
      markColor: PropTypes.string,
      isShadowed: PropTypes.bool,
      enableDiscussion: PropTypes.bool,
      discussion: PropTypes.array,
      keywords: PropTypes.string,
      prepTime: PropTypes.string,
      answerTime: PropTypes.string,
      createdAt: PropTypes.string,
    }),
    questionName: PropTypes.string,
    category: PropTypes.shape({
      categoryId: PropTypes.string,
      name: PropTypes.string,
      description: PropTypes.string,
      section: PropTypes.string,
    }),
  }).isRequired,
  onQuestionSelect: PropTypes.func.isRequired,
};

export default MultipleChoiceSingle;
