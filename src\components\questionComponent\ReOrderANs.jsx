import { useState, useEffect } from "react";
import Switch from "@mui/material/Switch";
import PropTypes from "prop-types";
import { CSS } from "@dnd-kit/utilities";
import {
  DndContext,
  useSensor,
  useSensors,
  TouchSensor,
  MouseSensor,
  closestCenter,
} from "@dnd-kit/core";
import {
  SortableContext,
  arrayMove,
  useSortable,
  rectSortingStrategy,
} from "@dnd-kit/sortable";
// Subscription imports
// import { useSubscriptionCheck } from "../../hooks/useSubscriptionCheck";
// import SubscriptionModal from "../others/SubscriptionModal";
// import SubscriptionIndicator from "../others/SubscriptionIndicator";
import SidebarToggle from "../common/SidebarToggle";
// Practice tracking imports
import {
  logPracticeAttempt,
  usePracticeInfo,
} from "../speech-ace-component/utils/practiceUtils";

const ReorderParagraphsWithButtons = ({ question, onQuestionSelect }) => {
  // Practice tracking
  const { setPracticeCount, setIsPracticed } = usePracticeInfo(
    question.questionId
  );

  // Component for each draggable sentence
  const DraggableItem = ({ id, value, index }) => {
    const { attributes, listeners, setNodeRef, transform, transition } =
      useSortable({ id });

    const style = {
      transition,
      cursor: "grab",
      fontSize: "20px",
      transform: CSS.Transform.toString(transform),
      backgroundColor: "white",
      padding: "15px 20px",
      borderRadius: "8px",
      border: "1px solid #140342",
      marginBottom: "10px",
      boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
    };

    return (
      <div
        ref={setNodeRef}
        className="w-full"
        style={style}
        {...attributes}
        {...listeners}
      >
        {`${index + 1}. ${value.text}`}
      </div>
    );
  };

  // Add PropTypes for DraggableItem
  DraggableItem.propTypes = {
    id: PropTypes.string.isRequired,
    value: PropTypes.shape({
      text: PropTypes.string.isRequired,
    }).isRequired,
    index: PropTypes.number.isRequired,
  };

  // Function to shuffle the sentences
  const shuffleSentences = (sentencesArray) => {
    return [...sentencesArray].sort(() => Math.random() - 0.5);
  };

  // Get sentences and correct answers from question data
  const getSentencesData = () => {
    // Handle new API structure
    if (question.sentences && question.correctOrder) {
      // Convert sentences array of objects to text array
      const sentencesTexts = question.sentences.map(
        (sentence) => sentence.text
      );

      // Convert correctOrder (array of IDs) to correct answers (array of texts)
      const correctAnswers = question.correctOrder
        .map((id) => {
          const sentence = question.sentences.find((s) => s.id === id);
          return sentence ? sentence.text : "";
        })
        .filter((text) => text !== "");

      return {
        sentences: sentencesTexts,
        correctAnswers: correctAnswers,
      };
    }
    // Handle old data formats (backward compatibility)
    else if (question.sentences && question.correctAnswers) {
      return {
        sentences: question.sentences,
        correctAnswers: question.correctAnswers,
      };
    } else if (question.options && question.options[0]) {
      return {
        sentences: question.options[0].sentences,
        correctAnswers: question.options[0].correctAnswers,
      };
    }
    return { sentences: [], correctAnswers: [] };
  };

  const { sentences: questionSentences, correctAnswers } = getSentencesData();

  // Function to initialize sentences
  const initializeSentences = () => {
    const initialSentences = questionSentences.map((text, index) => ({
      id: `${text}-${index}-${Date.now()}`, // Add timestamp for uniqueness
      text,
      originalIndex: index,
    }));
    return shuffleSentences(initialSentences);
  };

  // Initialize sentences state with a shuffled array
  const [sentences, setSentences] = useState(() => initializeSentences());

  // Store the original shuffled order to restore when hiding answer
  const [originalShuffledOrder, setOriginalShuffledOrder] = useState(sentences);
  const [userCurrentOrder, setUserCurrentOrder] = useState(sentences);

  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showAnswer, setShowAnswer] = useState(false);
  const [isCorrect, setIsCorrect] = useState(null);
  const [score, setScore] = useState(0);
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [timeElapsed, setTimeElapsed] = useState(0); // Time elapsed in seconds
  const [timerActive, setTimerActive] = useState(true);
  const maxScore = question?.maxScore || 3; // Default to 3 if not provided

  // Subscription logic
  // const {
  //   eligibility,
  //   loading: subscriptionLoading,
  //   showSubscriptionModal,
  //   canViewAnswer,
  //   handleSubmitWithCheck,
  //   closeSubscriptionModal,
  //   showSubscriptionRequiredModal,
  // } = useSubscriptionCheck(question.questionId);

  // Reset component when question changes
  useEffect(() => {
    const newSentences = initializeSentences();
    setSentences(newSentences);
    setOriginalShuffledOrder(newSentences);
    setUserCurrentOrder(newSentences);
    setIsSubmitted(false);
    setShowAnswer(false);
    setIsCorrect(null);
    setScore(0);
    setTimeElapsed(0); // Reset elapsed time to 0
    setTimerActive(true);
  }, [question?.questionId, question?.prompt]); // Reset when question ID or prompt changes

  // Initialize timer - infinite and counting upward
  useEffect(() => {
    let timer;
    if (timerActive) {
      timer = setInterval(() => {
        setTimeElapsed((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [timerActive]);

  useEffect(() => {
    const handleResize = () => setScreenWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  const isSmallScreen = screenWidth <= 768;

  // Set up sensors for both touch and mouse
  const touchSensor = useSensor(TouchSensor);
  const mouseSensor = useSensor(MouseSensor);
  const sensors = useSensors(mouseSensor, touchSensor);

  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (!over || active.id === over.id || showAnswer) return;

    setSentences((items) => {
      const oldIndex = items.findIndex((item) => item.id === active.id);
      const newIndex = items.findIndex((item) => item.id === over.id);
      const newOrder = arrayMove(items, oldIndex, newIndex);

      // Update user's current order when not showing answer
      if (!showAnswer) {
        setUserCurrentOrder(newOrder);
      }

      return newOrder;
    });
  };

  const handleSubmit = async () => {
    // Check subscription before proceeding
    // const success = await handleSubmitWithCheck(async () => {
    const currentOrder = sentences.map((item) => item.text);
    const isAnswerCorrect =
      JSON.stringify(currentOrder) === JSON.stringify(correctAnswers);

    setIsCorrect(isAnswerCorrect);
    setIsSubmitted(true);
    setTimerActive(false);

    if (isAnswerCorrect) {
      setScore(maxScore); // Full score for completely correct answer
    } else {
      // Calculate partial score based on correct positions
      let correctPositions = 0;
      for (
        let i = 0;
        i < Math.min(currentOrder.length, correctAnswers.length);
        i++
      ) {
        if (currentOrder[i] === correctAnswers[i]) {
          correctPositions++;
        }
      }
      // Give partial score: (correct positions / total positions) * maxScore
      const partialScore = Math.floor(
        (correctPositions / correctAnswers.length) * maxScore
      );
      setScore(partialScore);
    }
    // });

    // if (!success) {
    //   // If subscription check failed, ensure timer doesn't stop
    //   setTimerActive(true);
    // }

    // Log practice attempt
    const result = await logPracticeAttempt(question.questionId);
    if (result.success) {
      setPracticeCount(result.practiceCount);
      setIsPracticed(true);
    }
  };

  const handleRedo = () => {
    // Create new sentence objects to ensure a complete reset
    const newSentences = initializeSentences();

    // Shuffle and set the new sentences
    setSentences(newSentences);
    setOriginalShuffledOrder(newSentences);
    setUserCurrentOrder(newSentences);
    setIsSubmitted(false);
    setIsCorrect(null);
    setScore(0); // Reset score on redo
    setTimeElapsed(0); // Reset elapsed time to 0
    setTimerActive(true);
    setShowAnswer(false);
  };

  // Handle show answer with subscription check
  const handleShowAnswerToggle = async (checked) => {
    // if (checked && !canViewAnswer) {
    //   showSubscriptionRequiredModal();
    //   return;
    // }

    setShowAnswer(checked);

    if (checked) {
      // Show correct answer - create ordered sentences
      const orderedSentences = correctAnswers.map((text, index) => ({
        id: `correct-${index}-${Date.now()}`,
        text,
        originalIndex: index,
      }));
      setSentences(orderedSentences);
    } else {
      // Hide answer - restore to user's current state
      if (isSubmitted) {
        // If submitted, restore to the order at time of submission
        setSentences(userCurrentOrder);
      } else {
        // If not submitted, restore to original shuffled order
        setSentences(originalShuffledOrder);
      }
    }
  };

  return (
    <div
      style={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: "20px",
        backgroundColor: "#f9f9f9",
        borderRadius: "8px",
        position: "relative",
      }}
      className="question-container"
    >
      {/* Subscription Indicator */}
      {/* <SubscriptionIndicator
        eligibility={eligibility}
        loading={subscriptionLoading}
        position="top-right"
      /> */}

      <div
        style={{
          display: "flex",
          flexDirection: "column",
          width: isSmallScreen ? "100%" : "90%",
          marginTop: 20,
          marginBottom: 20,
          position: "relative",
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "15px",
          }}
        >
          {/* Timer display */}
          <div
            style={{
              display: "flex",
              alignItems: "center",
              backgroundColor: "#f0f0f0",
              padding: "5px 15px",
              borderRadius: "20px",
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
            }}
          >
            <span
              style={{
                fontSize: "18px",
                fontWeight: "bold",
                color: "#333",
              }}
            >
              Time: {formatTime(timeElapsed)}
            </span>
          </div>
        </div>

        {/* Prompt */}
        <div
          style={{
            backgroundColor: "white",
            padding: "15px 25px",
            borderRadius: "8px",
            marginBottom: "20px",
            boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
          }}
        >
          <p
            style={{
              fontSize: "18px",
              margin: 0,
              color: "#444",
              fontStyle: "italic",
            }}
          >
            {question.prompt}
          </p>
        </div>

        {/* Score display - only shown after submission */}
        {isSubmitted && (
          <div
            style={{
              display: "flex",
              justifyContent: "flex-end",
              marginBottom: "15px",
            }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                backgroundColor: "#f0f0f0",
                padding: "5px 15px",
                borderRadius: "20px",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
              }}
            >
              <span style={{ fontSize: "16px", fontWeight: "bold" }}>
                Score:{" "}
              </span>
              <span
                style={{
                  fontSize: "18px",
                  fontWeight: "bold",
                  color: score > 0 ? "#008800" : "#333",
                  marginLeft: "5px",
                }}
              >
                {score}/{maxScore}
              </span>
            </div>
          </div>
        )}

        {/* Draggable sentences */}
        <div
          style={{
            backgroundColor: "white",
            padding: "25px",
            borderRadius: "8px",
            boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
            position: "relative",
          }}
        >
          {/* Show Answer indicator */}
          {showAnswer && (
            <div
              style={{
                position: "absolute",
                top: "10px",
                right: "10px",
                backgroundColor: "#e8f5e8",
                color: "#2e7d32",
                padding: "4px 8px",
                borderRadius: "12px",
                fontSize: "12px",
                fontWeight: "600",
                border: "1px solid #4caf50",
              }}
            >
              ✓ Correct Order
            </div>
          )}

          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={sentences.map((item) => item.id)}
              strategy={rectSortingStrategy}
            >
              <div className="flex flex-col w-full gap-4">
                {sentences.map((item, index) => (
                  <DraggableItem
                    key={item.id}
                    id={item.id}
                    value={item}
                    index={index}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>

          {/* Instructions */}
          {!isSubmitted && !showAnswer && (
            <div
              style={{
                marginTop: "15px",
                padding: "10px",
                backgroundColor: "#f8f9fa",
                borderRadius: "6px",
                border: "1px solid #e9ecef",
              }}
            >
              <p
                style={{
                  margin: 0,
                  fontSize: "14px",
                  color: "#6c757d",
                  textAlign: "center",
                }}
              >
                💡 Drag and drop the sentences to arrange them in the correct
                order
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Buttons */}
      <div
        style={{
          display: "flex",
          flexDirection: isSmallScreen ? "column" : "row",
          justifyContent: "space-between",
          alignItems: isSmallScreen ? "stretch" : "center",
          width: isSmallScreen ? "100%" : "90%",
          marginTop: 20,
          gap: "15px",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: "15px",
            flexWrap: isSmallScreen ? "wrap" : "nowrap",
            justifyContent: isSmallScreen ? "center" : "flex-start",
          }}
        >
          <button
            style={{
              backgroundColor: isSubmitted ? "#d0d0d0" : "#D9D9D9",
              borderWidth: 1,
              borderColor: "#140342",
              borderStyle: "solid",
              padding: "10px 20px",
              borderRadius: "5px",
              cursor: isSubmitted ? "default" : "pointer",
              opacity: isSubmitted ? 0.7 : 1,
            }}
            onClick={handleSubmit}
            disabled={isSubmitted}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
              }}
            >
              Submit
            </p>
          </button>

          <button
            style={{
              backgroundColor: "#D9D9D9",
              borderWidth: 1,
              borderColor: "#140342",
              borderStyle: "solid",
              padding: "10px 20px",
              borderRadius: "5px",
              cursor: "pointer",
            }}
            onClick={handleRedo}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
              }}
            >
              Re-do
            </p>
          </button>

          <div
            style={{
              display: "flex",
              alignItems: "center",
              backgroundColor: "#f9f9f9",
              padding: "5px 10px",
              borderRadius: "5px",
              border: "1px solid #ddd",
            }}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
                marginRight: "5px",
              }}
            >
              {showAnswer ? "Hide Answer" : "Show Answer"}
            </p>
            <Switch
              onChange={(e, checked) => handleShowAnswerToggle(checked)}
              checked={showAnswer}
              size="small"
            />
          </div>


        </div>

        {isSubmitted && !showAnswer && (
          <div
            style={{
              padding: "10px 15px",
              borderRadius: "5px",
              backgroundColor: isCorrect
                ? "#e6ffe6"
                : score > 0
                ? "#fff5e6"
                : "#ffe6e6",
              border: `1px solid ${
                isCorrect ? "#008800" : score > 0 ? "#ff9500" : "#cc0000"
              }`,
              textAlign: "center",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              minWidth: isSmallScreen ? "auto" : "250px",
            }}
          >
            <div>
              <p
                style={{
                  margin: 0,
                  fontWeight: "bold",
                  color: isCorrect
                    ? "#008800"
                    : score > 0
                    ? "#ff9500"
                    : "#cc0000",
                }}
              >
                {isCorrect
                  ? `Correct! You earned ${score} point${
                      score !== 1 ? "s" : ""
                    }.`
                  : score > 0
                  ? `Partially correct! You earned ${score} out of ${maxScore} points.`
                  : "Incorrect - Try again"}
              </p>
              {!isCorrect && (
                <p
                  style={{
                    margin: "5px 0 0 0",
                    fontSize: "12px",
                    color: "#555",
                  }}
                >
                  {score > 0
                    ? "Some sentences are in the correct position. Rearrange the others."
                    : "Drag the sentences to rearrange them."}
                </p>
              )}
            </div>

            {score > 0 && (
              <div
                style={{
                  backgroundColor: isCorrect ? "#4CAF50" : "#FF9500",
                  color: "white",
                  padding: "5px 12px",
                  borderRadius: "20px",
                  fontSize: "16px",
                  fontWeight: "bold",
                  marginLeft: "10px",
                }}
              >
                +{score}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Correct answer display - only shown when show answer is toggled */}
      {showAnswer && (
        <div
          style={{
            marginTop: "20px",
            backgroundColor: "#f5f9ff",
            padding: "15px 20px",
            borderRadius: "8px",
            border: "1px solid #b8d0f0",
            width: isSmallScreen ? "100%" : "90%",
          }}
        >
          <h3
            style={{
              margin: "0 0 15px 0",
              color: "#333",
              fontSize: "18px",
              fontWeight: "600",
              textAlign: "center",
              borderBottom: "2px solid #e0e0e0",
              paddingBottom: "10px",
            }}
          >
            📝 Correct Order
          </h3>
          <div>
            {correctAnswers.map((sentence, index) => (
              <div
                key={index}
                style={{
                  padding: "12px 15px",
                  backgroundColor: "#fff",
                  marginBottom: "8px",
                  borderRadius: "6px",
                  border: "1px solid #ddd",
                  boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
                  fontSize: "16px",
                  color: "#333",
                }}
              >
                <span
                  style={{
                    fontWeight: "600",
                    color: "#2e7d32",
                    marginRight: "8px",
                  }}
                >
                  {index + 1}.
                </span>
                {sentence}
              </div>
            ))}
          </div>
          <p
            style={{
              margin: "15px 0 0 0",
              fontSize: "12px",
              color: "#666",
              textAlign: "center",
              fontStyle: "italic",
            }}
          >
            ✓ This is the correct order for the sentences
          </p>
        </div>
      )}

      {/* Subscription Modal */}
      {/* <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={closeSubscriptionModal}
        eligibility={eligibility}
        title="Subscription Required"
        message="Continue practicing with detailed feedback and analysis."
      /> */}

      {/* Sidebar Toggle */}
      <SidebarToggle
        isVisible={true}
        activeCategory="reading"
        currentQuestionType="678011ade0dfdc154eff11b8"
        onQuestionSelect={onQuestionSelect}
      />
    </div>
  );
};

ReorderParagraphsWithButtons.propTypes = {
  question: PropTypes.shape({
    questionId: PropTypes.string,
    prompt: PropTypes.string.isRequired,
    maxScore: PropTypes.number,
    duration: PropTypes.number,
    // Support new API structure
    sentences: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.number.isRequired,
        text: PropTypes.string.isRequired,
      })
    ),
    correctOrder: PropTypes.arrayOf(PropTypes.number),
    // Support old data formats for backward compatibility
    correctAnswers: PropTypes.arrayOf(PropTypes.string),
    options: PropTypes.arrayOf(
      PropTypes.shape({
        sentences: PropTypes.arrayOf(PropTypes.string),
        correctAnswers: PropTypes.arrayOf(PropTypes.string),
      })
    ),
  }).isRequired,
  onQuestionSelect: PropTypes.func.isRequired,
};

export default ReorderParagraphsWithButtons;
