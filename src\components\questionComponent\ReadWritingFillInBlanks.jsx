import { Switch } from "@mui/material";
import { useState, useEffect } from "react";
// Subscription imports
// import { useSubscriptionCheck } from "../../hooks/useSubscriptionCheck";
// import SubscriptionModal from "../others/SubscriptionModal";
// import SubscriptionIndicator from "../others/SubscriptionIndicator";
import PropTypes from "prop-types";
import SidebarToggle from "../common/SidebarToggle";
// Practice tracking imports
import {
  logPracticeAttempt,
  usePracticeInfo,
} from "../speech-ace-component/utils/practiceUtils";

const ReadWritingFillInBlanks = ({ question, onQuestionSelect }) => {
  // Use dropdownOptions instead of options for the new API structure
  const dropdownOptions = question?.dropdownOptions || question?.options || [];
  // Practice tracking
  const { setPracticeCount, setIsPracticed } = usePracticeInfo(
    question.questionId
  );

  const [selectedAnswers, setSelectedAnswers] = useState(
    Array(dropdownOptions.length).fill("")
  );
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [timeElapsed, setTimeElapsed] = useState(0); // Time elapsed in seconds
  const [timerActive, setTimerActive] = useState(true);
  const [score, setScore] = useState(0);
  const [isShowingAnswer, setIsShowingAnswer] = useState(false);
  const maxScore = dropdownOptions.length || 0;

  // Subscription logic
  // const {
  //   eligibility,
  //   loading: subscriptionLoading,
  //   showSubscriptionModal,
  //   canViewAnswer,
  //   handleSubmitWithCheck,
  //   closeSubscriptionModal,
  //   showSubscriptionRequiredModal,
  // } = useSubscriptionCheck(question.questionId);

  // Initialize timer - infinite and counting upward
  useEffect(() => {
    let timer;
    if (timerActive) {
      timer = setInterval(() => {
        setTimeElapsed((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [timerActive]);

  // Reset state when question changes
  useEffect(() => {
    if (question?.questionId) {
      setSelectedAnswers(Array(dropdownOptions.length).fill(""));
      setIsSubmitted(false);
      setIsShowingAnswer(false);
      setScore(0);
      setTimeElapsed(0); // Reset elapsed time to 0
      setTimerActive(true);
    }
  }, [question?.questionId, dropdownOptions.length]);

  useEffect(() => {
    const handleResize = () => setScreenWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  const handleSelect = (index, value) => {
    if (isSubmitted && !isShowingAnswer) return; // Prevent changes after submission unless showing answer

    const updatedAnswers = [...selectedAnswers];
    updatedAnswers[index] = value;
    setSelectedAnswers(updatedAnswers);
  };

  const calculateScore = () => {
    let correct = 0;
    selectedAnswers.forEach((answer, index) => {
      if (
        dropdownOptions[index] &&
        answer.trim() === dropdownOptions[index].correctAnswer.trim()
      ) {
        correct++;
      }
    });

    // Calculate score based on correct answers
    setScore(correct);
    return correct;
  };

  const handleDone = async () => {
    // Check subscription before proceeding
    // const success = await handleSubmitWithCheck(async () => {
    setIsSubmitted(true);
    setTimerActive(false);
    calculateScore();
    // });

    // if (!success) {
    //   // If subscription check failed, ensure timer doesn't stop
    //   setTimerActive(true);
    // }

    // Log practice attempt
    const result = await logPracticeAttempt(question.questionId);
    if (result.success) {
      setPracticeCount(result.practiceCount);
      setIsPracticed(true);
    }
  };

  const handleRedo = () => {
    setSelectedAnswers(Array(dropdownOptions.length).fill(""));
    setIsSubmitted(false);
    setIsShowingAnswer(false);
    setScore(0);
    setTimeElapsed(0); // Reset elapsed time to 0
    setTimerActive(true);
  };

  // Handle show answer with subscription check
  const handleShowAnswer = async () => {
    // if (canViewAnswer) {
    setIsShowingAnswer(!isShowingAnswer);
    // } else {
    //   showSubscriptionRequiredModal();
    // }
  };

  const isSmallScreen = screenWidth <= 768;

  useEffect(() => {
    if (isShowingAnswer) {
      // When showing answer, set all answers to correct answers
      const correctAnswers = dropdownOptions.map((opt) => opt.correctAnswer);
      setSelectedAnswers(correctAnswers);
    } else if (isSubmitted && !isShowingAnswer) {
      // When hiding answer after submission, restore user's selected answers
      // We don't need to do anything here as the answers are already stored
    }
  }, [isShowingAnswer, dropdownOptions, isSubmitted]);

  const getFeedback = (index) => {
    if (!isSubmitted || isShowingAnswer) return null;

    // Trim whitespace from both the selected answer and correct answer for comparison
    const selectedAnswer = selectedAnswers[index]?.trim();
    const correctAnswer = dropdownOptions[index]?.correctAnswer?.trim();
    const isCorrect = selectedAnswer === correctAnswer;

    return (
      <span
        style={{
          color: isCorrect ? "#008800" : "#cc0000",
          marginLeft: "10px",
          fontWeight: "500",
          fontSize: "16px",
        }}
      >
        {isCorrect ? "✓ Correct" : `✗ Incorrect`}
      </span>
    );
  };

  const renderPromptWithBlanks = () => {
    if (!question?.prompt) return null;

    // Split the prompt by "___" to get the parts between blanks
    const parts = question.prompt.split(/___+/);

    return (
      <div style={{ position: "relative" }}>
        {parts.map((part, idx) => (
          <span
            key={idx}
            style={{
              color: "black",
              fontWeight: "500",
              fontSize: "22px",
              lineHeight: 1.6,
              textAlign: "justify",
            }}
          >
            {part}
            {idx < dropdownOptions.length && (
              <>
                <select
                  value={selectedAnswers[idx]}
                  onChange={(e) => handleSelect(idx, e.target.value)}
                  disabled={isSubmitted && !isShowingAnswer}
                  style={{
                    margin: "0 8px",
                    padding: "8px 12px",
                    borderRadius: "4px",
                    border:
                      isSubmitted && !isShowingAnswer
                        ? selectedAnswers[idx].trim() ===
                          dropdownOptions[idx]?.correctAnswer?.trim()
                          ? "2px solid #008800" // Correct answer
                          : "2px solid #cc0000" // Incorrect answer
                        : "2px solid #140342", // Not submitted yet
                    backgroundColor: selectedAnswers[idx]
                      ? isShowingAnswer
                        ? "#e6ffe6" // Show answer mode
                        : isSubmitted
                        ? selectedAnswers[idx].trim() ===
                          dropdownOptions[idx]?.correctAnswer?.trim()
                          ? "#e6ffe6" // Correct answer
                          : "#ffe6e6" // Incorrect answer
                        : "#f0f0ff" // Not submitted yet
                      : "white",
                    color: "#140342",
                    fontSize: "20px",
                    cursor:
                      isSubmitted && !isShowingAnswer ? "default" : "pointer",
                    outline: "none",
                    minWidth: "120px",
                  }}
                >
                  <option value="" disabled>
                    Select...
                  </option>
                  {dropdownOptions[idx]?.options?.map((option, optIdx) => (
                    <option
                      key={optIdx}
                      value={option}
                      style={{
                        padding: "8px",
                        fontSize: "18px",
                      }}
                    >
                      {option}
                    </option>
                  ))}
                </select>
                {getFeedback(idx)}
              </>
            )}
          </span>
        ))}
      </div>
    );
  };

  return (
    <div
      style={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: "20px",
        backgroundColor: "#f9f9f9",
        borderRadius: "8px",
        position: "relative",
      }}
      className="question-container"
    >
      {/* Subscription Indicator */}
      {/* <SubscriptionIndicator
        eligibility={eligibility}
        loading={subscriptionLoading}
        position="top-right"
      /> */}

      <div
        style={{
          display: "flex",
          flexDirection: "column",
          width: isSmallScreen ? "100%" : "90%",
          marginTop: 20,
          marginBottom: 20,
          position: "relative",
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "15px",
          }}
        >
          {/* Timer display */}
          <div
            style={{
              display: "flex",
              alignItems: "center",
              backgroundColor: "#f0f0f0",
              padding: "5px 15px",
              borderRadius: "20px",
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
            }}
          >
            <span
              style={{
                fontSize: "18px",
                fontWeight: "bold",
                color: "#333",
              }}
            >
              Time: {formatTime(timeElapsed)}
            </span>
          </div>
        </div>

        {/* Score display - only shown after submission */}
        {isSubmitted && (
          <div
            style={{
              display: "flex",
              justifyContent: "flex-end",
              marginBottom: "15px",
            }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                backgroundColor: "#f0f0f0",
                padding: "5px 15px",
                borderRadius: "20px",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
              }}
            >
              <span style={{ fontSize: "16px", fontWeight: "bold" }}>
                Score:{" "}
              </span>
              <span
                style={{
                  fontSize: "18px",
                  fontWeight: "bold",
                  color: score > 0 ? "#008800" : "#333",
                  marginLeft: "5px",
                }}
              >
                {score}/{maxScore}
              </span>
            </div>
          </div>
        )}

        {/* Main content */}
        <div
          style={{
            backgroundColor: "white",
            padding: "25px",
            borderRadius: "8px",
            boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
          }}
        >
          {renderPromptWithBlanks()}
        </div>
      </div>

      <div
        style={{
          display: "flex",
          flexDirection: isSmallScreen ? "column" : "row",
          justifyContent: "space-between",
          alignItems: isSmallScreen ? "stretch" : "center",
          width: isSmallScreen ? "100%" : "90%",
          marginTop: 20,
          gap: "15px",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: "15px",
            flexWrap: isSmallScreen ? "wrap" : "nowrap",
            justifyContent: isSmallScreen ? "center" : "flex-start",
          }}
        >
          <button
            style={{
              backgroundColor: isSubmitted ? "#d0d0d0" : "#D9D9D9",
              borderWidth: 1,
              borderColor: "#140342",
              borderStyle: "solid",
              padding: "10px 20px",
              borderRadius: "5px",
              cursor: isSubmitted ? "default" : "pointer",
              opacity: isSubmitted ? 0.7 : 1,
            }}
            onClick={handleDone}
            disabled={isSubmitted}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
              }}
            >
              Submit
            </p>
          </button>

          <button
            style={{
              backgroundColor: "#D9D9D9",
              borderWidth: 1,
              borderColor: "#140342",
              borderStyle: "solid",
              padding: "10px 20px",
              borderRadius: "5px",
              cursor: "pointer",
            }}
            onClick={handleRedo}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
              }}
            >
              Re-do
            </p>
          </button>

          <div
            style={{
              display: "flex",
              alignItems: "center",
              backgroundColor: "#f9f9f9",
              padding: "5px 10px",
              borderRadius: "5px",
              border: "1px solid #ddd",
            }}
          >
            <p
              style={{
                margin: 0,
                color: "black",
                fontSize: 14,
                fontWeight: "500",
                marginRight: "5px",
              }}
            >
              {isShowingAnswer ? "Hide Answer" : "Show Answer"}
            </p>
            <Switch
              onChange={() => handleShowAnswer()}
              checked={isShowingAnswer}
              size="small"
            />
          </div>
        </div>

        {isSubmitted && (
          <div
            style={{
              padding: "10px 15px",
              borderRadius: "5px",
              backgroundColor: score === maxScore ? "#e6ffe6" : "#f8f8f8",
              border: `1px solid ${score === maxScore ? "#008800" : "#ddd"}`,
              textAlign: "center",
            }}
          >
            {score === maxScore ? (
              <p style={{ margin: 0, color: "#008800", fontWeight: "bold" }}>
                Perfect! You got all answers correct.
              </p>
            ) : (
              <p style={{ margin: 0, color: "#555" }}>
                You scored {score} out of {maxScore} points.
              </p>
            )}
          </div>
        )}
      </div>

      {/* Subscription Modal */}
      {/* <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={closeSubscriptionModal}
        eligibility={eligibility}
        title="Subscription Required"
        message="Continue practicing with detailed feedback and analysis."
      /> */}

      {/* Sidebar Toggle */}
      <SidebarToggle
        isVisible={true}
        activeCategory="reading"
        currentQuestionType="67801456e0dfdc154eff11ba"
        onQuestionSelect={onQuestionSelect}
      />
    </div>
  );
};

ReadWritingFillInBlanks.propTypes = {
  question: PropTypes.shape({
    questionId: PropTypes.string,
    maxScore: PropTypes.number,
    prompt: PropTypes.string,
    options: PropTypes.arrayOf(
      PropTypes.shape({
        correctAnswer: PropTypes.string,
        options: PropTypes.array,
      })
    ),
    dropdownOptions: PropTypes.arrayOf(
      PropTypes.shape({
        correctAnswer: PropTypes.string,
        options: PropTypes.array,
      })
    ),
  }).isRequired,
  onQuestionSelect: PropTypes.func.isRequired,
};

export default ReadWritingFillInBlanks;
