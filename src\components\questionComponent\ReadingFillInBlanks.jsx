import { useState, useEffect } from "react";
import { Switch } from "@mui/material";
import {
  DndContext,
  closestCenter,
  MouseSensor,
  TouchSensor,
  DragOverlay,
  useSensor,
  useSensors,
  useDraggable,
  useDroppable,
} from "@dnd-kit/core";
import { CSS } from "@dnd-kit/utilities";
import PropTypes from "prop-types";
// Subscription imports
// import { useSubscriptionCheck } from "../../hooks/useSubscriptionCheck";
// import SubscriptionModal from "../others/SubscriptionModal";
// import SubscriptionIndicator from "../others/SubscriptionIndicator";
import SidebarToggle from "../common/SidebarToggle";
// Practice tracking imports
import {
  logPracticeAttempt,
  usePracticeInfo,
} from "../speech-ace-component/utils/practiceUtils";

const DraggableOption = ({ id, text }) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useDraggable({
      id: id,
    });

  const style = {
    transform: CSS.Transform.toString(transform),
    padding: "10px 16px",
    backgroundColor: "#f0f0f0",
    border: "1px solid #ccc",
    borderRadius: "4px",
    cursor: "grab",
    margin: "4px",
    display: "inline-block",
    touchAction: "none",
    opacity: isDragging ? 0.5 : 1,
    width: "fit-content",
    minWidth: "100px",
    textAlign: "center",
    fontSize: "16px",
    fontWeight: "500",
  };

  return (
    <div ref={setNodeRef} style={style} {...listeners} {...attributes}>
      {text}
    </div>
  );
};

DraggableOption.propTypes = {
  id: PropTypes.string.isRequired,
  text: PropTypes.string.isRequired,
};

const DroppableBlank = ({ id, value, children }) => {
  const { setNodeRef, isOver: droppingOver } = useDroppable({
    id: id,
  });

  return (
    <div
      ref={setNodeRef}
      style={{
        display: "inline-flex",
        alignItems: "center",
        justifyContent: "center",
        minWidth: "120px",
        height: "45px",
        border: `2px ${droppingOver ? "solid" : "dashed"} ${
          droppingOver ? "#140342" : value ? "#140342" : "#ccc"
        }`,
        borderRadius: "8px",
        margin: "0 6px",
        padding: "8px 12px",
        backgroundColor: droppingOver ? "#e8f0fe" : value ? "#f8f9ff" : "white",
        transition: "all 0.3s ease",
        verticalAlign: "middle",
        boxShadow: value
          ? "0 2px 8px rgba(20, 3, 66, 0.15)"
          : droppingOver
          ? "0 2px 8px rgba(20, 3, 66, 0.25)"
          : "0 1px 3px rgba(0,0,0,0.1)",
      }}
    >
      {children}
    </div>
  );
};

DroppableBlank.propTypes = {
  id: PropTypes.string.isRequired,
  value: PropTypes.string,
  children: PropTypes.node,
};

const ReadingFillInBlanks = ({ question, onQuestionSelect }) => {
  // Practice tracking
  const { setPracticeCount, setIsPracticed } = usePracticeInfo(
    question.questionId
  );

  // Create draggable options from all dropdown options
  const createOptions = () => {
    if (!question?.dropdownOptions) return [];

    // Get all unique options from all dropdown sets
    const allOptions = new Set();
    question.dropdownOptions.forEach((dropdown) => {
      dropdown.options.forEach((option) => {
        allOptions.add(option);
      });
    });

    // Convert to array and create option objects
    return Array.from(allOptions).map((text, index) => ({
      id: `option-${index}-${text}`,
      text,
      isUsed: false,
      blankIndex: null,
    }));
  };

  // Parse blanks from prompt - looking for patterns like "(1) ___", "(2) ___"
  const parsePromptForBlanks = () => {
    if (!question?.prompt) return [];

    const blankPattern = /\((\d+)\)\s*___/g;
    const blanks = [];
    let match;

    while ((match = blankPattern.exec(question.prompt)) !== null) {
      const blankNumber = parseInt(match[1]);
      const dropdownOption = question.dropdownOptions?.find(
        (opt) => opt.blank === blankNumber
      );
      if (dropdownOption) {
        blanks.push({
          position: blankNumber,
          correctAnswer: dropdownOption.correctAnswer,
          originalIndex: blanks.length, // For ordering in the UI
        });
      }
    }

    // Sort by position to maintain correct order
    return blanks.sort((a, b) => a.position - b.position);
  };

  const [options, setOptions] = useState([]);
  const [filledAnswers, setFilledAnswers] = useState([]);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [activeId, setActiveId] = useState(null);
  const [showAnswer, setShowAnswer] = useState(false);
  const [timeElapsed, setTimeElapsed] = useState(0); // Time elapsed in seconds
  const [timerActive, setTimerActive] = useState(true);
  const [score, setScore] = useState(0);
  const blanks = parsePromptForBlanks();
  const maxScore = blanks.length || 0;

  // Subscription logic
  // const {
  //   eligibility,
  //   loading: subscriptionLoading,
  //   showSubscriptionModal,
  //   canViewAnswer,
  //   handleSubmitWithCheck,
  //   closeSubscriptionModal,
  //   showSubscriptionRequiredModal,
  // } = useSubscriptionCheck(question.questionId);

  // Reset state when question changes
  useEffect(() => {
    const newOptions = createOptions();
    setOptions(newOptions);
    setFilledAnswers(new Array(blanks.length).fill(null));
    setIsSubmitted(false);
    setShowAnswer(false);
    setScore(0);
    setTimeElapsed(0); // Reset elapsed time to 0
    setTimerActive(true);
    setActiveId(null);
  }, [question?.questionId]);

  // Initialize timer - infinite and counting upward
  useEffect(() => {
    let timer;
    if (timerActive) {
      timer = setInterval(() => {
        setTimeElapsed((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [timerActive]);

  // Format time as MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  useEffect(() => {
    const handleResize = () => setScreenWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const isSmallScreen = screenWidth <= 768;

  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 100,
        tolerance: 5,
      },
    })
  );

  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over) return;

    if (over.id.startsWith("blank-")) {
      const blankIndex = parseInt(over.id.split("-")[1]);

      // If there's already a word in the target blank, swap it back to options
      if (filledAnswers[blankIndex]) {
        setOptions(
          options.map((opt) =>
            opt.id === filledAnswers[blankIndex]
              ? { ...opt, isUsed: false, blankIndex: null }
              : opt
          )
        );
      }

      // Remove word from previous position if it exists
      if (filledAnswers.includes(active.id)) {
        const oldIndex = filledAnswers.indexOf(active.id);
        const newFilledAnswers = [...filledAnswers];
        newFilledAnswers[oldIndex] = null;
        setFilledAnswers(newFilledAnswers);
      }

      // Update options state
      setOptions(
        options.map((opt) =>
          opt.id === active.id ? { ...opt, isUsed: true, blankIndex } : opt
        )
      );

      // Update filled answers
      const newFilledAnswers = [...filledAnswers];
      newFilledAnswers[blankIndex] = active.id;
      setFilledAnswers(newFilledAnswers);
    } else if (over.id === "options-box") {
      // Return word to options box
      const blankIndex = options.find(
        (opt) => opt.id === active.id
      )?.blankIndex;
      if (blankIndex !== null) {
        const newFilledAnswers = [...filledAnswers];
        newFilledAnswers[blankIndex] = null;
        setFilledAnswers(newFilledAnswers);
      }

      setOptions(
        options.map((opt) =>
          opt.id === active.id
            ? { ...opt, isUsed: false, blankIndex: null }
            : opt
        )
      );
    }
  };

  // Calculate score based on correct answers
  const calculateScore = () => {
    let correctCount = 0;
    filledAnswers.forEach((answer, index) => {
      if (answer && blanks[index]) {
        const selectedOption = options.find((opt) => opt.id === answer);
        const correctAnswer = blanks[index].correctAnswer;
        if (
          selectedOption &&
          selectedOption.text.trim().toLowerCase() ===
            correctAnswer.trim().toLowerCase()
        ) {
          correctCount++;
        }
      }
    });
    return correctCount;
  };

  // Fill in all blanks with correct answers
  const fillCorrectAnswers = () => {
    const newOptions = [...options];
    const newFilledAnswers = new Array(blanks.length).fill(null);

    // Reset all options to unused
    newOptions.forEach((opt) => {
      opt.isUsed = false;
      opt.blankIndex = null;
    });

    // For each blank, find the matching option and mark it as used
    blanks.forEach((blank, index) => {
      const correctOption = newOptions.find(
        (opt) =>
          opt.text.trim().toLowerCase() ===
          blank.correctAnswer.trim().toLowerCase()
      );
      if (correctOption) {
        correctOption.isUsed = true;
        correctOption.blankIndex = index;
        newFilledAnswers[index] = correctOption.id;
      }
    });

    setOptions(newOptions);
    setFilledAnswers(newFilledAnswers);
  };

  // Handle show answer with subscription check
  const handleShowAnswerToggle = async (checked) => {
    // if (checked && !canViewAnswer) {
    //   showSubscriptionRequiredModal();
    //   return;
    // }

    setShowAnswer(checked);

    if (checked) {
      fillCorrectAnswers();
    }
  };

  // Render content with blanks - parse the prompt and replace numbered blanks
  const renderContentWithBlanks = () => {
    if (!question?.prompt) return null;

    const parts = [];
    const blankPattern = /\((\d+)\)\s*___/g;
    let lastIndex = 0;
    let match;
    let blankIndex = 0;

    while ((match = blankPattern.exec(question.prompt)) !== null) {
      // Add text before the blank
      if (match.index > lastIndex) {
        parts.push(
          <span key={`text-${parts.length}`}>
            {question.prompt.slice(lastIndex, match.index)}
          </span>
        );
      }

      // Add the droppable blank
      parts.push(
        <DroppableBlank
          key={`blank-${blankIndex}`}
          id={`blank-${blankIndex}`}
          value={filledAnswers[blankIndex]}
        >
          {filledAnswers[blankIndex] && (
            <div
              style={{
                padding: "2px 4px",
                margin: "0",
                backgroundColor: "transparent",
                color:
                  isSubmitted || showAnswer
                    ? (() => {
                        const selectedOption = options.find(
                          (opt) => opt.id === filledAnswers[blankIndex]
                        );
                        const correctAnswer = blanks[blankIndex]?.correctAnswer;
                        return selectedOption &&
                          selectedOption.text.trim().toLowerCase() ===
                            correctAnswer.trim().toLowerCase()
                          ? "#28a745"
                          : "#dc3545";
                      })()
                    : "#140342",
                borderRadius: "0",
                width: "auto",
                maxWidth: "100%",
                textAlign: "center",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
                fontSize: "16px",
                fontWeight: "700",
                fontStyle: "italic",
                display: "inline-flex",
                alignItems: "center",
                justifyContent: "center",
                gap: "4px",
                minHeight: "auto",
                border: "none",
                textDecoration:
                  isSubmitted || showAnswer ? "none" : "underline",
                textDecorationColor: "#140342",
                textDecorationStyle: "solid",
                textUnderlineOffset: "2px",
              }}
            >
              <span>
                {options.find((opt) => opt.id === filledAnswers[blankIndex])
                  ?.text || ""}
              </span>
              {(isSubmitted || showAnswer) && (
                <span
                  style={{
                    fontSize: "16px",
                    fontWeight: "bold",
                  }}
                >
                  {(() => {
                    const selectedOption = options.find(
                      (opt) => opt.id === filledAnswers[blankIndex]
                    );
                    const correctAnswer = blanks[blankIndex]?.correctAnswer;
                    return selectedOption &&
                      selectedOption.text.trim().toLowerCase() ===
                        correctAnswer.trim().toLowerCase()
                      ? "✓"
                      : "✗";
                  })()}
                </span>
              )}
            </div>
          )}
        </DroppableBlank>
      );

      lastIndex = match.index + match[0].length;
      blankIndex++;
    }

    // Add remaining text after the last blank
    if (lastIndex < question.prompt.length) {
      parts.push(
        <span key={`text-${parts.length}`}>
          {question.prompt.slice(lastIndex)}
        </span>
      );
    }

    return parts;
  };

  const { setNodeRef: setOptionsBoxRef } = useDroppable({
    id: "options-box",
  });

  const handleDone = async () => {
    // Check subscription before proceeding
    // const success = await handleSubmitWithCheck(async () => {
    setIsSubmitted(true);
    setTimerActive(false);
    const calculatedScore = calculateScore();
    setScore(calculatedScore);
    // });

    // if (!success) {
    //   // If subscription check failed, ensure timer doesn't stop
    //   setTimerActive(true);
    // }

    // Log practice attempt
    const result = await logPracticeAttempt(question.questionId);
    if (result.success) {
      setPracticeCount(result.practiceCount);
      setIsPracticed(true);
    }
  };

  const handleRedo = () => {
    const newOptions = createOptions();
    setOptions(newOptions);
    setFilledAnswers(new Array(blanks.length).fill(null));
    setIsSubmitted(false);
    setShowAnswer(false);
    setScore(0);
    setTimeElapsed(0); // Reset elapsed time to 0
    setTimerActive(true);
  };

  // Check if all blanks are filled
  const allBlanksFilled = () => {
    return filledAnswers.every((answer) => answer !== null);
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div
        style={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          padding: "20px",
          backgroundColor: "#f9f9f9",
          borderRadius: "8px",
          position: "relative",
        }}
        className="question-container"
      >
        {/* Subscription Indicator */}
        {/* <SubscriptionIndicator
          eligibility={eligibility}
          loading={subscriptionLoading}
          position="top-right"
        /> */}

        {/* Header with timer */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            width: isSmallScreen ? "100%" : "90%",
            marginBottom: "15px",
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              backgroundColor: "#f0f0f0",
              padding: "5px 15px",
              borderRadius: "20px",
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
            }}
          >
            <span
              style={{
                fontSize: "18px",
                fontWeight: "bold",
                color: "#333",
              }}
            >
              Time: {formatTime(timeElapsed)}
            </span>
          </div>
        </div>

        {/* Score display - only shown after submission */}
        {isSubmitted && (
          <div
            style={{
              display: "flex",
              justifyContent: "flex-end",
              width: isSmallScreen ? "100%" : "90%",
              marginBottom: "15px",
            }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                backgroundColor: "#f0f0f0",
                padding: "5px 15px",
                borderRadius: "20px",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
              }}
            >
              <span style={{ fontSize: "16px", fontWeight: "bold" }}>
                Score:{" "}
              </span>
              <span
                style={{
                  fontSize: "18px",
                  fontWeight: "bold",
                  color: score > 0 ? "#008800" : "#333",
                  marginLeft: "5px",
                }}
              >
                {score}/{maxScore}
              </span>
            </div>
          </div>
        )}

        {/* Main content */}
        <div
          style={{
            width: isSmallScreen ? "100%" : "90%",
            backgroundColor: "white",
            padding: "25px",
            borderRadius: "8px",
            boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
            marginBottom: "20px",
            position: "relative",
          }}
        >
          {/* Show Answer indicator */}
          {showAnswer && (
            <div
              style={{
                position: "absolute",
                top: "10px",
                right: "10px",
                backgroundColor: "#e8f5e8",
                color: "#2e7d32",
                padding: "4px 8px",
                borderRadius: "12px",
                fontSize: "12px",
                fontWeight: "600",
                border: "1px solid #4caf50",
              }}
            >
              ✓ Correct Answers
            </div>
          )}

          <p
            style={{
              fontSize: "16px",
              fontWeight: "bold",
              marginBottom: "20px",
            }}
          >
            Instructions: Fill in the blanks by dragging the words from the
            options below.
          </p>

          {/* Content with blanks */}
          <div
            style={{
              marginBottom: "30px",
              lineHeight: "2.5",
              fontSize: "18px",
            }}
          >
            {renderContentWithBlanks()}
          </div>

          {/* Draggable options */}
          <div
            ref={setOptionsBoxRef}
            style={{
              padding: "20px",
              border: "2px solid #140342",
              borderRadius: "8px",
              marginTop: "20px",
              minHeight: "80px",
              backgroundColor: "#f8f8f8",
              display: "flex",
              flexWrap: "wrap",
              gap: "10px",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <p
              style={{
                width: "100%",
                textAlign: "center",
                margin: "0 0 10px 0",
                fontWeight: "bold",
                fontSize: "14px",
                color: "#666",
              }}
            >
              Drag words from here to fill the blanks:
            </p>
            {options
              ?.filter((opt) => !opt.isUsed)
              .map((option) => (
                <DraggableOption
                  key={option.id}
                  id={option.id}
                  text={option.text}
                />
              ))}
          </div>
        </div>

        {/* Controls section */}
        <div
          style={{
            display: "flex",
            flexDirection: isSmallScreen ? "column" : "row",
            justifyContent: "space-between",
            alignItems: isSmallScreen ? "stretch" : "center",
            width: isSmallScreen ? "100%" : "90%",
            gap: "15px",
          }}
        >
          <div
            style={{
              display: "flex",
              gap: "15px",
              flexWrap: isSmallScreen ? "wrap" : "nowrap",
              justifyContent: isSmallScreen ? "center" : "flex-start",
            }}
          >
            <button
              style={{
                backgroundColor: isSubmitted ? "#d0d0d0" : "#D9D9D9",
                borderWidth: 1,
                borderColor: "#140342",
                borderStyle: "solid",
                padding: "10px 20px",
                borderRadius: "5px",
                cursor:
                  isSubmitted || !allBlanksFilled() ? "default" : "pointer",
                opacity: isSubmitted || !allBlanksFilled() ? 0.7 : 1,
              }}
              onClick={handleDone}
              disabled={isSubmitted || !allBlanksFilled()}
            >
              <p
                style={{
                  margin: 0,
                  color: "black",
                  fontSize: 14,
                  fontWeight: "500",
                }}
              >
                Submit
              </p>
            </button>

            <button
              style={{
                backgroundColor: "#D9D9D9",
                borderWidth: 1,
                borderColor: "#140342",
                borderStyle: "solid",
                padding: "10px 20px",
                borderRadius: "5px",
                cursor: "pointer",
              }}
              onClick={handleRedo}
            >
              <p
                style={{
                  margin: 0,
                  color: "black",
                  fontSize: 14,
                  fontWeight: "500",
                }}
              >
                Re-do
              </p>
            </button>

            <div
              style={{
                display: "flex",
                alignItems: "center",
                backgroundColor: "#f9f9f9",
                padding: "5px 10px",
                borderRadius: "5px",
                border: "1px solid #ddd",
              }}
            >
              <p
                style={{
                  margin: 0,
                  color: "black",
                  fontSize: 14,
                  fontWeight: "500",
                  marginRight: "5px",
                }}
              >
                {showAnswer ? "Hide Answer" : "Show Answer"}
              </p>
              <Switch
                onChange={(e, checked) => handleShowAnswerToggle(checked)}
                checked={showAnswer}
                size="small"
              />
            </div>
          </div>

          {/* Feedback message after submission */}
          {isSubmitted && !showAnswer && (
            <div
              style={{
                padding: "10px 15px",
                borderRadius: "5px",
                backgroundColor:
                  score === maxScore
                    ? "#e6ffe6"
                    : score > 0
                    ? "#fff5e6"
                    : "#ffe6e6",
                border: `1px solid ${
                  score === maxScore
                    ? "#008800"
                    : score > 0
                    ? "#ff9500"
                    : "#cc0000"
                }`,
                textAlign: "center",
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                minWidth: isSmallScreen ? "auto" : "250px",
              }}
            >
              <div>
                <p
                  style={{
                    margin: 0,
                    fontWeight: "bold",
                    color:
                      score === maxScore
                        ? "#008800"
                        : score > 0
                        ? "#ff9500"
                        : "#cc0000",
                  }}
                >
                  {score === maxScore
                    ? `Correct! You earned ${score} point${
                        score !== 1 ? "s" : ""
                      }.`
                    : score > 0
                    ? `Partially correct! You earned ${score} out of ${maxScore} points.`
                    : "Incorrect - Try again"}
                </p>
              </div>

              {score > 0 && (
                <div
                  style={{
                    backgroundColor: score === maxScore ? "#4CAF50" : "#FF9500",
                    color: "white",
                    padding: "5px 12px",
                    borderRadius: "20px",
                    fontSize: "16px",
                    fontWeight: "bold",
                    marginLeft: "10px",
                  }}
                >
                  +{score}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      <DragOverlay>
        {activeId ? (
          <div
            style={{
              padding: "8px 16px",
              backgroundColor: "#f0f0f0",
              border: "1px solid #ccc",
              borderRadius: "4px",
              cursor: "grab",
              width: "fit-content",
              minWidth: "100px",
              textAlign: "center",
              fontSize: "16px",
            }}
          >
            {options.find((opt) => opt.id === activeId)?.text || activeId}
          </div>
        ) : null}
      </DragOverlay>

      {/* Subscription Modal */}
      {/* <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={closeSubscriptionModal}
        eligibility={eligibility}
        title="Subscription Required"
        message="Continue practicing with detailed feedback and analysis."
      /> */}

      {/* Sidebar Toggle */}
      <SidebarToggle
        isVisible={true}
        activeCategory="reading"
        currentQuestionType="67894ed2102a6d6548ceec90"
        onQuestionSelect={onQuestionSelect}
      />
    </DndContext>
  );
};

ReadingFillInBlanks.propTypes = {
  question: PropTypes.shape({
    questionId: PropTypes.string.isRequired,
    questionNumber: PropTypes.string,
    prompt: PropTypes.string.isRequired,
    section: PropTypes.string,
    difficulty: PropTypes.string,
    maxScore: PropTypes.number,
    duration: PropTypes.number,
    dropdownOptions: PropTypes.arrayOf(
      PropTypes.shape({
        blank: PropTypes.number.isRequired,
        correctAnswer: PropTypes.string.isRequired,
        options: PropTypes.arrayOf(PropTypes.string).isRequired,
      })
    ).isRequired,
    categoryId: PropTypes.string,
    mocktestId: PropTypes.string,
    type: PropTypes.string,
    questionName: PropTypes.string,
  }).isRequired,
  onQuestionSelect: PropTypes.func.isRequired,
};

export default ReadingFillInBlanks;
