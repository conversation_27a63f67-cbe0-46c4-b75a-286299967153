import React, { useState, useEffect, useRef } from "react";
import AudioPlayer from "react-audio-player";
import "react-h5-audio-player/lib/styles.css";
import { toast } from "react-toastify";
import { server } from "@/api/services/server";
import { postRequest, uploadFile } from "@/api/services/controller";
import ShortAnswerScoring from "../speech-ace-component/AnswerScoringDialog";
import TranslationDialog from "../others/TranslationDialog";
import ShortAnswerHistoryButton from "../speech-ace-component/ShortAnswerHistoryDialog";
import { useAuth } from "../others/AuthContext";
import beepSound from "../../../public/assets/beep.mp3";
import {
  logPracticeAttempt,
  usePracticeInfo,
} from "../speech-ace-component/utils/practiceUtils";
// Subscription imports
import { useSubscriptionCheck } from "../../hooks/useSubscriptionCheck";
import SubscriptionModal from "../others/SubscriptionModal";
import SubscriptionIndicator from "../others/SubscriptionIndicator";
import SidebarToggle from "../common/SidebarToggle";
import PropTypes from "prop-types";

const AnswerShort = ({ question, onQuestionSelect }) => {
  const { prompt, options, duration } = question;
  const { user } = useAuth();

  console.log("user", user);

  const [loading, setLoading] = useState(false);
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [isRecording, setIsRecording] = useState(false);
  const [recordedBlob, setRecordedBlob] = useState(null);
  const [mediaRecorder, setMediaRecorder] = useState(null);
  const [audioUrl, setAudioUrl] = useState("");
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [speechAnalysis, setSpeechAnalysis] = useState(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isBlinking, setIsBlinking] = useState(false);
  const [recordingEnded, setRecordingEnded] = useState(false);
  const [hasRecorded, setHasRecorded] = useState(false);
  const [audioStarted, setAudioStarted] = useState(false);
  const [audioCompleted, setAudioCompleted] = useState(false);
  const [isTranslationDialogOpen, setIsTranslationDialogOpen] = useState(false);
  const [showAnswer, setShowAnswer] = useState(false);
  const { setPracticeCount, setIsPracticed } = usePracticeInfo(
    question.questionId
  );

  // Subscription logic
  const {
    eligibility,
    loading: subscriptionLoading,
    showSubscriptionModal,
    canViewAnswer,
    handleSubmitWithCheck,
    closeSubscriptionModal,
    showSubscriptionRequiredModal,
  } = useSubscriptionCheck(question.questionId);

  const toggleTranslationDialog = () => {
    setIsTranslationDialogOpen(!isTranslationDialogOpen);
  };

  // Timer states
  const [phase, setPhase] = useState("listening"); // Start with listening phase
  const [prepTime] = useState(3);
  const [speakTime] = useState(10);
  const [timer, setTimer] = useState(0); // Initial timer at 0
  const [isTimeUp, setIsTimeUp] = useState(false);

  // Ref for the audio player
  const audioPlayerRef = useRef(null);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  };

  // Initialize beep sounds
  useEffect(() => {
    const startBeep = new Audio(beepSound);
    const stopBeep = new Audio(beepSound);

    startBeep.load();
    stopBeep.load();

    window.startBeep = startBeep;
    window.stopBeep = stopBeep;

    return () => {
      window.startBeep = null;
      window.stopBeep = null;
    };
  }, []);

  function blobToBase64(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onloadend = () => resolve(reader?.result?.split(",")[1]);
      reader.onerror = (error) => reject(error);
    });
  }

  // Handle phase changes and timers
  useEffect(() => {
    let interval;

    if (phase === "listening") {
      // In listening phase, we wait for audio to finish
      // Timer is not used in this phase
    } else if (phase === "preparation" && timer > 0) {
      interval = setInterval(() => setTimer((prev) => prev - 1), 1000);
    } else if (phase === "preparation" && timer === 0) {
      setPhase("speaking");
      setTimer(speakTime);
      startRecording();
    } else if (phase === "speaking" && timer > 0 && isRecording) {
      interval = setInterval(() => setTimer((prev) => prev - 1), 1000);
    } else if ((phase === "speaking" && timer === 0) || !isRecording) {
      stopRecording();
      setIsTimeUp(true);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [timer, phase, isRecording, speakTime]);

  // Handle audio player events
  const handleAudioStart = () => {
    setAudioStarted(true);
    setPhase("listening");
  };

  const handleAudioEnd = () => {
    setAudioCompleted(true);
    setPhase("preparation");
    setTimer(prepTime);
  };

  useEffect(() => {
    const initializeMediaRecorder = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            channelCount: 1,
            sampleRate: 16000,
            echoCancellation: true,
            noiseSuppression: true,
          },
        });

        const audioContext = new AudioContext({ sampleRate: 16000 });
        const sourceNode = audioContext.createMediaStreamSource(stream);
        const processorNode = audioContext.createScriptProcessor(4096, 1, 1);

        let audioChunks = [];
        let isRecordingAudio = false;

        processorNode.onaudioprocess = (e) => {
          if (isRecordingAudio) {
            const audioData = e.inputBuffer.getChannelData(0);
            audioChunks.push(new Float32Array(audioData));
          }
        };

        sourceNode.connect(processorNode);
        processorNode.connect(audioContext.destination);

        const recorder = {
          start: () => {
            audioChunks = [];
            isRecordingAudio = true;
            if (window.startBeep) window.startBeep.play();
          },
          stop: () => {
            isRecordingAudio = false;
            if (window.stopBeep) window.stopBeep.play();

            const wavBlob = createWAVBlob(audioChunks, audioContext.sampleRate);
            setRecordedBlob(wavBlob);
            const url = URL.createObjectURL(wavBlob);
            setAudioUrl(url);
            setRecordingEnded(true);
          },
          state: "inactive",
        };

        setMediaRecorder(recorder);
      } catch (error) {
        console.error("Error initializing recorder:", error);
        toast.error(
          "Error accessing microphone. Please check your permissions."
        );
      }
    };

    initializeMediaRecorder();
  }, []);

  function createWAVBlob(audioChunks, sampleRate) {
    const numChannels = 1;
    const bitsPerSample = 16;
    const totalLength = audioChunks.reduce(
      (acc, chunk) => acc + chunk.length,
      0
    );
    const audioData = new Float32Array(totalLength);
    let offset = 0;

    for (const chunk of audioChunks) {
      audioData.set(chunk, offset);
      offset += chunk.length;
    }

    const wavBuffer = new ArrayBuffer(44 + audioData.length * 2);
    const view = new DataView(wavBuffer);

    const writeString = (view, offset, string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(view, 0, "RIFF");
    view.setUint32(4, 36 + audioData.length * 2, true);
    writeString(view, 8, "WAVE");
    writeString(view, 12, "fmt ");
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * 2, true);
    view.setUint16(32, numChannels * 2, true);
    view.setUint16(34, bitsPerSample, true);
    writeString(view, 36, "data");
    view.setUint32(40, audioData.length * 2, true);

    offset = 44;
    for (let i = 0; i < audioData.length; i++) {
      const sample = Math.max(-1, Math.min(1, audioData[i]));
      view.setInt16(
        offset,
        sample < 0 ? sample * 0x8000 : sample * 0x7fff,
        true
      );
      offset += 2;
    }

    return new Blob([wavBuffer], { type: "audio/wav" });
  }

  const startRecording = () => {
    if (mediaRecorder && !isRecording) {
      try {
        if (audioUrl) {
          URL.revokeObjectURL(audioUrl);
          setAudioUrl("");
        }

        // Reset recording state if starting fresh
        if (hasRecorded) {
          setRecordedBlob(null);
          setHasRecorded(false);
          setRecordingEnded(false);
        }

        mediaRecorder.start();
        setIsRecording(true);
        setIsBlinking(true);
        setRecordingEnded(false);
      } catch (error) {
        console.error("Error starting recording:", error);
        toast.error("Failed to start recording. Please try again.");
      }
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && isRecording) {
      mediaRecorder.stop();
      setIsRecording(false);
      setIsBlinking(false);
      setTimer(0);
      setHasRecorded(true);
    }
  };

  const handleRedo = () => {
    if (mediaRecorder && isRecording) {
      mediaRecorder.stop();
    }

    setPhase("listening");
    setTimer(0);
    setIsRecording(false);
    setRecordedBlob(null);
    setAudioUrl("");
    setSpeechAnalysis(null);
    setIsDialogOpen(false);
    setRecordingEnded(false);
    setIsTimeUp(false);
    setHasRecorded(false);
    setIsBlinking(false);
    setAudioStarted(false);
    setAudioCompleted(false);

    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }

    // Auto-start the audio again
    if (audioPlayerRef.current) {
      const audioElement = audioPlayerRef.current.audioEl.current;
      if (audioElement) {
        audioElement.play();
      }
    }

    toast.info("Ready for new recording. Audio will play now.");
  };

  const handleDone = async () => {
    if (!recordedBlob) {
      toast.error("No recording available. Please record audio first.");
      return;
    }

    // Check subscription before proceeding
    const success = await handleSubmitWithCheck(async () => {
      try {
        setLoading(true);
        const result = await logPracticeAttempt(question.questionId);
        if (result.success) {
          setPracticeCount(result.practiceCount);
          setIsPracticed(true);
        } else {
          toast.error(result.error || "Failed to log practice attempt.");
        }

        const formData = new FormData();
        formData.append("audio", recordedBlob, "recording.wav");

        // Get and clean the context keywords
        const keywords = question?.additionalProp1?.keywords;
        if (!keywords) {
          throw new Error("Keywords not found for this question");
        }

        const cleanedKeywords = keywords
          ?.split(",")
          ?.map((keyword) => keyword.trim())
          ?.join(",");

        console.log("Sending keywords for content analysis:", cleanedKeywords);
        formData.append("context", cleanedKeywords); // Note: Use "context" not "keywords" as per backend API

        // Use the answer-question endpoint
        const speechResponse = await fetch(
          "https://deep-ai.up.railway.app/api/pte/answer-question",
          {
            method: "POST",
            body: formData,
          }
        );

        if (!speechResponse.ok) {
          throw new Error(`Speech analysis failed: ${speechResponse.status}`);
        }

        const speechResults = await speechResponse.json();
        console.log("Speech Results:", speechResults);

        // Extract scores
        const contentData = speechResults?.content || {};
        console.log("Content Data:", contentData);

        // Upload the audio file
        const fileUrl = await blobToBase64(recordedBlob);
        const uploadedUrl = await uploadFile("", fileUrl, "audio/wav");

        // Extract relevant scores
        // Analyze content - we want full score (90) if ANY keyword matches
        const matchedKeywords =
          contentData.keywords?.filter((keyword) =>
            contentData.transcript?.toLowerCase().includes(keyword)
          ) || [];

        const anyKeywordMatched = matchedKeywords.length > 0;

        // Create the matchedKeywords and unmatchedKeywords arrays
        const unmatchedKeywords =
          contentData.keywords?.filter(
            (keyword) => !matchedKeywords.includes(keyword)
          ) || [];

        // Send to backend
        const uri = server.uri + "answers";
        const response = await postRequest(uri, {
          media: {
            url: uploadedUrl,
            type: "audio/mpeg",
          },
          questionId: question.questionId,
          speechAnalysis: {
            matchedKeywords: matchedKeywords || [],
            unmatchedKeywords: unmatchedKeywords || [],
            transcript: contentData.transcript || "",
            content: contentData || {},
            createdAt: new Date(),
          },
          userId: user?.id,
        });

        if (response) {
          // Add the matched/unmatched keywords to the speech results for our dialog
          speechResults.content = {
            ...contentData,
            matchedKeywords: matchedKeywords,
            unmatchedKeywords: unmatchedKeywords,
            anyMatch: anyKeywordMatched,
          };

          // Store all the analysis data for our dialog
          setSpeechAnalysis(speechResults);
          setIsDialogOpen(true);
          toast.success("Answer uploaded and analyzed successfully");
        }
      } catch (error) {
        console.error("Error:", error);
        toast.error(error.message || "Something went wrong");
      } finally {
        setLoading(false);
      }
    });

    if (!success) {
      setLoading(false);
    }
  };

  // Handle show answer with subscription check
  const handleShowAnswer = () => {
    if (!canViewAnswer) {
      showSubscriptionRequiredModal();
      return;
    }
    setShowAnswer(!showAnswer);
  };

  // Reset component when question changes
  useEffect(() => {
    setLoading(false);
    setIsRecording(false);
    setRecordedBlob(null);
    setAudioUrl("");
    setActiveDropdown(null);
    setSpeechAnalysis(null);
    setIsDialogOpen(false);
    setIsBlinking(false);
    setRecordingEnded(false);
    setHasRecorded(false);
    setPhase("listening");
    setTimer(0);
    setIsTimeUp(false);
    setAudioStarted(false);
    setAudioCompleted(false);
    setShowAnswer(false);

    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
  }, [question]);

  const LoadingSpinner = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500"></div>
    </div>
  );

  const isSmallScreen = screenWidth <= 768;

  const styles = `
    @keyframes blink {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }
    
    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 50px;
      height: 24px;
    }
    
    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 24px;
    }
    
    .slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    
    input:checked + .slider {
      background-color: #8055f6;
    }
    
    input:checked + .slider:before {
      transform: translateX(26px);
    }
    
    .button-container {
      display: flex;
      gap: 12px;
      align-items: center;
    }
    
    .action-button {
      padding: 10px 20px;
      border-radius: 5px;
      font-size: 14px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .submit-button {
      background-color: #D9D9D9;
      border: none;
      color: #333;
    }
    
    .redo-button {
      background-color: #D9D9D9;
      border: 1px solid #140342;
      color: #140342;
    }
    
    .translation-button {
      background-color: #D9D9D9;
      border: 1px solid #140342;
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 10px;
    }
    
    .answer-toggle-container {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .answer-text {
      color: #333;
      font-size: 14px;
    }
  `;

  const recordingButtonStyle = {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    width: "60px",
    height: "60px",
    borderRadius: "50%",
    backgroundColor: isRecording ? "#8055f6" : "lightgray",
    cursor: "pointer",
    animation: isBlinking ? "blink 1s infinite" : "none",
  };

  // Get keywords from question
  const keywords = question?.additionalProp1?.keywords || "";

  // Add CSS animations for modern UI
  useEffect(() => {
    const style = document.createElement("style");
    style.textContent = `
      @keyframes recordingPulse {
        0% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.8; transform: scale(1.05); }
        100% { opacity: 1; transform: scale(1); }
      }
      
      @keyframes buttonGlow {
        0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
        100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
      }
      
      @keyframes badgeBounce {
        0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
        40%, 43% { transform: translate3d(0, -8px, 0); }
        70% { transform: translate3d(0, -4px, 0); }
        90% { transform: translate3d(0, -2px, 0); }
      }
    `;
    document.head.appendChild(style);

    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, []);

  return (
    <>
      <style>{styles}</style>

      {isTranslationDialogOpen && (
        <TranslationDialog
          isOpen={isTranslationDialogOpen}
          onClose={toggleTranslationDialog}
          transcript={prompt}
          questionName={question?.questionName}
          questionNumber={question?.questionNumber}
        />
      )}

      <div
        style={{
          width: "90%",
          display: "flex",
          justifyContent: "space-between",
          marginBottom: "20px",
          marginLeft: "40px",
          marginTop: "40px",
        }}
      >
        <p
          style={{
            color: timer <= 5 && timer > 0 ? "#ff0000" : "#000000",
            fontWeight: "600",
            fontSize: "20px",
            lineHeight: 1.5,
            textAlign: "justify",
          }}
        >
          {phase === "listening"
            ? "Listening..."
            : phase === "preparation"
            ? "Prepare Time: "
            : "Test Time: "}
          {phase !== "listening" ? formatTime(timer) : ""}
        </p>
      </div>

      <div
        style={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          padding: "20px",
          backgroundColor: "#f9f9f9",
          borderRadius: "10px",
          position: "relative",
        }}
        className="question-container"
      >
        {/* Subscription Indicator */}
        <SubscriptionIndicator
          eligibility={eligibility}
          loading={subscriptionLoading}
          position="top-right"
        />

        <div
          style={{
            display: "flex",
            flexDirection: "column",
            width: isSmallScreen ? "100%" : "90%",
            marginBottom: "40px",
            flexWrap: "wrap",
          }}
        >
          {/* Audio Player */}
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              width: isSmallScreen ? "100vw" : "100%",
              marginTop: 10,
              marginBottom: 10,
            }}
          >
            <AudioPlayer
              ref={audioPlayerRef}
              src={question?.media?.url}
              controls
              autoPlay={true}
              onPlay={handleAudioStart}
              onEnded={handleAudioEnd}
            />

            {/* Recording Section */}
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                width: isSmallScreen ? "100%" : "90%",
                marginTop: "40px",
                marginBottom: "40px",
              }}
            >
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  backgroundColor: "#D3D3D3",
                  padding: "30px 5px",
                }}
              >
                <div
                  style={{
                    marginTop: "20px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    flexDirection: "column",
                    gap: 10,
                  }}
                >
                  {isTimeUp ? (
                    <p
                      style={{
                        color: "#000000",
                        fontSize: 20,
                        fontWeight: "500",
                      }}
                    >
                      Time&apos;s up! Submit to check score
                    </p>
                  ) : phase === "listening" ? (
                    <p
                      style={{
                        color: "#000000",
                        fontSize: 20,
                        fontWeight: "500",
                      }}
                    >
                      Listening to Audio...
                    </p>
                  ) : (
                    <p
                      style={{
                        color: "#000000",
                        fontSize: 20,
                        fontWeight: "500",
                      }}
                    >
                      {isRecording
                        ? "Recording in Progress"
                        : phase === "preparation"
                        ? "Preparing..."
                        : "Click To Start"}
                    </p>
                  )}

                  {/* Modern Skip Indicator */}
                  {(phase === "listening" || phase === "preparation") &&
                    !isRecording && (
                      <div
                        style={{
                          position: "relative",
                          marginBottom: "10px",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          gap: "8px",
                        }}
                      >
                        <div
                          style={{
                            backgroundColor: "#4CAF50",
                            color: "white",
                            padding: "6px 12px",
                            borderRadius: "20px",
                            fontSize: "12px",
                            fontWeight: "600",
                            animation: "recordingPulse 2s infinite",
                            boxShadow: "0 2px 8px rgba(76, 175, 80, 0.3)",
                          }}
                        >
                          💡 Skip & Start Recording
                        </div>
                      </div>
                    )}

                  {/* Recording Button */}
                  <div
                    style={{
                      ...recordingButtonStyle,
                      backgroundColor:
                        phase === "listening"
                          ? "#4CAF50"
                          : isRecording
                          ? "#8055f6"
                          : phase === "preparation"
                          ? "#FF9800"
                          : "lightgray",
                      cursor: "pointer",
                      position: "relative",
                      animation:
                        (phase === "listening" || phase === "preparation") &&
                        !isRecording
                          ? "buttonGlow 2s infinite"
                          : "none",
                      boxShadow:
                        (phase === "listening" || phase === "preparation") &&
                        !isRecording
                          ? "0 0 0 0 rgba(76, 175, 80, 0.7)"
                          : "none",
                    }}
                    onClick={
                      isRecording
                        ? stopRecording
                        : () => {
                            // Stop audio if playing during listening phase
                            if (
                              phase === "listening" &&
                              audioPlayerRef.current
                            ) {
                              try {
                                // Try different methods to pause the audio
                                if (audioPlayerRef.current.audioEl?.current) {
                                  audioPlayerRef.current.audioEl.current.pause();
                                } else if (
                                  audioPlayerRef.current.audio?.current
                                ) {
                                  audioPlayerRef.current.audio.current.pause();
                                } else if (audioPlayerRef.current.pause) {
                                  audioPlayerRef.current.pause();
                                } else {
                                  // Fallback: find audio element in DOM
                                  const audioElements =
                                    document.querySelectorAll("audio");
                                  audioElements.forEach((audio) => {
                                    if (!audio.paused) {
                                      audio.pause();
                                    }
                                  });
                                }
                                toast.info("Audio paused - Recording started");
                              } catch (error) {
                                console.log("Could not pause audio:", error);
                                // Continue with recording anyway
                              }
                            }

                            // Allow direct recording during any phase
                            if (
                              phase === "listening" ||
                              phase === "preparation"
                            ) {
                              setPhase("speaking");
                              setTimer(speakTime);
                              toast.success("🎤 Recording started early!");
                            }
                            startRecording();
                          }
                    }
                    title={
                      phase === "listening"
                        ? "Click to skip listening and start recording"
                        : phase === "preparation"
                        ? "Click to skip preparation and start recording"
                        : isRecording
                        ? "Click to stop recording"
                        : "Click to start recording"
                    }
                  >
                    <img
                      src="/assets/img/microphone.png"
                      alt={isRecording ? "Stop Recording" : "Start Recording"}
                      style={{
                        width: "30px",
                        height: "30px",
                      }}
                    />

                    {/* Skip Badge */}
                    {(phase === "listening" || phase === "preparation") &&
                      !isRecording && (
                        <div
                          style={{
                            position: "absolute",
                            top: "-8px",
                            right: "-8px",
                            backgroundColor: "#FF4444",
                            color: "white",
                            borderRadius: "50%",
                            width: "20px",
                            height: "20px",
                            fontSize: "12px",
                            fontWeight: "bold",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            animation: "badgeBounce 1s infinite",
                          }}
                        >
                          !
                        </div>
                      )}
                  </div>
                </div>

                {/* Info Section */}
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    marginTop: 20,
                    gap: 5,
                  }}
                >
                  <div
                    style={{
                      backgroundColor: "#9780ff",
                      borderRadius: "50%",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      width: "30px",
                      height: "30px",
                    }}
                  >
                    <p
                      style={{
                        color: "#000000",
                        fontSize: 18,
                        fontWeight: "600",
                      }}
                    >
                      i
                    </p>
                  </div>
                  <p
                    style={{
                      color: "#000000",
                      fontSize: 15,
                      fontWeight: "600",
                    }}
                  >
                    Use a headset with inline microphone to get accurate AI
                    scores
                  </p>
                </div>
              </div>

              {/* Recorded Audio Player */}
              {(recordingEnded || recordedBlob) && (
                <div style={{ marginTop: 20 }}>
                  <div style={{ display: "flex", flexDirection: "row" }}>
                    <AudioPlayer src={audioUrl} controls />
                  </div>
                  <p
                    style={{
                      color: "#000000",
                      fontSize: 15,
                      fontWeight: "600",
                    }}
                  >
                    AI Scoring and Audio Answer Download is available after
                    submission.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div
          className="my-7"
          style={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            width: isSmallScreen ? "100%" : "90%",
            marginTop: "20px",
          }}
        >
          <div className="button-container">
            {/* Submit Button */}
            <button
              style={{
                backgroundColor: loading ? "#A9A9A9" : "#D9D9D9",
                padding: "10px 20px",
                borderRadius: "5px",
                border: "none",
                cursor: loading || !hasRecorded ? "not-allowed" : "pointer",
                fontSize: "14px",
                color: "#333",
                textAlign: "center",
                display: "flex",
                alignItems: "center",
                gap: "8px",
                opacity: loading || !hasRecorded ? 0.7 : 1,
              }}
              onClick={handleDone}
              disabled={loading || !hasRecorded}
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-500"></div>
                  Processing...
                </>
              ) : (
                "Submit"
              )}
            </button>

            {/* Redo Button */}
            <button
              style={{
                backgroundColor: "#D9D9D9",
                padding: "10px 20px",
                borderRadius: "5px",
                border: "1px solid #140342",
                cursor: hasRecorded ? "pointer" : "not-allowed",
                fontSize: "14px",
                color: "#140342",
                textAlign: "center",
                opacity: hasRecorded ? 1 : 0.5,
              }}
              onClick={handleRedo}
              disabled={!hasRecorded}
            >
              Re-do
            </button>
            {/* History Button - keeping this since it was in the original */}
            <ShortAnswerHistoryButton questionId={question.questionId} />

            {/* Translation Button */}
            <button
              style={{
                backgroundColor: "#D9D9D9",
                padding: "10px 20px",
                borderRadius: "5px",
                border: "1px solid #140342",
                cursor: "pointer",
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                gap: "10px",
              }}
              onClick={toggleTranslationDialog}
            >
              <img
                src="/assets/img/translate.png"
                alt="translate"
                style={{ width: "20px", height: "20px" }}
              />
              <p style={{ color: "#522CFF", fontSize: "12px" }}>Translation</p>
            </button>

            {/* Answer Toggle Button */}
            <div className="answer-toggle-container">
              <p className="answer-text">Answer</p>
              <label className="toggle-switch">
                <input
                  type="checkbox"
                  checked={showAnswer}
                  onChange={handleShowAnswer}
                />
                <span className="slider"></span>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Speech Analysis Dialog */}
      {speechAnalysis && (
        <ShortAnswerScoring
          analysisData={speechAnalysis}
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          audioUrl={audioUrl}
        />
      )}

      {/* Loading Spinner */}
      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      )}
      {/* Answer Display */}
      {showAnswer && (
        <div
          style={{
            marginTop: "20px",
            padding: "15px",
            backgroundColor: "#f0f0f0",
            borderRadius: "5px",
            border: "1px solid #ddd",
            width: isSmallScreen ? "100%" : "90%",
          }}
        >
          <div style={{ marginBottom: "15px" }}>
            <h4
              style={{
                margin: "0 0 10px 0",
                fontSize: "16px",
                color: "#140342",
                fontWeight: "600",
              }}
            >
              Question:
            </h4>
            <div
              style={{
                padding: "10px",
                backgroundColor: "#f8f8f8",
                borderRadius: "4px",
              }}
            >
              {prompt || "No question available"}
            </div>
          </div>

          <div>
            <h4
              style={{
                margin: "0 0 10px 0",
                fontSize: "16px",
                color: "#140342",
                fontWeight: "600",
              }}
            >
              Expected Answers:
            </h4>
            <div
              style={{
                padding: "10px",
                backgroundColor: "#f8f8f8",
                borderRadius: "4px",
              }}
            >
              {keywords ? (
                <ul style={{ margin: 0, paddingLeft: "20px" }}>
                  {keywords.split(",").map((keyword, index) => (
                    <li key={index} style={{ marginBottom: "5px" }}>
                      {keyword.trim()}
                    </li>
                  ))}
                </ul>
              ) : (
                "No expected answers available"
              )}
            </div>
          </div>
        </div>
      )}

      {/* Subscription Modal */}
      <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={closeSubscriptionModal}
        eligibility={eligibility}
        title="Subscription Required"
        message="Continue practicing with detailed feedback and analysis."
      />

      {/* Sidebar Toggle */}
      <SidebarToggle
        isVisible={true}
        activeCategory="speaking"
        currentQuestionType="6783923483cd4009d9cddafa"
        onQuestionSelect={onQuestionSelect}
      />
    </>
  );
};

AnswerShort.propTypes = {
  question: PropTypes.shape({
    prompt: PropTypes.string,
    options: PropTypes.array,
    duration: PropTypes.number,
    media: PropTypes.shape({
      url: PropTypes.string,
    }),
    questionId: PropTypes.string,
    questionName: PropTypes.string,
    questionNumber: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    additionalProp1: PropTypes.shape({
      keywords: PropTypes.string,
    }),
  }).isRequired,
  onQuestionSelect: PropTypes.func.isRequired,
};

export default AnswerShort;
