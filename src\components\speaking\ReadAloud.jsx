import { useState, useEffect, useRef } from "react";
import AudioPlayer from "react-audio-player";
import "react-h5-audio-player/lib/styles.css";
import { postRequest, uploadFile } from "@/api/services/controller";
import { server } from "@/api/services/server";
import { toast } from "react-toastify";

import SpeechAnalysisDialog from "../speech-ace-component/SpeechAnalysisResults";
import LoadingOverlay from "../speech-ace-component/LoadingOverlay";
import beepSound from "../../../public/assets/beep.mp3";
import TranslationDialog from "../others/TranslationDialog";
import ClickableText from "../others/ClickableText";
import ImprovedShadowing from "../others/EnhancedShadowing";
import { useAuth } from "../others/AuthContext";
import AnswersHistoryButton from "../speech-ace-component/AnswersHistoryButton";
import {
  logPracticeAttempt,
  usePracticeInfo,
} from "../speech-ace-component/utils/practiceUtils";
import { useSubscriptionCheck } from "../../hooks/useSubscriptionCheck";
import SubscriptionModal from "../others/SubscriptionModal";
import SubscriptionIndicator from "../others/SubscriptionIndicator";
import SidebarToggle from "../common/SidebarToggle";
import PropTypes from "prop-types";

const ReadAloud = ({ question, onQuestionSelect }) => {
  const { prompt, duration } = question;
  const [loading, setLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [timer, setTimer] = useState(duration);
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [isRecording, setisRecording] = useState(false);
  const [recordedBlob, setRecordedBlob] = useState(null);
  const [mediaRecorder, setMediaRecorder] = useState(null);
  const [audioUrl, setAudioUrl] = useState("");
  const [speechAnalysis, setSpeechAnalysis] = useState(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isBlinking, setIsBlinking] = useState(false);
  const [recordingEnded, setRecordingEnded] = useState(false);
  const [isTimeUp, setIsTimeUp] = useState(false);
  const [hasRecorded, setHasRecorded] = useState(false);
  const [isTranslationDialogOpen, setIsTranslationDialogOpen] = useState(false);
  const { user } = useAuth();

  // Update screen width on resize for responsive design
  useEffect(() => {
    const handleResize = () => setScreenWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // New states for shadowing feature
  const [isShadowingMode, setIsShadowingMode] = useState(false);
  const [playbackRate, setPlaybackRate] = useState(1.0);
  const [volume, setVolume] = useState(1.0);
  const audioRef = useRef(null);

  const [phase, setPhase] = useState("preparation");
  const [prepTime] = useState(question?.additionalProp1?.prepTime || 35);
  const [speakTime] = useState(question?.additionalProp1?.answerTime || 40);

  // Use the custom hook for practice info
  const {
    practiceCount,
    setPracticeCount,
    isPracticed,
    setIsPracticed,
    error,
  } = usePracticeInfo(question.questionId);

  // Subscription logic
  const {
    eligibility,
    loading: subscriptionLoading,
    showSubscriptionModal,
    canViewAnswer,
    handleSubmitWithCheck,
    closeSubscriptionModal,
    showSubscriptionRequiredModal,
  } = useSubscriptionCheck(question.questionId);

  const toggleTranslationDialog = () => {
    setIsTranslationDialogOpen(!isTranslationDialogOpen);
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  };

  useEffect(() => {
    const startBeep = new Audio(beepSound);
    const stopBeep = new Audio(beepSound);

    startBeep.load();
    stopBeep.load();

    window.startBeep = startBeep;
    window.stopBeep = stopBeep;

    return () => {
      window.startBeep = null;
      window.stopBeep = null;
    };
  }, []);

  const handleDone = async () => {
    if (!recordedBlob) {
      toast.error("No recording available. Please record audio first.");
      return;
    }

    // Check subscription before proceeding
    const success = await handleSubmitWithCheck(async () => {
      try {
        setLoading(true);

        // Log practice attempt using the utility function
        const result = await logPracticeAttempt(question.questionId);
        if (result.success) {
          setPracticeCount(result.practiceCount);
          setIsPracticed(true);
        } else {
          toast.error(result.error || "Failed to log practice attempt.");
        }

        const formData = new FormData();
        formData.append("audio", recordedBlob, "recording.wav");
        formData.append("text", prompt);

        const speechResponse = await fetch(
          "https://deep-ai.up.railway.app/api/speech-analysis",
          {
            method: "POST",
            body: formData,
          }
        );

        if (!speechResponse.ok) {
          const errorData = await speechResponse.json();
          throw new Error(
            errorData.details ||
              `Speech analysis failed: ${speechResponse.status}`
          );
        }

        const speechResults = await speechResponse.json();
        const pteScores = speechResults;

        console.log(pteScores);

        const fileUrl = await blobToBase64(recordedBlob);
        const uploadedUrl = await uploadFile("", fileUrl, "audio/wav");

        const uri = server.uri + "answers";
        const response = await postRequest(uri, {
          media: {
            url: uploadedUrl,
            type: "audio",
          },
          questionId: question?.questionId,
          userId: user?.id,
          pte_score: pteScores,
        });

        if (response) {
          toast.success("Answer uploaded and analyzed successfully");
          setSpeechAnalysis(speechResults);
          setIsDialogOpen(true);
        }
      } catch (error) {
        console.error("Error processing recording:", error);
        toast.error(
          error.message || "Error processing recording. Please try again."
        );
      } finally {
        setLoading(false);
      }
    });

    if (!success) {
      setLoading(false);
    }
  };

  function blobToBase64(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onloadend = () => resolve(reader.result.split(",")[1]);
      reader.onerror = (error) => reject(error);
    });
  }

  useEffect(() => {
    const initializeMediaRecorder = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            channelCount: 1,
            sampleRate: 16000,
            echoCancellation: true,
            noiseSuppression: true,
          },
        });

        const audioContext = new AudioContext({ sampleRate: 16000 });
        const sourceNode = audioContext.createMediaStreamSource(stream);
        const processorNode = audioContext.createScriptProcessor(4096, 1, 1);

        let audioChunks = [];
        let isRecordingAudio = false;

        processorNode.onaudioprocess = (e) => {
          if (isRecordingAudio) {
            const audioData = e.inputBuffer.getChannelData(0);
            audioChunks.push(new Float32Array(audioData));
          }
        };

        sourceNode.connect(processorNode);
        processorNode.connect(audioContext.destination);

        const recorder = {
          start: () => {
            audioChunks = [];
            isRecordingAudio = true;
            if (window.startBeep) window.startBeep.play();
            console.log("Started recording in WAV format");
          },
          stop: () => {
            isRecordingAudio = false;
            if (window.stopBeep) window.stopBeep.play();

            const wavBlob = createWAVBlob(audioChunks, audioContext.sampleRate);
            setRecordedBlob(wavBlob);
            const url = URL.createObjectURL(wavBlob);
            setAudioUrl(url);
            setRecordingEnded(true);

            console.log("Recorded data available");
          },
          state: "inactive",
        };

        setMediaRecorder(recorder);
      } catch (error) {
        console.error("Error initializing audio recorder:", error);
        toast.error(
          "Error accessing microphone. Please check your permissions."
        );
      }
    };

    initializeMediaRecorder();
  }, []);

  function createWAVBlob(audioChunks, sampleRate) {
    const numChannels = 1;
    const bitsPerSample = 16;

    const totalLength = audioChunks.reduce(
      (acc, chunk) => acc + chunk.length,
      0
    );
    const audioData = new Float32Array(totalLength);
    let offset = 0;

    for (const chunk of audioChunks) {
      audioData.set(chunk, offset);
      offset += chunk.length;
    }

    const wavBuffer = new ArrayBuffer(44 + audioData.length * 2);
    const view = new DataView(wavBuffer);

    const writeString = (view, offset, string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(view, 0, "RIFF");
    view.setUint32(4, 36 + audioData.length * 2, true);
    writeString(view, 8, "WAVE");
    writeString(view, 12, "fmt ");
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * 2, true);
    view.setUint16(32, numChannels * 2, true);
    view.setUint16(34, bitsPerSample, true);
    writeString(view, 36, "data");
    view.setUint32(40, audioData.length * 2, true);

    offset = 44;
    for (let i = 0; i < audioData.length; i++) {
      const sample = Math.max(-1, Math.min(1, audioData[i]));
      view.setInt16(
        offset,
        sample < 0 ? sample * 0x8000 : sample * 0x7fff,
        true
      );
      offset += 2;
    }

    return new Blob([wavBuffer], { type: "audio/wav" });
  }

  useEffect(() => {
    let interval;

    if (!isSubmitted && !isShadowingMode) {
      if (phase === "preparation" && timer > 0) {
        interval = setInterval(() => setTimer((prev) => prev - 1), 1000);
      } else if (phase === "preparation" && timer === 0) {
        setPhase("speaking");
        setTimer(speakTime);
        startRecording();
      } else if (phase === "speaking" && timer > 0 && isRecording) {
        interval = setInterval(() => setTimer((prev) => prev - 1), 1000);
      } else if ((phase === "speaking" && timer === 0) || !isRecording) {
        stopRecording();
        setIsTimeUp(true);
      }
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [timer, phase, isSubmitted, isRecording, speakTime, isShadowingMode]);

  useEffect(() => {
    if (!isShadowingMode) {
      setPhase("preparation");
      setTimer(prepTime);
      setisRecording(false);
      setRecordedBlob(null);
      setAudioUrl("");
      setSpeechAnalysis(null);
      setIsDialogOpen(false);
      setRecordingEnded(false);
      setIsTimeUp(false);
    }
  }, [question, isShadowingMode, prepTime]);

  const startRecording = () => {
    if (mediaRecorder && !isRecording) {
      try {
        if (audioUrl) {
          URL.revokeObjectURL(audioUrl);
          setAudioUrl("");
        }

        // Reset recording state if starting fresh
        if (hasRecorded) {
          setRecordedBlob(null);
          setHasRecorded(false);
          setRecordingEnded(false);
        }

        mediaRecorder.start();
        setisRecording(true);
        setIsBlinking(true);
        setRecordingEnded(false);
        setPhase("speaking");
        setTimer(speakTime);
      } catch (error) {
        console.error("Error starting recording:", error);
        toast.error("Failed to start recording. Please try again.");
      }
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && isRecording) {
      mediaRecorder.stop();
      setisRecording(false);
      setIsBlinking(false);
      setTimer(0);
      setHasRecorded(true);
    }
  };

  const handleRedo = () => {
    if (mediaRecorder && isRecording) {
      mediaRecorder.stop();
    }

    setPhase("preparation");
    setTimer(prepTime);
    setisRecording(false);
    setRecordedBlob(null);
    setAudioUrl("");
    setSpeechAnalysis(null);
    setIsDialogOpen(false);
    setRecordingEnded(false);
    setIsTimeUp(false);
    setHasRecorded(false);
    setIsBlinking(false);

    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }

    toast.info("Ready for new recording. Preparation time started.");
  };

  const toggleShadowingMode = () => {
    setIsShadowingMode(!isShadowingMode);

    if (isShadowingMode) {
      setPhase("preparation");
      setTimer(prepTime);
    }
  };

  const isSmallScreen = screenWidth <= 768;

  const styles = `
    @keyframes blink {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }
  `;

  const recordingButtonStyle = {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    width: "60px",
    height: "60px",
    borderRadius: "50%",
    backgroundColor: isRecording ? "#8055f6" : "lightgray",
    cursor: "pointer",
    animation: isBlinking ? "blink 1s infinite" : "none",
  };

  const buttonStyle = {
    backgroundColor: "#D9D9D9",
    padding: "10px 20px",
    borderRadius: "5px",
    border: "1px solid #140342",
    cursor: "pointer",
    fontSize: "14px",
    color: "#140342",
    textAlign: "center",
    display: "flex",
    alignItems: "center",
    gap: "8px",
    transition: "all 0.2s ease",
  };

  return (
    <>
      <SidebarToggle
        isVisible={true}
        activeCategory="speaking"
        selectedTab={localStorage.getItem("selectedPteTab") || "PTE Core"}
        currentQuestionType="6787cc2b486e6c04269a34e1"
        onQuestionSelect={onQuestionSelect}
      />
      <TranslationDialog
        isOpen={isTranslationDialogOpen}
        onClose={toggleTranslationDialog}
        transcript={prompt}
        questionName={question.questionName}
        questionNumber={question.questionNumber}
      />

      <div
        style={{
          width: isSmallScreen ? "95%" : "90%",
          display: "flex",
          justifyContent: "space-between",
          marginBottom: isSmallScreen ? "15px" : "20px",
          marginLeft: isSmallScreen ? "10px" : "40px",
          marginRight: isSmallScreen ? "10px" : "0",
          marginTop: isSmallScreen ? "20px" : "20px",
        }}
      >
        <p
          style={{
            color: timer <= 5 ? "#ff0000" : "#000000",
            fontWeight: "600",
            fontSize: isSmallScreen ? "16px" : "20px",
            lineHeight: 1.5,
            textAlign: "justify",
          }}
        >
          {isShadowingMode
            ? ""
            : `${
                phase === "preparation" ? "Prepare Time: " : "Test Time: "
              }${formatTime(timer)}`}
        </p>
      </div>
      <div
        style={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          padding: isSmallScreen ? "15px 10px" : "0px 10px",
          backgroundColor: "#f9f9f9",
          borderRadius: "10px",
          position: "relative",
        }}
        className="question-container"
      >
        <style>{styles}</style>
        <LoadingOverlay open={loading} />

        {/* Subscription Indicator */}
        <SubscriptionIndicator
          eligibility={eligibility}
          loading={subscriptionLoading}
          position="top-right"
        />

        <div
          style={{
            display: "flex",
            flexDirection: "column",
            width: isSmallScreen ? "100%" : "90%",
            marginTop: isSmallScreen ? "20px" : "0px",
            marginBottom: isSmallScreen ? "20px" : "0px",
            flexWrap: "wrap",
            paddingLeft: isSmallScreen ? "5px" : "0",
            paddingRight: isSmallScreen ? "5px" : "0",
          }}
        >
          {error && (
            <p
              style={{
                color: "red",
                fontSize: isSmallScreen ? "14px" : "16px",
              }}
            >
              Error fetching practice info: {error}
            </p>
          )}
          {!isShadowingMode && (
            <span
              className="font-bold my-3"
              style={{
                color: "#333",
                fontWeight: "600",
                fontSize: isSmallScreen ? "16px" : "8px",
                lineHeight: 1.4,
                textAlign: "justify",
                marginBottom: isSmallScreen ? "10px" : "0px",
              
                wordBreak: "break-word",
                hyphens: "auto",
                border: "1px solid #140342",
                borderStyle: "dashed",
                padding: isSmallScreen ? "10px 10px" : "10px 10px",
              }}
            >
              <ClickableText text={prompt} />
            </span>
          )}
          {isShadowingMode ? (
            <div>
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  backgroundColor: "#f4f0ff",
                  padding: "30px 20px",
                  borderRadius: "12px",
                }}
              >
                <div
                  style={{
                    marginBottom: "20px",
                    width: "100%",
                  }}
                >
                  {question?.media?.url && (
                    <div style={{ marginBottom: "20px" }}>
                      <h4
                        style={{
                          margin: "0 0 10px 0",
                          fontSize: "16px",
                          color: "#140342",
                          fontWeight: "600",
                        }}
                      >
                        Listen & Shadow
                      </h4>
                      <audio
                        ref={audioRef}
                        controls
                        src={question.media.url}
                        style={{
                          width: "100%",
                          borderRadius: "8px",
                          backgroundColor: "white",
                          boxShadow: "0 2px 5px rgba(0,0,0,0.1)",
                        }}
                        playbackRate={playbackRate}
                        volume={volume}
                      />
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                          marginTop: "15px",
                          padding: "10px 15px",
                          backgroundColor: "white",
                          borderRadius: "8px",
                          boxShadow: "0 2px 5px rgba(0,0,0,0.05)",
                        }}
                      >
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "10px",
                          }}
                        >
                          <span style={{ fontSize: "14px", color: "#333" }}>
                            Speed:
                          </span>
                          <select
                            value={playbackRate}
                            onChange={(e) => {
                              const newRate = parseFloat(e.target.value);
                              setPlaybackRate(newRate);
                              if (audioRef.current) {
                                audioRef.current.playbackRate = newRate;
                              }
                            }}
                            style={{
                              padding: "6px 10px",
                              borderRadius: "5px",
                              border: "1px solid #ddd",
                              backgroundColor: "#fafafa",
                              color: "#333",
                              fontSize: "14px",
                            }}
                          >
                            <option value="0.5">0.5x (Slow)</option>
                            <option value="0.75">0.75x</option>
                            <option value="1.0">1.0x (Normal)</option>
                            <option value="1.25">1.25x</option>
                            <option value="1.5">1.5x</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <ImprovedShadowing
                  text={prompt}
                  questionId={question?.questionId}
                />
              </div>
            </div>
          ) : (
            <div>
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  backgroundColor: "#D3D3D3",
                  marginTop: isSmallScreen ? "10px" : "20px",
                  marginBottom: isSmallScreen ? "10px" : "20px", 
                  padding: isSmallScreen ? "10px 10px" : "30px 5px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    // backgroundColor: "#D3D3D3",
                    width: "100%",
                  }}
                >
                  <div
                    style={{
                      marginTop: isSmallScreen ? "10px" : "20px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      flexDirection: "column",
                      gap: 10,
                    }}
                  >
                    {isTimeUp ? (
                      <p
                        style={{
                          color: "#000000",
                          fontSize: isSmallScreen ? 16 : 20,
                          fontWeight: "500",
                          textAlign: "center",
                          paddingLeft: isSmallScreen ? "10px" : "0",
                          paddingRight: isSmallScreen ? "10px" : "0",
                        }}
                      >
                        Time&apos;s up! Submit to check score
                      </p>
                    ) : (
                      <p
                        style={{
                          color: "#000000",
                          fontSize: isSmallScreen ? 16 : 20,
                          fontWeight: "500",
                          textAlign: "center",
                          paddingLeft: isSmallScreen ? "10px" : "0",
                          paddingRight: isSmallScreen ? "10px" : "0",
                        }}
                      >
                        {isRecording
                          ? "Click To Stop"
                          : phase === "preparation"
                          ? "Preparing... (Click to start recording)"
                          : "Click To Start"}
                      </p>
                    )}
                    <div
                      style={{
                        ...recordingButtonStyle,
                        backgroundColor: isRecording
                          ? "#8055f6"
                          : phase === "preparation"
                          ? "#FF9800"
                          : "lightgray",
                        cursor: "pointer",
                        width: isSmallScreen ? "50px" : "60px",
                        height: isSmallScreen ? "50px" : "60px",
                      }}
                      onClick={
                        isRecording
                          ? stopRecording
                          : () => {
                              // Allow direct recording during preparation phase
                              if (phase === "preparation") {
                                setPhase("speaking");
                                setTimer(speakTime);
                              }
                              startRecording();
                            }
                      }
                    >
                      <img
                        src="/assets/img/microphone.png"
                        alt={isRecording ? "Stop Recording" : "Start Recording"}
                        style={{
                          width: isSmallScreen ? "25px" : "30px",
                          height: isSmallScreen ? "25px" : "30px",
                        }}
                      />
                    </div>
                  </div>
                  <div
                    style={{
                      display: "flex",
                      alignItems: isSmallScreen ? "flex-start" : "center",
                      justifyContent: isSmallScreen ? "center" : "flex-end",
                      flexDirection: isSmallScreen ? "column" : "row",
                      marginLeft: isSmallScreen ? "0" : "30vw",
                      gap: isSmallScreen ? 10 : 5,
                      marginTop: 20,
                      padding: isSmallScreen ? "0 10px" : "0",
                      maxWidth: "100%",
                    }}
                  >
                    <div
                      style={{
                        backgroundColor: "#9780ff",
                        borderRadius: "50%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        width: isSmallScreen ? "25px" : "30px",
                        height: isSmallScreen ? "25px" : "30px",
                        flexShrink: 0,
                      }}
                    >
                      <p
                        style={{
                          color: "#000000",
                          fontSize: isSmallScreen ? 14 : 18,
                          fontWeight: "600",
                          margin: 0,
                        }}
                      >
                        i
                      </p>
                    </div>
                    <p
                      style={{
                        color: "#000000",
                        fontSize: isSmallScreen ? 12 : 15,
                        fontWeight: "600",
                        textAlign: isSmallScreen ? "center" : "left",
                        margin: 0,
                        lineHeight: 1.3,
                      }}
                    >
                      Use a headset with inline microphone to get accurate AI
                      scores
                    </p>
                  </div>
                </div>
              </div>
              {(recordingEnded || recordedBlob) && (
                <div
                  style={{ marginTop: isSmallScreen ? 15 : 20, width: "100%" }}
                >
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      width: "100%",
                      justifyContent: "start",
                    }}
                  >
                    <AudioPlayer
                      src={audioUrl}
                      controls
                      style={{
                        width: "100%",
                        maxWidth: isSmallScreen ? "100%" : "400px",
                      }}
                    />
                  </div>
                  <p
                    style={{
                      color: "#000000",
                      fontSize: isSmallScreen ? 12 : 15,
                      fontWeight: "600",
                      textAlign: "start",
                      marginTop: isSmallScreen ? 10 : 15,
                      paddingLeft: isSmallScreen ? "10px" : "0",
                      paddingRight: isSmallScreen ? "10px" : "0",
                      lineHeight: 1.3,
                    }}
                  >
                    AI Scoring and Audio Answer Download is available after
                    submission.
                  </p>
                </div>
              )}
            </div>
          )}
          {speechAnalysis && (
            <SpeechAnalysisDialog
              analysisData={speechAnalysis}
              isOpen={isDialogOpen}
              onClose={() => setIsDialogOpen(false)}
              audioUrl={audioUrl}
            />
          )}
        </div>
        <div
          className=""
          style={{
            display: "flex",
            flexDirection: isSmallScreen ? "column" : "row",
            justifyContent: "space-between",
            alignItems: isSmallScreen ? "stretch" : "center",
            width: isSmallScreen ? "100%" : "90%",
            marginTop: isSmallScreen ? "15px" : "20px",
            gap: isSmallScreen ? "15px" : "0",
            paddingLeft: isSmallScreen ? "5px" : "0",
            paddingRight: isSmallScreen ? "5px" : "0",
          }}
        >
          <div
            style={{
              display: "flex",
              gap: isSmallScreen ? "8px" : "12px",
              flexWrap: "wrap",
              justifyContent: isSmallScreen ? "center" : "flex-start",
            }}
          >
            {!isShadowingMode && (
              <>
                <button
                  style={{
                    backgroundColor: loading ? "#A9A9A9" : "#D9D9D9",
                    padding: isSmallScreen ? "8px 16px" : "10px 20px",
                    borderRadius: "5px",
                    border: "none",
                    cursor: loading ? "not-allowed" : "pointer",
                    fontSize: isSmallScreen ? "12px" : "14px",
                    color: "#333",
                    textAlign: "center",
                    display: "flex",
                    alignItems: "center",
                    gap: isSmallScreen ? "6px" : "8px",
                    opacity: loading ? 0.7 : 1,
                    minWidth: isSmallScreen ? "70px" : "auto",
                    justifyContent: "center",
                  }}
                  onClick={handleDone}
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <div
                        style={{
                          width: isSmallScreen ? "14px" : "16px",
                          height: isSmallScreen ? "14px" : "16px",
                          border: "2px solid #666",
                          borderTopColor: "transparent",
                          borderRadius: "50%",
                          animation: "spin 1s linear infinite",
                        }}
                      />
                      {isSmallScreen ? "..." : "Processing..."}
                    </>
                  ) : (
                    "Submit"
                  )}
                </button>
                <button
                  style={{
                    backgroundColor: "#D9D9D9",
                    padding: isSmallScreen ? "8px 16px" : "10px 20px",
                    borderRadius: "5px",
                    border: "1px solid #140342",
                    cursor: hasRecorded ? "pointer" : "not-allowed",
                    fontSize: isSmallScreen ? "12px" : "14px",
                    color: "#140342",
                    textAlign: "center",
                    opacity: hasRecorded ? 1 : 0.5,
                    minWidth: isSmallScreen ? "60px" : "auto",
                  }}
                  onClick={handleRedo}
                  disabled={!hasRecorded}
                >
                  Re-do
                </button>
                <AnswersHistoryButton questionId={question?.questionId} />
                <button
                  style={{
                    backgroundColor: "#D9D9D9",
                    padding: isSmallScreen ? "8px 12px" : "10px 20px",
                    borderRadius: "5px",
                    border: "1px solid #140342",
                    cursor: "pointer",
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    gap: isSmallScreen ? "6px" : "10px",
                    fontSize: isSmallScreen ? "11px" : "12px",
                  }}
                  onClick={toggleTranslationDialog}
                >
                  <img
                    src="/assets/img/translate.png"
                    alt="translate"
                    style={{
                      width: isSmallScreen ? "16px" : "20px",
                      height: isSmallScreen ? "16px" : "20px",
                    }}
                  />
                  <p
                    style={{
                      color: "#522CFF",
                      fontSize: isSmallScreen ? "11px" : "12px",
                      margin: 0,
                    }}
                  >
                    {isSmallScreen ? "Translate" : "Translation"}
                  </p>
                </button>
              </>
            )}
            <button
              style={{
                ...buttonStyle,
                ...(isShadowingMode
                  ? {
                      backgroundColor: "#140342",
                      color: "white",
                    }
                  : {}),
                padding: isSmallScreen ? "8px 12px" : "10px 20px",
                fontSize: isSmallScreen ? "11px" : "14px",
                gap: isSmallScreen ? "6px" : "8px",
              }}
              onClick={toggleShadowingMode}
            >
              <img
                src="/assets/img/microphone.png"
                alt="shadowing"
                style={{
                  width: isSmallScreen ? "16px" : "20px",
                  height: isSmallScreen ? "16px" : "20px",
                  filter: isShadowingMode ? "brightness(0) invert(1)" : "none",
                }}
              />
              <span>
                {isShadowingMode
                  ? isSmallScreen
                    ? "Practice"
                    : "Practice Mode"
                  : isSmallScreen
                  ? "Shadow"
                  : "Shadowing Mode"}
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Subscription Modal */}
      <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={closeSubscriptionModal}
        eligibility={eligibility}
        title="Subscription Required"
        message="Continue practicing with detailed feedback and analysis."
      />

      {/* Sidebar Toggle */}
      <SidebarToggle
        isVisible={true}
        activeCategory="speaking"
        currentQuestionType="6787cc2b486e6c04269a34e1"
        onQuestionSelect={onQuestionSelect}
      />
    </>
  );
};

ReadAloud.propTypes = {
  question: PropTypes.shape({
    prompt: PropTypes.string,
    options: PropTypes.array,
    duration: PropTypes.number,
    media: PropTypes.shape({
      url: PropTypes.string,
    }),
    questionId: PropTypes.string,
    questionName: PropTypes.string,
    questionNumber: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    additionalProp1: PropTypes.shape({
      prepTime: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      answerTime: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    }),
  }).isRequired,
  onQuestionSelect: PropTypes.func,
};

export default ReadAloud;
