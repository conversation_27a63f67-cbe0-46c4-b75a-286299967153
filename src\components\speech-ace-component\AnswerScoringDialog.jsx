import React, { useState, useRef, useEffect } from "react";
import AudioPlayer from "react-audio-player";

const ShortAnswerScoring = ({ analysisData, isOpen, onClose, audioUrl }) => {
  const [feedbackOption, setFeedbackOption] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [progress, setProgress] = useState(0);

  const audioRef = useRef(null);

  // Debug: Log the incoming data to understand its structure
  console.log("ShortAnswerScoring data:", analysisData);

  // Set up audio player controls
  useEffect(() => {
    if (audioUrl && audioRef.current) {
      const audio = audioRef.current.audioEl.current;

      const handleTimeUpdate = () => {
        setCurrentTime(audio.currentTime);
        setProgress((audio.currentTime / audio.duration) * 100);
      };

      const handleDurationChange = () => {
        setDuration(audio.duration);
      };

      const handleEnded = () => {
        setIsPlaying(false);
        setCurrentTime(0);
        setProgress(0);
      };

      audio.addEventListener("timeupdate", handleTimeUpdate);
      audio.addEventListener("durationchange", handleDurationChange);
      audio.addEventListener("ended", handleEnded);

      return () => {
        audio.removeEventListener("timeupdate", handleTimeUpdate);
        audio.removeEventListener("durationchange", handleDurationChange);
        audio.removeEventListener("ended", handleEnded);
      };
    }
  }, [audioUrl, isOpen]);

  // Play/pause toggle function
  const togglePlayPause = () => {
    if (audioRef.current) {
      const audio = audioRef.current.audioEl.current;
      if (isPlaying) {
        audio.pause();
      } else {
        audio.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  // Format time (00:00)
  const formatTime = (seconds) => {
    if (isNaN(seconds)) return "00:00";
    const mins = Math.floor(seconds / 60)
      .toString()
      .padStart(2, "0");
    const secs = Math.floor(seconds % 60)
      .toString()
      .padStart(2, "0");
    return `${mins}:${secs}`;
  };

  if (!isOpen) return null;

  // NEW: Extract data from the nested structure in the updated JSON format
  let matchedKeywords = [];
  let unmatchedKeywords = [];
  let transcript = "";

  // First, try to get data from the speechAnalysis structure (from history)
  if (analysisData?.speechAnalysis) {
    matchedKeywords = analysisData.speechAnalysis.matchedKeywords || [];
    unmatchedKeywords = analysisData.speechAnalysis.unmatchedKeywords || [];

    // Get transcript from either direct property or nested content
    transcript =
      analysisData.speechAnalysis.transcript ||
      (analysisData.speechAnalysis.content &&
        analysisData.speechAnalysis.content.transcript) ||
      "";
  }
  // Fallback to the original content structure (direct API response)
  else if (analysisData?.content) {
    matchedKeywords = analysisData.content.matchedKeywords || [];
    unmatchedKeywords = analysisData.content.unmatchedKeywords || [];
    transcript = analysisData.content.transcript || "";
  }

  // Calculate score
  const anyKeywordMatched = matchedKeywords.length > 0;
  const contentScore = anyKeywordMatched ? 1 : 0; // 1/1 if any keyword matches, 0/1 if none match

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1000,
      }}
    >
      <div
        style={{
          backgroundColor: "white",
          borderRadius: "12px",
          padding: "24px",
          width: "90%",
          maxWidth: "600px",
          maxHeight: "90vh",
          overflowY: "auto",
          boxShadow: "0 4px 15px rgba(0, 0, 0, 0.1)",
        }}
      >
        {/* Header with close button */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "20px",
          }}
        >
          <h2 style={{ fontSize: "24px", margin: 0, color: "#140342" }}>
            Answer Score
          </h2>
          <button
            onClick={onClose}
            style={{
              background: "none",
              border: "none",
              fontSize: "24px",
              cursor: "pointer",
              color: "#666",
            }}
          >
            ×
          </button>
        </div>

        {/* Total Score Circle */}
        <div
          style={{
            display: "flex",
            alignItems: "center",
            marginBottom: "30px",
            justifyContent: "space-between",
          }}
        >
          <div
            style={{
              position: "relative",
              width: "80px",
              height: "80px",
              borderRadius: "50%",
              backgroundColor: "#f5f5f5",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {/* Score */}
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <span
                style={{
                  fontSize: "24px",
                  fontWeight: "bold",
                  color: "#140342",
                }}
              >
                {contentScore}/1
              </span>
            </div>
          </div>

          {/* Content score */}
          <div
            style={{
              flex: 1,
              marginLeft: "20px",
              padding: "15px",
              borderRadius: "8px",
              backgroundColor: "#f9f9f9",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <span style={{ color: "#140342", fontWeight: "500" }}>
                Content:
              </span>
              <div style={{ display: "flex", alignItems: "center" }}>
                <span
                  style={{
                    marginRight: "8px",
                    fontWeight: "bold",
                    color: "#140342",
                  }}
                >
                  {contentScore}/1
                </span>
                <div
                  style={{
                    width: "20px",
                    height: "20px",
                    borderRadius: "50%",
                    backgroundColor: contentScore === 1 ? "#10B981" : "#EF4444",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    color: "white",
                    fontSize: "12px",
                  }}
                >
                  {contentScore === 1 ? "✓" : "✗"}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* NEW: Keywords section */}
        <div style={{ marginBottom: "24px" }}>
          <h3 style={{ marginBottom: "12px", color: "#140342" }}>
            Keywords Analysis
          </h3>
          <div
            style={{
              padding: "15px",
              backgroundColor: "#f9f9f9",
              borderRadius: "8px",
              color: "#333",
            }}
          >
            {matchedKeywords && matchedKeywords.length > 0 ? (
              <div style={{ marginBottom: "12px" }}>
                <div
                  style={{
                    fontWeight: "500",
                    marginBottom: "4px",
                    color: "#10B981",
                  }}
                >
                  Matched Keywords:
                </div>
                <div style={{ display: "flex", flexWrap: "wrap", gap: "8px" }}>
                  {matchedKeywords.map((keyword, idx) => (
                    <span
                      key={idx}
                      style={{
                        backgroundColor: "#10B98120",
                        color: "#10B981",
                        padding: "4px 8px",
                        borderRadius: "4px",
                        fontSize: "12px",
                      }}
                    >
                      {keyword}
                    </span>
                  ))}
                </div>
              </div>
            ) : (
              <div style={{ color: "#EF4444", marginBottom: "12px" }}>
                No keywords matched
              </div>
            )}

            {unmatchedKeywords && unmatchedKeywords.length > 0 && (
              <div>
                <div
                  style={{
                    fontWeight: "500",
                    marginBottom: "4px",
                    color: "#EF4444",
                  }}
                >
                  Expected Keywords:
                </div>
                <div style={{ display: "flex", flexWrap: "wrap", gap: "8px" }}>
                  {unmatchedKeywords.map((keyword, idx) => (
                    <span
                      key={idx}
                      style={{
                        backgroundColor: "#EF444420",
                        color: "#EF4444",
                        padding: "4px 8px",
                        borderRadius: "4px",
                        fontSize: "12px",
                      }}
                    >
                      {keyword}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Audio player */}
        <div style={{ marginBottom: "24px" }}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              padding: "10px",
              backgroundColor: "#f9f9f9",
              borderRadius: "8px",
            }}
          >
            <button
              onClick={togglePlayPause}
              style={{
                width: "40px",
                height: "40px",
                borderRadius: "50%",
                backgroundColor: "#8055f6",
                border: "none",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                cursor: "pointer",
                marginRight: "15px",
                color: "white",
                fontSize: "20px",
              }}
            >
              {isPlaying ? "❚❚" : "▶"}
            </button>

            <div style={{ flex: 1 }}>
              <div
                style={{
                  height: "6px",
                  backgroundColor: "#e0e0e0",
                  borderRadius: "3px",
                  position: "relative",
                }}
              >
                <div
                  style={{
                    position: "absolute",
                    left: 0,
                    top: 0,
                    height: "100%",
                    width: `${progress}%`,
                    backgroundColor: "#8055f6",
                    borderRadius: "3px",
                  }}
                ></div>
              </div>
            </div>

            <div
              style={{ marginLeft: "15px", color: "#666", fontSize: "14px" }}
            >
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
          </div>

          {/* Actual audio player (hidden but functional) */}
          <AudioPlayer
            ref={audioRef}
            src={audioUrl}
            autoPlay={true}
            controls={false}
            style={{ display: "none" }}
          />
        </div>

        {/* Transcript section */}
        <div>
          <h3 style={{ marginBottom: "12px", color: "#140342" }}>
            Your Response
          </h3>
          <div
            style={{
              padding: "15px",
              backgroundColor: "#f9f9f9",
              borderRadius: "8px",
              color: "#333",
              lineHeight: "1.6",
            }}
          >
            {transcript || "No transcript available."}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShortAnswerScoring;
