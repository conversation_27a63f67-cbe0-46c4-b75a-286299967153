import  { useState } from 'react';
import ReadAloudAnswersHistory from './ReadAloudAnswersHistory';
import QuestionAnswerIcon from '@mui/icons-material/QuestionAnswer';

const AnswersHistoryButton = ({ questionId }) => {
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);

  const openHistory = () => setIsHistoryOpen(true);
  const closeHistory = () => setIsHistoryOpen(false);

  return (
    <>
      <button
        onClick={openHistory}
        style={{
          backgroundColor: '#D9D9D9',
          padding: '10px 20px',
          borderRadius: '5px',
          border: '1px solid #140342',
          cursor: 'pointer',
          fontSize: '14px',
          color: '#140342',
          textAlign: 'center',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          transition: 'all 0.2s ease',
        }}
      >
        
        <QuestionAnswerIcon style={{ color: '#140342' }} />
        <span>View History</span>
      </button>

      {isHistoryOpen && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000,
          }}
        >
          <ReadAloudAnswersHistory
            questionId={questionId}
            onClose={closeHistory}
          />
        </div>
      )}
    </>
  );
};

export default AnswersHistoryButton;