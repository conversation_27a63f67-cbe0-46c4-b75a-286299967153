import { useState, useEffect, useRef } from "react";
import { server } from "@/api/services/server";
import { useAuth } from "../others/AuthContext";
import PropTypes from "prop-types";

/**
 * Bookmark dropdown component that uses meaningful labels instead of colors
 *
 * @param {Object} props - Component props
 * @param {string} props.questionId - ID of the question
 * @param {string} props.initialLabel - Initial bookmark label
 * @param {Function} props.onLabelChange - Callback function when label changes
 * @returns {JSX.Element} - Bookmark dropdown component
 */
const BookmarkDropdown = ({
  questionId,
  initialLabel = "",
  onLabelChange = () => {},
}) => {
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [currentLabel, setCurrentLabel] = useState(initialLabel);
  const [isLoading, setIsLoading] = useState(false);
  const dropdownRef = useRef(null);

  // Bookmark labels with their corresponding icons and colors
  const bookmarkOptions = [
    {
      label: "Review",
      icon: "🔄",
      color: "#FFA500",
      bgColor: "#fff3e0",
      description: "Needs review",
    },
    {
      label: "Important",
      icon: "⭐",
      color: "#FF0000",
      bgColor: "#ffebee",
      description: "Important question",
    },
    {
      label: "Challenging",
      icon: "🔥",
      color: "#800080",
      bgColor: "#f3e5f5",
      description: "Challenging question",
    },
    {
      label: "Favorite",
      icon: "❤️",
      color: "#008000",
      bgColor: "#e8f5e9",
      description: "Favorite question",
    },
    {
      label: "Later",
      icon: "⏰",
      color: "#607D8B",
      bgColor: "#f3f4f6",
      description: "Save for later",
    },
  ];

  // Fetch the current bookmark label from the API on component mount
  useEffect(() => {
    const fetchCurrentLabel = async () => {
      if (!questionId) return;

      setIsLoading(true);
      try {
        // Get userId from AuthContext or localStorage
        const userId = user?.id || localStorage.getItem("isUserId");

        if (!userId) {
          console.warn("No user ID found for bookmark fetch");
          setIsLoading(false);
          return;
        }

        // Fetch question with user-specific questionmetas
        const filterQuery = {
          include: [
            {
              relation: "questionmetas",
              scope: {
                where: { userId: userId },
              },
            },
          ],
        };

        const response = await fetch(
          `${server.uri}questions/${questionId}?filter=${encodeURIComponent(
            JSON.stringify(filterQuery)
          )}`
        );

        if (!response.ok) {
          console.error("Failed to fetch question data");
          return;
        }

        const questionData = await response.json();

        // Get user-specific bookmark label from questionmetas
        const userMeta =
          questionData.questionmetas && questionData.questionmetas.length > 0
            ? questionData.questionmetas[0]
            : null;

        const apiLabel = userMeta?.markLabel || "";

        // Only update if different from initialLabel prop
        if (apiLabel !== initialLabel) {
          setCurrentLabel(apiLabel);

          // Notify parent if needed
          if (onLabelChange && apiLabel !== initialLabel) {
            onLabelChange(apiLabel);
          }
        }
      } catch (error) {
        console.error("Error fetching bookmark label:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCurrentLabel();
  }, [questionId, user?.id]);

  // Update internal state when initialLabel prop changes
  useEffect(() => {
    if (initialLabel !== undefined && initialLabel !== currentLabel) {
      setCurrentLabel(initialLabel);
    }
  }, [initialLabel]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Update bookmark label in the API
  const handleMarkQuestion = async (label) => {
    if (!questionId) return;

    setIsLoading(true);
    try {
      // Get userId from AuthContext or localStorage
      const userId = user?.id || localStorage.getItem("isUserId");

      if (!userId) {
        console.error("No user ID found for bookmark update");
        setIsLoading(false);
        return;
      }

      // Fetch question with user-specific questionmetas
      const filterQuery = {
        include: [
          {
            relation: "questionmetas",
            scope: {
              where: { userId: userId },
            },
          },
        ],
      };

      const response = await fetch(
        `${server.uri}questions/${questionId}?filter=${encodeURIComponent(
          JSON.stringify(filterQuery)
        )}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch question data");
      }

      const questionData = await response.json();

      // Check if user has existing questionmeta
      const existingMeta =
        questionData.questionmetas && questionData.questionmetas.length > 0
          ? questionData.questionmetas[0]
          : null;

      if (existingMeta && existingMeta.metaId) {
        // Update existing questionmeta
        const updatedMeta = {
          ...existingMeta,
          markLabel: label,
          createdAt: new Date().toISOString(),
        };

        const updateResponse = await fetch(
          `${server.uri}questionmetas/${existingMeta.metaId}`,
          {
            method: "PATCH",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(updatedMeta),
          }
        );

        if (!updateResponse.ok) {
          throw new Error("Failed to update questionmeta");
        }
      } else {
        // Create new questionmeta
        const newMeta = {
          questionId: questionId,
          userId: userId,
          questionName: questionData.prompt || "",
          markLabel: label,
          practiceCount: "0",
          practiceStatus: "Undone",
          isShadowed: false,
          enableDiscussion: true,
          keywords: "",
          prepTime: "",
          answerTime: "",
          createdAt: new Date().toISOString(),
          discussion: [],
        };

        const createResponse = await fetch(`${server.uri}questionmetas`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(newMeta),
        });

        if (!createResponse.ok) {
          throw new Error("Failed to create questionmeta");
        }
      }

      // Update local state
      setCurrentLabel(label);
      setIsOpen(false);

      // Notify parent component
      if (onLabelChange) {
        onLabelChange(label);
      }
    } catch (error) {
      console.error("Error updating bookmark:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Get current option details
  const getCurrentOption = () => {
    return (
      bookmarkOptions.find((option) => option.label === currentLabel) || null
    );
  };

  const currentOption = getCurrentOption();

  return (
    <div
      className="bookmark-dropdown"
      style={{ position: "relative" }}
      ref={dropdownRef}
    >
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: "40px",
          height: "40px",
          borderRadius: "50%",
          backgroundColor: currentOption ? currentOption.color : "transparent",
          cursor: isLoading ? "wait" : "pointer",
          border: currentOption ? "none" : "1px solid #ccc",
          boxShadow: currentOption ? "0 2px 4px rgba(0,0,0,0.2)" : "none",
          transition: "all 0.2s ease",
          opacity: isLoading ? 0.7 : 1,
        }}
        onClick={() => !isLoading && setIsOpen(!isOpen)}
      >
        {currentOption ? (
          <span style={{ fontSize: "18px" }}>{currentOption.icon}</span>
        ) : (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="#140342"
            stroke="#140342"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
          </svg>
        )}
      </div>

      {/* Current label indicator */}
      {currentOption && (
        <div
          style={{
            position: "absolute",
            top: "-6px",
            right: "-6px",
            backgroundColor: "#fff",
            borderRadius: "50%",
            padding: "2px",
            boxShadow: "0 1px 3px rgba(0,0,0,0.2)",
          }}
        >
          <div
            style={{
              width: "12px",
              height: "12px",
              backgroundColor: currentOption.color,
              borderRadius: "50%",
            }}
          />
        </div>
      )}

      {isOpen && (
        <div
          className="dropdown-content"
          style={{
            display: "block",
            position: "absolute",
            backgroundColor: "#f9f9f9",
            minWidth: "200px",
            boxShadow: "0px 8px 16px 0px rgba(0,0,0,0.2)",
            zIndex: 10,
            top: "45px",
            right: 0,
            borderRadius: "8px",
            overflow: "hidden",
            border: "1px solid #eee",
          }}
        >
          {/* Current label display at top of dropdown */}
          <div
            style={{
              padding: "8px 16px",
              borderBottom: "1px solid #eee",
              backgroundColor: "#f4f0ff",
              color: "#140342",
              fontWeight: "500",
              fontSize: "14px",
              textAlign: "center",
            }}
          >
            Current: {currentOption ? currentOption.label : "None"}
          </div>

          {/* Bookmark options */}
          {bookmarkOptions.map((option) => (
            <div
              key={option.label}
              style={{
                padding: "12px 16px",
                cursor: isLoading ? "wait" : "pointer",
                backgroundColor:
                  currentLabel === option.label
                    ? option.bgColor
                    : "transparent",
                borderLeft:
                  currentLabel === option.label
                    ? `4px solid ${option.color}`
                    : "none",
                paddingLeft: currentLabel === option.label ? "12px" : "16px",
                transition: "all 0.2s ease",
                opacity: isLoading ? 0.7 : 1,
              }}
              onClick={() => !isLoading && handleMarkQuestion(option.label)}
              onMouseEnter={(e) => {
                if (currentLabel !== option.label) {
                  e.target.style.backgroundColor = option.bgColor;
                }
              }}
              onMouseLeave={(e) => {
                if (currentLabel !== option.label) {
                  e.target.style.backgroundColor = "transparent";
                }
              }}
            >
              <div style={{ display: "flex", alignItems: "center" }}>
                <span
                  style={{
                    fontSize: "16px",
                    marginRight: "8px",
                  }}
                >
                  {option.icon}
                </span>
                <div style={{ flex: 1 }}>
                  <div style={{ fontWeight: "500", color: "#333" }}>
                    {option.label}
                  </div>
                  <div style={{ fontSize: "12px", color: "#666" }}>
                    {option.description}
                  </div>
                </div>
                {currentLabel === option.label && (
                  <span
                    style={{
                      marginLeft: "auto",
                      color: "#666",
                      fontSize: "12px",
                    }}
                  >
                    ✓
                  </span>
                )}
              </div>
            </div>
          ))}

          {/* Remove bookmark option */}
          <div
            style={{
              padding: "12px 16px",
              cursor: isLoading ? "wait" : "pointer",
              backgroundColor: currentLabel === "" ? "#f5f5f5" : "transparent",
              transition: "all 0.2s ease",
              opacity: isLoading ? 0.7 : 1,
              borderTop: "1px solid #eee",
            }}
            onClick={() => !isLoading && handleMarkQuestion("")}
            onMouseEnter={(e) => {
              if (currentLabel !== "") {
                e.target.style.backgroundColor = "#f5f5f5";
              }
            }}
            onMouseLeave={(e) => {
              if (currentLabel !== "") {
                e.target.style.backgroundColor = "transparent";
              }
            }}
          >
            <div style={{ display: "flex", alignItems: "center" }}>
              <div
                style={{
                  width: "16px",
                  height: "16px",
                  border: "1px solid #ccc",
                  borderRadius: "50%",
                  marginRight: "8px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <span style={{ fontSize: "10px", color: "#999" }}>×</span>
              </div>
              <div style={{ flex: 1 }}>
                <div style={{ fontWeight: "500", color: "#333" }}>
                  Remove Bookmark
                </div>
                <div style={{ fontSize: "12px", color: "#666" }}>
                  Clear bookmark label
                </div>
              </div>
              {currentLabel === "" && (
                <span
                  style={{
                    marginLeft: "auto",
                    color: "#666",
                    fontSize: "12px",
                  }}
                >
                  ✓
                </span>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

BookmarkDropdown.propTypes = {
  questionId: PropTypes.string.isRequired,
  initialLabel: PropTypes.string,
  onLabelChange: PropTypes.func,
};

export default BookmarkDropdown;
