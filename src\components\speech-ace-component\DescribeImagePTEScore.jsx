import React from "react";
import AudioPlayer from "react-audio-player";
import "react-h5-audio-player/lib/styles.css";

const PTEScoringDialog = ({ analysisData, isOpen, onClose, audioUrl }) => {
  if (!isOpen) return null;

  const scores = analysisData?.speech_score?.pte_score || {};

  // Function to calculate pronunciation/fluency scores relative to content score
  const calculateRelativeScore = (
    originalScore,
    scoreType = "pronunciation"
  ) => {
    // Get content score (0-90 scale)
    const contentScore = Math.round(analysisData?.content?.score || 0);

    // Get content percentage
    const contentPercentage = contentScore / 90;

    // Special logic for fluency
    if (scoreType === "fluency") {
      // If content is perfect (90/90), give fluency the full score
      if (contentScore === 90) {
        return 90;
      }

      // If content is more than 60% accurate, treat it as 100% accurate for fluency calculation
      if (contentPercentage > 0.6) {
        return originalScore;
      }
    }

    // Get original score percentage
    const originalScorePercentage = originalScore / 90;

    // Calculate relative score by multiplying percentages and scaling back to 90
    const relativeScore = Math.round(
      contentPercentage * originalScorePercentage * 90
    );

    return relativeScore;
  };

  // Get the original scores
  const originalContentScore = Math.round(analysisData?.content?.score || 0);
  const originalPronunciationScore = scores.pronunciation || 0;
  const originalFluencyScore = scores.fluency || 0;

  // Calculate relative scores
  const relativePronunciationScore = calculateRelativeScore(
    originalPronunciationScore,
    "pronunciation"
  );
  const relativeFluencyScore = calculateRelativeScore(
    originalFluencyScore,
    "fluency"
  );

  const calculateOverallScore = () => {
    const relevantScores = [
      relativePronunciationScore,
      relativeFluencyScore,
      originalContentScore,
    ];

    const validScores = relevantScores.filter(
      (score) => typeof score === "number"
    );
    if (validScores.length === 0) return 0;

    const sum = validScores.reduce((acc, curr) => acc + curr, 0);
    return Math.round(sum / validScores.length);
  };

  const overallScore = calculateOverallScore();

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1000,
      }}
    >
      <div
        style={{
          backgroundColor: "white",
          borderRadius: "12px",
          padding: window.innerWidth <= 768 ? "16px" : "24px",
          width: "95%",
          maxWidth: "800px",
          maxHeight: "90vh",
          overflowY: "auto",
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "20px",
          }}
        >
          <h2 style={{ fontSize: "24px", margin: 0 }}>AI Score</h2>
          <button
            onClick={onClose}
            style={{
              background: "none",
              border: "none",
              fontSize: "24px",
              cursor: "pointer",
            }}
          >
            ×
          </button>
        </div>

        {/* Scoring Table */}
        <table
          style={{
            width: "100%",
            borderCollapse: "collapse",
            marginBottom: "24px",
          }}
        >
          <thead>
            <tr style={{ backgroundColor: "#f8f9fa" }}>
              <th style={tableHeaderStyle}>Component</th>
              <th style={tableHeaderStyle}>Score</th>
              <th style={tableHeaderStyle}>Suggestion</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={tableCellStyle}>
                Content <InfoIcon />
              </td>
              <td style={tableCellStyle}>
                {originalContentScore || 0}
                /90
              </td>
              <td style={tableCellStyle}>
                A good answer of DI will have: picture info, comparison and
                conclusion.
              </td>
            </tr>
            <tr>
              <td style={tableCellStyle}>
                Pronunciation <InfoIcon />
              </td>
              <td style={tableCellStyle}>
                {relativePronunciationScore || 0}/90
                {originalPronunciationScore !== relativePronunciationScore && (
                  <span
                    style={{
                      fontSize: "12px",
                      color: "#666",
                      marginLeft: "4px",
                    }}
                  >
                    (Original: {originalPronunciationScore}/90)
                  </span>
                )}
              </td>
              <td style={tableCellStyle}>AI scoring</td>
            </tr>
            <tr>
              <td style={tableCellStyle}>
                Fluency <InfoIcon />
              </td>
              <td style={tableCellStyle}>
                {relativeFluencyScore || 0}/90
                {originalFluencyScore !== relativeFluencyScore && (
                  <span
                    style={{
                      fontSize: "12px",
                      color: "#666",
                      marginLeft: "4px",
                    }}
                  >
                    (Original: {originalFluencyScore}/90)
                  </span>
                )}
                {/* Show indicator when content enables full fluency scoring */}
                {originalContentScore === 90 && (
                  <span
                    style={{
                      fontSize: "10px",
                      color: "#10B981",
                      marginLeft: "4px",
                      fontWeight: "bold",
                    }}
                  >
                    ✓ Perfect content - max fluency
                  </span>
                )}
                {originalContentScore / 90 > 0.6 &&
                  originalContentScore !== 90 && (
                    <span
                      style={{
                        fontSize: "10px",
                        color: "#10B981",
                        marginLeft: "4px",
                        fontWeight: "bold",
                      }}
                    >
                      ✓ Full scoring enabled
                    </span>
                  )}
              </td>
              <td style={tableCellStyle}>
                AI scoring
                {originalContentScore === 90 && (
                  <div
                    style={{
                      fontSize: "12px",
                      color: "#10B981",
                      marginTop: "4px",
                    }}
                  >
                    Perfect content (90/90) - maximum fluency score awarded
                  </div>
                )}
                {originalContentScore / 90 > 0.6 &&
                  originalContentScore !== 90 && (
                    <div
                      style={{
                        fontSize: "12px",
                        color: "#10B981",
                        marginTop: "4px",
                      }}
                    >
                      Content &gt;60% accurate - full fluency scoring applied
                    </div>
                  )}
              </td>
            </tr>
          </tbody>
        </table>

        <div style={{ marginBottom: "24px" }}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "8px",
              marginBottom: "12px",
              fontSize: "16px",
            }}
          >
            <span>Max Score: 90</span>
            <span style={{ margin: "0 8px" }}>|</span>
            <span>
              Overall Score:{" "}
              <span
                style={{
                  fontWeight: "bold",
                  color:
                    overallScore >= 70
                      ? "#10B981"
                      : overallScore >= 50
                      ? "#F59E0B"
                      : "#EF4444",
                }}
              >
                {overallScore}
              </span>
            </span>
          </div>
        </div>

        {/* Audio Player */}
        <div style={{ marginBottom: "24px" }}>
          <h3 style={{ marginBottom: "12px" }}>Your Recording</h3>
          <AudioPlayer src={audioUrl} controls autoPlay={true} />
        </div>

        {/* Content Analysis Section */}
        {analysisData?.content && (
          <div style={{ marginBottom: "24px" }}>
            <h3 style={{ marginBottom: "12px" }}>Content Analysis</h3>
            <div
              style={{
                marginBottom: "8px",
                display: "flex",
                gap: "12px",
                flexWrap: "wrap",
              }}
            >
              <Legend color="#10B981" text="Matched Keywords" />
              <Legend color="#EF4444" text="Incorrect Pronunciation" />
              <Legend color="#6B7280" text="Extra Words" />
            </div>
            <ContentComparison
              transcript={analysisData?.speech_score?.transcript || ""}
              keywords={analysisData?.content?.keywords || []}
              providedText={analysisData?.content?.provided_text || ""}
              similarityScore={analysisData?.content?.similarity_score || 0}
              customCalculation={
                analysisData?.content?.customCalculation || false
              }
              wordScoreList={analysisData?.speech_score?.word_score_list || []}
            />
          </div>
        )}

        {/* Speech Recognition */}
        <div>
          <h3 style={{ marginBottom: "12px" }}>AI Speech Recognition</h3>
          <div
            style={{
              marginBottom: "8px",
              display: "flex",
              gap: "12px",
            }}
          >
            <span style={legendStyle("green")}>Good</span>
            <span style={legendStyle("yellow")}>Average</span>
            <span style={legendStyle("red")}>Bad</span>
            <span style={legendStyle("gray")}>/</span>
            <span style={legendStyle("gray")}>Pause</span>
          </div>
          <p style={{ marginBottom: "16px", color: "#666", fontSize: "14px" }}>
            Click on the colored text to check score details
          </p>
          <div style={{ lineHeight: "1.6" }}>
            {analysisData?.speech_score?.word_score_list?.map((word, index) => (
              <ColoredWord key={index} word={word} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

const tableHeaderStyle = {
  padding: "12px",
  textAlign: "left",
  borderBottom: "2px solid #dee2e6",
};

const tableCellStyle = {
  padding: "12px",
  borderBottom: "1px solid #dee2e6",
};

const InfoIcon = () => (
  <span
    style={{
      display: "inline-flex",
      alignItems: "center",
      justifyContent: "center",
      width: "16px",
      height: "16px",
      borderRadius: "50%",
      backgroundColor: "#e9ecef",
      fontSize: "12px",
      marginLeft: "4px",
      cursor: "help",
    }}
  >
    i
  </span>
);

const Legend = ({ color, text }) => (
  <span
    style={{
      display: "flex",
      alignItems: "center",
      gap: "4px",
      fontSize: "14px",
    }}
  >
    <span
      style={{
        width: "12px",
        height: "12px",
        borderRadius: "50%",
        backgroundColor: color,
      }}
    />
    {text}
  </span>
);

const ContentComparison = ({
  transcript,
  keywords,
  providedText,
  similarityScore,
}) => {
  // Normalize text for comparison
  const normalizeText = (text) => {
    return text
      .toLowerCase()
      .replace(/[.,!?;:]/g, "")
      .split(/\s+/)
      .filter((word) => word.length > 0);
  };

  const transcriptWords = normalizeText(transcript);
  const providedWords = normalizeText(providedText);
  const keywordList = keywords || [];

  // Create word analysis
  const analyzeWords = () => {
    const analysis = [];
    const providedSet = new Set(providedWords);

    // Analyze transcript words
    transcriptWords.forEach((word, index) => {
      let status = "extra"; // Default to extra
      let matchType = "";

      // Check if word matches any keyword (improved matching for multi-word keywords)
      const matchesKeyword = keywordList.some((keyword) => {
        const keywordLower = keyword.toLowerCase();
        const wordLower = word.toLowerCase();

        // Direct match
        if (keywordLower === wordLower) return true;

        // Check if the word is part of a multi-word keyword
        if (keywordLower.includes(" ")) {
          const keywordWords = keywordLower.split(/\s+/);
          return keywordWords.some(
            (kw) =>
              kw === wordLower ||
              kw.includes(wordLower) ||
              wordLower.includes(kw)
          );
        }

        // Single word keyword matching
        return (
          keywordLower.includes(wordLower) || wordLower.includes(keywordLower)
        );
      });

      // Check if word is in provided text
      const inProvidedText = providedSet.has(word);

      if (matchesKeyword) {
        status = "matched";
        matchType = "keyword";
      } else if (inProvidedText) {
        status = "matched";
        matchType = "content";
      }

      analysis.push({
        word,
        status,
        matchType,
        index,
      });
    });

    return analysis;
  };

  const wordAnalysis = analyzeWords();

  // Get missing keywords
  const getMissingKeywords = () => {
    const transcriptText = transcript.toLowerCase();
    return keywordList.filter(
      (keyword) => !transcriptText.includes(keyword.toLowerCase())
    );
  };

  const missingKeywords = getMissingKeywords();

  return (
    <div>
      {/* Similarity Score */}
      <div
        style={{
          marginBottom: "16px",
          padding: "12px",
          backgroundColor: "#f8f9fa",
          borderRadius: "8px",
          border: "1px solid #dee2e6",
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <span style={{ fontWeight: "500" }}>Content Similarity:</span>
          <span
            style={{
              fontWeight: "bold",
              color:
                similarityScore >= 0.7
                  ? "#10B981"
                  : similarityScore >= 0.5
                  ? "#F59E0B"
                  : "#EF4444",
            }}
          >
            {Math.round(similarityScore * 100)}%
          </span>
        </div>
      </div>

      {/* Your Transcript with Word-by-Word Analysis */}
      <div style={{ marginBottom: "16px" }}>
        <h4 style={{ marginBottom: "8px", fontSize: "16px" }}>
          Your Transcript:
        </h4>
        <div
          style={{
            lineHeight: "1.8",
            padding: "12px",
            backgroundColor: "#f8f9fa",
            borderRadius: "8px",
            border: "1px solid #dee2e6",
            maxHeight: "200px",
            overflowY: "auto",
            wordWrap: "break-word",
            wordBreak: "break-word",
            whiteSpace: "normal",
          }}
        >
          {wordAnalysis.map((item, index) => (
            <span
              key={index}
              style={{
                color: item.status === "matched" ? "#10B981" : "#6B7280",
                backgroundColor:
                  item.status === "matched"
                    ? item.matchType === "keyword"
                      ? "#10B98120"
                      : "#3B82F620"
                    : "#F3F4F620",
                padding: "2px 4px",
                margin: "0 2px 2px 0",
                borderRadius: "3px",
                cursor: "pointer",
                fontWeight: item.matchType === "keyword" ? "600" : "normal",
                display: "inline-block",
                fontSize: "14px",
                lineHeight: "1.4",
              }}
              title={
                item.status === "matched"
                  ? `Matched ${
                      item.matchType === "keyword" ? "keyword" : "content word"
                    }`
                  : "Extra word (not in expected content)"
              }
            >
              {item.word}
            </span>
          ))}
        </div>
      </div>

      {/* Missing Keywords */}
      {missingKeywords.length > 0 && (
        <div style={{ marginBottom: "16px" }}>
          <h4
            style={{ marginBottom: "8px", fontSize: "16px", color: "#EF4444" }}
          >
            Missing Keywords:
          </h4>
          <div
            style={{
              display: "flex",
              flexWrap: "wrap",
              gap: "6px",
              maxHeight: "120px",
              overflowY: "auto",
            }}
          >
            {missingKeywords.map((keyword, index) => (
              <span
                key={index}
                style={{
                  backgroundColor: "#FEE2E2",
                  color: "#EF4444",
                  padding: "4px 8px",
                  borderRadius: "4px",
                  fontSize: window.innerWidth <= 768 ? "12px" : "14px",
                  fontWeight: "500",
                  whiteSpace: "nowrap",
                }}
              >
                {keyword}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Expected Content */}
      <div>
        <h4 style={{ marginBottom: "8px", fontSize: "16px" }}>
          Expected Keywords:
        </h4>
        <div
          style={{
            padding: "12px",
            backgroundColor: "#f0f9ff",
            borderRadius: "8px",
            border: "1px solid #bfdbfe",
            fontSize: "14px",
            lineHeight: "1.6",
          }}
        >
          {keywordList.length > 0
            ? keywordList.join(", ")
            : "No keywords provided"}
        </div>
      </div>
    </div>
  );
};

const legendStyle = (color) => ({
  display: "flex",
  alignItems: "center",
  gap: "4px",
  fontSize: "14px",
  ":before": {
    content: '""',
    display: "inline-block",
    width: "12px",
    height: "12px",
    borderRadius: "50%",
    backgroundColor:
      color === "green"
        ? "#10B981"
        : color === "yellow"
        ? "#F59E0B"
        : color === "red"
        ? "#EF4444"
        : "#6B7280",
  },
});

const ColoredWord = ({ word }) => {
  const getColor = (score) => {
    if (score >= 80) return "#10B981";
    if (score >= 60) return "#F59E0B";
    return "#EF4444";
  };

  return (
    <span
      style={{
        color: getColor(word.quality_score),
        cursor: "pointer",
        padding: "0 2px",
      }}
      title={`Quality Score: ${word.quality_score.toFixed(1)}`}
    >
      {word.word}
      {word.ending_punctuation || " "}
    </span>
  );
};

export default PTEScoringDialog;
