import React from "react";

const EmailScoreDialog = ({ aiScore, isOpen, onClose }) => {
  if (!isOpen || !aiScore) return null;

  // Calculate overall percentage from totalScore
  const overallPercentage = Math.round(
    (aiScore.totalScore / aiScore.maxScore) * 100
  );

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1000,
      }}
    >
      <div
        style={{
          backgroundColor: "#f4f0ff",
          borderRadius: "12px",
          padding: "24px",
          maxWidth: "800px",
          width: "90%",
          maxHeight: "90vh",
          overflowY: "auto",
          position: "relative",
        }}
      >
        <button
          onClick={onClose}
          style={{
            position: "absolute",
            right: "16px",
            top: "16px",
            background: "none",
            border: "none",
            fontSize: "24px",
            cursor: "pointer",
          }}
        >
          ×
        </button>

        {/* AI Score Overview Section */}
        <div style={{ marginBottom: "32px" }}>
          <h2
            style={{
              fontSize: "20px",
              fontWeight: "600",
              marginBottom: "16px",
            }}
          >
            Email Assessment Score
          </h2>

          <div
            style={{
              padding: "20px",
              backgroundColor: "white",
              boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
              borderRadius: "16px",
              height: "200px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                gap: "16px",
              }}
            >
              <div
                style={{
                  position: "relative",
                  width: "120px",
                  height: "120px",
                }}
              >
                <svg
                  viewBox="0 0 36 36"
                  style={{ width: "100%", height: "100%" }}
                >
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="#E2E8F0"
                    strokeWidth="3"
                  />
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="#140342"
                    strokeWidth="3"
                    strokeDasharray={`${overallPercentage}, 100`}
                  />
                  <text
                    x="18"
                    y="20.35"
                    style={{ fontSize: "8px", fontWeight: "500" }}
                    textAnchor="middle"
                  >
                    {`${aiScore.totalScore}/${aiScore.maxScore}`}
                  </text>
                </svg>
              </div>

              <div
                style={{ display: "flex", flexDirection: "column", gap: "8px" }}
              >
                <ScoreRow
                  label="Content"
                  value={aiScore.content.score}
                  maxValue={aiScore.content.maxScore}
                />
                <ScoreRow
                  label="Form & Conventions"
                  value={aiScore.form.score + aiScore.conventions.score}
                  maxValue={
                    aiScore.form.maxScore + aiScore.conventions.maxScore
                  }
                />
                <ScoreRow
                  label="Organization"
                  value={aiScore.organization.score}
                  maxValue={aiScore.organization.maxScore}
                />
                <ScoreRow
                  label="Language Quality"
                  value={
                    aiScore.vocabulary.score +
                    aiScore.grammar.score +
                    aiScore.spelling.score
                  }
                  maxValue={
                    aiScore.vocabulary.maxScore +
                    aiScore.grammar.maxScore +
                    aiScore.spelling.maxScore
                  }
                />
              </div>
            </div>
          </div>
        </div>

        {/* Skill Analysis */}
        <div style={{ marginBottom: "32px" }}>
          <h2
            style={{
              fontSize: "18px",
              fontWeight: "600",
              marginBottom: "12px",
            }}
          >
            Assessment Details
          </h2>

          <p
            style={{
              fontSize: "14px",
              color: "#666",
              marginBottom: "16px",
              lineHeight: "1.5",
            }}
          >
            This analysis shows how your email performed in different criteria.
            Focus on improving the areas with lower scores to enhance your
            overall email writing skills.
          </p>

          <div
            style={{
              width: "100%",
              border: "1px solid #e0e0e0",
              borderRadius: "5px",
              overflow: "hidden",
              marginBottom: "20px",
            }}
          >
            <table style={{ width: "100%", borderCollapse: "collapse" }}>
              <thead>
                <tr style={{ backgroundColor: "#f5f5f5" }}>
                  <th
                    style={{
                      padding: "10px 15px",
                      textAlign: "left",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    Component
                  </th>
                  <th
                    style={{
                      padding: "10px 15px",
                      textAlign: "center",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    Score
                  </th>
                  <th
                    style={{
                      padding: "10px 15px",
                      textAlign: "left",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    Suggestion
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td
                    style={{
                      padding: "10px 15px",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    Content{" "}
                    <span
                      style={{
                        color: "#4caf50",
                        marginLeft: "5px",
                        cursor: "pointer",
                      }}
                      title="Relevance to prompt, addressing all required points"
                    >
                      ⓘ
                    </span>
                  </td>
                  <td
                    style={{
                      padding: "10px 15px",
                      textAlign: "center",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    {aiScore.content.score}/{aiScore.content.maxScore}
                  </td>
                  <td
                    style={{
                      padding: "10px 15px",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    {aiScore.content.suggestion}
                  </td>
                </tr>
                <tr>
                  <td
                    style={{
                      padding: "10px 15px",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    Form{" "}
                    <span
                      style={{
                        color: "#4caf50",
                        marginLeft: "5px",
                        cursor: "pointer",
                      }}
                      title="Appropriate email structure"
                    >
                      ⓘ
                    </span>
                  </td>
                  <td
                    style={{
                      padding: "10px 15px",
                      textAlign: "center",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    {aiScore.form.score}/{aiScore.form.maxScore}
                  </td>
                  <td
                    style={{
                      padding: "10px 15px",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    {aiScore.form.suggestion}
                  </td>
                </tr>
                <tr>
                  <td
                    style={{
                      padding: "10px 15px",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    Email Conventions{" "}
                    <span
                      style={{
                        color: "#4caf50",
                        marginLeft: "5px",
                        cursor: "pointer",
                      }}
                      title="Proper salutations, closings, etc."
                    >
                      ⓘ
                    </span>
                  </td>
                  <td
                    style={{
                      padding: "10px 15px",
                      textAlign: "center",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    {aiScore.conventions.score}/{aiScore.conventions.maxScore}
                  </td>
                  <td
                    style={{
                      padding: "10px 15px",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    {aiScore.conventions.suggestion}
                  </td>
                </tr>
                <tr>
                  <td
                    style={{
                      padding: "10px 15px",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    Organization{" "}
                    <span
                      style={{
                        color: "#4caf50",
                        marginLeft: "5px",
                        cursor: "pointer",
                      }}
                      title="Logical flow, paragraphing, transitions"
                    >
                      ⓘ
                    </span>
                  </td>
                  <td
                    style={{
                      padding: "10px 15px",
                      textAlign: "center",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    {aiScore.organization.score}/{aiScore.organization.maxScore}
                  </td>
                  <td
                    style={{
                      padding: "10px 15px",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    {aiScore.organization.suggestion}
                  </td>
                </tr>
                <tr>
                  <td
                    style={{
                      padding: "10px 15px",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    Vocabulary{" "}
                    <span
                      style={{
                        color: "#4caf50",
                        marginLeft: "5px",
                        cursor: "pointer",
                      }}
                      title="Lexical resource, collocations, word choice"
                    >
                      ⓘ
                    </span>
                  </td>
                  <td
                    style={{
                      padding: "10px 15px",
                      textAlign: "center",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    {aiScore.vocabulary.score}/{aiScore.vocabulary.maxScore}
                  </td>
                  <td
                    style={{
                      padding: "10px 15px",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    {aiScore.vocabulary.suggestion}
                  </td>
                </tr>
                <tr>
                  <td
                    style={{
                      padding: "10px 15px",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    Grammar{" "}
                    <span
                      style={{
                        color: "#4caf50",
                        marginLeft: "5px",
                        cursor: "pointer",
                      }}
                      title="Grammatical accuracy, sentence structures"
                    >
                      ⓘ
                    </span>
                  </td>
                  <td
                    style={{
                      padding: "10px 15px",
                      textAlign: "center",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    {aiScore.grammar.score}/{aiScore.grammar.maxScore}
                  </td>
                  <td
                    style={{
                      padding: "10px 15px",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    {aiScore.grammar.suggestion}
                  </td>
                </tr>
                <tr>
                  <td
                    style={{
                      padding: "10px 15px",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    Spelling{" "}
                    <span
                      style={{
                        color: "#4caf50",
                        marginLeft: "5px",
                        cursor: "pointer",
                      }}
                      title="Spelling accuracy"
                    >
                      ⓘ
                    </span>
                  </td>
                  <td
                    style={{
                      padding: "10px 15px",
                      textAlign: "center",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    {aiScore.spelling.score}/{aiScore.spelling.maxScore}
                  </td>
                  <td
                    style={{
                      padding: "10px 15px",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    {aiScore.spelling.suggestion}
                  </td>
                </tr>
              </tbody>
              <tfoot>
                <tr style={{ backgroundColor: "#f5f5f5" }}>
                  <td colSpan="3" style={{ padding: "10px 15px" }}>
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        flexWrap: "wrap",
                      }}
                    >
                      <div>
                        Max Score: <strong>{aiScore.maxScore}</strong> | Your
                        Score:{" "}
                        <strong style={{ color: "#140342" }}>
                          {aiScore.totalScore}
                        </strong>
                      </div>
                      <div>
                        <span style={{ marginRight: "15px" }}>
                          Score accurate?
                        </span>
                        <label
                          style={{ marginRight: "15px", cursor: "pointer" }}
                        >
                          <input type="radio" name="accuracy" /> Accurate
                        </label>
                        <label
                          style={{ marginRight: "15px", cursor: "pointer" }}
                        >
                          <input type="radio" name="accuracy" /> Too high
                        </label>
                        <label style={{ cursor: "pointer" }}>
                          <input type="radio" name="accuracy" /> Too low
                        </label>
                      </div>
                    </div>
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>

          <h3
            style={{
              fontSize: "16px",
              fontWeight: "600",
              marginBottom: "12px",
            }}
          >
            Performance Visualization
          </h3>

          <div
            style={{
              display: "flex",
              flexDirection: "column",
              gap: "16px",
              border: "2px solid #140342",
              padding: "16px",
              borderRadius: "10px",
            }}
          >
            <SkillBar
              label="Content"
              value={Math.round(
                (aiScore.content.score / aiScore.content.maxScore) * 100
              )}
              tooltip={aiScore.content.suggestion}
            />
            <SkillBar
              label="Form"
              value={Math.round(
                (aiScore.form.score / aiScore.form.maxScore) * 100
              )}
              tooltip={aiScore.form.suggestion}
            />
            <SkillBar
              label="Email Conventions"
              value={Math.round(
                (aiScore.conventions.score / aiScore.conventions.maxScore) * 100
              )}
              tooltip={aiScore.conventions.suggestion}
            />
            <SkillBar
              label="Organization"
              value={Math.round(
                (aiScore.organization.score / aiScore.organization.maxScore) *
                  100
              )}
              tooltip={aiScore.organization.suggestion}
            />
            <SkillBar
              label="Vocabulary"
              value={Math.round(
                (aiScore.vocabulary.score / aiScore.vocabulary.maxScore) * 100
              )}
              tooltip={aiScore.vocabulary.suggestion}
            />
            <SkillBar
              label="Grammar"
              value={Math.round(
                (aiScore.grammar.score / aiScore.grammar.maxScore) * 100
              )}
              tooltip={aiScore.grammar.suggestion}
            />
            <SkillBar
              label="Spelling"
              value={Math.round(
                (aiScore.spelling.score / aiScore.spelling.maxScore) * 100
              )}
              tooltip={aiScore.spelling.suggestion}
            />
          </div>
        </div>

        {/* Email with Feedback */}
        <div style={{ marginBottom: "32px" }}>
          <h2
            style={{
              fontSize: "18px",
              fontWeight: "600",
              marginBottom: "12px",
            }}
          >
            Annotated Email
          </h2>

          <div
            className="email-with-feedback"
            style={{
              marginTop: "10px",
              border: "1px solid #e0e0e0",
              borderRadius: "10px",
              padding: "20px",
              backgroundColor: "white",
            }}
          >
            <div dangerouslySetInnerHTML={{ __html: aiScore.annotatedText }} />
            <div
              style={{
                textAlign: "center",
                color: "#666",
                marginTop: "16px",
                fontSize: "14px",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  gap: "16px",
                  marginBottom: "8px",
                }}
              >
                <div
                  style={{ display: "flex", alignItems: "center", gap: "4px" }}
                >
                  <span
                    style={{
                      display: "inline-block",
                      width: "12px",
                      height: "12px",
                      backgroundColor: "red",
                      borderRadius: "50%",
                    }}
                  ></span>
                  <span>Grammar</span>
                </div>
                <div
                  style={{ display: "flex", alignItems: "center", gap: "4px" }}
                >
                  <span
                    style={{
                      display: "inline-block",
                      width: "12px",
                      height: "12px",
                      backgroundColor: "orange",
                      borderRadius: "50%",
                    }}
                  ></span>
                  <span>Vocabulary</span>
                </div>
                <div
                  style={{ display: "flex", alignItems: "center", gap: "4px" }}
                >
                  <span
                    style={{
                      display: "inline-block",
                      width: "12px",
                      height: "12px",
                      backgroundColor: "blue",
                      borderRadius: "50%",
                    }}
                  ></span>
                  <span>Spelling</span>
                </div>
              </div>
              Hover over highlighted text for detailed feedback
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            gap: "16px",
            marginTop: "16px",
          }}
        >
          <button
            style={{
              padding: "12px 24px",
              backgroundColor: "#9e9e9e",
              color: "white",
              border: "none",
              borderRadius: "8px",
              fontSize: "14px",
              fontWeight: "500",
              cursor: "pointer",
            }}
            onClick={onClose}
          >
            Close
          </button>
          <button
            style={{
              padding: "12px 24px",
              backgroundColor: "#e67e22",
              color: "white",
              border: "none",
              borderRadius: "8px",
              fontSize: "14px",
              fontWeight: "500",
              cursor: "pointer",
            }}
          >
            Request Teacher Feedback
          </button>
          <button
            style={{
              padding: "12px 24px",
              backgroundColor: "#00bcd4",
              color: "white",
              border: "none",
              borderRadius: "8px",
              fontSize: "14px",
              fontWeight: "500",
              cursor: "pointer",
            }}
          >
            Share Result
          </button>
        </div>
      </div>
    </div>
  );
};

// Helper components
const ScoreRow = ({ label, value, maxValue }) => (
  <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
    <span style={{ fontSize: "16px", color: "#140342", fontWeight: "600" }}>
      {label}:
    </span>
    <span style={{ fontSize: "16px", fontWeight: "600", color: "#140342" }}>
      {value}/{maxValue}
    </span>
  </div>
);

const SkillBar = ({ label, value, tooltip }) => (
  <div title={tooltip}>
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        marginBottom: "4px",
      }}
    >
      <span style={{ fontSize: "14px" }}>{label}</span>
      <span style={{ fontSize: "14px", color: "#666" }}>{value}%</span>
    </div>
    <div
      style={{
        width: "100%",
        height: "16px",
        backgroundColor: "#f0f0f0",
        borderRadius: "43px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          width: `${value}%`,
          height: "100%",
          backgroundColor:
            value >= 80 ? "#140342" : value >= 60 ? "#140342" : "#140342",
          opacity: value >= 80 ? 1 : value >= 60 ? 0.7 : 0.4,
          borderRadius: "4px",
        }}
      ></div>
    </div>
  </div>
);

export default EmailScoreDialog;
