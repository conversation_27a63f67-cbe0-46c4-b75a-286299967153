import React from "react";
import {
  Backdrop,
  CircularProgress,
  Typography,
  Paper,
  Box,
  LinearProgress,
} from "@mui/material";
import PsychologyIcon from "@mui/icons-material/Psychology";
import AssessmentIcon from "@mui/icons-material/Assessment";
import RecordVoiceOverIcon from "@mui/icons-material/RecordVoiceOver";
import SpellcheckIcon from "@mui/icons-material/Spellcheck";

const LoadingOverlay = ({ open }) => {
  const [progress, setProgress] = React.useState(0);

  React.useEffect(() => {
    if (open) {
      const timer = setInterval(() => {
        setProgress((oldProgress) => {
          if (oldProgress === 100) {
            return 0;
          }
          const diff = Math.random() * 10;
          return Math.min(oldProgress + diff, 100);
        });
      }, 500);

      return () => {
        clearInterval(timer);
      };
    }
  }, [open]);

  return (
    <Backdrop
      sx={{
        color: "#fff",
        zIndex: (theme) => theme.zIndex.drawer + 1,
        backgroundColor: "rgba(0, 0, 0, 0.85)",
        backdropFilter: "blur(6px)",
      }}
      open={open}
    >
      <Paper
        elevation={24}
        sx={{
          p: 4,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          maxWidth: 400,
          width: "90%",
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          borderRadius: 4,
          position: "relative",
          overflow: "hidden",
        }}
      >
        {/* Progress bar at the top */}
        <LinearProgress
          variant="determinate"
          value={progress}
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            height: 4,
            "& .MuiLinearProgress-bar": {
              backgroundColor: "#8055f6",
            },
            backgroundColor: "rgba(128, 85, 246, 0.2)",
          }}
        />

        <Box
          sx={{
            position: "relative",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            mb: 3,
            mt: 2,
          }}
        >
          <CircularProgress
            size={80}
            thickness={3}
            sx={{
              color: "#140342",
              position: "absolute",
            }}
          />
          <PsychologyIcon
            sx={{
              fontSize: 40,
              color: "#140342",
              animation: "pulse 2s infinite ease-in-out",
              "@keyframes pulse": {
                "0%": { transform: "scale(1)", opacity: 0.8 },
                "50%": { transform: "scale(1.1)", opacity: 1 },
                "100%": { transform: "scale(1)", opacity: 0.8 },
              },
            }}
          />
        </Box>

        <Typography
          variant="h6"
          sx={{
            color: "#333",
            mb: 2,
            textAlign: "center",
            fontWeight: 600,
            letterSpacing: "-0.5px",
          }}
        >
          Analyzing Your Speech
        </Typography>

        {/* Analysis Steps */}
        <Box sx={{ width: "100%", mb: 3 }}>
          {[
            { icon: RecordVoiceOverIcon, text: "Analyzing pronunciation" },
            { icon: SpellcheckIcon, text: "Checking fluency" },
            { icon: AssessmentIcon, text: "Calculating PTE scores" },
          ].map(({ icon: Icon, text }, index) => (
            <Box
              key={index}
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 2,
                mb: 1.5,
                opacity: progress > index * 30 ? 1 : 0.5,
                transform: `translateX(${progress > index * 30 ? 0 : -10}px)`,
                transition: "all 0.3s ease-in-out",
              }}
            >
              <Icon sx={{ color: "#140342", fontSize: 20 }} />
              <Typography
                variant="body2"
                sx={{
                  color: "#666",
                  fontWeight: progress > index * 30 ? 500 : 400,
                }}
              >
                {text}
              </Typography>
            </Box>
          ))}
        </Box>

        {/* Animated dots */}
        <Box
          sx={{
            display: "flex",
            gap: 1,
            mt: 1,
          }}
        >
          {[0, 1, 2].map((i) => (
            <Box
              key={i}
              sx={{
                width: 6,
                height: 6,
                backgroundColor: "#140342",
                borderRadius: "50%",
                animation: "bounce 1.4s infinite ease-in-out",
                animationDelay: `${i * 0.16}s`,
                "@keyframes bounce": {
                  "0%, 100%": {
                    transform: "translateY(0)",
                    opacity: 0.5,
                  },
                  "50%": {
                    transform: "translateY(-8px)",
                    opacity: 1,
                  },
                },
              }}
            />
          ))}
        </Box>
      </Paper>
    </Backdrop>
  );
};

export default LoadingOverlay;
