import { useState, useEffect, useCallback } from "react";
import {
  Box,
  Paper,
  Typography,
  Tabs,
  Tab,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Grid,
  Container,
  styled,
} from "@mui/material";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import PropTypes from "prop-types";
import PTEScoreLoader from "./PTEScoreLoader";
import ResultSection from "./ResultSection";
import PTECertificate from "./PTECertificate";
import { server } from "../../api/services/server";
import { useNavigate } from "react-router-dom";
import { PTECrossSectionalScoring } from "../../utils/pteScoring";

const StyledBox = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(4),
}));

const StyledCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(4),
  borderRadius: theme.spacing(1),
}));

const StyledTypography = styled(Typography)(() => ({
  fontWeight: 700,
  letterSpacing: "-0.5px",
  color: "#140342",
}));

const StyledSubTypography = styled(Typography)(() => ({
  fontWeight: 500,
  fontSize: "1.1rem",
  color: "#140342",
}));

const TabPanel = ({ children, value, index }) => (
  <div hidden={value !== index} style={{ padding: "24px" }}>
    {value === index && children}
  </div>
);

TabPanel.propTypes = {
  children: PropTypes.node,
  value: PropTypes.number.isRequired,
  index: PropTypes.number.isRequired,
};

const ScoreDisplay = styled(Box)(({ theme }) => ({
  textAlign: "center",
  padding: theme.spacing(3),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.background.paper,
  boxShadow: theme.shadows[1],
  transition: "transform 0.2s ease-in-out",
  "&:hover": {
    transform: "translateY(-4px)",
    boxShadow: theme.shadows[3],
  },
}));

// Helper function to safely extract score values
const getScoreValue = (scoreData) => {
  if (typeof scoreData === "object" && scoreData !== null) {
    return scoreData.score || 0;
  }
  return scoreData || 0;
};

// Modified function to treat content score consistently with other scores
const calculateOverallScore = (scores) => {
  let total = 0;
  let count = 0;

  // Add pronunciation if available
  if (scores.pronunciation !== undefined) {
    total += scores.pronunciation;
    count++;
  }

  // Add fluency if available
  if (scores.fluency !== undefined) {
    total += scores.fluency;
    count++;
  }

  // Add content score if available - now treating it consistently
  if (scores.content !== undefined) {
    total += scores.content;
    count++;
  }

  // Calculate average, or return 0 if no scores available
  return count > 0 ? Math.round(total / count) : 0;
};

// Function to calculate overall writing score (summarize written text)
const calculateOverallWritingScore = (scores) => {
  let total = 0;
  let count = 0;

  // Add all writing components
  ["content", "form", "grammar", "vocabulary"].forEach((component) => {
    if (scores[component] !== undefined) {
      total += scores[component];
      count++;
    }
  });

  // Calculate average, or return 0 if no scores available
  return count > 0 ? Math.round(total / count) : 0;
};

// Function to calculate overall essay score
const calculateOverallEssayScore = (scores) => {
  let total = 0;
  let count = 0;

  // Add all essay components
  [
    "content",
    "form",
    "grammar",
    "spelling",
    "vocabularyRange",
    "generalLinguisticRange",
    "developmentStructureCoherence",
  ].forEach((component) => {
    if (scores[component] !== undefined) {
      total += scores[component];
      count++;
    }
  });

  // Calculate average, or return 0 if no scores available
  return count > 0 ? Math.round(total / count) : 0;
};

// Function to calculate text similarity for Write from Dictation
const calculateTextSimilarity = (text1, text2) => {
  if (!text1 || !text2) return 0;

  // Simple word-based similarity
  const words1 = text1.split(/\s+/).filter((word) => word.length > 0);
  const words2 = text2.split(/\s+/).filter((word) => word.length > 0);

  if (words1.length === 0 && words2.length === 0) return 1;
  if (words1.length === 0 || words2.length === 0) return 0;

  // Count matching words
  let matches = 0;
  const maxLength = Math.max(words1.length, words2.length);

  for (let i = 0; i < Math.min(words1.length, words2.length); i++) {
    if (words1[i] === words2[i]) {
      matches++;
    }
  }

  return matches / maxLength;
};

// Function to calculate reading/listening score based on answer correctness
const calculateReadingListeningScore = (userAnswer, questionData) => {
  if (!userAnswer || !questionData) {
    console.log("Missing userAnswer or questionData:", {
      userAnswer,
      questionData,
    });
    return 0;
  }

  console.log("Calculating score for:", {
    questionId: questionData.questionId,
    questionType: questionData.category?.name,
    userAnswer: userAnswer.useranswers,
    questionOptions: questionData.options,
    dropdownOptions: questionData.dropdownOptions,
    incorrectWords: questionData.incorrectWords,
    userScore: userAnswer.score,
  });

  // For multiple choice questions
  if (questionData.options && Array.isArray(questionData.options)) {
    const correctOption = questionData.options.find((opt) => opt.isCorrect);
    if (!correctOption) {
      console.log("No correct option found");
      return 0;
    }

    // Check if user's answer matches correct answer
    if (userAnswer.useranswers && Array.isArray(userAnswer.useranswers)) {
      const userAnswerText =
        userAnswer.useranswers[0]?.optionText || userAnswer.useranswers[0];
      const isCorrect = userAnswerText === correctOption.optionText;
      const score = isCorrect ? questionData.maxScore || 1 : 0;

      console.log("Multiple choice scoring:", {
        userAnswerText,
        correctAnswer: correctOption.optionText,
        isCorrect,
        score,
      });

      return score;
    }
  }

  // For dropdown/fill-in-the-blanks questions
  if (
    questionData.dropdownOptions &&
    Array.isArray(questionData.dropdownOptions)
  ) {
    if (userAnswer.useranswers && Array.isArray(userAnswer.useranswers)) {
      let correctCount = 0;
      questionData.dropdownOptions.forEach((dropdown, index) => {
        const userAnswerText =
          userAnswer.useranswers[index]?.optionText ||
          userAnswer.useranswers[index];
        if (userAnswerText === dropdown.correctAnswer) {
          correctCount++;
        }
      });
      const score = Math.round(
        (correctCount / questionData.dropdownOptions.length) *
          (questionData.maxScore || 1)
      );

      console.log("Dropdown scoring:", {
        correctCount,
        totalQuestions: questionData.dropdownOptions.length,
        score,
      });

      return score;
    }
  }

  // For Highlight Incorrect Words questions
  if (
    questionData.incorrectWords &&
    Array.isArray(questionData.incorrectWords)
  ) {
    if (userAnswer.useranswers && Array.isArray(userAnswer.useranswers)) {
      // Get the words that should be highlighted (incorrect words)
      const wordsToHighlight = questionData.incorrectWords.map((item) =>
        item.incorrect.toLowerCase()
      );

      // Get the words the user highlighted
      const userHighlighted = userAnswer.useranswers.map((word) =>
        word.toLowerCase()
      );

      // Count correct highlights
      let correctCount = 0;
      userHighlighted.forEach((word) => {
        if (wordsToHighlight.includes(word)) {
          correctCount++;
        }
      });

      // Calculate score based on correct highlights
      const score = Math.min(
        questionData.maxScore || 1,
        Math.max(0, correctCount)
      );

      console.log("Highlight Incorrect Words scoring:", {
        wordsToHighlight,
        userHighlighted,
        correctCount,
        score,
      });

      return score;
    }
  }

  // For Write from Dictation questions
  if (
    questionData.category?.name === "Write From Dictation" ||
    userAnswer.type === "write from dictation"
  ) {
    const userText = userAnswer.useranswers || userAnswer.answer || "";
    const correctText =
      questionData.transcript ||
      questionData.answer ||
      userAnswer.correctAnswer?.transcript ||
      userAnswer.correctAnswer?.answer ||
      "";

    if (typeof userText === "string" && typeof correctText === "string") {
      // Simple text similarity scoring (can be enhanced)
      const similarity = calculateTextSimilarity(
        userText.toLowerCase().trim(),
        correctText.toLowerCase().trim()
      );
      const score = Math.round(similarity * (questionData.maxScore || 1));

      console.log("Write from Dictation scoring:", {
        userText,
        correctText,
        similarity,
        score,
      });

      return score;
    }
  }

  // Fallback to existing score
  const fallbackScore = userAnswer.score || 0;
  console.log("Using fallback score:", fallbackScore);
  return fallbackScore;
};

// Function to calculate section average from complete data
const calculateSectionAverage = (completeData, sectionType) => {
  const attemptedQuestions = completeData.filter((q) => q.attempted);
  if (attemptedQuestions.length === 0) return 0;

  const totalScore = attemptedQuestions.reduce((sum, question) => {
    // For speaking/writing, use pteScores.overall; for reading/listening, use score
    const score = question.pteScores?.overall || question.score || 0;
    return sum + score;
  }, 0);

  return Math.round(totalScore / attemptedQuestions.length);
};

const ResultComponent = ({ mockTestId, userId, attemptNumber }) => {
  const [value, setValue] = useState(0);
  const [speakingScores, setSpeakingScores] = useState([]);
  const [writingScores, setWritingScores] = useState([]);
  const [essayScores, setEssayScores] = useState([]);
  const [showDetailedScores, setShowDetailedScores] = useState(false);
  const [showWritingDetailedScores, setShowWritingDetailedScores] =
    useState(false);
  const [showEssayDetailedScores, setShowEssayDetailedScores] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  const [selectedWritingQuestion, setSelectedWritingQuestion] = useState(null);
  const [selectedEssayQuestion, setSelectedEssayQuestion] = useState(null);
  const [selectedReadingQuestion, setSelectedReadingQuestion] = useState(null);
  const [showReadingDetailedScores, setShowReadingDetailedScores] =
    useState(false);
  const [selectedListeningQuestion, setSelectedListeningQuestion] =
    useState(null);
  const [showListeningDetailedScores, setShowListeningDetailedScores] =
    useState(false);
  const [loading, setLoading] = useState(true);
  const [taskTypeStats, setTaskTypeStats] = useState({});
  const [readingScores, setReadingScores] = useState([]);
  const [listeningScores, setListeningScores] = useState([]);
  const [attemptHistory, setAttemptHistory] = useState([]);
  const [overallScores, setOverallScores] = useState({
    speaking: 0,
    writing: 0,
    reading: 0,
    listening: 0,
    overall: 0,
  });
  const [readingResults, setReadingResults] = useState(null);
  const [listeningResults, setListeningResults] = useState(null);
  const [crossSectionalScores, setCrossSectionalScores] = useState({
    speaking: 0,
    writing: 0,
    reading: 0,
    listening: 0,
    overall: 0,
  });
  const [allMockTestQuestions, setAllMockTestQuestions] = useState({
    speaking: [],
    writing: [],
    reading: [],
    listening: [],
  });
  const navigate = useNavigate();

  // Add this function to calculate writing score out of 90
  const calculateWritingOverallScore = () => {
    let totalWritingScore = 0;
    let maxWritingScore = 0;

    // Add summarize written text scores
    writingScores.forEach((score) => {
      const content = getScoreValue(score.pteScores?.content);
      const form = getScoreValue(score.pteScores?.form);
      const grammar = getScoreValue(score.pteScores?.grammar);
      const vocabulary = getScoreValue(score.pteScores?.vocabulary);

      totalWritingScore += content + form + grammar + vocabulary;
      maxWritingScore +=
        (score.pteScores?.content?.maxScore || 2) +
        (score.pteScores?.form?.maxScore || 2) +
        (score.pteScores?.grammar?.maxScore || 2) +
        (score.pteScores?.vocabulary?.maxScore || 2);
    });

    // Add essay scores
    essayScores.forEach((score) => {
      const content = getScoreValue(score.pteScores?.content);
      const form = getScoreValue(score.pteScores?.form);
      const grammar = getScoreValue(score.pteScores?.grammar);
      const spelling = getScoreValue(score.pteScores?.spelling);
      const vocabularyRange = getScoreValue(score.pteScores?.vocabularyRange);
      const generalLinguisticRange = getScoreValue(
        score.pteScores?.generalLinguisticRange
      );
      const developmentStructureCoherence = getScoreValue(
        score.pteScores?.developmentStructureCoherence
      );

      totalWritingScore +=
        content +
        form +
        grammar +
        spelling +
        vocabularyRange +
        generalLinguisticRange +
        developmentStructureCoherence;
      maxWritingScore +=
        (score.pteScores?.content?.maxScore || 3) +
        (score.pteScores?.form?.maxScore || 2) +
        (score.pteScores?.grammar?.maxScore || 2) +
        (score.pteScores?.spelling?.maxScore || 2) +
        (score.pteScores?.vocabularyRange?.maxScore || 2) +
        (score.pteScores?.generalLinguisticRange?.maxScore || 2) +
        (score.pteScores?.developmentStructureCoherence?.maxScore || 2);
    });

    return maxWritingScore > 0
      ? Math.round((totalWritingScore / maxWritingScore) * 90)
      : 0;
  };

  useEffect(() => {
    const fetchResults = async () => {
      try {
        console.log("Fetching results for:", {
          mockTestId,
          userId,
          attemptNumber,
        });

        // Fetch all mock test questions first
        const allQuestionsResponse = await fetch(
          `${server.uri}questionsBysection/${mockTestId}`
        );
        const allQuestionsData = await allQuestionsResponse.json();

        if (allQuestionsData) {
          // Log the structure to understand the API response
          console.log("Raw API response structure:", allQuestionsData);

          // Group questions by their actual section from the question data
          const groupedQuestions = {
            speaking: [],
            writing: [],
            reading: [],
            listening: [],
          };

          // If allQuestionsData is an array of arrays (sections)
          if (Array.isArray(allQuestionsData)) {
            allQuestionsData.forEach((sectionQuestions, index) => {
              if (Array.isArray(sectionQuestions)) {
                sectionQuestions.forEach((question) => {
                  const questionSection =
                    question.section || question.category?.section;
                  if (questionSection && groupedQuestions[questionSection]) {
                    groupedQuestions[questionSection].push(question);
                  } else {
                    // Fallback to index-based assignment if section is not clear
                    const sectionNames = [
                      "speaking",
                      "writing",
                      "reading",
                      "listening",
                    ];
                    const fallbackSection = sectionNames[index] || "listening";
                    groupedQuestions[fallbackSection].push(question);
                  }
                });
              }
            });
          }

          setAllMockTestQuestions(groupedQuestions);
          console.log("Grouped mock test questions:", groupedQuestions);
        }

        const response = await fetch(`${server.uri}answers`);
        const data = await response.json();

        console.log("All answers data:", data);

        // Filter answers for current attempt with improved logic
        const filterCondition = (answer) => {
          const matchesUser = answer.userId === userId;
          const matchesMockTest =
            answer.mockTestId === mockTestId ||
            answer.mocktestId === mockTestId;
          const matchesAttempt =
            !attemptNumber || answer.attemptNumber === attemptNumber;

          console.log("Filter check:", {
            answer: answer.questionId,
            matchesUser,
            matchesMockTest,
            matchesAttempt,
            answerAttempt: answer.attemptNumber,
            targetAttempt: attemptNumber,
          });

          return matchesUser && matchesMockTest && matchesAttempt;
        };

        // Speaking answers
        const speakingAnswers = data.filter(
          (answer) =>
            filterCondition(answer) &&
            answer.pteScores &&
            (answer.section === "speaking" || answer.taskType) // Include taskType for speaking
        );

        console.log("Filtered speaking answers:", speakingAnswers);

        // Writing answers - filter for summarize-written-text
        const writingAnswers = data.filter(
          (answer) =>
            filterCondition(answer) &&
            answer.section === "writing" &&
            (answer.type === "summarize-written-text" ||
              answer.type === "summarize written text") &&
            answer.additionalProps?.scoreResponse
        );

        console.log("Filtered writing answers:", writingAnswers);

        // Essay answers - filter for essay-writing type
        const essayAnswers = data.filter(
          (answer) =>
            filterCondition(answer) &&
            answer.section === "writing" &&
            (answer.type === "essay-writing" ||
              answer.type === "essay writing") &&
            answer.additionalProps?.scoreResponse
        );

        console.log("Filtered essay answers:", essayAnswers);

        // Ensure all necessary score properties exist and convert similarity_score to content
        const normalizedSpeakingAnswers = speakingAnswers.map((answer) => {
          // Convert similarity_score to content score on the same scale as pronunciation and fluency
          const similarityScore = answer.pteScores.similarity_score || 0;
          const contentScore = Math.round(similarityScore * 100);

          const normalizedScores = {
            pronunciation: answer.pteScores.pronunciation || 0,
            fluency: answer.pteScores.fluency || 0,
            content: contentScore,
            // Keep the original for reference if needed
            similarity_score: similarityScore,
          };
          return {
            ...answer,
            pteScores: normalizedScores,
          };
        });

        // Process writing answers - use scoreResponse from additionalProps
        const normalizedWritingAnswers = writingAnswers.map((answer) => {
          return {
            ...answer,
            pteScores: answer.additionalProps.scoreResponse, // Use the AI scoring response directly
          };
        });

        // Process essay answers - use scoreResponse from additionalProps
        const normalizedEssayAnswers = essayAnswers.map((answer) => {
          return {
            ...answer,
            pteScores: answer.additionalProps.scoreResponse, // Use the AI scoring response directly
          };
        });

        const taskGroups = {};
        normalizedSpeakingAnswers.forEach((answer) => {
          const type = answer.taskType || "Other";
          if (!taskGroups[type]) {
            taskGroups[type] = [];
          }
          taskGroups[type].push(answer);
        });

        const typeStats = {};
        Object.entries(taskGroups).forEach(([type, answers]) => {
          const avgScores = {
            pronunciation: Math.round(
              answers.reduce((sum, a) => sum + a.pteScores.pronunciation, 0) /
                answers.length
            ),
            fluency: Math.round(
              answers.reduce((sum, a) => sum + a.pteScores.fluency, 0) /
                answers.length
            ),
            content: Math.round(
              answers.reduce((sum, a) => sum + a.pteScores.content, 0) /
                answers.length
            ),
            // Keep original similarity_score for reference
            similarity_score: parseFloat(
              (
                answers.reduce(
                  (sum, a) => sum + (a.pteScores.similarity_score || 0),
                  0
                ) / answers.length
              ).toFixed(2)
            ),
          };
          // Calculate overall as average of all three scores
          avgScores.overall = calculateOverallScore(avgScores);
          typeStats[type] = avgScores;
        });

        // Reading answers - improved filtering
        const readingAnswers = data.filter(
          (answer) => filterCondition(answer) && answer.section === "reading"
        );

        console.log("Filtered reading answers:", readingAnswers);
        console.log("Reading answers count:", readingAnswers.length);
        if (readingAnswers.length > 0) {
          console.log("Sample reading answer:", readingAnswers[0]);
        }

        // Listening answers - improved filtering
        const listeningAnswers = data.filter(
          (answer) => filterCondition(answer) && answer.section === "listening"
        );

        console.log("Filtered listening answers:", listeningAnswers);
        console.log("Listening answers count:", listeningAnswers.length);
        if (listeningAnswers.length > 0) {
          console.log("Sample listening answer:", listeningAnswers[0]);
        }

        setReadingScores(readingAnswers);
        setListeningScores(listeningAnswers);
        setSpeakingScores(normalizedSpeakingAnswers);
        setWritingScores(normalizedWritingAnswers);
        setEssayScores(normalizedEssayAnswers);
        setTaskTypeStats(typeStats);

        // Fetch attempt history with improved error handling
        try {
          const historyResponse = await fetch(
            `${server.uri}logs?filter=${encodeURIComponent(
              JSON.stringify({
                where: {
                  or: [{ mocktestId: mockTestId }, { mockTestId: mockTestId }],
                  userId: userId,
                },
                order: "attemptNumber ASC",
              })
            )}`
          );

          if (historyResponse.ok) {
            const historyData = await historyResponse.json();
            console.log("Attempt history data:", historyData);

            const completedLogs = (historyData || []).filter(
              (log) => log.status === "completed"
            );

            const uniqueAttempts = [];
            const seen = new Set();
            for (const log of completedLogs) {
              if (!seen.has(log.attemptNumber)) {
                uniqueAttempts.push(log);
                seen.add(log.attemptNumber);
              }
            }

            // Sort by attemptNumber ascending
            uniqueAttempts.sort((a, b) => a.attemptNumber - b.attemptNumber);
            console.log("Processed attempt history:", uniqueAttempts);
            setAttemptHistory(uniqueAttempts);
          }
        } catch (historyError) {
          console.error("Error fetching attempt history:", historyError);
        }

        setLoading(false);
      } catch (error) {
        console.error("Error fetching results:", error);
        setLoading(false);
      }
    };

    if (mockTestId && userId) {
      fetchResults();
    }
  }, [mockTestId, userId, attemptNumber]);

  const calculateAverageScores = () => {
    if (speakingScores.length === 0) return null;

    const sum = speakingScores.reduce(
      (acc, curr) => ({
        pronunciation: acc.pronunciation + (curr.pteScores.pronunciation || 0),
        fluency: acc.fluency + (curr.pteScores.fluency || 0),
        content: acc.content + (curr.pteScores.content || 0),
      }),
      { pronunciation: 0, fluency: 0, content: 0 }
    );

    const averages = {
      pronunciation: Math.round(sum.pronunciation / speakingScores.length),
      fluency: Math.round(sum.fluency / speakingScores.length),
      content: Math.round(sum.content / speakingScores.length),
    };

    // Calculate overall as average of all three scores
    averages.overall = calculateOverallScore(averages);

    return averages;
  };

  const calculateWritingAverageScores = () => {
    if (writingScores.length === 0) return null;

    const sum = writingScores.reduce(
      (acc, curr) => ({
        content: acc.content + getScoreValue(curr.pteScores?.content),
        form: acc.form + getScoreValue(curr.pteScores?.form),
        grammar: acc.grammar + getScoreValue(curr.pteScores?.grammar),
        vocabulary: acc.vocabulary + getScoreValue(curr.pteScores?.vocabulary),
      }),
      { content: 0, form: 0, grammar: 0, vocabulary: 0 }
    );

    const averages = {
      content: Math.round(sum.content / writingScores.length),
      form: Math.round(sum.form / writingScores.length),
      grammar: Math.round(sum.grammar / writingScores.length),
      vocabulary: Math.round(sum.vocabulary / writingScores.length),
    };

    // Calculate overall as average of all four scores
    averages.overall = calculateOverallWritingScore(averages);

    return averages;
  };

  const calculateEssayAverageScores = () => {
    if (essayScores.length === 0) return null;

    const sum = essayScores.reduce(
      (acc, curr) => ({
        content: acc.content + getScoreValue(curr.pteScores?.content),
        form: acc.form + getScoreValue(curr.pteScores?.form),
        grammar: acc.grammar + getScoreValue(curr.pteScores?.grammar),
        spelling: acc.spelling + getScoreValue(curr.pteScores?.spelling),
        vocabularyRange:
          acc.vocabularyRange + getScoreValue(curr.pteScores?.vocabularyRange),
        generalLinguisticRange:
          acc.generalLinguisticRange +
          getScoreValue(curr.pteScores?.generalLinguisticRange),
        developmentStructureCoherence:
          acc.developmentStructureCoherence +
          getScoreValue(curr.pteScores?.developmentStructureCoherence),
      }),
      {
        content: 0,
        form: 0,
        grammar: 0,
        spelling: 0,
        vocabularyRange: 0,
        generalLinguisticRange: 0,
        developmentStructureCoherence: 0,
      }
    );

    const averages = {
      content: Math.round(sum.content / essayScores.length),
      form: Math.round(sum.form / essayScores.length),
      grammar: Math.round(sum.grammar / essayScores.length),
      spelling: Math.round(sum.spelling / essayScores.length),
      vocabularyRange: Math.round(sum.vocabularyRange / essayScores.length),
      generalLinguisticRange: Math.round(
        sum.generalLinguisticRange / essayScores.length
      ),
      developmentStructureCoherence: Math.round(
        sum.developmentStructureCoherence / essayScores.length
      ),
    };

    // Calculate overall as average of all seven scores
    averages.overall = calculateOverallEssayScore(averages);

    return averages;
  };

  // Helper function to create complete section data including unattempted questions
  const createCompleteSectionData = (section, attemptedQuestions) => {
    const allSectionQuestions = allMockTestQuestions[section] || [];
    const completeData = [];

    allSectionQuestions.forEach((question) => {
      const attemptedQuestion = attemptedQuestions.find(
        (attempted) => attempted.questionId === question.questionId
      );

      if (attemptedQuestion) {
        // Question was attempted - use actual data
        let calculatedScore = attemptedQuestion.score || 0;

        // For reading and listening, calculate score based on answer correctness
        if (section === "reading" || section === "listening") {
          calculatedScore = calculateReadingListeningScore(
            attemptedQuestion,
            question
          );
          console.log(
            `Calculated score for ${section} question ${question.questionId}:`,
            calculatedScore
          );
        }

        completeData.push({
          ...attemptedQuestion,
          attempted: true,
          questionData: question,
          section: section, // Override section to match the expected section
          score: calculatedScore, // Use calculated score
        });
      } else {
        // Question was not attempted - create zero score entry
        completeData.push({
          questionId: question.questionId,
          type:
            question.category?.name?.toLowerCase().replace(/\s+/g, "-") ||
            "unknown",
          score: 0,
          maxScore: question.maxScore || 1,
          percentage: 0,
          attempted: false,
          questionData: question,
          userAnswers: null,
          correctAnswers: question.correctAnswers || null,
          section: section, // Set section to match the expected section
        });
      }
    });

    return completeData;
  };

  // Callback functions to receive results from ResultSection components
  const handleReadingResults = (results) => {
    console.log("Reading results received:", results);
    setReadingResults(results);
  };

  const handleListeningResults = (results) => {
    console.log("Listening results received:", results);
    setListeningResults(results);
  };

  // Helper function to create complete question results including unattempted questions
  const createCompleteQuestionResults = useCallback(() => {
    const allQuestionResults = [];

    // Helper function to find user answer for a question
    const findUserAnswer = (questionId, section) => {
      switch (section) {
        case "speaking":
          return speakingScores.find(
            (score) => score.questionId === questionId
          );
        case "writing":
          return [...writingScores, ...essayScores].find(
            (score) => score.questionId === questionId
          );
        case "reading":
          return readingScores.find((score) => score.questionId === questionId);
        case "listening":
          return listeningScores.find(
            (score) => score.questionId === questionId
          );
        default:
          return null;
      }
    };

    // Process all sections
    Object.keys(allMockTestQuestions).forEach((section) => {
      const sectionQuestions = allMockTestQuestions[section];

      sectionQuestions.forEach((question) => {
        const userAnswer = findUserAnswer(question.questionId, section);

        if (userAnswer) {
          // Question was attempted - use actual score
          let score = 0;
          let maxScore = 1;

          if (section === "speaking") {
            score = calculateOverallScore(userAnswer.pteScores);
            maxScore = 100;
          } else if (section === "writing") {
            score = userAnswer.additionalProps?.scoreResponse?.totalScore || 0;
            maxScore = userAnswer.additionalProps?.scoreResponse?.maxScore || 1;
          } else {
            score = userAnswer.score || 0;
            maxScore = userAnswer.maxScore || 1;
          }

          allQuestionResults.push({
            questionId: question.questionId,
            type:
              question.category?.name?.toLowerCase().replace(/\s+/g, "-") ||
              question.type ||
              "unknown",
            score: score,
            maxScore: maxScore,
            section: section,
            attempted: true,
          });
        } else {
          // Question was not attempted - score is 0
          allQuestionResults.push({
            questionId: question.questionId,
            type:
              question.category?.name?.toLowerCase().replace(/\s+/g, "-") ||
              question.type ||
              "unknown",
            score: 0,
            maxScore: question.maxScore || 1,
            section: section,
            attempted: false,
          });
        }
      });
    });

    return allQuestionResults;
  }, [
    speakingScores,
    writingScores,
    essayScores,
    readingScores,
    listeningScores,
    allMockTestQuestions,
  ]);

  // Add this useEffect to calculate overall scores using PTE cross-sectional methodology
  useEffect(() => {
    const calculateOverallScoresForAll = () => {
      // Initialize PTE scoring engine
      const pteScoring = new PTECrossSectionalScoring();

      // Get complete question results including unattempted questions
      const allQuestionResults = createCompleteQuestionResults();

      // Calculate cross-sectional scores
      const crossSectionalScores =
        pteScoring.aggregateScores(allQuestionResults);
      const overallScore =
        pteScoring.calculateOverallScore(crossSectionalScores);

      console.log("PTE Cross-Sectional Scores calculated:", {
        crossSectionalScores,
        overallScore,
        allQuestionResults: allQuestionResults.length,
      });

      // Update state with cross-sectional scores
      setCrossSectionalScores({
        ...crossSectionalScores,
        overall: overallScore,
      });

      setOverallScores({
        speaking: crossSectionalScores.speaking,
        writing: crossSectionalScores.writing,
        reading: crossSectionalScores.reading,
        listening: crossSectionalScores.listening,
        overall: overallScore,
      });
    };

    calculateOverallScoresForAll();
  }, [
    speakingScores,
    writingScores,
    essayScores,
    readingScores,
    listeningScores,
    allMockTestQuestions,
    createCompleteQuestionResults,
  ]);

  const handleTabChange = (event, newValue) => {
    setValue(newValue);
  };

  const handleQuestionClick = (question) => {
    setSelectedQuestion(question);
    setShowDetailedScores(true);
  };

  const handleWritingQuestionClick = (question) => {
    setSelectedWritingQuestion(question);
    setShowWritingDetailedScores(true);
  };

  const handleEssayQuestionClick = (question) => {
    setSelectedEssayQuestion(question);
    setShowEssayDetailedScores(true);
  };

  const handleReadingQuestionClick = (question) => {
    console.log("Reading question clicked:", question);
    try {
      setSelectedReadingQuestion(question);
      setShowReadingDetailedScores(true);
    } catch (error) {
      console.error("Error in handleReadingQuestionClick:", error);
    }
  };

  const handleListeningQuestionClick = (question) => {
    console.log("Listening question clicked:", question);
    try {
      setSelectedListeningQuestion(question);
      setShowListeningDetailedScores(true);
    } catch (error) {
      console.error("Error in handleListeningQuestionClick:", error);
    }
  };

  const averageScores = calculateAverageScores();
  const writingAverageScores = calculateWritingAverageScores();
  const essayAverageScores = calculateEssayAverageScores();

  const chartData = Object.entries(taskTypeStats).map(([type, scores]) => ({
    name: type,
    pronunciation: scores.pronunciation,
    fluency: scores.fluency,
    content: scores.content,
  }));

  // Chart data for writing (summarize written text)
  const writingChartData =
    writingScores.length > 0
      ? [
          {
            name: "Summarize Written Text",
            content: writingAverageScores?.content || 0,
            form: writingAverageScores?.form || 0,
            grammar: writingAverageScores?.grammar || 0,
            vocabulary: writingAverageScores?.vocabulary || 0,
          },
        ]
      : [];

  // Chart data for essays
  const essayChartData =
    essayScores.length > 0
      ? [
          {
            name: "Essay Writing",
            content: essayAverageScores?.content || 0,
            form: essayAverageScores?.form || 0,
            grammar: essayAverageScores?.grammar || 0,
            spelling: essayAverageScores?.spelling || 0,
            vocabularyRange: essayAverageScores?.vocabularyRange || 0,
            generalLinguisticRange:
              essayAverageScores?.generalLinguisticRange || 0,
            developmentStructureCoherence:
              essayAverageScores?.developmentStructureCoherence || 0,
          },
        ]
      : [];

  // Combined writing chart data
  const combinedWritingChartData = [
    ...(writingScores.length > 0 ? writingChartData : []),
    ...(essayScores.length > 0 ? essayChartData : []),
  ];

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="100vh"
        marginTop="20px"
      >
        <PTEScoreLoader />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ marginTop: "140px" }}>
      <Paper elevation={3} sx={{ borderRadius: 2 }}>
        {/* Header with attempt information */}
        <Box sx={{ p: 3, borderBottom: "1px solid #e0e0e0" }}>
          <Typography
            variant="h4"
            sx={{ color: "#140342", fontWeight: 700, mb: 1 }}
          >
            PTE Practice Test Results
          </Typography>
          {attemptNumber && (
            <Typography variant="h6" sx={{ color: "#666", mb: 2 }}>
              Attempt {attemptNumber}
            </Typography>
          )}

          {/* Attempt History Selector */}
          {attemptHistory.length > 1 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" sx={{ color: "#666", mb: 1 }}>
                View Other Attempts:
              </Typography>
              <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                {attemptHistory.map((attempt) => (
                  <Button
                    key={attempt.attemptNumber}
                    variant={
                      attempt.attemptNumber === attemptNumber
                        ? "contained"
                        : "outlined"
                    }
                    size="small"
                    onClick={() => {
                      const currentUrl = new URL(window.location);
                      currentUrl.searchParams.set(
                        "attempt",
                        attempt.attemptNumber
                      );
                      navigate(currentUrl.pathname + currentUrl.search);
                    }}
                    sx={{
                      backgroundColor:
                        attempt.attemptNumber === attemptNumber
                          ? "#140342"
                          : "transparent",
                      color:
                        attempt.attemptNumber === attemptNumber
                          ? "white"
                          : "#140342",
                      borderColor: "#140342",
                      "&:hover": {
                        backgroundColor:
                          attempt.attemptNumber === attemptNumber
                            ? "#1e0a5c"
                            : "rgba(20, 3, 66, 0.1)",
                      },
                    }}
                  >
                    Attempt {attempt.attemptNumber}
                    {attempt.status === "completed" && " ✓"}
                  </Button>
                ))}
              </Box>
            </Box>
          )}
        </Box>

        <Tabs
          value={value}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          centered
          sx={{
            "& .MuiTab-root": {
              fontWeight: 600,
              fontSize: "1.1rem",
              minWidth: 120,
              color: "#140342",
            },
            "& .Mui-selected": {
              color: "#140342 !important",
            },
            "& .MuiTabs-indicator": {
              backgroundColor: "#140342",
            },
          }}
        >
          <Tab label="Overall" />
          <Tab label="Speaking" />
          <Tab label="Writing" />
          <Tab label="Reading" />
          <Tab label="Listening" />
        </Tabs>

        {/* Overall Tab */}
        <TabPanel value={value} index={0}>
          <StyledCard>
            <CardContent>
              <StyledTypography variant="h5" gutterBottom>
                Overall Test Performance
              </StyledTypography>
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} md={6}>
                  <ScoreDisplay
                    sx={{
                      backgroundColor: "#140342",
                      color: "white",
                      minHeight: "150px",
                    }}
                  >
                    <Typography
                      variant="h2"
                      sx={{ fontWeight: 700, mb: 1, color: "white" }}
                    >
                      {overallScores.overall}
                    </Typography>
                    <Typography
                      variant="h5"
                      sx={{ fontWeight: 600, color: "white" }}
                    >
                      Overall Score
                    </Typography>
                    <Typography variant="body1" sx={{ mt: 1, opacity: 0.9 }}>
                      Out of 90
                    </Typography>
                  </ScoreDisplay>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <ScoreDisplay>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {overallScores.speaking}
                        </Typography>
                        <StyledSubTypography>Speaking</StyledSubTypography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={6}>
                      <ScoreDisplay>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {overallScores.writing}
                        </Typography>
                        <StyledSubTypography>Writing</StyledSubTypography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={6}>
                      <ScoreDisplay>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {overallScores.reading}
                        </Typography>
                        <StyledSubTypography>Reading</StyledSubTypography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={6}>
                      <ScoreDisplay>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {overallScores.listening}
                        </Typography>
                        <StyledSubTypography>Listening</StyledSubTypography>
                      </ScoreDisplay>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>

              {/* Performance Chart */}
              <StyledTypography variant="h6" gutterBottom sx={{ mt: 4 }}>
                PTE Cross-Sectional Skill Scores
              </StyledTypography>
              <Typography variant="body2" sx={{ mb: 3, color: "#666" }}>
                These scores reflect the official PTE cross-sectional scoring
                methodology where questions contribute to multiple skills.
              </Typography>
              <Box height={300}>
                <ResponsiveContainer>
                  <BarChart
                    data={[
                      { name: "Speaking", score: overallScores.speaking },
                      { name: "Writing", score: overallScores.writing },
                      { name: "Reading", score: overallScores.reading },
                      { name: "Listening", score: overallScores.listening },
                    ]}
                  >
                    <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.1} />
                    <XAxis
                      dataKey="name"
                      tick={{
                        fontSize: "1rem",
                        fontWeight: 500,
                        fill: "#140342",
                      }}
                    />
                    <YAxis
                      domain={[0, 90]}
                      tick={{
                        fontSize: "1rem",
                        fontWeight: 500,
                        fill: "#140342",
                      }}
                    />
                    <Tooltip
                      contentStyle={{
                        borderRadius: "8px",
                        fontWeight: 500,
                        fontSize: "1rem",
                        color: "#140342",
                      }}
                    />
                    <Bar dataKey="score" fill="#140342" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </StyledCard>

          {/* Certificate Section */}
          <StyledCard>
            <CardContent>
              <PTECertificate
                studentName="Practice Test Student" // You can get this from user data
                overallScore={overallScores.overall}
                speakingScore={overallScores.speaking}
                writingScore={overallScores.writing}
                readingScore={overallScores.reading}
                listeningScore={overallScores.listening}
                testDate={new Date().toLocaleDateString()}
                attemptNumber={attemptNumber || 1}
              />
            </CardContent>
          </StyledCard>
        </TabPanel>

        <TabPanel value={value} index={1}>
          {allMockTestQuestions.speaking.length > 0 ? (
            (() => {
              const completeData = createCompleteSectionData(
                "speaking",
                speakingScores
              );
              console.log("Speaking complete data:", completeData);

              // Calculate averages including unattempted questions
              const calculateCompleteSpeakingAverages = () => {
                if (completeData.length === 0) return null;

                const attemptedQuestions = completeData.filter(
                  (q) => q.attempted
                );
                if (attemptedQuestions.length === 0) {
                  return {
                    pronunciation: 0,
                    fluency: 0,
                    content: 0,
                    overall: 0,
                  };
                }

                const sum = attemptedQuestions.reduce(
                  (acc, curr) => ({
                    pronunciation:
                      acc.pronunciation + (curr.pteScores?.pronunciation || 0),
                    fluency: acc.fluency + (curr.pteScores?.fluency || 0),
                    content: acc.content + (curr.pteScores?.content || 0),
                  }),
                  { pronunciation: 0, fluency: 0, content: 0 }
                );

                const averages = {
                  pronunciation: Math.round(
                    sum.pronunciation / attemptedQuestions.length
                  ),
                  fluency: Math.round(sum.fluency / attemptedQuestions.length),
                  content: Math.round(sum.content / attemptedQuestions.length),
                };

                averages.overall = calculateOverallScore(averages);
                return averages;
              };

              const completeSpeakingAverages =
                calculateCompleteSpeakingAverages();

              return (
                <>
                  <StyledCard>
                    <CardContent>
                      <StyledTypography variant="h5" gutterBottom>
                        Overall Speaking Performance
                      </StyledTypography>
                      {completeSpeakingAverages && (
                        <Grid container spacing={3}>
                          <Grid item xs={3}>
                            <ScoreDisplay>
                              <Typography
                                variant="h3"
                                sx={{ fontWeight: 700, color: "#140342" }}
                              >
                                {completeSpeakingAverages.pronunciation}
                              </Typography>
                              <StyledSubTypography>
                                Pronunciation
                              </StyledSubTypography>
                            </ScoreDisplay>
                          </Grid>
                          <Grid item xs={3}>
                            <ScoreDisplay>
                              <Typography
                                variant="h3"
                                sx={{ fontWeight: 700, color: "#140342" }}
                              >
                                {completeSpeakingAverages.fluency}
                              </Typography>
                              <StyledSubTypography>Fluency</StyledSubTypography>
                            </ScoreDisplay>
                          </Grid>
                          <Grid item xs={3}>
                            <ScoreDisplay>
                              <Typography
                                variant="h3"
                                sx={{ fontWeight: 700, color: "#140342" }}
                              >
                                {completeSpeakingAverages.content}
                              </Typography>
                              <StyledSubTypography>Content</StyledSubTypography>
                            </ScoreDisplay>
                          </Grid>
                          <Grid item xs={3}>
                            <ScoreDisplay>
                              <Typography
                                variant="h3"
                                sx={{ fontWeight: 700, color: "#140342" }}
                              >
                                {completeSpeakingAverages.overall}
                              </Typography>
                              <StyledSubTypography>Overall</StyledSubTypography>
                            </ScoreDisplay>
                          </Grid>
                        </Grid>
                      )}
                    </CardContent>
                  </StyledCard>

                  <StyledCard>
                    <CardContent>
                      <StyledTypography variant="h5" gutterBottom>
                        Performance by Task Type
                      </StyledTypography>
                      <Box height={400}>
                        <ResponsiveContainer>
                          <BarChart data={chartData}>
                            <CartesianGrid
                              strokeDasharray="3 3"
                              strokeOpacity={0.1}
                            />
                            <XAxis
                              dataKey="name"
                              tick={{
                                fontSize: "1rem",
                                fontWeight: 500,
                                fill: "#140342",
                              }}
                            />
                            <YAxis
                              tick={{
                                fontSize: "1rem",
                                fontWeight: 500,
                                fill: "#140342",
                              }}
                            />
                            <Tooltip
                              contentStyle={{
                                borderRadius: "8px",
                                fontWeight: 500,
                                fontSize: "1rem",
                                color: "#140342",
                              }}
                            />
                            <Legend
                              wrapperStyle={{
                                fontWeight: 500,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            />
                            <Bar
                              dataKey="pronunciation"
                              fill="#140342"
                              name="Pronunciation"
                              radius={[4, 4, 0, 0]}
                            />
                            <Bar
                              dataKey="fluency"
                              fill="#281C63"
                              name="Fluency"
                              radius={[4, 4, 0, 0]}
                            />
                            <Bar
                              dataKey="content"
                              fill="#3D3584"
                              name="Content"
                              radius={[4, 4, 0, 0]}
                            />
                          </BarChart>
                        </ResponsiveContainer>
                      </Box>
                    </CardContent>
                  </StyledCard>

                  <StyledBox>
                    <StyledTypography variant="h5" gutterBottom>
                      Question Details
                    </StyledTypography>
                    <TableContainer
                      component={Paper}
                      sx={{
                        borderRadius: 2,
                        boxShadow: 2,
                      }}
                    >
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Task Type
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Status
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Pronunciation
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Fluency
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Content
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Actions
                            </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {completeData.map((question, index) => (
                            <TableRow key={question.questionId || index}>
                              <TableCell
                                sx={{
                                  fontSize: "1rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                {question.questionData?.category?.name ||
                                  question.taskType ||
                                  "Other"}
                              </TableCell>
                              <TableCell align="right">
                                {question.attempted ? (
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      color: "#4caf50",
                                      fontWeight: 600,
                                      fontSize: "0.875rem",
                                    }}
                                  >
                                    ATTEMPTED
                                  </Typography>
                                ) : (
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      color: "#f44336",
                                      fontWeight: 600,
                                      fontSize: "0.875rem",
                                    }}
                                  >
                                    NOT ATTEMPTED
                                  </Typography>
                                )}
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontSize: "1rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                {question.attempted
                                  ? question.pteScores?.pronunciation || 0
                                  : 0}
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontSize: "1rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                {question.attempted
                                  ? question.pteScores?.fluency || 0
                                  : 0}
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontSize: "1rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                {question.attempted
                                  ? question.pteScores?.content || 0
                                  : 0}
                              </TableCell>
                              <TableCell align="right">
                                {question.attempted ? (
                                  <Button
                                    variant="contained"
                                    size="medium"
                                    onClick={() =>
                                      handleQuestionClick(question)
                                    }
                                    sx={{
                                      backgroundColor: "#140342",
                                      "&:hover": {
                                        backgroundColor: "#281C63",
                                      },
                                      fontWeight: 600,
                                      borderRadius: "8px",
                                    }}
                                  >
                                    Details
                                  </Button>
                                ) : (
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      color: "#666",
                                      fontStyle: "italic",
                                    }}
                                  >
                                    No details
                                  </Typography>
                                )}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </StyledBox>
                </>
              );
            })()
          ) : (
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography variant="h6" color="text.secondary">
                No speaking questions in this mock test.
              </Typography>
            </Box>
          )}

          <Dialog
            open={showDetailedScores}
            onClose={() => setShowDetailedScores(false)}
            maxWidth="md"
            fullWidth
            PaperProps={{
              sx: {
                borderRadius: 2,
              },
            }}
          >
            <DialogTitle>
              <StyledTypography variant="h5">
                Detailed Scores - {selectedQuestion?.taskType || "Question"}
              </StyledTypography>
            </DialogTitle>
            <DialogContent>
              {selectedQuestion && (
                <Box p={3}>
                  <Grid container spacing={3}>
                    <Grid item xs={4}>
                      <ScoreDisplay>
                        <StyledSubTypography>Pronunciation</StyledSubTypography>
                        <Typography
                          variant="h3"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {selectedQuestion.pteScores.pronunciation}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={4}>
                      <ScoreDisplay>
                        <StyledSubTypography>Fluency</StyledSubTypography>
                        <Typography
                          variant="h3"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {selectedQuestion.pteScores.fluency}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={4}>
                      <ScoreDisplay>
                        <StyledSubTypography>Content</StyledSubTypography>
                        <Typography
                          variant="h3"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {selectedQuestion.pteScores.content || 0}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={12}>
                      <ScoreDisplay>
                        <StyledSubTypography>Overall</StyledSubTypography>
                        <Typography
                          variant="h3"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {selectedQuestion?.pteScores
                            ? calculateOverallScore(selectedQuestion.pteScores)
                            : 0}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                  </Grid>
                  {selectedQuestion.media?.url && (
                    <Box mt={4}>
                      <StyledTypography variant="h6" gutterBottom>
                        Recording
                      </StyledTypography>
                      <audio
                        controls
                        style={{
                          width: "100%",
                          borderRadius: "8px",
                        }}
                      >
                        <source
                          src={selectedQuestion.media.url}
                          type="audio/wav"
                        />
                        Your browser does not support the audio element.
                      </audio>
                    </Box>
                  )}
                </Box>
              )}
            </DialogContent>
          </Dialog>
        </TabPanel>

        <TabPanel value={value} index={2}>
          {(() => {
            const completeData = createCompleteSectionData("writing", [
              ...writingScores,
              ...essayScores,
            ]);

            if (allMockTestQuestions.writing.length === 0) {
              return (
                <Box sx={{ textAlign: "center", py: 4 }}>
                  <Typography variant="h6" color="text.secondary">
                    No writing questions in this mock test.
                  </Typography>
                </Box>
              );
            }

            console.log("Writing complete data:", completeData);

            // Calculate averages including unattempted questions
            const calculateCompleteWritingAverages = () => {
              const attemptedQuestions = completeData.filter(
                (q) => q.attempted
              );
              if (attemptedQuestions.length === 0) {
                return {
                  summarizeWrittenText: {
                    content: 0,
                    form: 0,
                    grammar: 0,
                    vocabulary: 0,
                    overall: 0,
                  },
                  essay: {
                    content: 0,
                    form: 0,
                    grammar: 0,
                    spelling: 0,
                    vocabularyRange: 0,
                    generalLinguisticRange: 0,
                    developmentStructureCoherence: 0,
                    overall: 0,
                  },
                };
              }

              const writingQuestions = attemptedQuestions.filter(
                (q) =>
                  q.questionData?.category?.name
                    ?.toLowerCase()
                    .includes("summarize") ||
                  q.taskType?.toLowerCase().includes("summarize")
              );
              const essayQuestions = attemptedQuestions.filter(
                (q) =>
                  q.questionData?.category?.name
                    ?.toLowerCase()
                    .includes("essay") ||
                  q.taskType?.toLowerCase().includes("essay")
              );

              // Calculate writing averages
              let writingAverages = {
                content: 0,
                form: 0,
                grammar: 0,
                vocabulary: 0,
                overall: 0,
              };
              if (writingQuestions.length > 0) {
                const writingSum = writingQuestions.reduce(
                  (acc, curr) => ({
                    content: acc.content + (curr.pteScores?.content || 0),
                    form: acc.form + (curr.pteScores?.form || 0),
                    grammar: acc.grammar + (curr.pteScores?.grammar || 0),
                    vocabulary:
                      acc.vocabulary + (curr.pteScores?.vocabulary || 0),
                  }),
                  { content: 0, form: 0, grammar: 0, vocabulary: 0 }
                );

                writingAverages = {
                  content: Math.round(
                    writingSum.content / writingQuestions.length
                  ),
                  form: Math.round(writingSum.form / writingQuestions.length),
                  grammar: Math.round(
                    writingSum.grammar / writingQuestions.length
                  ),
                  vocabulary: Math.round(
                    writingSum.vocabulary / writingQuestions.length
                  ),
                };
                writingAverages.overall =
                  calculateOverallScore(writingAverages);
              }

              // Calculate essay averages
              let essayAverages = {
                content: 0,
                form: 0,
                grammar: 0,
                spelling: 0,
                vocabularyRange: 0,
                generalLinguisticRange: 0,
                developmentStructureCoherence: 0,
                overall: 0,
              };
              if (essayQuestions.length > 0) {
                const essaySum = essayQuestions.reduce(
                  (acc, curr) => ({
                    content: acc.content + (curr.pteScores?.content || 0),
                    form: acc.form + (curr.pteScores?.form || 0),
                    grammar: acc.grammar + (curr.pteScores?.grammar || 0),
                    spelling: acc.spelling + (curr.pteScores?.spelling || 0),
                    vocabularyRange:
                      acc.vocabularyRange +
                      (curr.pteScores?.vocabularyRange || 0),
                    generalLinguisticRange:
                      acc.generalLinguisticRange +
                      (curr.pteScores?.generalLinguisticRange || 0),
                    developmentStructureCoherence:
                      acc.developmentStructureCoherence +
                      (curr.pteScores?.developmentStructureCoherence || 0),
                  }),
                  {
                    content: 0,
                    form: 0,
                    grammar: 0,
                    spelling: 0,
                    vocabularyRange: 0,
                    generalLinguisticRange: 0,
                    developmentStructureCoherence: 0,
                  }
                );

                essayAverages = {
                  content: Math.round(essaySum.content / essayQuestions.length),
                  form: Math.round(essaySum.form / essayQuestions.length),
                  grammar: Math.round(essaySum.grammar / essayQuestions.length),
                  spelling: Math.round(
                    essaySum.spelling / essayQuestions.length
                  ),
                  vocabularyRange: Math.round(
                    essaySum.vocabularyRange / essayQuestions.length
                  ),
                  generalLinguisticRange: Math.round(
                    essaySum.generalLinguisticRange / essayQuestions.length
                  ),
                  developmentStructureCoherence: Math.round(
                    essaySum.developmentStructureCoherence /
                      essayQuestions.length
                  ),
                };
                essayAverages.overall =
                  calculateOverallEssayScore(essayAverages);
              }

              return {
                summarizeWrittenText: writingAverages,
                essay: essayAverages,
              };
            };

            const completeWritingAverages = calculateCompleteWritingAverages();

            return (
              <>
                {/* Combined Writing Performance Overview */}
                <StyledCard>
                  <CardContent>
                    <StyledTypography variant="h5" gutterBottom>
                      Overall Writing Performance
                    </StyledTypography>
                    <Grid container spacing={2}>
                      {completeData.some(
                        (q) =>
                          q.questionData?.category?.name
                            ?.toLowerCase()
                            .includes("summarize") ||
                          q.taskType?.toLowerCase().includes("summarize")
                      ) && (
                        <Grid item xs={12} md={6}>
                          <Paper
                            sx={{
                              p: 2,
                              backgroundColor: "#f8f9fa",
                              borderRadius: 2,
                            }}
                          >
                            <Typography
                              variant="h6"
                              sx={{ fontWeight: 600, color: "#140342", mb: 2 }}
                            >
                              Summarize Written Text
                            </Typography>
                            <Grid container spacing={2}>
                              <Grid item xs={6}>
                                <ScoreDisplay
                                  sx={{ minHeight: "auto", p: 1.5 }}
                                >
                                  <Typography
                                    variant="h5"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {
                                      completeWritingAverages
                                        .summarizeWrittenText.content
                                    }
                                  </Typography>
                                  <Typography variant="body2">
                                    Content
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                              <Grid item xs={6}>
                                <ScoreDisplay
                                  sx={{ minHeight: "auto", p: 1.5 }}
                                >
                                  <Typography
                                    variant="h5"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {
                                      completeWritingAverages
                                        .summarizeWrittenText.form
                                    }
                                  </Typography>
                                  <Typography variant="body2">Form</Typography>
                                </ScoreDisplay>
                              </Grid>
                              <Grid item xs={6}>
                                <ScoreDisplay
                                  sx={{ minHeight: "auto", p: 1.5 }}
                                >
                                  <Typography
                                    variant="h5"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {
                                      completeWritingAverages
                                        .summarizeWrittenText.grammar
                                    }
                                  </Typography>
                                  <Typography variant="body2">
                                    Grammar
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                              <Grid item xs={6}>
                                <ScoreDisplay
                                  sx={{ minHeight: "auto", p: 1.5 }}
                                >
                                  <Typography
                                    variant="h5"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {
                                      completeWritingAverages
                                        .summarizeWrittenText.vocabulary
                                    }
                                  </Typography>
                                  <Typography variant="body2">
                                    Vocabulary
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                            </Grid>
                          </Paper>
                        </Grid>
                      )}

                      {completeData.some(
                        (q) =>
                          q.questionData?.category?.name
                            ?.toLowerCase()
                            .includes("essay") ||
                          q.taskType?.toLowerCase().includes("essay")
                      ) && (
                        <Grid item xs={12} md={6}>
                          <Paper
                            sx={{
                              p: 2,
                              backgroundColor: "#f0f7ff",
                              borderRadius: 2,
                            }}
                          >
                            <Typography
                              variant="h6"
                              sx={{ fontWeight: 600, color: "#140342", mb: 2 }}
                            >
                              Essay Writing
                            </Typography>
                            <Grid container spacing={1}>
                              <Grid item xs={4}>
                                <ScoreDisplay sx={{ minHeight: "auto", p: 1 }}>
                                  <Typography
                                    variant="h6"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {completeWritingAverages.essay.content}
                                  </Typography>
                                  <Typography variant="caption">
                                    Content
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                              <Grid item xs={4}>
                                <ScoreDisplay sx={{ minHeight: "auto", p: 1 }}>
                                  <Typography
                                    variant="h6"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {completeWritingAverages.essay.form}
                                  </Typography>
                                  <Typography variant="caption">
                                    Form
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                              <Grid item xs={4}>
                                <ScoreDisplay sx={{ minHeight: "auto", p: 1 }}>
                                  <Typography
                                    variant="h6"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {completeWritingAverages.essay.grammar}
                                  </Typography>
                                  <Typography variant="caption">
                                    Grammar
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                              <Grid item xs={4}>
                                <ScoreDisplay sx={{ minHeight: "auto", p: 1 }}>
                                  <Typography
                                    variant="h6"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {completeWritingAverages.essay.spelling}
                                  </Typography>
                                  <Typography variant="caption">
                                    Spelling
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                              <Grid item xs={4}>
                                <ScoreDisplay sx={{ minHeight: "auto", p: 1 }}>
                                  <Typography
                                    variant="h6"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {
                                      completeWritingAverages.essay
                                        .vocabularyRange
                                    }
                                  </Typography>
                                  <Typography variant="caption">
                                    Vocab Range
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                              <Grid item xs={4}>
                                <ScoreDisplay sx={{ minHeight: "auto", p: 1 }}>
                                  <Typography
                                    variant="h6"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {
                                      completeWritingAverages.essay
                                        .generalLinguisticRange
                                    }
                                  </Typography>
                                  <Typography variant="caption">
                                    Linguistic
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                              <Grid item xs={12}>
                                <ScoreDisplay sx={{ minHeight: "auto", p: 1 }}>
                                  <Typography
                                    variant="h6"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {
                                      completeWritingAverages.essay
                                        .developmentStructureCoherence
                                    }
                                  </Typography>
                                  <Typography variant="caption">
                                    Development & Coherence
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                            </Grid>
                          </Paper>
                        </Grid>
                      )}
                    </Grid>
                  </CardContent>
                </StyledCard>

                {/* Writing Performance Chart */}
                <StyledCard>
                  <CardContent>
                    <StyledTypography variant="h5" gutterBottom>
                      Writing Performance Chart
                    </StyledTypography>
                    <Box height={400}>
                      <ResponsiveContainer>
                        <BarChart data={combinedWritingChartData}>
                          <CartesianGrid
                            strokeDasharray="3 3"
                            strokeOpacity={0.1}
                          />
                          <XAxis
                            dataKey="name"
                            tick={{
                              fontSize: "1rem",
                              fontWeight: 500,
                              fill: "#140342",
                            }}
                          />
                          <YAxis
                            tick={{
                              fontSize: "1rem",
                              fontWeight: 500,
                              fill: "#140342",
                            }}
                          />
                          <Tooltip
                            contentStyle={{
                              borderRadius: "8px",
                              fontWeight: 500,
                              fontSize: "1rem",
                              color: "#140342",
                            }}
                          />
                          <Legend
                            wrapperStyle={{
                              fontWeight: 500,
                              fontSize: "1.1rem",
                              color: "#140342",
                            }}
                          />
                          <Bar
                            dataKey="content"
                            fill="#140342"
                            name="Content"
                            radius={[4, 4, 0, 0]}
                          />
                          <Bar
                            dataKey="form"
                            fill="#281C63"
                            name="Form"
                            radius={[4, 4, 0, 0]}
                          />
                          <Bar
                            dataKey="grammar"
                            fill="#3D3584"
                            name="Grammar"
                            radius={[4, 4, 0, 0]}
                          />
                          {essayScores.length > 0 && (
                            <>
                              <Bar
                                dataKey="spelling"
                                fill="#4E4BA5"
                                name="Spelling"
                                radius={[4, 4, 0, 0]}
                              />
                              <Bar
                                dataKey="vocabularyRange"
                                fill="#5B5BC6"
                                name="Vocab Range"
                                radius={[4, 4, 0, 0]}
                              />
                              <Bar
                                dataKey="generalLinguisticRange"
                                fill="#6B6BE7"
                                name="Linguistic"
                                radius={[4, 4, 0, 0]}
                              />
                              <Bar
                                dataKey="developmentStructureCoherence"
                                fill="#7B7BF8"
                                name="Development"
                                radius={[4, 4, 0, 0]}
                              />
                            </>
                          )}
                          {writingScores.length > 0 && (
                            <Bar
                              dataKey="vocabulary"
                              fill="#4E4BA5"
                              name="Vocabulary"
                              radius={[4, 4, 0, 0]}
                            />
                          )}
                        </BarChart>
                      </ResponsiveContainer>
                    </Box>
                  </CardContent>
                </StyledCard>

                {/* Summarize Written Text Table */}
                {writingScores.length > 0 && (
                  <StyledCard>
                    <CardContent>
                      <Typography
                        variant="h6"
                        sx={{ fontWeight: 600, color: "#140342", mb: 2 }}
                      >
                        Summarize Written Text
                      </Typography>
                      <TableContainer
                        component={Paper}
                        sx={{ borderRadius: 2, boxShadow: 2, mb: 4 }}
                      >
                        <Table>
                          <TableHead>
                            <TableRow>
                              <TableCell
                                sx={{
                                  fontWeight: 600,
                                  fontSize: "1.1rem",
                                  color: "#140342",
                                }}
                              >
                                Task Type
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontWeight: 600,
                                  fontSize: "1.1rem",
                                  color: "#140342",
                                }}
                              >
                                Content
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontWeight: 600,
                                  fontSize: "1.1rem",
                                  color: "#140342",
                                }}
                              >
                                Form
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontWeight: 600,
                                  fontSize: "1.1rem",
                                  color: "#140342",
                                }}
                              >
                                Grammar
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontWeight: 600,
                                  fontSize: "1.1rem",
                                  color: "#140342",
                                }}
                              >
                                Vocabulary
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontWeight: 600,
                                  fontSize: "1.1rem",
                                  color: "#140342",
                                }}
                              >
                                Actions
                              </TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {writingScores.map((score, index) => (
                              <TableRow key={score.answerId || index}>
                                <TableCell
                                  sx={{
                                    fontSize: "1rem",
                                    fontWeight: 500,
                                    color: "#140342",
                                  }}
                                >
                                  Summarize Written Text
                                </TableCell>
                                <TableCell
                                  align="right"
                                  sx={{
                                    fontSize: "1rem",
                                    fontWeight: 500,
                                    color: "#140342",
                                  }}
                                >
                                  {getScoreValue(score.pteScores?.content)}
                                </TableCell>
                                <TableCell
                                  align="right"
                                  sx={{
                                    fontSize: "1rem",
                                    fontWeight: 500,
                                    color: "#140342",
                                  }}
                                >
                                  {getScoreValue(score.pteScores?.form)}
                                </TableCell>
                                <TableCell
                                  align="right"
                                  sx={{
                                    fontSize: "1rem",
                                    fontWeight: 500,
                                    color: "#140342",
                                  }}
                                >
                                  {getScoreValue(score.pteScores?.grammar)}
                                </TableCell>
                                <TableCell
                                  align="right"
                                  sx={{
                                    fontSize: "1rem",
                                    fontWeight: 500,
                                    color: "#140342",
                                  }}
                                >
                                  {getScoreValue(score.pteScores?.vocabulary)}
                                </TableCell>
                                <TableCell align="right">
                                  <Button
                                    variant="contained"
                                    size="medium"
                                    onClick={() =>
                                      handleWritingQuestionClick(score)
                                    }
                                    sx={{
                                      backgroundColor: "#140342",
                                      "&:hover": { backgroundColor: "#281C63" },
                                      fontWeight: 600,
                                      borderRadius: "8px",
                                    }}
                                  >
                                    Details
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </CardContent>
                  </StyledCard>
                )}

                {/* Essay Writing Table */}
                {essayScores.length > 0 && (
                  <StyledCard>
                    <CardContent>
                      <Typography
                        variant="h6"
                        sx={{ fontWeight: 600, color: "#140342", mb: 2 }}
                      >
                        Essay Writing
                      </Typography>
                      <TableContainer
                        component={Paper}
                        sx={{ borderRadius: 2, boxShadow: 2 }}
                      >
                        <Table>
                          <TableHead>
                            <TableRow>
                              <TableCell
                                sx={{
                                  fontWeight: 600,
                                  fontSize: "1rem",
                                  color: "#140342",
                                }}
                              >
                                Task Type
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontWeight: 600,
                                  fontSize: "0.9rem",
                                  color: "#140342",
                                }}
                              >
                                Content
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontWeight: 600,
                                  fontSize: "0.9rem",
                                  color: "#140342",
                                }}
                              >
                                Form
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontWeight: 600,
                                  fontSize: "0.9rem",
                                  color: "#140342",
                                }}
                              >
                                Grammar
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontWeight: 600,
                                  fontSize: "0.9rem",
                                  color: "#140342",
                                }}
                              >
                                Spelling
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontWeight: 600,
                                  fontSize: "0.9rem",
                                  color: "#140342",
                                }}
                              >
                                Vocab Range
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontWeight: 600,
                                  fontSize: "0.9rem",
                                  color: "#140342",
                                }}
                              >
                                Linguistic
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontWeight: 600,
                                  fontSize: "0.9rem",
                                  color: "#140342",
                                }}
                              >
                                Development
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontWeight: 600,
                                  fontSize: "0.9rem",
                                  color: "#140342",
                                }}
                              >
                                Actions
                              </TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {essayScores.map((score, index) => (
                              <TableRow key={score.answerId || index}>
                                <TableCell
                                  sx={{
                                    fontSize: "1rem",
                                    fontWeight: 500,
                                    color: "#140342",
                                  }}
                                >
                                  Essay Writing
                                </TableCell>
                                <TableCell
                                  align="right"
                                  sx={{
                                    fontSize: "0.9rem",
                                    fontWeight: 500,
                                    color: "#140342",
                                  }}
                                >
                                  {getScoreValue(score.pteScores?.content)}
                                </TableCell>
                                <TableCell
                                  align="right"
                                  sx={{
                                    fontSize: "0.9rem",
                                    fontWeight: 500,
                                    color: "#140342",
                                  }}
                                >
                                  {getScoreValue(score.pteScores?.form)}
                                </TableCell>
                                <TableCell
                                  align="right"
                                  sx={{
                                    fontSize: "0.9rem",
                                    fontWeight: 500,
                                    color: "#140342",
                                  }}
                                >
                                  {getScoreValue(score.pteScores?.grammar)}
                                </TableCell>
                                <TableCell
                                  align="right"
                                  sx={{
                                    fontSize: "0.9rem",
                                    fontWeight: 500,
                                    color: "#140342",
                                  }}
                                >
                                  {getScoreValue(score.pteScores?.spelling)}
                                </TableCell>
                                <TableCell
                                  align="right"
                                  sx={{
                                    fontSize: "0.9rem",
                                    fontWeight: 500,
                                    color: "#140342",
                                  }}
                                >
                                  {getScoreValue(
                                    score.pteScores?.vocabularyRange
                                  )}
                                </TableCell>
                                <TableCell
                                  align="right"
                                  sx={{
                                    fontSize: "0.9rem",
                                    fontWeight: 500,
                                    color: "#140342",
                                  }}
                                >
                                  {getScoreValue(
                                    score.pteScores?.generalLinguisticRange
                                  )}
                                </TableCell>
                                <TableCell
                                  align="right"
                                  sx={{
                                    fontSize: "0.9rem",
                                    fontWeight: 500,
                                    color: "#140342",
                                  }}
                                >
                                  {getScoreValue(
                                    score.pteScores
                                      ?.developmentStructureCoherence
                                  )}
                                </TableCell>
                                <TableCell align="right">
                                  <Button
                                    variant="contained"
                                    size="medium"
                                    onClick={() =>
                                      handleEssayQuestionClick(score)
                                    }
                                    sx={{
                                      backgroundColor: "#140342",
                                      "&:hover": { backgroundColor: "#281C63" },
                                      fontWeight: 600,
                                      borderRadius: "8px",
                                    }}
                                  >
                                    Details
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </CardContent>
                  </StyledCard>
                )}

                {/* Writing Questions Table */}
                <StyledBox>
                  <StyledTypography variant="h5" gutterBottom>
                    Question Details
                  </StyledTypography>
                  <TableContainer
                    component={Paper}
                    sx={{
                      borderRadius: 2,
                      boxShadow: 2,
                    }}
                  >
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell
                            sx={{
                              fontWeight: 600,
                              fontSize: "1.1rem",
                              color: "#140342",
                            }}
                          >
                            Task Type
                          </TableCell>
                          <TableCell
                            align="right"
                            sx={{
                              fontWeight: 600,
                              fontSize: "1.1rem",
                              color: "#140342",
                            }}
                          >
                            Status
                          </TableCell>
                          <TableCell
                            align="right"
                            sx={{
                              fontWeight: 600,
                              fontSize: "1.1rem",
                              color: "#140342",
                            }}
                          >
                            Score
                          </TableCell>
                          <TableCell
                            align="right"
                            sx={{
                              fontWeight: 600,
                              fontSize: "1.1rem",
                              color: "#140342",
                            }}
                          >
                            Actions
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {completeData.map((question, index) => (
                          <TableRow key={question.questionId || index}>
                            <TableCell
                              sx={{
                                fontSize: "1rem",
                                fontWeight: 500,
                                color: "#140342",
                              }}
                            >
                              {question.questionData?.category?.name ||
                                question.taskType ||
                                "Other"}
                            </TableCell>
                            <TableCell align="right">
                              {question.attempted ? (
                                <Typography
                                  variant="body2"
                                  sx={{
                                    color: "#4caf50",
                                    fontWeight: 600,
                                    fontSize: "0.875rem",
                                  }}
                                >
                                  ATTEMPTED
                                </Typography>
                              ) : (
                                <Typography
                                  variant="body2"
                                  sx={{
                                    color: "#f44336",
                                    fontWeight: 600,
                                    fontSize: "0.875rem",
                                  }}
                                >
                                  NOT ATTEMPTED
                                </Typography>
                              )}
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontSize: "1rem",
                                fontWeight: 500,
                                color: "#140342",
                              }}
                            >
                              {question.attempted
                                ? question.questionData?.category?.name
                                    ?.toLowerCase()
                                    .includes("essay")
                                  ? calculateOverallEssayScore(
                                      question.pteScores || {}
                                    )
                                  : calculateOverallScore(
                                      question.pteScores || {}
                                    )
                                : 0}
                            </TableCell>
                            <TableCell align="right">
                              {question.attempted ? (
                                <Button
                                  variant="contained"
                                  size="medium"
                                  onClick={() => {
                                    if (
                                      question.questionData?.category?.name
                                        ?.toLowerCase()
                                        .includes("essay")
                                    ) {
                                      handleEssayQuestionClick(question);
                                    } else {
                                      handleWritingQuestionClick(question);
                                    }
                                  }}
                                  sx={{
                                    backgroundColor: "#140342",
                                    "&:hover": {
                                      backgroundColor: "#281C63",
                                    },
                                    fontWeight: 600,
                                    borderRadius: "8px",
                                  }}
                                >
                                  Details
                                </Button>
                              ) : (
                                <Typography
                                  variant="body2"
                                  sx={{
                                    color: "#666",
                                    fontStyle: "italic",
                                  }}
                                >
                                  No details
                                </Typography>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </StyledBox>
              </>
            );
          })()}

          {/* Summarize Written Text Dialog */}
          <Dialog
            open={showWritingDetailedScores}
            onClose={() => setShowWritingDetailedScores(false)}
            maxWidth="lg"
            fullWidth
            PaperProps={{ sx: { borderRadius: 2 } }}
          >
            <DialogTitle>
              <StyledTypography variant="h5">
                Detailed Scores - Summarize Written Text
              </StyledTypography>
            </DialogTitle>
            <DialogContent>
              {selectedWritingQuestion && (
                <Box p={3}>
                  <Grid container spacing={3}>
                    <Grid item xs={2.4}>
                      <ScoreDisplay>
                        <StyledSubTypography>Content</StyledSubTypography>
                        <Typography
                          variant="h3"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedWritingQuestion.pteScores?.content
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedWritingQuestion.pteScores?.content
                            ?.maxScore || 2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={2.4}>
                      <ScoreDisplay>
                        <StyledSubTypography>Form</StyledSubTypography>
                        <Typography
                          variant="h3"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedWritingQuestion.pteScores?.form
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedWritingQuestion.pteScores?.form?.maxScore ||
                            2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={2.4}>
                      <ScoreDisplay>
                        <StyledSubTypography>Grammar</StyledSubTypography>
                        <Typography
                          variant="h3"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedWritingQuestion.pteScores?.grammar
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedWritingQuestion.pteScores?.grammar
                            ?.maxScore || 2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={2.4}>
                      <ScoreDisplay>
                        <StyledSubTypography>Vocabulary</StyledSubTypography>
                        <Typography
                          variant="h3"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedWritingQuestion.pteScores?.vocabulary
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedWritingQuestion.pteScores?.vocabulary
                            ?.maxScore || 2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={2.4}>
                      <ScoreDisplay>
                        <StyledSubTypography>Total</StyledSubTypography>
                        <Typography
                          variant="h3"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {selectedWritingQuestion.pteScores?.totalScore || 0}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedWritingQuestion.pteScores?.maxScore || 8}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                  </Grid>

                  {/* Display suggestions for each category */}
                  <Box sx={{ mt: 4 }}>
                    <StyledTypography variant="h6" gutterBottom>
                      Detailed Feedback
                    </StyledTypography>
                    <Grid container spacing={2}>
                      {["content", "form", "grammar", "vocabulary"].map(
                        (category) => (
                          <Grid item xs={12} sm={6} key={category}>
                            <Paper
                              sx={{
                                p: 2,
                                backgroundColor: "#f8f9fa",
                                borderRadius: 2,
                              }}
                            >
                              <Typography
                                variant="subtitle2"
                                sx={{
                                  fontWeight: 600,
                                  color: "#140342",
                                  mb: 1,
                                }}
                              >
                                {category.charAt(0).toUpperCase() +
                                  category.slice(1)}{" "}
                                Feedback:
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{ color: "#666" }}
                              >
                                {selectedWritingQuestion.pteScores?.[category]
                                  ?.suggestion ||
                                  "No specific feedback available for this category."}
                              </Typography>
                            </Paper>
                          </Grid>
                        )
                      )}
                    </Grid>
                  </Box>

                  {/* Display student answer and word count */}
                  <Box sx={{ mt: 4 }}>
                    <StyledTypography variant="h6" gutterBottom>
                      Your Summary
                    </StyledTypography>
                    <Paper
                      sx={{ p: 2, backgroundColor: "#f8f9fa", borderRadius: 2 }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          mb: 2,
                        }}
                      >
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          Word Count:{" "}
                          {selectedWritingQuestion.pteScores?.wordCount || 0}
                        </Typography>
                        <Typography variant="body2" sx={{ color: "#666" }}>
                          Required: 5-75 words
                        </Typography>
                      </Box>
                      <Typography
                        variant="body1"
                        sx={{ fontStyle: "italic", lineHeight: 1.6 }}
                      >
                        "
                        {selectedWritingQuestion.answer || "No answer provided"}
                        "
                      </Typography>
                    </Paper>
                  </Box>

                  {/* Display annotated text if available */}
                  {selectedWritingQuestion.pteScores?.annotatedText && (
                    <Box sx={{ mt: 4 }}>
                      <StyledTypography variant="h6" gutterBottom>
                        Annotated Analysis
                      </StyledTypography>
                      <Paper
                        sx={{
                          p: 2,
                          backgroundColor: "#f8f9fa",
                          borderRadius: 2,
                        }}
                      >
                        <div
                          dangerouslySetInnerHTML={{
                            __html:
                              selectedWritingQuestion.pteScores.annotatedText,
                          }}
                          style={{ lineHeight: 1.6, fontSize: "1rem" }}
                        />
                      </Paper>
                    </Box>
                  )}

                  {/* Display original text for reference */}
                  {selectedWritingQuestion.prompt && (
                    <Box sx={{ mt: 4 }}>
                      <StyledTypography variant="h6" gutterBottom>
                        Original Text (Reference)
                      </StyledTypography>
                      <Paper
                        sx={{
                          p: 2,
                          backgroundColor: "#e3f2fd",
                          borderRadius: 2,
                        }}
                      >
                        <Typography variant="body2" sx={{ lineHeight: 1.6 }}>
                          {selectedWritingQuestion.prompt}
                        </Typography>
                      </Paper>
                    </Box>
                  )}
                </Box>
              )}
            </DialogContent>
          </Dialog>

          {/* Essay Writing Dialog */}
          <Dialog
            open={showEssayDetailedScores}
            onClose={() => setShowEssayDetailedScores(false)}
            maxWidth="lg"
            fullWidth
            PaperProps={{ sx: { borderRadius: 2 } }}
          >
            <DialogTitle>
              <StyledTypography variant="h5">
                Detailed Scores - Essay Writing
              </StyledTypography>
            </DialogTitle>
            <DialogContent>
              {selectedEssayQuestion && (
                <Box p={3}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6} md={3}>
                      <ScoreDisplay>
                        <StyledSubTypography>Content</StyledSubTypography>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedEssayQuestion.pteScores?.content
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedEssayQuestion.pteScores?.content?.maxScore ||
                            3}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <ScoreDisplay>
                        <StyledSubTypography>Form</StyledSubTypography>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(selectedEssayQuestion.pteScores?.form)}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedEssayQuestion.pteScores?.form?.maxScore || 2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <ScoreDisplay>
                        <StyledSubTypography>Grammar</StyledSubTypography>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedEssayQuestion.pteScores?.grammar
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedEssayQuestion.pteScores?.grammar?.maxScore ||
                            2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <ScoreDisplay>
                        <StyledSubTypography>Spelling</StyledSubTypography>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedEssayQuestion.pteScores?.spelling
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedEssayQuestion.pteScores?.spelling
                            ?.maxScore || 2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <ScoreDisplay>
                        <StyledSubTypography>
                          Vocabulary Range
                        </StyledSubTypography>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedEssayQuestion.pteScores?.vocabularyRange
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedEssayQuestion.pteScores?.vocabularyRange
                            ?.maxScore || 2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <ScoreDisplay>
                        <StyledSubTypography>
                          Linguistic Range
                        </StyledSubTypography>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedEssayQuestion.pteScores
                              ?.generalLinguisticRange
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedEssayQuestion.pteScores
                            ?.generalLinguisticRange?.maxScore || 2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <ScoreDisplay>
                        <StyledSubTypography>Development</StyledSubTypography>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedEssayQuestion.pteScores
                              ?.developmentStructureCoherence
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedEssayQuestion.pteScores
                            ?.developmentStructureCoherence?.maxScore || 2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={12}>
                      <ScoreDisplay>
                        <StyledSubTypography>Total Score</StyledSubTypography>
                        <Typography
                          variant="h2"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {selectedEssayQuestion.pteScores?.totalScore || 0}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max: {selectedEssayQuestion.pteScores?.maxScore || 15}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                  </Grid>

                  {/* Display suggestions for each category */}
                  <Box sx={{ mt: 4 }}>
                    <StyledTypography variant="h6" gutterBottom>
                      Detailed Feedback
                    </StyledTypography>
                    <Grid container spacing={2}>
                      {[
                        "content",
                        "form",
                        "grammar",
                        "spelling",
                        "vocabularyRange",
                        "generalLinguisticRange",
                        "developmentStructureCoherence",
                      ].map((category) => (
                        <Grid item xs={12} sm={6} md={4} key={category}>
                          <Paper
                            sx={{
                              p: 2,
                              backgroundColor: "#f8f9fa",
                              borderRadius: 2,
                            }}
                          >
                            <Typography
                              variant="subtitle2"
                              sx={{ fontWeight: 600, color: "#140342", mb: 1 }}
                            >
                              {category === "vocabularyRange"
                                ? "Vocabulary Range"
                                : category === "generalLinguisticRange"
                                ? "Linguistic Range"
                                : category === "developmentStructureCoherence"
                                ? "Development & Structure"
                                : category.charAt(0).toUpperCase() +
                                  category.slice(1)}{" "}
                              Feedback:
                            </Typography>
                            <Typography variant="body2" sx={{ color: "#666" }}>
                              {selectedEssayQuestion.pteScores?.[category]
                                ?.suggestion ||
                                "No specific feedback available for this category."}
                            </Typography>
                          </Paper>
                        </Grid>
                      ))}
                    </Grid>
                  </Box>

                  {/* Relevance Check */}
                  {selectedEssayQuestion.pteScores?.relevanceCheck && (
                    <Box sx={{ mt: 4 }}>
                      <StyledTypography variant="h6" gutterBottom>
                        Relevance Check
                      </StyledTypography>
                      <Paper
                        sx={{
                          p: 2,
                          backgroundColor: selectedEssayQuestion.pteScores
                            .relevanceCheck.isRelevant
                            ? "#e8f5e8"
                            : "#ffebee",
                          borderRadius: 2,
                          border: `1px solid ${
                            selectedEssayQuestion.pteScores.relevanceCheck
                              .isRelevant
                              ? "#4caf50"
                              : "#f44336"
                          }`,
                        }}
                      >
                        <Typography
                          variant="subtitle2"
                          sx={{
                            fontWeight: 600,
                            color: selectedEssayQuestion.pteScores
                              .relevanceCheck.isRelevant
                              ? "#2e7d32"
                              : "#d32f2f",
                            mb: 1,
                          }}
                        >
                          {selectedEssayQuestion.pteScores.relevanceCheck
                            .isRelevant
                            ? "✓ Relevant to Prompt"
                            : "✗ Not Relevant to Prompt"}
                        </Typography>
                        <Typography variant="body2" sx={{ color: "#666" }}>
                          {
                            selectedEssayQuestion.pteScores.relevanceCheck
                              .explanation
                          }
                        </Typography>
                      </Paper>
                    </Box>
                  )}

                  {/* Display student answer and word count */}
                  <Box sx={{ mt: 4 }}>
                    <StyledTypography variant="h6" gutterBottom>
                      Your Essay
                    </StyledTypography>
                    <Paper
                      sx={{ p: 2, backgroundColor: "#f8f9fa", borderRadius: 2 }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          mb: 2,
                        }}
                      >
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          Word Count:{" "}
                          {selectedEssayQuestion.pteScores?.wordCount || 0}
                        </Typography>
                        <Typography variant="body2" sx={{ color: "#666" }}>
                          Recommended: 200-300 words
                        </Typography>
                      </Box>
                      <Typography
                        variant="body1"
                        sx={{ lineHeight: 1.8, whiteSpace: "pre-wrap" }}
                      >
                        {selectedEssayQuestion.answer || "No answer provided"}
                      </Typography>
                    </Paper>
                  </Box>

                  {/* Display annotated text if available */}
                  {selectedEssayQuestion.pteScores?.annotatedText && (
                    <Box sx={{ mt: 4 }}>
                      <StyledTypography variant="h6" gutterBottom>
                        Annotated Analysis
                      </StyledTypography>
                      <Paper
                        sx={{
                          p: 2,
                          backgroundColor: "#f8f9fa",
                          borderRadius: 2,
                        }}
                      >
                        <div
                          dangerouslySetInnerHTML={{
                            __html:
                              selectedEssayQuestion.pteScores.annotatedText,
                          }}
                          style={{ lineHeight: 1.8, fontSize: "1rem" }}
                        />
                      </Paper>
                    </Box>
                  )}

                  {/* Display essay prompt for reference */}
                  {selectedEssayQuestion.prompt && (
                    <Box sx={{ mt: 4 }}>
                      <StyledTypography variant="h6" gutterBottom>
                        Essay Prompt (Reference)
                      </StyledTypography>
                      <Paper
                        sx={{
                          p: 2,
                          backgroundColor: "#e3f2fd",
                          borderRadius: 2,
                        }}
                      >
                        <Typography variant="body2" sx={{ lineHeight: 1.6 }}>
                          {selectedEssayQuestion.prompt}
                        </Typography>
                      </Paper>
                    </Box>
                  )}
                </Box>
              )}
            </DialogContent>
          </Dialog>
        </TabPanel>

        {/* Reading Tab */}
        <TabPanel value={value} index={3}>
          {allMockTestQuestions.reading.length > 0 ? (
            (() => {
              const completeData = createCompleteSectionData(
                "reading",
                readingScores
              );
              console.log("Reading complete data:", completeData);

              return (
                <>
                  {/* Reading Performance Overview */}
                  <StyledCard>
                    <CardContent>
                      <StyledTypography variant="h5" gutterBottom>
                        Reading Performance
                      </StyledTypography>
                      <Grid container spacing={3}>
                        <Grid item xs={12} md={6}>
                          <Paper
                            sx={{
                              p: 2,
                              backgroundColor: "#f8f9fa",
                              borderRadius: 2,
                            }}
                          >
                            <Typography
                              variant="h6"
                              sx={{ fontWeight: 600, color: "#140342", mb: 2 }}
                            >
                              Overall Reading Score
                            </Typography>
                            <Typography
                              variant="h3"
                              sx={{ fontWeight: 700, color: "#140342" }}
                            >
                              {calculateSectionAverage(completeData, "reading")}
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{ color: "#666", mt: 1 }}
                            >
                              Average across all reading questions
                            </Typography>
                          </Paper>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Paper
                            sx={{
                              p: 2,
                              backgroundColor: "#e8f5e8",
                              borderRadius: 2,
                            }}
                          >
                            <Typography
                              variant="h6"
                              sx={{ fontWeight: 600, color: "#140342", mb: 2 }}
                            >
                              Questions Attempted
                            </Typography>
                            <Typography
                              variant="h3"
                              sx={{ fontWeight: 700, color: "#140342" }}
                            >
                              {completeData.filter((q) => q.attempted).length} /{" "}
                              {completeData.length}
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{ color: "#666", mt: 1 }}
                            >
                              Completion rate:{" "}
                              {Math.round(
                                (completeData.filter((q) => q.attempted)
                                  .length /
                                  completeData.length) *
                                  100
                              )}
                              %
                            </Typography>
                          </Paper>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </StyledCard>

                  {/* Reading Questions Table */}
                  <StyledBox>
                    <StyledTypography variant="h6" gutterBottom>
                      Reading Questions Details
                    </StyledTypography>
                    <TableContainer component={Paper}>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Question Type
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Status
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Score
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Actions
                            </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {completeData.map((question, index) => (
                            <TableRow key={question.questionId || index}>
                              <TableCell
                                sx={{
                                  fontSize: "1rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                {question.questionData?.category?.name ||
                                  question.taskType ||
                                  "Reading Question"}
                              </TableCell>
                              <TableCell align="right">
                                {question.attempted ? (
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      color: "#4caf50",
                                      fontWeight: 600,
                                      fontSize: "0.875rem",
                                    }}
                                  >
                                    ATTEMPTED
                                  </Typography>
                                ) : (
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      color: "#f44336",
                                      fontWeight: 600,
                                      fontSize: "0.875rem",
                                    }}
                                  >
                                    NOT ATTEMPTED
                                  </Typography>
                                )}
                              </TableCell>
                              <TableCell align="right">
                                <Typography
                                  variant="h6"
                                  sx={{
                                    fontWeight: 600,
                                    color: question.attempted
                                      ? "#140342"
                                      : "#999",
                                  }}
                                >
                                  {question.attempted
                                    ? question.pteScores?.overall ||
                                      question.score ||
                                      0
                                    : "0"}
                                </Typography>
                              </TableCell>
                              <TableCell align="right">
                                {question.attempted ? (
                                  <Button
                                    variant="contained"
                                    size="medium"
                                    onClick={() =>
                                      handleReadingQuestionClick(question)
                                    }
                                    sx={{
                                      backgroundColor: "#140342",
                                      "&:hover": {
                                        backgroundColor: "#281C63",
                                      },
                                      fontWeight: 600,
                                      borderRadius: "8px",
                                    }}
                                  >
                                    Details
                                  </Button>
                                ) : (
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      color: "#666",
                                      fontStyle: "italic",
                                    }}
                                  >
                                    No details
                                  </Typography>
                                )}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </StyledBox>
                </>
              );
            })()
          ) : (
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography variant="h6" color="text.secondary">
                No reading questions in this mock test.
              </Typography>
            </Box>
          )}
        </TabPanel>

        {/* Listening Tab */}
        <TabPanel value={value} index={4}>
          {allMockTestQuestions.listening.length > 0 ? (
            (() => {
              const completeData = createCompleteSectionData(
                "listening",
                listeningScores
              );
              console.log("Listening complete data:", completeData);

              return (
                <>
                  {/* Listening Performance Overview */}
                  <StyledCard>
                    <CardContent>
                      <StyledTypography variant="h5" gutterBottom>
                        Listening Performance
                      </StyledTypography>
                      <Grid container spacing={3}>
                        <Grid item xs={12} md={6}>
                          <Paper
                            sx={{
                              p: 2,
                              backgroundColor: "#f8f9fa",
                              borderRadius: 2,
                            }}
                          >
                            <Typography
                              variant="h6"
                              sx={{ fontWeight: 600, color: "#140342", mb: 2 }}
                            >
                              Overall Listening Score
                            </Typography>
                            <Typography
                              variant="h3"
                              sx={{ fontWeight: 700, color: "#140342" }}
                            >
                              {calculateSectionAverage(
                                completeData,
                                "listening"
                              )}
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{ color: "#666", mt: 1 }}
                            >
                              Average across all listening questions
                            </Typography>
                          </Paper>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Paper
                            sx={{
                              p: 2,
                              backgroundColor: "#e8f5e8",
                              borderRadius: 2,
                            }}
                          >
                            <Typography
                              variant="h6"
                              sx={{ fontWeight: 600, color: "#140342", mb: 2 }}
                            >
                              Questions Attempted
                            </Typography>
                            <Typography
                              variant="h3"
                              sx={{ fontWeight: 700, color: "#140342" }}
                            >
                              {completeData.filter((q) => q.attempted).length} /{" "}
                              {completeData.length}
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{ color: "#666", mt: 1 }}
                            >
                              Completion rate:{" "}
                              {Math.round(
                                (completeData.filter((q) => q.attempted)
                                  .length /
                                  completeData.length) *
                                  100
                              )}
                              %
                            </Typography>
                          </Paper>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </StyledCard>

                  {/* Listening Questions Table */}
                  <StyledBox>
                    <StyledTypography variant="h6" gutterBottom>
                      Listening Questions Details
                    </StyledTypography>
                    <TableContainer component={Paper}>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Question Type
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Status
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Score
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Actions
                            </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {completeData.map((question, index) => (
                            <TableRow key={question.questionId || index}>
                              <TableCell
                                sx={{
                                  fontSize: "1rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                {question.questionData?.category?.name ||
                                  question.taskType ||
                                  "Listening Question"}
                              </TableCell>
                              <TableCell align="right">
                                {question.attempted ? (
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      color: "#4caf50",
                                      fontWeight: 600,
                                      fontSize: "0.875rem",
                                    }}
                                  >
                                    ATTEMPTED
                                  </Typography>
                                ) : (
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      color: "#f44336",
                                      fontWeight: 600,
                                      fontSize: "0.875rem",
                                    }}
                                  >
                                    NOT ATTEMPTED
                                  </Typography>
                                )}
                              </TableCell>
                              <TableCell align="right">
                                <Typography
                                  variant="h6"
                                  sx={{
                                    fontWeight: 600,
                                    color: question.attempted
                                      ? "#140342"
                                      : "#999",
                                  }}
                                >
                                  {question.attempted
                                    ? question.pteScores?.overall ||
                                      question.score ||
                                      0
                                    : "0"}
                                </Typography>
                              </TableCell>
                              <TableCell align="right">
                                {question.attempted ? (
                                  <Button
                                    variant="contained"
                                    size="medium"
                                    onClick={() =>
                                      handleListeningQuestionClick(question)
                                    }
                                    sx={{
                                      backgroundColor: "#140342",
                                      "&:hover": {
                                        backgroundColor: "#281C63",
                                      },
                                      fontWeight: 600,
                                      borderRadius: "8px",
                                    }}
                                  >
                                    Details
                                  </Button>
                                ) : (
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      color: "#666",
                                      fontStyle: "italic",
                                    }}
                                  >
                                    No details
                                  </Typography>
                                )}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </StyledBox>
                </>
              );
            })()
          ) : (
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography variant="h6" color="text.secondary">
                No listening questions in this mock test.
              </Typography>
            </Box>
          )}
        </TabPanel>
      </Paper>

      {/* Reading Question Details Dialog */}
      <Dialog
        open={showReadingDetailedScores}
        onClose={() => setShowReadingDetailedScores(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h5" sx={{ fontWeight: 600, color: "#140342" }}>
            Reading Question Details
          </Typography>
        </DialogTitle>
        <DialogContent>
          {selectedReadingQuestion ? (
            <Box sx={{ mt: 2 }}>
              {/* Question Information */}
              <Paper
                sx={{
                  p: 3,
                  mb: 3,
                  backgroundColor: "#f8f9fa",
                  borderRadius: 2,
                }}
              >
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Question Information
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      Question Type:
                    </Typography>
                    <Typography variant="body1">
                      {selectedReadingQuestion.questionData?.category?.name ||
                        selectedReadingQuestion.taskType ||
                        "Reading Question"}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      Overall Score:
                    </Typography>
                    <Typography
                      variant="h4"
                      sx={{ fontWeight: 700, color: "#140342" }}
                    >
                      {selectedReadingQuestion.pteScores?.overall ||
                        selectedReadingQuestion.score ||
                        0}
                    </Typography>
                  </Grid>
                </Grid>
              </Paper>

              {/* User Answer */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Your Answer
                </Typography>
                <Paper
                  sx={{
                    p: 2,
                    backgroundColor: "#e3f2fd",
                    borderRadius: 2,
                  }}
                >
                  <Typography
                    variant="body1"
                    sx={{ lineHeight: 1.8, whiteSpace: "pre-wrap" }}
                  >
                    {selectedReadingQuestion.answer ||
                      (selectedReadingQuestion.useranswers &&
                      Array.isArray(selectedReadingQuestion.useranswers)
                        ? selectedReadingQuestion.useranswers
                            .map((ans) => ans.optionText || ans)
                            .join(", ")
                        : typeof selectedReadingQuestion.useranswers ===
                          "string"
                        ? selectedReadingQuestion.useranswers
                        : typeof selectedReadingQuestion.useranswers ===
                            "object" &&
                          selectedReadingQuestion.useranswers !== null
                        ? selectedReadingQuestion.useranswers.answer ||
                          selectedReadingQuestion.useranswers.transcript ||
                          JSON.stringify(selectedReadingQuestion.useranswers)
                        : selectedReadingQuestion.useranswers) ||
                      "No answer provided"}
                  </Typography>
                </Paper>
              </Box>

              {/* Correct Answer */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Correct Answer
                </Typography>
                <Paper
                  sx={{
                    p: 2,
                    backgroundColor: "#e8f5e8",
                    borderRadius: 2,
                  }}
                >
                  <Typography
                    variant="body1"
                    sx={{ lineHeight: 1.8, whiteSpace: "pre-wrap" }}
                  >
                    {selectedReadingQuestion.questionData?.options?.find(
                      (opt) => opt.isCorrect
                    )?.optionText ||
                      selectedReadingQuestion.questionData?.dropdownOptions
                        ?.map(
                          (dropdown) =>
                            `${dropdown.blank}: ${dropdown.correctAnswer}`
                        )
                        .join(", ") ||
                      (selectedReadingQuestion.questionData?.incorrectWords &&
                        `Incorrect words to highlight: ${selectedReadingQuestion.questionData.incorrectWords
                          .map(
                            (item) => `"${item.incorrect}" → "${item.correct}"`
                          )
                          .join(", ")}`) ||
                      selectedReadingQuestion.questionData?.transcript ||
                      selectedReadingQuestion.questionData?.answer ||
                      selectedReadingQuestion.correctAnswer?.transcript ||
                      selectedReadingQuestion.correctAnswer?.answer ||
                      selectedReadingQuestion.correctAnswer ||
                      "Correct answer not available"}
                  </Typography>
                </Paper>
              </Box>

              {/* Question Content */}
              {selectedReadingQuestion.questionData?.content && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                    Question Content
                  </Typography>
                  <Paper
                    sx={{
                      p: 2,
                      backgroundColor: "#f5f5f5",
                      borderRadius: 2,
                    }}
                  >
                    <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
                      {selectedReadingQuestion.questionData.content}
                    </Typography>
                  </Paper>
                </Box>
              )}
            </Box>
          ) : (
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography variant="body1" color="text.secondary">
                No question data available
              </Typography>
            </Box>
          )}
        </DialogContent>
      </Dialog>

      {/* Listening Question Details Dialog */}
      <Dialog
        open={showListeningDetailedScores}
        onClose={() => setShowListeningDetailedScores(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h5" sx={{ fontWeight: 600, color: "#140342" }}>
            Listening Question Details
          </Typography>
        </DialogTitle>
        <DialogContent>
          {selectedListeningQuestion ? (
            <Box sx={{ mt: 2 }}>
              {/* Question Information */}
              <Paper
                sx={{
                  p: 3,
                  mb: 3,
                  backgroundColor: "#f8f9fa",
                  borderRadius: 2,
                }}
              >
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Question Information
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      Question Type:
                    </Typography>
                    <Typography variant="body1">
                      {selectedListeningQuestion.questionData?.category?.name ||
                        selectedListeningQuestion.taskType ||
                        "Listening Question"}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      Overall Score:
                    </Typography>
                    <Typography
                      variant="h4"
                      sx={{ fontWeight: 700, color: "#140342" }}
                    >
                      {selectedListeningQuestion.pteScores?.overall ||
                        selectedListeningQuestion.score ||
                        0}
                    </Typography>
                  </Grid>
                </Grid>
              </Paper>

              {/* User Answer */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Your Answer
                </Typography>
                <Paper
                  sx={{
                    p: 2,
                    backgroundColor: "#e3f2fd",
                    borderRadius: 2,
                  }}
                >
                  <Typography
                    variant="body1"
                    sx={{ lineHeight: 1.8, whiteSpace: "pre-wrap" }}
                  >
                    {selectedListeningQuestion.answer ||
                      (selectedListeningQuestion.useranswers &&
                      Array.isArray(selectedListeningQuestion.useranswers)
                        ? selectedListeningQuestion.useranswers
                            .map((ans) => ans.optionText || ans)
                            .join(", ")
                        : typeof selectedListeningQuestion.useranswers ===
                          "string"
                        ? selectedListeningQuestion.useranswers
                        : typeof selectedListeningQuestion.useranswers ===
                            "object" &&
                          selectedListeningQuestion.useranswers !== null
                        ? selectedListeningQuestion.useranswers.answer ||
                          selectedListeningQuestion.useranswers.transcript ||
                          JSON.stringify(selectedListeningQuestion.useranswers)
                        : selectedListeningQuestion.useranswers) ||
                      "No answer provided"}
                  </Typography>
                </Paper>
              </Box>

              {/* Correct Answer */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Correct Answer
                </Typography>
                <Paper
                  sx={{
                    p: 2,
                    backgroundColor: "#e8f5e8",
                    borderRadius: 2,
                  }}
                >
                  <Typography
                    variant="body1"
                    sx={{ lineHeight: 1.8, whiteSpace: "pre-wrap" }}
                  >
                    {selectedListeningQuestion.questionData?.options?.find(
                      (opt) => opt.isCorrect
                    )?.optionText ||
                      selectedListeningQuestion.questionData?.dropdownOptions
                        ?.map(
                          (dropdown) =>
                            `${dropdown.blank}: ${dropdown.correctAnswer}`
                        )
                        .join(", ") ||
                      (selectedListeningQuestion.questionData?.incorrectWords &&
                        `Incorrect words to highlight: ${selectedListeningQuestion.questionData.incorrectWords
                          .map(
                            (item) => `"${item.incorrect}" → "${item.correct}"`
                          )
                          .join(", ")}`) ||
                      selectedListeningQuestion.questionData?.transcript ||
                      selectedListeningQuestion.questionData?.answer ||
                      selectedListeningQuestion.correctAnswer?.transcript ||
                      selectedListeningQuestion.correctAnswer?.answer ||
                      selectedListeningQuestion.correctAnswer ||
                      "Correct answer not available"}
                  </Typography>
                </Paper>
              </Box>

              {/* Question Content */}
              {selectedListeningQuestion.questionData?.content && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                    Question Content
                  </Typography>
                  <Paper
                    sx={{
                      p: 2,
                      backgroundColor: "#f5f5f5",
                      borderRadius: 2,
                    }}
                  >
                    <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
                      {selectedListeningQuestion.questionData.content}
                    </Typography>
                  </Paper>
                </Box>
              )}
            </Box>
          ) : (
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography variant="body1" color="text.secondary">
                No question data available
              </Typography>
            </Box>
          )}
        </DialogContent>
      </Dialog>
    </Container>
  );
};

ResultComponent.propTypes = {
  mockTestId: PropTypes.string.isRequired,
  userId: PropTypes.string.isRequired,
  attemptNumber: PropTypes.number,
};

export default ResultComponent;
