import React from "react";
import { <PERSON>, Button, Typography, Paper, Grid, Divider } from "@mui/material";
import { jsPDF } from "jspdf";
import html2canvas from "html2canvas";
import DownloadIcon from "@mui/icons-material/Download";
import CertificateIcon from "@mui/icons-material/EmojiEvents";
import { useAuth } from "../others/AuthContext";

const PTECertificate = ({
  overallScore,
  speakingScore,
  writingScore,
  readingScore,
  listeningScore,
  testDate,
  attemptNumber,
}) => {
  const { user } = useAuth();

  const getScoreLevel = (score) => {
    if (score >= 79) return { level: "Expert", color: "#4CAF50" };
    if (score >= 65) return { level: "Proficient", color: "#2196F3" };
    if (score >= 50) return { level: "Competent", color: "#FF9800" };
    if (score >= 36) return { level: "Modest", color: "#FF5722" };
    return { level: "Limited", color: "#F44336" };
  };

  const generateCertificate = async () => {
    // Create a new element for PDF generation that includes the heading
    const pdfContainer = document.createElement("div");
    pdfContainer.style.padding = "40px";
    pdfContainer.style.backgroundColor = "#ffffff";
    pdfContainer.style.width = "1200px";
    pdfContainer.style.fontFamily = "Arial, sans-serif";

    // Add heading
    const heading = document.createElement("div");
    heading.style.textAlign = "center";
    heading.style.marginBottom = "30px";
    heading.innerHTML = `
      <h1 style="font-size: 28px; font-weight: 600; color: #140342; margin: 0;">
        Practice Test Certificate
      </h1>
    `;

    // Clone the certificate element
    const certificateElement = document.getElementById("pte-certificate");
    const certificateClone = certificateElement.cloneNode(true);

    // Remove any absolute positioned elements that might cause issues
    certificateClone.style.position = "relative";
    certificateClone.style.margin = "0 auto";
    certificateClone.style.maxWidth = "1120px";

    pdfContainer.appendChild(heading);
    pdfContainer.appendChild(certificateClone);

    // Temporarily add to document for rendering
    document.body.appendChild(pdfContainer);

    try {
      // Hide the download button during PDF generation
      const downloadButton = document.querySelector(
        '[data-testid="download-button"]'
      );
      if (downloadButton) {
        downloadButton.style.display = "none";
      }

      const canvas = await html2canvas(pdfContainer, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: "#ffffff",
        width: 1200,
        height: 900,
        scrollX: 0,
        scrollY: 0,
      });

      const imgData = canvas.toDataURL("image/png", 1.0);
      const pdf = new jsPDF({
        orientation: "landscape",
        unit: "mm",
        format: "a4",
      });

      // Calculate dimensions to fit A4 landscape and center
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();

      // Calculate image dimensions maintaining aspect ratio
      const imgAspectRatio = canvas.width / canvas.height;
      const pdfAspectRatio = pdfWidth / pdfHeight;

      let imgWidth, imgHeight, xOffset, yOffset;

      if (imgAspectRatio > pdfAspectRatio) {
        // Image is wider than PDF page
        imgWidth = pdfWidth * 0.95; // Use 95% of page width
        imgHeight = imgWidth / imgAspectRatio;
        xOffset = (pdfWidth - imgWidth) / 2;
        yOffset = (pdfHeight - imgHeight) / 2;
      } else {
        // Image is taller than PDF page
        imgHeight = pdfHeight * 0.95; // Use 95% of page height
        imgWidth = imgHeight * imgAspectRatio;
        xOffset = (pdfWidth - imgWidth) / 2;
        yOffset = (pdfHeight - imgHeight) / 2;
      }

      pdf.addImage(imgData, "PNG", xOffset, yOffset, imgWidth, imgHeight);

      const studentName = user?.name || "Student";
      const fileName = `PTE-Certificate-${studentName.replace(
        /\s+/g,
        "-"
      )}-${testDate.replace(/\//g, "-")}.pdf`;
      pdf.save(fileName);

      // Show the download button again
      if (downloadButton) {
        downloadButton.style.display = "flex";
      }
    } catch (error) {
      console.error("Error generating certificate:", error);
      alert("Error generating certificate. Please try again.");
    } finally {
      // Remove the temporary container
      document.body.removeChild(pdfContainer);
    }
  };

  const overallLevel = getScoreLevel(overallScore);

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: "auto" }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Typography variant="h5" sx={{ fontWeight: 600, color: "#140342" }}>
          Practice Test Certificate
        </Typography>
        <Button
          data-testid="download-button"
          variant="contained"
          startIcon={<DownloadIcon />}
          onClick={generateCertificate}
          sx={{
            backgroundColor: "#140342",
            "&:hover": { backgroundColor: "#281C63" },
            fontWeight: 600,
            px: 3,
            py: 1,
          }}
        >
          Download Certificate
        </Button>
      </Box>

      {/* Certificate Preview */}
      <Paper
        id="pte-certificate"
        elevation={3}
        sx={{
          p: 4,
          backgroundColor: "#ffffff",
          border: "4px solid #140342",
          borderRadius: 3,
          position: "relative",
          height: "auto",
          maxWidth: "1200px",
          backgroundImage: `
            radial-gradient(circle at 25% 25%, rgba(20,3,66,0.03) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(20,3,66,0.03) 0%, transparent 50%)
          `,
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
        }}
      >
        {/* Header Section */}
        <Box sx={{ textAlign: "center", mb: 2 }}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              mb: 2,
            }}
          >
            <img
              src="/assets/img/general/logo.png"
              alt="DeepSight Academy Logo"
              style={{ height: "60px", width: "auto", marginRight: "12px" }}
              onError={(e) => {
                // Fallback to icon if logo fails to load
                e.target.style.display = "none";
                e.target.nextSibling.style.display = "inline-block";
              }}
            />
            <CertificateIcon
              sx={{
                fontSize: 40,
                color: "#140342",
                mr: 2,
                display: "none", // Initially hidden, shown if logo fails
              }}
            />
            <Typography
              variant="h3"
              sx={{
                fontWeight: 700,
                color: "#140342",
                fontFamily: "serif",
                fontSize: { xs: "1.8rem", md: "2.2rem" },
              }}
            >
              Deep Insight Academy
            </Typography>
          </Box>

          <Typography
            variant="h4"
            sx={{
              fontWeight: 600,
              color: "#281C63",
              mb: 1,
              fontSize: { xs: "1.2rem", md: "1.5rem" },
            }}
          >
            PTE Practice Test Certificate
          </Typography>

          <Divider
            sx={{
              backgroundColor: "#140342",
              height: 2,
              width: "200px",
              mx: "auto",
            }}
          />
        </Box>

        {/* Main Content Section */}
        <Box sx={{ flex: 1 }}>
          {/* Certificate Text */}
          <Box sx={{ textAlign: "center", mb: 3 }}>
            <Typography
              variant="body1"
              sx={{ mb: 1, color: "#666", fontSize: "1rem" }}
            >
              This is to certify that
            </Typography>

            <Typography
              variant="h4"
              sx={{
                fontWeight: 700,
                color: "#140342",
                mb: 2,
                borderBottom: "3px solid #140342",
                display: "inline-block",
                px: 2,
                py: 0.5,
                fontSize: { xs: "1.5rem", md: "2rem" },
              }}
            >
              {user?.name || "Student Name"}
            </Typography>

            <Typography
              variant="body1"
              sx={{ mb: 2, color: "#666", fontSize: "1rem" }}
            >
              has successfully completed the PTE Practice Test
            </Typography>

            <Typography
              variant="body1"
              sx={{ mb: 2, color: "#140342", fontWeight: 600 }}
            >
              Test Date: {testDate}
            </Typography>
          </Box>

          {/* Scores Section */}
          <Grid container spacing={3} sx={{ mb: 3, justifyContent: "center" }}>
            <Grid item xs={12} md={5}>
              <Paper
                elevation={8}
                sx={{
                  p: 4,
                  textAlign: "center",
                  backgroundColor: overallLevel.color,
                  color: "white",
                  borderRadius: 4,
                  position: "relative",
                  overflow: "hidden",
                  border: "3px solid rgba(255,255,255,0.2)",
                  boxShadow: `0 20px 40px rgba(${
                    overallLevel.color === "#4CAF50"
                      ? "76, 175, 80"
                      : overallLevel.color === "#2196F3"
                      ? "33, 150, 243"
                      : overallLevel.color === "#FF9800"
                      ? "255, 152, 0"
                      : overallLevel.color === "#FF5722"
                      ? "255, 87, 34"
                      : "244, 67, 54"
                  }, 0.3)`,
                  "&::before": {
                    content: '""',
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: `radial-gradient(circle at 30% 30%, rgba(255,255,255,0.15) 0%, transparent 50%)`,
                    pointerEvents: "none",
                  },
                  "&::after": {
                    content: '""',
                    position: "absolute",
                    top: -50,
                    right: -50,
                    width: 100,
                    height: 100,
                    background: "rgba(255,255,255,0.1)",
                    borderRadius: "50%",
                    pointerEvents: "none",
                  },
                }}
              >
                <Box sx={{ position: "relative", zIndex: 1 }}>
                  <Typography
                    variant="h1"
                    sx={{
                      fontWeight: 800,
                      mb: 1,
                      fontSize: "3.5rem",
                      textShadow: "0 4px 8px rgba(0,0,0,0.2)",
                      color: "white",
                    }}
                  >
                    {overallScore}
                  </Typography>
                  <Typography
                    variant="h5"
                    sx={{
                      fontWeight: 700,
                      mb: 0.5,
                      textShadow: "0 2px 4px rgba(0,0,0,0.2)",
                      letterSpacing: "0.5px",
                      color: "white",
                    }}
                  >
                    Overall Score
                  </Typography>
                  <Box
                    sx={{
                      display: "inline-block",
                      px: 2,
                      py: 0.5,
                      backgroundColor: "rgba(255,255,255,0.2)",
                      borderRadius: 20,
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(255,255,255,0.3)",
                    }}
                  >
                    <Typography
                      variant="body1"
                      sx={{
                        fontWeight: 600,
                        textShadow: "0 1px 2px rgba(0,0,0,0.2)",
                        color: "white",
                      }}
                    >
                      {overallLevel.level} Level
                    </Typography>
                  </Box>
                </Box>
              </Paper>
            </Grid>

            <Grid item xs={12} md={7}>
              <Grid container spacing={1.5}>
                <Grid item xs={6}>
                  <Paper
                    elevation={2}
                    sx={{
                      p: 2,
                      textAlign: "center",
                      backgroundColor: "#f8f9fa",
                      borderRadius: 1,
                      border: "1px solid #e0e0e0",
                    }}
                  >
                    <Typography
                      variant="h5"
                      sx={{ fontWeight: 700, color: "#140342", mb: 0.5 }}
                    >
                      {speakingScore}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "#666", fontWeight: 600 }}
                    >
                      Speaking
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper
                    elevation={2}
                    sx={{
                      p: 2,
                      textAlign: "center",
                      backgroundColor: "#f8f9fa",
                      borderRadius: 1,
                      border: "1px solid #e0e0e0",
                    }}
                  >
                    <Typography
                      variant="h5"
                      sx={{ fontWeight: 700, color: "#140342", mb: 0.5 }}
                    >
                      {writingScore}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "#666", fontWeight: 600 }}
                    >
                      Writing
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper
                    elevation={2}
                    sx={{
                      p: 2,
                      textAlign: "center",
                      backgroundColor: "#f8f9fa",
                      borderRadius: 1,
                      border: "1px solid #e0e0e0",
                    }}
                  >
                    <Typography
                      variant="h5"
                      sx={{ fontWeight: 700, color: "#140342", mb: 0.5 }}
                    >
                      {readingScore}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "#666", fontWeight: 600 }}
                    >
                      Reading
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper
                    elevation={2}
                    sx={{
                      p: 2,
                      textAlign: "center",
                      backgroundColor: "#f8f9fa",
                      borderRadius: 1,
                      border: "1px solid #e0e0e0",
                    }}
                  >
                    <Typography
                      variant="h5"
                      sx={{ fontWeight: 700, color: "#140342", mb: 0.5 }}
                    >
                      {listeningScore}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "#666", fontWeight: 600 }}
                    >
                      Listening
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </Grid>
          </Grid>

          {/* Score Interpretation */}
          <Box sx={{ mb: 3, textAlign: "center" }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: "#140342",
                mb: 1.5,
                fontSize: "1.1rem",
              }}
            >
              Score Interpretation Guide
            </Typography>
            <Grid container spacing={1} sx={{ justifyContent: "center" }}>
              <Grid item xs={6} md={3}>
                <Typography
                  variant="body2"
                  sx={{ color: "#4CAF50", fontWeight: 600 }}
                >
                  Expert (79-90)
                </Typography>
              </Grid>
              <Grid item xs={6} md={3}>
                <Typography
                  variant="body2"
                  sx={{ color: "#2196F3", fontWeight: 600 }}
                >
                  Proficient (65-78)
                </Typography>
              </Grid>
              <Grid item xs={6} md={3}>
                <Typography
                  variant="body2"
                  sx={{ color: "#FF9800", fontWeight: 600 }}
                >
                  Competent (50-64)
                </Typography>
              </Grid>
              <Grid item xs={6} md={3}>
                <Typography
                  variant="body2"
                  sx={{ color: "#FF5722", fontWeight: 600 }}
                >
                  Modest (36-49)
                </Typography>
              </Grid>
            </Grid>
          </Box>
        </Box>

        {/* Footer Section - Fixed positioning */}
        <Box
          sx={{
            mt: 2,
            pt: 2,
            borderTop: "2px solid #140342",
            textAlign: "center",
          }}
        >
          <Typography
            variant="body2"
            sx={{ color: "#666", mb: 1, fontSize: "0.8rem" }}
          >
            This certificate is issued for practice purposes only and is not an
            official PTE Academic certificate.
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: "#140342",
              fontWeight: 700,
              mb: 0.5,
              fontSize: "0.9rem",
            }}
          >
            Deep Insight Academy - Excellence in Education
          </Typography>
          <Typography
            variant="caption"
            sx={{ color: "#999", fontSize: "0.7rem" }}
          >
            Certificate generated on {new Date().toLocaleDateString()}
          </Typography>
        </Box>

        {/* Decorative Corner Elements */}
        <Box
          sx={{
            position: "absolute",
            top: 15,
            left: 15,
            width: 40,
            height: 40,
            border: "2px solid #140342",
            borderRadius: "50%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "rgba(20,3,66,0.1)",
          }}
        >
          <CertificateIcon sx={{ color: "#140342", fontSize: 20 }} />
        </Box>

        <Box
          sx={{
            position: "absolute",
            top: 15,
            right: 15,
            width: 40,
            height: 40,
            border: "2px solid #140342",
            borderRadius: "50%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "rgba(20,3,66,0.1)",
          }}
        >
          <CertificateIcon sx={{ color: "#140342", fontSize: 20 }} />
        </Box>

        <Box
          sx={{
            position: "absolute",
            bottom: 15,
            left: 15,
            width: 40,
            height: 40,
            border: "2px solid #140342",
            borderRadius: "50%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "rgba(20,3,66,0.1)",
          }}
        >
          <CertificateIcon sx={{ color: "#140342", fontSize: 20 }} />
        </Box>

        <Box
          sx={{
            position: "absolute",
            bottom: 15,
            right: 15,
            width: 40,
            height: 40,
            border: "2px solid #140342",
            borderRadius: "50%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "rgba(20,3,66,0.1)",
          }}
        >
          <CertificateIcon sx={{ color: "#140342", fontSize: 20 }} />
        </Box>
      </Paper>

      {/* Certificate Info */}
      <Box
        sx={{
          mt: 3,
          p: 3,
          backgroundColor: "#f0f7ff",
          borderRadius: 2,
          border: "1px solid #e3f2fd",
        }}
      >
        <Typography
          variant="body1"
          sx={{ color: "#1976d2", textAlign: "center", fontWeight: 500 }}
        >
          📝 This certificate represents your performance on this practice test.
          For official PTE Academic certification, please register for the
          official test through Pearson VUE.
        </Typography>
      </Box>
    </Box>
  );
};

export default PTECertificate;
