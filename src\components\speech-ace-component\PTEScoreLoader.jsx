import React from "react";
import { Box, Typography, CircularProgress, styled } from "@mui/material";

const LoaderContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  minHeight: "400px",
  padding: theme.spacing(3),
  gap: theme.spacing(3),
}));

const TextContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  gap: theme.spacing(1),
}));

const ScoringText = styled(Typography)(({ theme }) => ({
  position: "absolute",
  color: theme.palette.text.secondary,
  fontSize: "1.1rem",
  fontWeight: 600,
}));

const AIScoreLoader = () => {
  return (
    <LoaderContainer>
      <Box
        position="relative"
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <CircularProgress
          size={100} // Increased from 80
          thickness={2}
          sx={{
            color: "#140342",
            ".MuiCircularProgress-circle": {
              strokeLinecap: "round",
            },
          }}
        />
        <ScoringText>Scoring</ScoringText>
      </Box>

      <TextContainer>
        <Typography
          variant="h5" // Changed from h6
          sx={{
            fontWeight: 700, // Increased from 500
            letterSpacing: "-0.5px",
          }}
        >
          AI Scoring Ongoing ...
        </Typography>
        <Typography
          sx={{
            fontSize: "1.1rem", // Increased size
            fontWeight: 500, // Added weight
            color: "text.secondary",
            lineHeight: 1.5,
          }}
        >
          takes around 30 - 60 sec
        </Typography>
        <Typography
          sx={{
            fontSize: "1.1rem", // Increased size
            fontWeight: 500, // Added weight
            color: "text.secondary",
            lineHeight: 1.5,
          }}
        >
          you can check back later
        </Typography>
      </TextContainer>
    </LoaderContainer>
  );
};

export default AIScoreLoader;
