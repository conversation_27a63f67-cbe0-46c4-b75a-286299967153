import React, { useState, useEffect } from "react";
import RespondScoringDialog from "../speech-ace-component/RespondScoringDialog";
import { server } from "@/api/services/server";
import { useAuth } from "../others/AuthContext";
import PropTypes from "prop-types";

const RespondAnswersHistory = ({ questionId, onClose }) => {
  const [answers, setAnswers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    const fetchAnswers = async () => {
      // Make sure we have a user ID and question ID before attempting to fetch
      if (!user?.id || !questionId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log(
          "Fetching answers with userId:",
          user.id,
          "and questionId:",
          questionId
        );

        // Fetch answers based on user ID and question ID
        const response = await fetch(
          `${server.uri}answers?userid=${user.id}&questionId=${questionId}`
        );

        if (!response.ok) {
          throw new Error(`Failed to fetch answers (${response.status})`);
        }

        const data = await response.json();
        console.log("Raw API response:", data);

        // Filter the answers with more flexible matching
        const filteredAnswers = data.filter((answer) => {
          // Check for user ID match in various properties
          const userIdMatch =
            (answer.userId &&
              answer.userId.toString() === user.id.toString()) ||
            (answer.userid &&
              answer.userid.toString() === user.id.toString()) ||
            (answer.user_id &&
              answer.user_id.toString() === user.id.toString());

          // Check for question ID match in various properties
          const questionIdMatch =
            (answer.questionId && answer.questionId === questionId) ||
            (answer.practice_id && answer.practice_id === questionId);

          console.log(
            "Answer:",
            answer.answerId,
            "User match:",
            userIdMatch,
            "Question match:",
            questionIdMatch
          );

          return userIdMatch && questionIdMatch;
        });

        console.log("Filtered answers:", filteredAnswers);

        // Sort answers by ID or other property
        const sortedAnswers = [...filteredAnswers].reverse();

        setAnswers(sortedAnswers);
      } catch (err) {
        console.error("Error fetching answers:", err);
        setError(err.message || "Failed to load answers history");
      } finally {
        setLoading(false);
      }
    };

    fetchAnswers();
  }, [user?.id, questionId]);

  const handleAnswerClick = (answer) => {
    try {
      console.log("Selected answer for dialog:", answer);

      // Extract speech analysis data with proper fallbacks
      const analysisData = answer.speechAnalysis || {};
      console.log("Analysis data for dialog:", analysisData);

      setSelectedAnswer({
        ...answer,
        analysisData,
      });

      setIsDialogOpen(true);
    } catch (err) {
      console.error("Error preparing answer data:", err);
    }
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
  };

  // Helper function to get color based on score
  const getScoreColor = (score) => {
    if (score >= 70) return "#10B981";
    if (score >= 50) return "#F59E0B";
    return "#EF4444";
  };

  // Function to calculate original content score
  const getContentScore = (answer) => {
    return Math.round(answer.speechAnalysis?.content?.score || 0);
  };

  // Helper function to get pronunciation score
  const getPronunciationScore = (answer) => {
    return answer.speechAnalysis?.speech_score?.pte_score?.pronunciation || 0;
  };

  // Helper function to get fluency score
  const getFluencyScore = (answer) => {
    return answer.speechAnalysis?.speech_score?.pte_score?.fluency || 0;
  };

  // Function to calculate relative score based on content similarity - matching RespondScoringDialog.jsx
  const calculateRelativeScore = (answer, originalScore) => {
    // Get content score (0-90 scale)
    const contentScore = getContentScore(answer);

    // Get content percentage
    const contentPercentage = contentScore / 90;

    // Get original score percentage
    const originalScorePercentage = originalScore / 90;

    // Calculate relative score by multiplying percentages and scaling back to 90
    const relativeScore = Math.round(
      contentPercentage * originalScorePercentage * 90
    );

    return relativeScore;
  };

  // Calculate overall score using the same formula as RespondScoringDialog
  const calculateOverallScore = (answer) => {
    if (!answer.speechAnalysis) return 0;

    const contentScore = getContentScore(answer);
    const pronunciationScore = getPronunciationScore(answer);
    const fluencyScore = getFluencyScore(answer);

    // Calculate relative scores
    const relativePronunciationScore = calculateRelativeScore(
      answer,
      pronunciationScore
    );
    const relativeFluencyScore = calculateRelativeScore(answer, fluencyScore);

    const relevantScores = [
      relativePronunciationScore,
      relativeFluencyScore,
      contentScore,
    ];

    const validScores = relevantScores.filter(
      (score) => typeof score === "number"
    );

    if (validScores.length === 0) return 0;

    const sum = validScores.reduce((acc, curr) => acc + curr, 0);
    return Math.round(sum / validScores.length);
  };

  return (
    <div
      className="answers-history-container"
      style={{
        backgroundColor: "#f4f0ff",
        borderRadius: "12px",
        padding: "20px",
        maxWidth: "800px",
        width: "100%",
        maxHeight: "90vh",
        overflowY: "auto",
      }}
    >
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "20px",
        }}
      >
        <h2 style={{ fontSize: "24px", fontWeight: "600", color: "#140342" }}>
          Your Response History
        </h2>
        <button
          onClick={onClose}
          style={{
            background: "none",
            border: "none",
            fontSize: "24px",
            cursor: "pointer",
          }}
        >
          ×
        </button>
      </div>

      {loading && (
        <div style={{ textAlign: "center", padding: "20px" }}>
          <div
            style={{
              width: "40px",
              height: "40px",
              margin: "0 auto",
              border: "4px solid #f3f3f3",
              borderTop: "4px solid #140342",
              borderRadius: "50%",
              animation: "spin 1s linear infinite",
            }}
          ></div>
          <p style={{ marginTop: "10px", color: "#666" }}>
            Loading your answers...
          </p>
        </div>
      )}

      {error && !loading && (
        <div style={{ textAlign: "center", padding: "20px", color: "#FF3C00" }}>
          <p>Error: {error}</p>
          <button
            onClick={() => window.location.reload()}
            style={{
              marginTop: "10px",
              backgroundColor: "#140342",
              color: "white",
              border: "none",
              padding: "8px 16px",
              borderRadius: "5px",
              cursor: "pointer",
            }}
          >
            Try Again
          </button>
        </div>
      )}

      {!loading && !error && answers.length === 0 && (
        <div style={{ textAlign: "center", padding: "20px", color: "#666" }}>
          <p>You haven&apos;t submitted any answers for this question yet.</p>
        </div>
      )}

      {!loading && !error && answers.length > 0 && (
        <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
          {answers.map((answer, index) => {
            const overallScore = calculateOverallScore(answer);
            const contentScore = getContentScore(answer);
            const pronunciationScore = getPronunciationScore(answer);
            const fluencyScore = getFluencyScore(answer);
            const relativePronunciationScore = calculateRelativeScore(
              answer,
              pronunciationScore
            );
            const relativeFluencyScore = calculateRelativeScore(
              answer,
              fluencyScore
            );

            return (
              <div
                key={answer.answerId || index}
                style={{
                  backgroundColor: "white",
                  borderRadius: "8px",
                  padding: "16px",
                  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                  cursor: "pointer",
                  transition: "transform 0.2s, box-shadow 0.2s",
                }}
                onClick={() => handleAnswerClick(answer)}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "10px",
                  }}
                >
                  <span style={{ fontWeight: "600", fontSize: "16px" }}>
                    Attempt #{answers.length - index}
                  </span>
                </div>

                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "20px",
                    marginTop: "10px",
                  }}
                >
                  <div
                    style={{
                      position: "relative",
                      width: "60px",
                      height: "60px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <svg
                      viewBox="0 0 36 36"
                      style={{ width: "100%", height: "100%" }}
                    >
                      <path
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        stroke="#E2E8F0"
                        strokeWidth="3"
                      />
                      <path
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        stroke={getScoreColor(overallScore)}
                        strokeWidth="3"
                        strokeDasharray={`${(overallScore * 100) / 90}, 100`}
                      />
                      <text
                        x="18"
                        y="20.35"
                        style={{
                          fontSize: "8px",
                          fontWeight: "500",
                          textAnchor: "middle",
                        }}
                      >
                        {`${overallScore}/90`}
                      </text>
                    </svg>
                  </div>

                  <div style={{ flex: 1 }}>
                    <div
                      style={{ display: "flex", flexWrap: "wrap", gap: "12px" }}
                    >
                      <ScoreItem label="Content" value={contentScore} />
                      <ScoreItem
                        label="Pronunciation"
                        value={relativePronunciationScore}
                        originalValue={
                          pronunciationScore !== relativePronunciationScore
                            ? pronunciationScore
                            : undefined
                        }
                      />
                      <ScoreItem
                        label="Fluency"
                        value={relativeFluencyScore}
                        originalValue={
                          fluencyScore !== relativeFluencyScore
                            ? fluencyScore
                            : undefined
                        }
                      />
                    </div>
                  </div>
                </div>

                {answer.speechAnalysis?.speech_score?.transcript && (
                  <div
                    style={{
                      marginTop: "12px",
                      borderTop: "1px solid #e0e0e0",
                      paddingTop: "12px",
                    }}
                  >
                    <p
                      style={{
                        fontSize: "14px",
                        color: "#666",
                        marginBottom: "6px",
                      }}
                    >
                      Your transcript:
                    </p>
                    <p
                      style={{
                        fontSize: "14px",
                        color: "#333",
                        lineHeight: "1.4",
                        maxHeight: "60px",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                      }}
                    >
                      {answer.speechAnalysis.speech_score.transcript}
                    </p>
                  </div>
                )}

                {answer.media?.url && (
                  <div style={{ marginTop: "12px" }}>
                    <audio
                      src={answer.media.url}
                      controls
                      style={{ width: "100%", height: "36px" }}
                    />
                  </div>
                )}

                <button
                  style={{
                    backgroundColor: "#140342",
                    color: "white",
                    border: "none",
                    borderRadius: "5px",
                    padding: "8px 16px",
                    marginTop: "12px",
                    cursor: "pointer",
                    fontSize: "14px",
                    fontWeight: "500",
                    display: "flex",
                    alignItems: "center",
                    gap: "8px",
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAnswerClick(answer);
                  }}
                >
                  <span>View Detailed Analysis</span>
                </button>
              </div>
            );
          })}
        </div>
      )}

      {selectedAnswer && (
        <RespondScoringDialog
          analysisData={selectedAnswer.analysisData}
          isOpen={isDialogOpen}
          onClose={closeDialog}
          audioUrl={selectedAnswer.media?.url || ""}
        />
      )}

      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

// Score item component for displaying individual scores
const ScoreItem = ({ label, value, originalValue }) => {
  // Handle missing or invalid values
  const numericValue = typeof value === "number" ? Math.min(value, 90) : 0;

  return (
    <div style={{ display: "flex", alignItems: "center", gap: "5px" }}>
      <span style={{ fontSize: "14px", color: "#666" }}>{label}:</span>
      <span
        style={{
          fontSize: "14px",
          fontWeight: "600",
          color:
            numericValue >= 70
              ? "#10B981"
              : numericValue >= 50
              ? "#F59E0B"
              : "#EF4444",
        }}
      >
        {numericValue}/90
      </span>
      {originalValue && (
        <span style={{ fontSize: "12px", color: "#666", marginLeft: "5px" }}>
          ({originalValue}/90)
        </span>
      )}
    </div>
  );
};

ScoreItem.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.number.isRequired,
  originalValue: PropTypes.number,
};

RespondAnswersHistory.propTypes = {
  questionId: PropTypes.string.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default RespondAnswersHistory;
