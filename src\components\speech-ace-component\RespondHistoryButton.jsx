import React, { useState } from "react";
import RespondAnswersHistory from "./RespondAnswersHistory";

const RespondHistoryButton = ({ questionId }) => {
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);

  const openHistory = () => setIsHistoryOpen(true);
  const closeHistory = () => setIsHistoryOpen(false);

  return (
    <>
      <button
        onClick={openHistory}
        style={{
          backgroundColor: "#D9D9D9",
          padding: "10px 20px",
          borderRadius: "5px",
          border: "1px solid #140342",
          cursor: "pointer",
          fontSize: "14px",
          color: "#140342",
          textAlign: "center",
          display: "flex",
          alignItems: "center",
          gap: "8px",
          transition: "all 0.2s ease",
        }}
      >
        <svg
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="#140342"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
        </svg>
        <span>View History</span>
      </button>

      {isHistoryOpen && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 1000,
          }}
        >
          <RespondAnswersHistory
            questionId={questionId}
            onClose={closeHistory}
          />
        </div>
      )}
    </>
  );
};

export default RespondHistoryButton;
