import React from "react";
import AudioPlayer from "react-audio-player";

// Import ContentComparison from RetellLectureScoringDialog
const ContentComparison = ({
  transcript,
  keywords,
  providedText,
  similarityScore,
  customCalculation = false,
  wordScoreList = [], // Add word score list for pronunciation quality
}) => {
  // Normalize text for comparison
  const normalizeText = (text) => {
    return text
      .toLowerCase()
      .replace(/[.,!?;:]/g, "")
      .split(/\s+/)
      .filter((word) => word.length > 0);
  };

  const transcriptWords = normalizeText(transcript);
  const providedWords = normalizeText(providedText);
  const keywordList = keywords || [];

  // Create word analysis with pronunciation quality consideration
  const analyzeWords = () => {
    const analysis = [];
    const providedSet = new Set(providedWords);

    // Create a map of words to their pronunciation scores
    const wordScoreMap = {};
    if (wordScoreList && wordScoreList.length > 0) {
      wordScoreList.forEach((wordScore, idx) => {
        const normalizedWord = wordScore.word
          .toLowerCase()
          .replace(/[.,!?;:]/g, "");
        wordScoreMap[normalizedWord] = {
          score: wordScore.quality_score,
          index: idx,
        };
      });
    }

    // Enhanced logic for keyword matching with pronunciation consideration
    transcriptWords.forEach((word, index) => {
      let status = "extra"; // Default to extra
      let matchType = "";
      let pronunciationScore = wordScoreMap[word]?.score || 100; // Default to good if no score

      // Check if word matches any keyword (improved matching for multi-word keywords)
      const matchesKeyword = keywordList.some((keyword) => {
        const keywordLower = keyword.toLowerCase();
        const wordLower = word.toLowerCase();

        // Direct match
        if (keywordLower === wordLower) return true;

        // Check if the word is part of a multi-word keyword
        if (keywordLower.includes(" ")) {
          const keywordWords = keywordLower.split(/\s+/);
          return keywordWords.some(
            (kw) =>
              kw === wordLower ||
              kw.includes(wordLower) ||
              wordLower.includes(kw)
          );
        }

        // Single word keyword matching
        return (
          keywordLower.includes(wordLower) || wordLower.includes(keywordLower)
        );
      });

      // Check if word is in provided text
      const inProvidedText = providedSet.has(word);

      if (matchesKeyword) {
        // Check pronunciation quality for keywords
        if (pronunciationScore >= 60) {
          status = "matched";
          matchType = "keyword";
        } else {
          status = "incorrect"; // Poor pronunciation of keyword
          matchType = "keyword";
        }
      } else if (inProvidedText) {
        // Check pronunciation quality for content words
        if (pronunciationScore >= 60) {
          status = "matched";
          matchType = "content";
        } else {
          status = "incorrect"; // Poor pronunciation of content word
          matchType = "content";
        }
      }

      analysis.push({
        word,
        status,
        matchType,
        index,
        pronunciationScore,
      });
    });

    return analysis;
  };

  const wordAnalysis = analyzeWords();

  // Get missing keywords/words including poorly pronounced ones
  const getMissingItems = () => {
    const missingItems = [];

    // For Respond component, show missing keywords (improved multi-word keyword detection)
    const notMentionedKeywords = keywordList.filter((keyword) => {
      const keywordLower = keyword.toLowerCase();

      // For multi-word keywords, check if all words are present
      if (keywordLower.includes(" ")) {
        const keywordWords = keywordLower.split(/\s+/);
        return !keywordWords.every((kw) =>
          transcriptWords.some(
            (tw) => tw === kw || tw.includes(kw) || kw.includes(tw)
          )
        );
      }

      // For single word keywords, check if present in transcript
      return !transcriptWords.some(
        (tw) =>
          tw === keywordLower ||
          tw.includes(keywordLower) ||
          keywordLower.includes(tw)
      );
    });

    // Add keywords that were mentioned but with poor pronunciation
    const poorlyPronounced = wordAnalysis
      .filter(
        (item) => item.status === "incorrect" && item.matchType === "keyword"
      )
      .map((item) => item.word);

    missingItems.push(...notMentionedKeywords, ...poorlyPronounced);

    // Remove duplicates
    return [...new Set(missingItems)];
  };

  const missingItems = getMissingItems();

  return (
    <div>
      {/* Similarity Score */}
      <div style={{ marginBottom: "16px" }}>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            marginBottom: "8px",
          }}
        >
          <span style={{ fontSize: "16px", fontWeight: "500" }}>
            Content Similarity:
          </span>
          <span
            style={{
              fontSize: "18px",
              fontWeight: "bold",
              color:
                similarityScore >= 0.7
                  ? "#10B981"
                  : similarityScore >= 0.5
                  ? "#F59E0B"
                  : "#EF4444",
            }}
          >
            {Math.round(similarityScore * 100)}%
          </span>
        </div>
      </div>

      {/* Your Transcript with Word-by-Word Analysis */}
      <div style={{ marginBottom: "16px" }}>
        <h4 style={{ marginBottom: "8px", fontSize: "16px" }}>
          Your Transcript:
        </h4>
        <div
          style={{
            lineHeight: "1.8",
            padding: "12px",
            backgroundColor: "#f8f9fa",
            borderRadius: "8px",
            border: "1px solid #dee2e6",
            maxHeight: "200px",
            overflowY: "auto",
            wordWrap: "break-word",
            wordBreak: "break-word",
            whiteSpace: "normal",
          }}
        >
          {wordAnalysis.map((item, index) => (
            <span
              key={index}
              style={{
                color:
                  item.status === "matched"
                    ? "#10B981"
                    : item.status === "incorrect"
                    ? "#EF4444"
                    : "#6B7280",
                backgroundColor:
                  item.status === "matched"
                    ? item.matchType === "keyword" ||
                      item.matchType === "expected"
                      ? "#10B98120"
                      : "#3B82F620"
                    : item.status === "incorrect"
                    ? "#FEE2E2"
                    : "#F3F4F620",
                padding: "2px 4px",
                margin: "0 2px 2px 0",
                borderRadius: "3px",
                cursor: "pointer",
                fontWeight:
                  item.matchType === "keyword" || item.matchType === "expected"
                    ? "600"
                    : "normal",
                display: "inline-block",
                fontSize: "14px",
                lineHeight: "1.4",
                textDecoration:
                  item.status === "incorrect" ? "line-through" : "none",
              }}
              title={
                item.status === "matched"
                  ? `Matched ${
                      item.matchType === "keyword"
                        ? "keyword"
                        : item.matchType === "expected"
                        ? "expected word"
                        : "content word"
                    } (Pronunciation: ${
                      item.pronunciationScore?.toFixed(1) || "N/A"
                    })`
                  : item.status === "incorrect"
                  ? `Incorrect pronunciation of ${
                      item.matchType === "keyword"
                        ? "keyword"
                        : item.matchType === "expected"
                        ? "expected word"
                        : "content word"
                    } (Score: ${item.pronunciationScore?.toFixed(1) || "N/A"})`
                  : "Extra word (not in expected content)"
              }
            >
              {item.word}
            </span>
          ))}
        </div>
      </div>

      {/* Missing Keywords/Words */}
      {missingItems.length > 0 && (
        <div style={{ marginBottom: "16px" }}>
          <h4
            style={{ marginBottom: "8px", fontSize: "16px", color: "#EF4444" }}
          >
            Missing Keywords:
          </h4>
          <div
            style={{
              display: "flex",
              flexWrap: "wrap",
              gap: "6px",
              maxHeight: "120px",
              overflowY: "auto",
            }}
          >
            {missingItems.map((item, index) => (
              <span
                key={index}
                style={{
                  backgroundColor: "#FEE2E2",
                  color: "#EF4444",
                  padding: "4px 8px",
                  borderRadius: "4px",
                  fontSize: window.innerWidth <= 768 ? "12px" : "14px",
                  fontWeight: "500",
                  whiteSpace: "nowrap",
                }}
              >
                {item}
              </span>
            ))}
          </div>
          <p
            style={{
              fontSize: "12px",
              color: "#EF4444",
              marginTop: "8px",
              fontStyle: "italic",
            }}
          >
            💡 These keywords were either not mentioned or pronounced
            incorrectly (pronunciation score below 60). This affects your
            content score.
          </p>
        </div>
      )}

      {/* Expected Content */}
      <div>
        <h4 style={{ marginBottom: "8px", fontSize: "16px" }}>
          Expected Keywords:
        </h4>
        <div
          style={{
            padding: "12px",
            backgroundColor: "#f0f9ff",
            borderRadius: "8px",
            border: "1px solid #bfdbfe",
            fontSize: "14px",
            lineHeight: "1.6",
          }}
        >
          {providedText || "No expected keywords provided"}
        </div>
      </div>
    </div>
  );
};

const RespondScoringDialog = ({ analysisData, isOpen, onClose, audioUrl }) => {
  if (!isOpen) return null;

  // Fix the data path to access PTE scores
  const scores = analysisData?.speech_score?.pte_score || {};

  // Function to calculate pronunciation/fluency scores relative to content score
  const calculateRelativeScore = (originalScore) => {
    // Get content score (0-90 scale)
    const contentScore = Math.round(analysisData?.content?.score || 0);

    // Get content percentage
    const contentPercentage = contentScore / 90;

    // Get original score percentage
    const originalScorePercentage = originalScore / 90;

    // Calculate relative score by multiplying percentages and scaling back to 90
    const relativeScore = Math.round(
      contentPercentage * originalScorePercentage * 90
    );

    return relativeScore;
  };

  // Get the original scores
  const originalContentScore = Math.round(analysisData?.content?.score || 0);
  const originalPronunciationScore = scores.pronunciation || 0;
  const originalFluencyScore = scores.fluency || 0;

  // Calculate relative scores
  const relativePronunciationScore = calculateRelativeScore(
    originalPronunciationScore
  );
  const relativeFluencyScore = calculateRelativeScore(originalFluencyScore);

  const calculateOverallScore = () => {
    const relevantScores = [
      relativePronunciationScore,
      relativeFluencyScore,
      originalContentScore,
    ];

    const validScores = relevantScores.filter(
      (score) => typeof score === "number"
    );
    if (validScores.length === 0) return 0;

    const sum = validScores.reduce((acc, curr) => acc + curr, 0);
    return Math.round(sum / validScores.length);
  };

  const overallScore = calculateOverallScore();

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1000,
      }}
    >
      <div
        style={{
          backgroundColor: "white",
          borderRadius: "12px",
          padding: "24px",
          width: "90%",
          maxWidth: "800px",
          maxHeight: "90vh",
          overflowY: "auto",
        }}
      >
        {/* Header */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "20px",
          }}
        >
          <h2 style={{ fontSize: "24px", margin: 0 }}>AI Score</h2>
          <button
            onClick={onClose}
            style={{
              background: "none",
              border: "none",
              fontSize: "24px",
              cursor: "pointer",
            }}
          >
            ×
          </button>
        </div>

        {/* Scores Table */}
        <table
          style={{
            width: "100%",
            borderCollapse: "collapse",
            marginBottom: "24px",
          }}
        >
          <thead>
            <tr style={{ backgroundColor: "#f8f9fa" }}>
              <th style={tableHeaderStyle}>Component</th>
              <th style={tableHeaderStyle}>Score</th>
              <th style={tableHeaderStyle}>Suggestion</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={tableCellStyle}>
                Content <InfoIcon />
              </td>
              <td style={tableCellStyle}>
                {originalContentScore || 0}
                /90
              </td>
              <td style={tableCellStyle}>
                {analysisData?.content?.score < 60
                  ? "Make sure your response is relevant to the situation and complete"
                  : analysisData?.content?.score < 75
                  ? "Good response, try to add more relevant details"
                  : "Excellent response content"}
              </td>
            </tr>
            <tr>
              <td style={tableCellStyle}>
                Pronunciation <InfoIcon />
              </td>
              <td style={tableCellStyle}>
                {relativePronunciationScore || 0}/90
                {originalPronunciationScore !== relativePronunciationScore && (
                  <span
                    style={{
                      fontSize: "12px",
                      color: "#666",
                      marginLeft: "4px",
                    }}
                  >
                    (Original: {originalPronunciationScore}/90)
                  </span>
                )}
              </td>
              <td style={tableCellStyle}>
                {relativePronunciationScore < 60
                  ? "• Unrecognizable pronunciation\n• Speaking too fast/slow\n• Too many/few pauses"
                  : relativePronunciationScore < 75
                  ? "Good pronunciation, focus on clarity"
                  : "Excellent pronunciation"}
              </td>
            </tr>
            <tr>
              <td style={tableCellStyle}>
                Fluency <InfoIcon />
              </td>
              <td style={tableCellStyle}>
                {relativeFluencyScore || 0}/90
                {originalFluencyScore !== relativeFluencyScore && (
                  <span
                    style={{
                      fontSize: "12px",
                      color: "#666",
                      marginLeft: "4px",
                    }}
                  >
                    (Original: {originalFluencyScore}/90)
                  </span>
                )}
              </td>
              <td style={tableCellStyle}>
                {relativeFluencyScore < 60
                  ? "• Too many pauses\n• Speaking too slow/fast\n• Discontinuous speech"
                  : relativeFluencyScore < 75
                  ? "Good flow, try to speak more smoothly"
                  : "Excellent speech flow"}
              </td>
            </tr>
          </tbody>
        </table>

        {/* Content Analysis */}
        {analysisData?.content && (
          <div style={{ marginBottom: "24px" }}>
            <h3 style={{ marginBottom: "16px", fontSize: "18px" }}>
              Content Analysis
            </h3>
            <div
              style={{
                marginBottom: "8px",
                display: "flex",
                gap: window.innerWidth <= 768 ? "8px" : "12px",
                flexWrap: "wrap",
                fontSize: window.innerWidth <= 768 ? "12px" : "14px",
              }}
            >
              <Legend color="#10B981" text="Matched Keywords" />
              <Legend color="#EF4444" text="Incorrect Pronunciation" />
              <Legend color="#6B7280" text="Extra Words" />
            </div>
            <ContentComparison
              transcript={analysisData?.speech_score?.transcript || ""}
              keywords={analysisData?.content?.keywords || []}
              providedText={analysisData?.content?.provided_text || ""}
              similarityScore={analysisData?.content?.similarity_score || 0}
              customCalculation={
                analysisData?.content?.customCalculation || false
              }
              wordScoreList={analysisData?.speech_score?.word_score_list || []}
            />
          </div>
        )}

        {/* Overall Score */}
        <div style={{ marginBottom: "24px" }}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "8px",
              marginBottom: "12px",
              fontSize: "16px",
            }}
          >
            <span>Max Score: 90</span>
            <span style={{ margin: "0 8px" }}>|</span>
            <span>
              Overall Score:{" "}
              <span
                style={{
                  fontWeight: "bold",
                  color:
                    overallScore >= 70
                      ? "#10B981"
                      : overallScore >= 50
                      ? "#F59E0B"
                      : "#EF4444",
                }}
              >
                {overallScore}
              </span>
            </span>
          </div>
        </div>
        {/* Recording Playback */}
        <div style={{ marginBottom: "24px" }}>
          <h3 style={{ marginBottom: "12px" }}>Your Recording</h3>
          <AudioPlayer src={audioUrl} controls autoPlay={true} />
        </div>

        {/* Speech Recognition */}
        <div>
          <h3 style={{ marginBottom: "12px" }}>AI Speech Recognition</h3>
          <div
            style={{
              marginBottom: "8px",
              display: "flex",
              gap: "12px",
            }}
          >
            <Legend color="#10B981" text="Good" />
            <Legend color="#F59E0B" text="Average" />
            <Legend color="#EF4444" text="Need Improvement" />
          </div>
          <div style={{ lineHeight: "1.6" }}>
            {analysisData?.speech_score?.word_score_list?.map((word, index) => (
              <ColoredWord key={index} word={word} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

const tableHeaderStyle = {
  padding: "12px",
  textAlign: "left",
  borderBottom: "2px solid #dee2e6",
};

const tableCellStyle = {
  padding: "12px",
  borderBottom: "1px solid #dee2e6",
  whiteSpace: "pre-line",
};

const InfoIcon = () => (
  <span
    style={{
      display: "inline-flex",
      alignItems: "center",
      justifyContent: "center",
      width: "16px",
      height: "16px",
      borderRadius: "50%",
      backgroundColor: "#e9ecef",
      fontSize: "12px",
      marginLeft: "4px",
      cursor: "help",
    }}
  >
    i
  </span>
);

const Legend = ({ color, text }) => (
  <span
    style={{
      display: "flex",
      alignItems: "center",
      gap: "4px",
      fontSize: "14px",
    }}
  >
    <span style={{ color }}>{"\u25CF"}</span>
    {text}
  </span>
);

const ColoredWord = ({ word }) => {
  const getColor = (score) => {
    if (score >= 80) return "#10B981";
    if (score >= 60) return "#F59E0B";
    return "#EF4444";
  };

  return (
    <span
      style={{
        color: getColor(word.quality_score),
        cursor: "pointer",
        padding: "0 2px",
      }}
      title={`Quality Score: ${word.quality_score.toFixed(1)}`}
    >
      {word.word}
      {word.ending_punctuation || " "}
    </span>
  );
};

export default RespondScoringDialog;
