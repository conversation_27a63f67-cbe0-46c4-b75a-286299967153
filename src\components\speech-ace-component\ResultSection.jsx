import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import {
  Box,
  Paper,
  Typography,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableSortLabel,
  styled,
} from "@mui/material";
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { PTECrossSectionalScoring } from "../../utils/pteScoring";

const StyledCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  borderRadius: theme.spacing(1),
}));

const StyledTypography = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  letterSpacing: "-0.5px",
  color: "#140342",
}));

// Sorting functions
const descendingComparator = (a, b, orderBy) => {
  if (b[orderBy] < a[orderBy]) return -1;
  if (b[orderBy] > a[orderBy]) return 1;
  return 0;
};

const getComparator = (order, orderBy) => {
  return order === "desc"
    ? (a, b) => descendingComparator(a, b, orderBy)
    : (a, b) => -descendingComparator(a, b, orderBy);
};

const calculateScore = (
  userAnswers,
  correctAnswers,
  type,
  maxScoreIfCorrect = 1
) => {
  if (!userAnswers || !correctAnswers) return 0;

  switch (type?.toLowerCase()) {
    // Listening section specific types
    case "highlight-incorrect-words":
    case "highlight incorrect words": {
      // New API structure uses incorrectWords array instead of correctAnswers
      const incorrectWords = correctAnswers?.incorrectWords || correctAnswers;

      if (!Array.isArray(incorrectWords) || !Array.isArray(userAnswers)) {
        return 0;
      }

      // Extract the words that should be highlighted (incorrect words)
      const wordsToHighlight = incorrectWords.map((item) =>
        typeof item === "object" ? item.incorrect : item
      );

      // Extract the words the user actually highlighted
      const userHighlighted = userAnswers.map((answer) =>
        typeof answer === "object"
          ? answer.word || answer.optionText || answer
          : answer
      );

      // Count how many incorrect words the user correctly identified
      let correctCount = 0;
      userHighlighted.forEach((word) => {
        if (wordsToHighlight.includes(word)) {
          correctCount++;
        }
      });

      // If there are no correct highlights or no words to highlight, return 0
      if (correctCount === 0 || wordsToHighlight.length === 0) {
        return 0;
      }

      // Calculate points per correct highlight
      const pointsPerCorrect = maxScoreIfCorrect / wordsToHighlight.length;

      // Return points for correct highlights
      return correctCount * pointsPerCorrect;
    }

    case "select-missing-word":
    case "select missing word": {
      if (!userAnswers || !correctAnswers) return 0;

      // Find the correct option from the options array
      const correctOption = Array.isArray(correctAnswers)
        ? correctAnswers.find((option) => option.isCorrect)?.optionText
        : correctAnswers.optionText;

      // Get user's selected answer
      const userAnswer = Array.isArray(userAnswers)
        ? userAnswers[0]
        : userAnswers;

      // Award full points if the answer is correct
      return userAnswer === correctOption ? maxScoreIfCorrect : 0;
    }

    case "highlight correct summary": {
      const correctOption = correctAnswers.find(
        (option) => option.isCorrect
      )?.text;
      const userAnswer = Array.isArray(userAnswers)
        ? userAnswers[0]
        : userAnswers;

      // Award full points if the answer is correct
      return userAnswer === correctOption ? maxScoreIfCorrect : 0;
    }

    // Write from dictation specific type
    case "write from dictation":
    case "write-from-dictation": {
      if (!correctAnswers) return 0;

      console.log("Processing write from dictation:", {
        type,
        correctAnswers,
        userAnswers,
        maxScoreIfCorrect,
      });

      // Get the correct transcript
      const correctText =
        correctAnswers.transcript || correctAnswers.answer || "";

      // Get user's answer
      let userText = "";
      if (typeof userAnswers === "string") {
        userText = userAnswers;
      } else if (Array.isArray(userAnswers)) {
        userText = userAnswers[0] || "";
      } else if (userAnswers && typeof userAnswers === "object") {
        userText = userAnswers.text || userAnswers.answer || "";
      }

      console.log("Write from dictation comparison:", {
        correctText,
        userText,
        maxScoreIfCorrect,
      });

      // Normalize both texts for comparison
      const normalizeText = (text) => {
        return text
          .toLowerCase()
          .replace(/[^\w\s]/g, "") // Remove punctuation
          .replace(/\s+/g, " ") // Normalize spaces
          .trim();
      };

      const normalizedCorrect = normalizeText(correctText);
      const normalizedUser = normalizeText(userText);

      console.log("Normalized texts:", {
        normalizedCorrect,
        normalizedUser,
      });

      // Split into words for scoring
      const correctWords = normalizedCorrect
        .split(" ")
        .filter((word) => word.length > 0);
      const userWords = normalizedUser
        .split(" ")
        .filter((word) => word.length > 0);

      // Calculate score based on word accuracy
      let correctWordCount = 0;
      let totalWords = correctWords.length;

      if (totalWords === 0) return 0;

      // Check each correct word against user words
      correctWords.forEach((correctWord) => {
        if (userWords.includes(correctWord)) {
          correctWordCount++;
        }
      });

      // Calculate partial score based on word accuracy
      const wordAccuracy = correctWordCount / totalWords;
      const finalScore = wordAccuracy * maxScoreIfCorrect;

      console.log("Write from dictation scoring:", {
        correctWordCount,
        totalWords,
        wordAccuracy,
        finalScore,
        maxScoreIfCorrect,
      });

      return Math.max(0, Math.min(finalScore, maxScoreIfCorrect));
    }
    // Shared types between reading and listening
    case "multiple-choice-single":
    case "multiple choice (single)": {
      const correctOption = correctAnswers.find(
        (option) => option.isCorrect
      )?.optionText;
      const userAnswer = Array.isArray(userAnswers)
        ? userAnswers[0]
        : typeof userAnswers === "string"
        ? userAnswers
        : Object.keys(userAnswers)[0];

      // Award full points if the answer is correct
      return userAnswer === correctOption ? maxScoreIfCorrect : 0;
    }

    case "multiple choice (multiple)": {
      const correctOptions = correctAnswers
        .filter((option) => option.isCorrect)
        .map((option) => option.optionText);

      if (Array.isArray(userAnswers)) {
        const userSelectedOptions = userAnswers.map(
          (answer) => answer.optionText
        );

        // Count how many correct options the user selected
        let correctCount = 0;
        userSelectedOptions.forEach((selection) => {
          if (correctOptions.includes(selection)) {
            correctCount++;
          }
        });

        // Award points for each correct option selected
        if (correctCount === 0 || correctOptions.length === 0) {
          return 0;
        }

        // Calculate points per correct option
        const pointsPerCorrect = maxScoreIfCorrect / correctOptions.length;

        // Return points for correct selections
        return correctCount * pointsPerCorrect;
      }

      const userSelectedOptions = Object.keys(userAnswers).filter(
        (key) => userAnswers[key] === true
      );

      // Count how many correct options the user selected
      let correctCount = 0;
      userSelectedOptions.forEach((selection) => {
        if (correctOptions.includes(selection)) {
          correctCount++;
        }
      });

      // Award points for each correct option selected
      if (correctCount === 0 || correctOptions.length === 0) {
        return 0;
      }

      // Calculate points per correct option
      const pointsPerCorrect = maxScoreIfCorrect / correctOptions.length;

      // Return points for correct selections
      return correctCount * pointsPerCorrect;
    }

    // Reading specific types
    case "re-order paragraphs":
    case "reorder-paragraphs": {
      if (!Array.isArray(userAnswers) || !Array.isArray(correctAnswers))
        return 0;

      // Count how many positions are correct
      let correctCount = 0;
      userAnswers.forEach((sentence, index) => {
        if (sentence === correctAnswers[index]) {
          correctCount++;
        }
      });

      // Award points for each correct position
      if (correctCount === 0 || correctAnswers.length === 0) {
        return 0;
      }

      // Calculate points per correct position
      const pointsPerCorrect = maxScoreIfCorrect / correctAnswers.length;

      // Return points for correct positions
      return correctCount * pointsPerCorrect;
    }

    case "reading & writing：fill in the blanks":
    case "reading-writing-fill-in-the-blanks": {
      if (!Array.isArray(correctAnswers)) return 0;

      // Count how many blanks are filled correctly
      let correctCount = 0;
      correctAnswers.forEach((answer, index) => {
        const userAnswer = Array.isArray(userAnswers)
          ? (userAnswers[index]?.optionText || userAnswers[index]?.answer || "")
              .toLowerCase()
              .trim()
          : "";

        if (userAnswer === answer.correctAnswer.toLowerCase().trim()) {
          correctCount++;
        }
      });

      // Award points for each correct blank
      if (correctCount === 0 || correctAnswers.length === 0) {
        return 0;
      }

      // Calculate points per correct blank
      const pointsPerCorrect = maxScoreIfCorrect / correctAnswers.length;

      // Return points for correct blanks
      return correctCount * pointsPerCorrect;
    }

    case "listening：fill in the blanks":
    case "listening:fill in the blanks":
    case "fill-in-blanks":
    case "fill-in-the-blanks": {
      if (!Array.isArray(correctAnswers)) return 0;

      console.log("Processing listening fill-in-blanks:", {
        type,
        correctAnswers,
        userAnswers,
        maxScoreIfCorrect,
      });

      // Handle different data structures for listening fill-in-blanks
      let correctCount = 0;

      correctAnswers.forEach((correctBlank) => {
        const position = correctBlank.position;
        const correctAnswer = correctBlank.correctAnswer.toLowerCase().trim();

        // Find user answer for this position
        const userBlank = Array.isArray(userAnswers)
          ? userAnswers.find((ua) => ua.position === position)
          : null;

        const userAnswer = userBlank
          ? (userBlank.answer || userBlank.optionText || "")
              .toLowerCase()
              .trim()
          : "";

        console.log(`Position ${position}:`, {
          correctAnswer,
          userAnswer,
          isCorrect: userAnswer === correctAnswer,
        });

        if (userAnswer && userAnswer === correctAnswer) {
          correctCount++;
        }
      });

      console.log(`Correct answers: ${correctCount}/${correctAnswers.length}`);

      // Award points for each correct blank
      if (correctCount === 0 || correctAnswers.length === 0) {
        return 0;
      }

      // Calculate points per correct blank
      const pointsPerCorrect = maxScoreIfCorrect / correctAnswers.length;

      // Return points for correct blanks
      const finalScore = correctCount * pointsPerCorrect;
      console.log(`Final score: ${finalScore}`);

      return finalScore;
    }

    case "reading：fill in the blanks":
    case "reading:fill in the blanks": {
      if (!Array.isArray(userAnswers) || !Array.isArray(correctAnswers))
        return 0;

      // Count how many blanks are filled correctly
      let correctCount = 0;
      for (
        let i = 0;
        i < Math.min(userAnswers.length, correctAnswers.length);
        i++
      ) {
        const userAnswer = (
          userAnswers[i]?.optionText ||
          userAnswers[i]?.answer ||
          ""
        )
          .toLowerCase()
          .trim();
        const correctAnswer = (correctAnswers[i]?.correctAnswer || "")
          .toLowerCase()
          .trim();

        if (userAnswer && userAnswer === correctAnswer) {
          correctCount++;
        }
      }

      // Award points for each correct blank
      if (correctCount === 0 || correctAnswers.length === 0) {
        return 0;
      }

      // Calculate points per correct blank
      const pointsPerCorrect = maxScoreIfCorrect / correctAnswers.length;

      // Return points for correct blanks
      return correctCount * pointsPerCorrect;
    }

    default:
      console.warn(`Unknown question type: ${type}`);
      return 0;
  }
};

const ResultSection = ({ data, section, onResultsCalculated }) => {
  const [results, setResults] = useState({
    overall: 0,
    byType: {},
    questions: [],
    crossSectionalScores: { speaking: 0, writing: 0, reading: 0, listening: 0 },
  });
  const [order, setOrder] = useState("desc");
  const [orderBy, setOrderBy] = useState("percentage");

  const handleRequestSort = (property) => {
    const isDesc = orderBy === property && order === "desc";
    setOrder(isDesc ? "asc" : "desc");
    setOrderBy(property);
  };

  useEffect(() => {
    const processResults = () => {
      console.log("Processing results for section:", section);
      console.log("Available data:", data);
      console.log("Data length:", data?.length);

      // Check if data is available
      if (!data || data.length === 0) {
        console.log("No data available for processing");
        setResults({
          overall: 0,
          byType: {},
          questions: [],
          crossSectionalScores: {
            speaking: 0,
            writing: 0,
            reading: 0,
            listening: 0,
          },
        });
        return;
      }

      // Initialize the PTE Cross-Sectional Scoring Engine
      const pteScoring = new PTECrossSectionalScoring();

      const questionResults = data
        .filter((item) => {
          const matchesSection = item.section === section;
          const hasType = item.type;

          // Always include questions that match section and have type
          // This includes both attempted and unattempted questions
          return matchesSection && hasType;
        })
        .map((item) => {
          let score, maxScore;

          // Check if question was attempted
          if (item.attempted === false) {
            // Unattempted question - use zero score
            score = 0;
            maxScore = item.maxScore || 1;
          } else if (item.score !== undefined) {
            // Question already has calculated score (from MockTestResult)
            score = item.score;
            maxScore = item.maxScore || 1;
          } else if (item.type === "summarize-spoken-text") {
            // Use AI scoring from additionalProps
            score = item.additionalProps?.scoreResponse?.totalScore || 0;
            maxScore = item.additionalProps?.scoreResponse?.maxScore || 1;
          } else if (
            item.type === "write from dictation" ||
            item.type === "write-from-dictation"
          ) {
            // Use traditional scoring for write from dictation
            score = calculateScore(
              item.useranswers,
              { transcript: item.transcript, answer: item.answer },
              item.type,
              item.maxScoreIfCorrect || item.maxScore || 1
            );
            maxScore = item.maxScoreIfCorrect || item.maxScore || 1;
          } else {
            // Use traditional scoring
            score = calculateScore(
              item.useranswers,
              item.correctAnswer,
              item.type,
              item.maxScoreIfCorrect || 1
            );
            maxScore = item.maxScoreIfCorrect || 1;
          }

          const result = {
            questionId: item.questionId,
            type: item.type,
            prompt: item.prompt,
            score,
            maxScore,
            percentage: maxScore > 0 ? (score / maxScore) * 100 : 0,
            attempted: item.attempted !== false, // Default to true if not specified
            hasMedia: Boolean(item.media),
            mediaType: item.media?.type,
            additionalProps: {
              ...item.additionalProps,
              correctAnswer: item.correctAnswer, // Include for fill-in-blanks breakdown
              incorrectWords: item.incorrectWords, // Include for highlight-incorrect-words
              transcript: item.transcript, // Include original transcript
              incorrectTranscript: item.incorrectTranscript, // Include incorrect transcript
              answer: item.answer, // Include correct answer for write-from-dictation
              useranswers: item.useranswers, // Include for comparison
            },
            answer: item.answer, // Include the actual answer for display
            media: item.media, // Include media info
          };

          console.log(`Processed ${item.type}:`, result);
          return result;
        });

      console.log("Filtered question results:", questionResults);

      const typeGroups = {};
      questionResults.forEach((result) => {
        if (!typeGroups[result.type]) {
          typeGroups[result.type] = {
            totalScore: 0,
            maxScore: 0,
            count: 0,
          };
        }
        typeGroups[result.type].totalScore += result.score;
        typeGroups[result.type].maxScore += result.maxScore;
        typeGroups[result.type].count++;
      });

      const byType = Object.entries(typeGroups).reduce((acc, [type, data]) => {
        acc[type] = {
          ...data,
          percentage:
            data.maxScore > 0 ? (data.totalScore / data.maxScore) * 100 : 0,
        };
        return acc;
      }, {});

      const totalScore = questionResults.reduce((sum, q) => sum + q.score, 0);
      const totalMaxScore = questionResults.reduce(
        (sum, q) => sum + q.maxScore,
        0
      );
      const overallPercentage =
        totalMaxScore > 0 ? (totalScore / totalMaxScore) * 100 : 0;

      // Calculate cross-sectional scores using the PTE scoring engine
      const crossSectionalScores = pteScoring.aggregateScores(questionResults);

      console.log("Final results:", {
        overall: Math.round(overallPercentage),
        byType,
        questionsCount: questionResults.length,
        crossSectionalScores,
      });

      const finalResults = {
        overall: Math.round(overallPercentage),
        byType,
        questions: questionResults,
        crossSectionalScores,
      };

      setResults(finalResults);

      // Notify parent component of the calculated results
      if (onResultsCalculated) {
        onResultsCalculated(finalResults);
      }
    };

    processResults();
  }, [data, section, onResultsCalculated]);

  const chartData = Object.entries(results.byType)
    .sort((a, b) => b[1].percentage - a[1].percentage)
    .map(([type, data]) => ({
      name: type
        ?.split(/[ -]/)
        .map(
          (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        )
        .join(" "),
      percentage: Math.round(data.percentage),
    }));

  // Cross-sectional scores chart data
  const crossSectionalChartData = [
    { name: "Speaking", score: results.crossSectionalScores?.speaking || 0 },
    { name: "Writing", score: results.crossSectionalScores?.writing || 0 },
    { name: "Reading", score: results.crossSectionalScores?.reading || 0 },
    { name: "Listening", score: results.crossSectionalScores?.listening || 0 },
  ].filter((item) => item.score > 0);

  const headCells = [
    { id: "type", label: "Question Type", numeric: false },
    { id: "prompt", label: "Question", numeric: false },
    { id: "score", label: "Score", numeric: true },
    { id: "maxScore", label: "Max Score", numeric: true },
    { id: "percentage", label: "Percentage", numeric: true },
  ];

  // Sort the questions based on current sorting criteria
  const sortedQuestions = React.useMemo(() => {
    return [...results.questions].sort(getComparator(order, orderBy));
  }, [results.questions, order, orderBy]);

  return (
    <Box sx={{ py: 3, display: "flex", flexDirection: "column", gap: 3 }}>
      <StyledCard>
        <CardContent>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 700,
              color: "#140342",
              mb: 2,
            }}
          >
            Overall Performance
          </Typography>
          <Box
            sx={{
              textAlign: "center",
              p: 3,
              bgcolor: "#EEF2FF",
              borderRadius: 1,
            }}
          >
            <Typography
              variant="h2"
              sx={{
                fontWeight: 700,
                color: "#140342",
                mb: 1,
              }}
            >
              {results.overall}%
            </Typography>
            <Typography sx={{ color: "#140342" }}>Overall Score</Typography>
          </Box>
        </CardContent>
      </StyledCard>

      <StyledCard>
        <CardContent>
          <StyledTypography variant="h5" gutterBottom>
            Performance by Question Type
          </StyledTypography>
          <Box sx={{ height: 400, mt: 2 }}>
            <ResponsiveContainer>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.1} />
                <XAxis
                  dataKey="name"
                  tick={{
                    fontSize: "1rem",
                    fontWeight: 500,
                    fill: "#140342",
                  }}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis
                  tick={{
                    fontSize: "1rem",
                    fontWeight: 500,
                    fill: "#140342",
                  }}
                />
                <Tooltip
                  contentStyle={{
                    borderRadius: "8px",
                    fontWeight: 500,
                    fontSize: "1rem",
                    color: "#140342",
                  }}
                />
                <Legend
                  wrapperStyle={{
                    fontWeight: 500,
                    fontSize: "1.1rem",
                    color: "#140342",
                  }}
                />
                <Bar
                  dataKey="percentage"
                  fill="#140342"
                  name="Score %"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </Box>
        </CardContent>
      </StyledCard>

      {/* Cross-Sectional Scores Display */}
      {crossSectionalChartData.length > 0 && (
        <StyledCard>
          <CardContent>
            <StyledTypography variant="h5" gutterBottom>
              PTE Cross-Sectional Skill Scores
            </StyledTypography>
            <Typography variant="body2" sx={{ mb: 3, color: "#666" }}>
              These scores show how your performance in this section contributes
              to different PTE skills according to official scoring methodology.
            </Typography>
            <Box sx={{ height: 300, mt: 2 }}>
              <ResponsiveContainer>
                <BarChart data={crossSectionalChartData}>
                  <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.1} />
                  <XAxis
                    dataKey="name"
                    tick={{
                      fontSize: "1rem",
                      fontWeight: 500,
                      fill: "#140342",
                    }}
                  />
                  <YAxis
                    domain={[0, 90]}
                    tick={{
                      fontSize: "1rem",
                      fontWeight: 500,
                      fill: "#140342",
                    }}
                  />
                  <Tooltip
                    contentStyle={{
                      borderRadius: "8px",
                      fontWeight: 500,
                      fontSize: "1rem",
                      color: "#140342",
                    }}
                  />
                  <Legend
                    wrapperStyle={{
                      fontWeight: 500,
                      fontSize: "1.1rem",
                      color: "#140342",
                    }}
                  />
                  <Bar
                    dataKey="score"
                    fill="#140342"
                    name="PTE Score"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </Box>
          </CardContent>
        </StyledCard>
      )}

      <StyledCard>
        <CardContent>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 700,
              color: "#140342",
              mb: 2,
            }}
          >
            Question Details
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  {headCells.map((headCell) => (
                    <TableCell
                      key={headCell.id}
                      align={headCell.numeric ? "right" : "left"}
                      sortDirection={orderBy === headCell.id ? order : false}
                      sx={{ fontWeight: 600, color: "#140342" }}
                    >
                      <TableSortLabel
                        active={orderBy === headCell.id}
                        direction={orderBy === headCell.id ? order : "asc"}
                        onClick={() => handleRequestSort(headCell.id)}
                      >
                        {headCell.label}
                      </TableSortLabel>
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {sortedQuestions.map((question, index) => (
                  <TableRow key={question.questionId || index}>
                    <TableCell sx={{ color: "#140342" }}>
                      <Box
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          gap: 0.5,
                        }}
                      >
                        <Box
                          sx={{ display: "flex", alignItems: "center", gap: 1 }}
                        >
                          <Typography variant="body2">
                            {question.type
                              ?.split(/[ -]/)
                              .map(
                                (word) =>
                                  word.charAt(0).toUpperCase() +
                                  word.slice(1).toLowerCase()
                              )
                              .join(" ")}
                          </Typography>
                          {question.attempted === false && (
                            <Typography
                              variant="caption"
                              sx={{
                                backgroundColor: "#ffeb3b",
                                color: "#333",
                                px: 1,
                                py: 0.25,
                                borderRadius: 1,
                                fontWeight: 600,
                                fontSize: "0.7rem",
                              }}
                            >
                              NOT ATTEMPTED
                            </Typography>
                          )}
                        </Box>
                        {question.hasMedia && (
                          <Typography
                            variant="caption"
                            sx={{ display: "block", color: "#666" }}
                          >
                            {question.mediaType === "audio" ? "🎵 Audio" : ""}
                          </Typography>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell
                      sx={{
                        maxWidth: "300px",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        color: "#140342",
                      }}
                    >
                      {Array.isArray(question.prompt)
                        ? question.prompt[0]
                        : question.prompt}
                    </TableCell>

                    {/* Enhanced display for summarize-spoken-text */}
                    {(question.type === "summarize-spoken-text" ||
                      question.type === "summarize spoken text" ||
                      question.type?.toLowerCase().includes("summarize")) &&
                    question.additionalProps?.scoreResponse ? (
                      <TableCell colSpan={3} sx={{ padding: 2 }}>
                        <Box>
                          <Typography
                            variant="subtitle2"
                            sx={{ fontWeight: 700, color: "#140342", mb: 1 }}
                          >
                            AI Scoring Breakdown:
                          </Typography>

                          {/* Main score display */}
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              mb: 2,
                              gap: 2,
                            }}
                          >
                            <Typography
                              variant="h6"
                              sx={{ fontWeight: 700, color: "#140342" }}
                            >
                              Total Score:{" "}
                              {
                                question.additionalProps.scoreResponse
                                  .totalScore
                              }{" "}
                              /{" "}
                              {question.additionalProps.scoreResponse.maxScore}
                            </Typography>
                            <Typography variant="body2" sx={{ color: "#666" }}>
                              ({Math.round(question.percentage)}%)
                            </Typography>
                          </Box>

                          {/* Category scores */}
                          <Box
                            sx={{
                              display: "grid",
                              gridTemplateColumns:
                                "repeat(auto-fit, minmax(150px, 1fr))",
                              gap: 1,
                              mb: 2,
                            }}
                          >
                            {[
                              "content",
                              "form",
                              "grammar",
                              "spelling",
                              "vocabulary",
                            ].map((cat) => (
                              <Box
                                key={cat}
                                sx={{
                                  p: 1,
                                  backgroundColor: "#f5f5f5",
                                  borderRadius: 1,
                                  border: "1px solid #e0e0e0",
                                }}
                              >
                                <Typography
                                  variant="body2"
                                  sx={{ fontWeight: 600 }}
                                >
                                  {cat.charAt(0).toUpperCase() + cat.slice(1)}:
                                </Typography>
                                <Typography variant="body2">
                                  {question.additionalProps.scoreResponse[cat]
                                    ?.score || 0}{" "}
                                  /{" "}
                                  {question.additionalProps.scoreResponse[cat]
                                    ?.maxScore || 0}
                                </Typography>
                                {question.additionalProps.scoreResponse[cat]
                                  ?.suggestion && (
                                  <Typography
                                    variant="caption"
                                    sx={{ color: "#666", fontStyle: "italic" }}
                                  >
                                    {
                                      question.additionalProps.scoreResponse[
                                        cat
                                      ].suggestion
                                    }
                                  </Typography>
                                )}
                              </Box>
                            ))}
                          </Box>

                          {/* Additional info */}
                          <Box
                            sx={{
                              mt: 2,
                              p: 2,
                              backgroundColor: "#f9f9f9",
                              borderRadius: 1,
                            }}
                          >
                            <Typography variant="body2" sx={{ mb: 1 }}>
                              <strong>Word Count:</strong>{" "}
                              {question.additionalProps.scoreResponse.wordCount}
                            </Typography>

                            {question.answer && (
                              <Box sx={{ mt: 1 }}>
                                <Typography
                                  variant="body2"
                                  sx={{ fontWeight: 600, mb: 0.5 }}
                                >
                                  Student Answer:
                                </Typography>
                                <Typography
                                  variant="body2"
                                  sx={{
                                    fontStyle: "italic",
                                    backgroundColor: "white",
                                    p: 1,
                                    borderRadius: 1,
                                    border: "1px solid #e0e0e0",
                                    maxHeight: "100px",
                                    overflow: "auto",
                                  }}
                                >
                                  "{question.answer}"
                                </Typography>
                              </Box>
                            )}

                            {question.additionalProps.scoreResponse
                              .annotatedText && (
                              <Box sx={{ mt: 1 }}>
                                <Typography
                                  variant="body2"
                                  sx={{ fontWeight: 600, mb: 0.5 }}
                                >
                                  Annotated Text:
                                </Typography>
                                <div
                                  style={{
                                    fontSize: "0.875rem",
                                    backgroundColor: "white",
                                    padding: "8px",
                                    borderRadius: "4px",
                                    border: "1px solid #e0e0e0",
                                    maxHeight: "100px",
                                    overflow: "auto",
                                  }}
                                  dangerouslySetInnerHTML={{
                                    __html:
                                      question.additionalProps.scoreResponse
                                        .annotatedText,
                                  }}
                                />
                              </Box>
                            )}
                          </Box>
                        </Box>
                      </TableCell>
                    ) : /* Enhanced display for highlight-incorrect-words */
                    question.type === "highlight-incorrect-words" ||
                      question.type === "highlight incorrect words" ? (
                      <TableCell colSpan={3} sx={{ padding: 2 }}>
                        <Box>
                          <Typography
                            variant="subtitle2"
                            sx={{ fontWeight: 700, color: "#140342", mb: 1 }}
                          >
                            Highlight Incorrect Words Results:
                          </Typography>

                          {/* Main score display */}
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              mb: 2,
                              gap: 2,
                            }}
                          >
                            <Typography
                              variant="h6"
                              sx={{ fontWeight: 700, color: "#140342" }}
                            >
                              Score: {question.score} / {question.maxScore}
                            </Typography>
                            <Typography variant="body2" sx={{ color: "#666" }}>
                              ({Math.round(question.percentage)}%)
                            </Typography>
                          </Box>

                          {/* Word-by-word breakdown */}
                          {(question.additionalProps?.incorrectWords ||
                            question.additionalProps?.correctAnswer
                              ?.incorrectWords) && (
                            <Box sx={{ mb: 2 }}>
                              <Typography
                                variant="body2"
                                sx={{ fontWeight: 600, mb: 1 }}
                              >
                                Words Analysis:
                              </Typography>

                              {/* Display transcript with highlighting */}
                              {question.additionalProps.incorrectTranscript && (
                                <Box sx={{ mb: 2 }}>
                                  <Typography
                                    variant="body2"
                                    sx={{ fontWeight: 600, mb: 1 }}
                                  >
                                    Transcript (words to highlight shown in
                                    red):
                                  </Typography>
                                  <Box
                                    sx={{
                                      p: 2,
                                      backgroundColor: "#f9f9f9",
                                      borderRadius: 1,
                                      border: "1px solid #e0e0e0",
                                      fontFamily: "monospace",
                                      lineHeight: 1.6,
                                    }}
                                  >
                                    {question.additionalProps.incorrectTranscript
                                      .split(" ")
                                      .map((word, idx) => {
                                        const incorrectWords =
                                          question.additionalProps
                                            .incorrectWords ||
                                          question.additionalProps.correctAnswer
                                            ?.incorrectWords ||
                                          [];
                                        const isIncorrect = incorrectWords.some(
                                          (item) =>
                                            item.incorrect ===
                                            word.replace(/[.,!?]/g, "")
                                        );
                                        const wasHighlighted =
                                          question.additionalProps.useranswers?.some(
                                            (answer) =>
                                              (answer.word ||
                                                answer.optionText ||
                                                answer) ===
                                              word.replace(/[.,!?]/g, "")
                                          );

                                        return (
                                          <span
                                            key={idx}
                                            style={{
                                              backgroundColor: isIncorrect
                                                ? wasHighlighted
                                                  ? "#c8e6c9"
                                                  : "#ffcdd2"
                                                : wasHighlighted
                                                ? "#fff3e0"
                                                : "transparent",
                                              padding: "2px 4px",
                                              margin: "0 2px",
                                              borderRadius: "3px",
                                              border: wasHighlighted
                                                ? "1px solid #666"
                                                : "none",
                                            }}
                                          >
                                            {word}
                                          </span>
                                        );
                                      })}
                                  </Box>
                                  <Box
                                    sx={{
                                      mt: 1,
                                      display: "flex",
                                      gap: 2,
                                      flexWrap: "wrap",
                                    }}
                                  >
                                    <Typography
                                      variant="caption"
                                      sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 0.5,
                                      }}
                                    >
                                      <span
                                        style={{
                                          width: 12,
                                          height: 12,
                                          backgroundColor: "#c8e6c9",
                                          borderRadius: 2,
                                          border: "1px solid #666",
                                        }}
                                      ></span>
                                      Correctly highlighted
                                    </Typography>
                                    <Typography
                                      variant="caption"
                                      sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 0.5,
                                      }}
                                    >
                                      <span
                                        style={{
                                          width: 12,
                                          height: 12,
                                          backgroundColor: "#ffcdd2",
                                          borderRadius: 2,
                                        }}
                                      ></span>
                                      Should be highlighted (missed)
                                    </Typography>
                                    <Typography
                                      variant="caption"
                                      sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 0.5,
                                      }}
                                    >
                                      <span
                                        style={{
                                          width: 12,
                                          height: 12,
                                          backgroundColor: "#fff3e0",
                                          borderRadius: 2,
                                          border: "1px solid #666",
                                        }}
                                      ></span>
                                      Incorrectly highlighted
                                    </Typography>
                                  </Box>
                                </Box>
                              )}

                              <Box
                                sx={{
                                  display: "grid",
                                  gridTemplateColumns:
                                    "repeat(auto-fit, minmax(250px, 1fr))",
                                  gap: 1,
                                }}
                              >
                                {(
                                  question.additionalProps.incorrectWords ||
                                  question.additionalProps.correctAnswer
                                    ?.incorrectWords ||
                                  []
                                ).map((wordPair, idx) => {
                                  const wasHighlighted =
                                    question.additionalProps.useranswers?.some(
                                      (answer) =>
                                        (answer.word ||
                                          answer.optionText ||
                                          answer) === wordPair.incorrect
                                    );

                                  return (
                                    <Box
                                      key={idx}
                                      sx={{
                                        p: 1.5,
                                        backgroundColor: wasHighlighted
                                          ? "#e8f5e8"
                                          : "#ffebee",
                                        borderRadius: 1,
                                        border: "1px solid",
                                        borderColor: wasHighlighted
                                          ? "#4caf50"
                                          : "#f44336",
                                      }}
                                    >
                                      <Typography
                                        variant="body2"
                                        sx={{ fontWeight: 600, mb: 0.5 }}
                                      >
                                        Word {idx + 1}:
                                      </Typography>
                                      <Typography
                                        variant="body2"
                                        sx={{ color: "#666", mb: 0.5 }}
                                      >
                                        <strong>Transcript says:</strong> "
                                        {wordPair.incorrect}"
                                      </Typography>
                                      <Typography
                                        variant="body2"
                                        sx={{ color: "#666", mb: 0.5 }}
                                      >
                                        <strong>Audio says:</strong> "
                                        {wordPair.correct}"
                                      </Typography>
                                      <Typography
                                        variant="caption"
                                        sx={{
                                          color: wasHighlighted
                                            ? "#2e7d32"
                                            : "#d32f2f",
                                          fontWeight: 600,
                                          display: "flex",
                                          alignItems: "center",
                                          mt: 0.5,
                                        }}
                                      >
                                        {wasHighlighted
                                          ? "✓ Correctly highlighted"
                                          : "✗ Missed"}
                                      </Typography>
                                    </Box>
                                  );
                                })}
                              </Box>
                            </Box>
                          )}

                          {/* Audio info */}
                          {question.media?.url && (
                            <Box
                              sx={{
                                mt: 2,
                                p: 2,
                                backgroundColor: "#f0f7ff",
                                borderRadius: 1,
                                border: "1px solid #e3f2fd",
                              }}
                            >
                              <Typography
                                variant="body2"
                                sx={{ color: "#1976d2", fontWeight: 600 }}
                              >
                                🎵 Audio Question
                              </Typography>
                              <Typography
                                variant="caption"
                                sx={{ color: "#666" }}
                              >
                                Student listened to audio and identified words
                                that differed from the transcript
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      </TableCell>
                    ) : question.type === "write from dictation" ||
                      question.type === "write-from-dictation" ? (
                      <TableCell colSpan={3} sx={{ padding: 2 }}>
                        <Box>
                          <Typography
                            variant="subtitle2"
                            sx={{ fontWeight: 700, color: "#140342", mb: 1 }}
                          >
                            Write from Dictation Results:
                          </Typography>

                          {/* Main score display */}
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              mb: 2,
                              gap: 2,
                            }}
                          >
                            <Typography
                              variant="h6"
                              sx={{ fontWeight: 700, color: "#140342" }}
                            >
                              Score: {question.score} / {question.maxScore}
                            </Typography>
                            <Typography variant="body2" sx={{ color: "#666" }}>
                              ({Math.round(question.percentage)}%)
                            </Typography>
                          </Box>

                          {/* Text comparison */}
                          {question.additionalProps?.correctAnswer && (
                            <Box sx={{ mb: 2 }}>
                              <Typography
                                variant="body2"
                                sx={{ fontWeight: 600, mb: 1 }}
                              >
                                Text Comparison:
                              </Typography>

                              {/* Correct text */}
                              <Box
                                sx={{
                                  mb: 1,
                                  p: 2,
                                  backgroundColor: "#e8f5e8",
                                  borderRadius: 1,
                                  border: "1px solid #4caf50",
                                }}
                              >
                                <Typography
                                  variant="body2"
                                  sx={{
                                    fontWeight: 600,
                                    color: "#2e7d32",
                                    mb: 0.5,
                                  }}
                                >
                                  ✓ Correct Text:
                                </Typography>
                                <Typography
                                  variant="body2"
                                  sx={{
                                    fontStyle: "italic",
                                    color: "#333",
                                  }}
                                >
                                  "
                                  {question.additionalProps.correctAnswer
                                    .transcript ||
                                    question.additionalProps.correctAnswer
                                      .answer ||
                                    "No correct text available"}
                                  "
                                </Typography>
                              </Box>

                              {/* User's answer */}
                              <Box
                                sx={{
                                  p: 2,
                                  backgroundColor:
                                    question.score === question.maxScore
                                      ? "#e8f5e8"
                                      : "#ffebee",
                                  borderRadius: 1,
                                  border: "1px solid",
                                  borderColor:
                                    question.score === question.maxScore
                                      ? "#4caf50"
                                      : "#f44336",
                                }}
                              >
                                <Typography
                                  variant="body2"
                                  sx={{
                                    fontWeight: 600,
                                    color:
                                      question.score === question.maxScore
                                        ? "#2e7d32"
                                        : "#d32f2f",
                                    mb: 0.5,
                                  }}
                                >
                                  {question.score === question.maxScore
                                    ? "✓"
                                    : "✗"}{" "}
                                  Your Answer:
                                </Typography>
                                <Typography
                                  variant="body2"
                                  sx={{
                                    fontStyle: "italic",
                                    color: "#333",
                                  }}
                                >
                                  "
                                  {question.additionalProps?.useranswers ||
                                    question.answer ||
                                    "No answer provided"}
                                  "
                                </Typography>
                              </Box>
                            </Box>
                          )}

                          {/* Word analysis */}
                          <Box
                            sx={{
                              mt: 2,
                              p: 2,
                              backgroundColor: "#f9f9f9",
                              borderRadius: 1,
                            }}
                          >
                            <Typography variant="body2" sx={{ mb: 1 }}>
                              <strong>Analysis:</strong>
                            </Typography>

                            {question.additionalProps?.correctAnswer && (
                              <Box>
                                <Typography variant="body2" sx={{ mb: 0.5 }}>
                                  Expected:{" "}
                                  {
                                    (
                                      question.additionalProps.correctAnswer
                                        .transcript ||
                                      question.additionalProps.correctAnswer
                                        .answer ||
                                      ""
                                    ).split(" ").length
                                  }{" "}
                                  words
                                </Typography>
                                <Typography variant="body2" sx={{ mb: 0.5 }}>
                                  Your response:{" "}
                                  {
                                    (
                                      question.additionalProps?.useranswers ||
                                      question.answer ||
                                      ""
                                    ).split(" ").length
                                  }{" "}
                                  words
                                </Typography>
                                <Typography
                                  variant="body2"
                                  sx={{ color: "#666" }}
                                >
                                  Accuracy: {Math.round(question.percentage)}%
                                </Typography>
                              </Box>
                            )}

                            {/* Audio info if available */}
                            {question.media?.url && (
                              <Box
                                sx={{
                                  mt: 1,
                                  pt: 1,
                                  borderTop: "1px solid #ddd",
                                }}
                              >
                                <Typography
                                  variant="body2"
                                  sx={{ color: "#1976d2", fontWeight: 600 }}
                                >
                                  🎵 Audio Dictation Question
                                </Typography>
                                <Typography
                                  variant="caption"
                                  sx={{ color: "#666" }}
                                >
                                  Student listened to audio and wrote down what
                                  they heard
                                </Typography>
                              </Box>
                            )}
                          </Box>
                        </Box>
                      </TableCell>
                    ) : /* Enhanced display for listening fill-in-the-blanks */
                    question.type === "listening：fill in the blanks" ||
                      question.type === "listening:fill in the blanks" ||
                      question.type === "fill-in-blanks" ||
                      question.type === "fill-in-the-blanks" ||
                      question.type?.toLowerCase().includes("fill") ? (
                      <TableCell colSpan={3} sx={{ padding: 2 }}>
                        <Box>
                          <Typography
                            variant="subtitle2"
                            sx={{ fontWeight: 700, color: "#140342", mb: 1 }}
                          >
                            Fill-in-the-Blanks Results:
                          </Typography>

                          {/* Main score display */}
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              mb: 2,
                              gap: 2,
                            }}
                          >
                            <Typography
                              variant="h6"
                              sx={{ fontWeight: 700, color: "#140342" }}
                            >
                              Score: {question.score} / {question.maxScore}
                            </Typography>
                            <Typography variant="body2" sx={{ color: "#666" }}>
                              ({Math.round(question.percentage)}%)
                            </Typography>
                          </Box>

                          {/* Blank-by-blank breakdown */}
                          {question.additionalProps?.correctAnswer &&
                            question.additionalProps?.useranswers && (
                              <Box sx={{ mb: 2 }}>
                                <Typography
                                  variant="body2"
                                  sx={{ fontWeight: 600, mb: 1 }}
                                >
                                  Answer Breakdown:
                                </Typography>
                                <Box
                                  sx={{
                                    display: "grid",
                                    gridTemplateColumns:
                                      "repeat(auto-fit, minmax(200px, 1fr))",
                                    gap: 1,
                                  }}
                                >
                                  {question.additionalProps.correctAnswer.map(
                                    (blank, idx) => {
                                      const userBlank =
                                        question.additionalProps.useranswers.find(
                                          (ua) => ua.position === blank.position
                                        );
                                      const userAnswer =
                                        userBlank?.answer ||
                                        userBlank?.optionText ||
                                        "";
                                      const isCorrect =
                                        userAnswer.toLowerCase().trim() ===
                                        blank.correctAnswer
                                          .toLowerCase()
                                          .trim();

                                      return (
                                        <Box
                                          key={blank.position}
                                          sx={{
                                            p: 1.5,
                                            backgroundColor: isCorrect
                                              ? "#e8f5e8"
                                              : "#ffebee",
                                            borderRadius: 1,
                                            border: "1px solid",
                                            borderColor: isCorrect
                                              ? "#4caf50"
                                              : "#f44336",
                                          }}
                                        >
                                          <Typography
                                            variant="body2"
                                            sx={{ fontWeight: 600, mb: 0.5 }}
                                          >
                                            Blank {blank.position}:
                                          </Typography>
                                          <Typography
                                            variant="body2"
                                            sx={{ color: "#666" }}
                                          >
                                            <strong>Your answer:</strong> "
                                            {userAnswer || "No answer"}"
                                          </Typography>
                                          <Typography
                                            variant="body2"
                                            sx={{ color: "#666" }}
                                          >
                                            <strong>Correct:</strong> "
                                            {blank.correctAnswer}"
                                          </Typography>
                                          <Typography
                                            variant="caption"
                                            sx={{
                                              color: isCorrect
                                                ? "#2e7d32"
                                                : "#d32f2f",
                                              fontWeight: 600,
                                              display: "flex",
                                              alignItems: "center",
                                              mt: 0.5,
                                            }}
                                          >
                                            {isCorrect
                                              ? "✓ Correct"
                                              : "✗ Incorrect"}
                                          </Typography>
                                        </Box>
                                      );
                                    }
                                  )}
                                </Box>
                              </Box>
                            )}

                          {/* Audio info if available */}
                          {question.media?.url && (
                            <Box
                              sx={{
                                mt: 2,
                                p: 2,
                                backgroundColor: "#f0f7ff",
                                borderRadius: 1,
                                border: "1px solid #e3f2fd",
                              }}
                            >
                              <Typography
                                variant="body2"
                                sx={{ color: "#1976d2", fontWeight: 600 }}
                              >
                                🎵 Audio Question
                              </Typography>
                              <Typography
                                variant="caption"
                                sx={{ color: "#666" }}
                              >
                                Student listened to audio recording and filled
                                in the blanks
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      </TableCell>
                    ) : /* Enhanced display for select-missing-word */
                    question.type === "select-missing-word" ||
                      question.type === "select missing word" ? (
                      <TableCell colSpan={3} sx={{ padding: 2 }}>
                        <Box>
                          <Typography
                            variant="subtitle2"
                            sx={{ fontWeight: 700, color: "#140342", mb: 1 }}
                          >
                            Select Missing Word Results:
                          </Typography>

                          {/* Main score display */}
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              mb: 2,
                              gap: 2,
                            }}
                          >
                            <Typography
                              variant="h6"
                              sx={{ fontWeight: 700, color: "#140342" }}
                            >
                              Score: {question.score} / {question.maxScore}
                            </Typography>
                            <Typography variant="body2" sx={{ color: "#666" }}>
                              ({Math.round(question.percentage)}%)
                            </Typography>
                          </Box>

                          {/* Question context */}
                          <Box sx={{ mb: 2 }}>
                            <Typography
                              variant="body2"
                              sx={{ fontWeight: 600, mb: 1 }}
                            >
                              Question Context:
                            </Typography>
                            <Box
                              sx={{
                                p: 2,
                                backgroundColor: "#f9f9f9",
                                borderRadius: 1,
                                border: "1px solid #e0e0e0",
                              }}
                            >
                              <Typography variant="body2" sx={{ mb: 1 }}>
                                <strong>Topic:</strong>{" "}
                                {question.additionalProps?.questionName ||
                                  "Audio Recording"}
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{ fontStyle: "italic" }}
                              >
                                "{question.prompt}"
                              </Typography>
                            </Box>
                          </Box>

                          {/* Answer analysis */}
                          {question.additionalProps?.correctAnswer && (
                            <Box sx={{ mb: 2 }}>
                              <Typography
                                variant="body2"
                                sx={{ fontWeight: 600, mb: 1 }}
                              >
                                Answer Analysis:
                              </Typography>

                              <Box sx={{ display: "grid", gap: 2 }}>
                                {/* User's answer */}
                                <Box
                                  sx={{
                                    p: 2,
                                    backgroundColor:
                                      question.additionalProps
                                        .useranswers?.[0] ===
                                      question.additionalProps.correctAnswer.find(
                                        (opt) => opt.isCorrect
                                      )?.optionText
                                        ? "#e8f5e8"
                                        : "#ffebee",
                                    borderRadius: 1,
                                    border: "1px solid",
                                    borderColor:
                                      question.additionalProps
                                        .useranswers?.[0] ===
                                      question.additionalProps.correctAnswer.find(
                                        (opt) => opt.isCorrect
                                      )?.optionText
                                        ? "#4caf50"
                                        : "#f44336",
                                  }}
                                >
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      fontWeight: 600,
                                      mb: 1,
                                      color:
                                        question.additionalProps
                                          .useranswers?.[0] ===
                                        question.additionalProps.correctAnswer.find(
                                          (opt) => opt.isCorrect
                                        )?.optionText
                                          ? "#2e7d32"
                                          : "#d32f2f",
                                    }}
                                  >
                                    {question.additionalProps
                                      .useranswers?.[0] ===
                                    question.additionalProps.correctAnswer.find(
                                      (opt) => opt.isCorrect
                                    )?.optionText
                                      ? "✓ Your Answer (Correct):"
                                      : "✗ Your Answer (Incorrect):"}
                                  </Typography>
                                  <Typography
                                    variant="body1"
                                    sx={{
                                      fontFamily: "monospace",
                                      backgroundColor: "white",
                                      p: 1.5,
                                      borderRadius: 1,
                                      border: "1px solid #e0e0e0",
                                    }}
                                  >
                                    "
                                    {question.additionalProps
                                      .useranswers?.[0] || "No answer selected"}
                                    "
                                  </Typography>
                                </Box>

                                {/* Correct answer */}
                                <Box
                                  sx={{
                                    p: 2,
                                    backgroundColor: "#e3f2fd",
                                    borderRadius: 1,
                                    border: "1px solid #2196f3",
                                  }}
                                >
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      fontWeight: 600,
                                      mb: 1,
                                      color: "#1976d2",
                                    }}
                                  >
                                    💡 Correct Answer:
                                  </Typography>
                                  <Typography
                                    variant="body1"
                                    sx={{
                                      fontFamily: "monospace",
                                      backgroundColor: "white",
                                      p: 1.5,
                                      borderRadius: 1,
                                      border: "1px solid #e0e0e0",
                                    }}
                                  >
                                    "
                                    {question.additionalProps.correctAnswer.find(
                                      (opt) => opt.isCorrect
                                    )?.optionText || "N/A"}
                                    "
                                  </Typography>
                                </Box>
                              </Box>
                            </Box>
                          )}

                          {/* All options breakdown */}
                          {question.additionalProps?.correctAnswer && (
                            <Box sx={{ mb: 2 }}>
                              <Typography
                                variant="body2"
                                sx={{ fontWeight: 600, mb: 1 }}
                              >
                                All Options:
                              </Typography>

                              <Box
                                sx={{
                                  display: "grid",
                                  gridTemplateColumns:
                                    "repeat(auto-fit, minmax(200px, 1fr))",
                                  gap: 1,
                                }}
                              >
                                {question.additionalProps.correctAnswer.map(
                                  (option, idx) => {
                                    const isUserChoice =
                                      question.additionalProps
                                        .useranswers?.[0] === option.optionText;
                                    const isCorrect = option.isCorrect;

                                    return (
                                      <Box
                                        key={idx}
                                        sx={{
                                          p: 1.5,
                                          backgroundColor: isCorrect
                                            ? isUserChoice
                                              ? "#c8e6c9"
                                              : "#e8f5e8"
                                            : isUserChoice
                                            ? "#ffcdd2"
                                            : "#f5f5f5",
                                          borderRadius: 1,
                                          border: "1px solid",
                                          borderColor: isCorrect
                                            ? "#4caf50"
                                            : isUserChoice
                                            ? "#f44336"
                                            : "#e0e0e0",
                                        }}
                                      >
                                        <Typography
                                          variant="body2"
                                          sx={{ fontWeight: 600, mb: 0.5 }}
                                        >
                                          Option {idx + 1}:
                                        </Typography>
                                        <Typography
                                          variant="body2"
                                          sx={{ color: "#666", mb: 0.5 }}
                                        >
                                          "{option.optionText}"
                                        </Typography>
                                        <Box
                                          sx={{
                                            display: "flex",
                                            gap: 1,
                                            flexWrap: "wrap",
                                          }}
                                        >
                                          {isCorrect && (
                                            <Typography
                                              variant="caption"
                                              sx={{
                                                color: "#2e7d32",
                                                fontWeight: 600,
                                                backgroundColor:
                                                  "rgba(76, 175, 80, 0.1)",
                                                px: 1,
                                                py: 0.25,
                                                borderRadius: 0.5,
                                              }}
                                            >
                                              ✓ Correct
                                            </Typography>
                                          )}
                                          {isUserChoice && (
                                            <Typography
                                              variant="caption"
                                              sx={{
                                                color: isCorrect
                                                  ? "#2e7d32"
                                                  : "#d32f2f",
                                                fontWeight: 600,
                                                backgroundColor: isCorrect
                                                  ? "rgba(76, 175, 80, 0.1)"
                                                  : "rgba(244, 67, 54, 0.1)",
                                                px: 1,
                                                py: 0.25,
                                                borderRadius: 0.5,
                                              }}
                                            >
                                              {isCorrect
                                                ? "Your choice ✓"
                                                : "Your choice ✗"}
                                            </Typography>
                                          )}
                                        </Box>
                                      </Box>
                                    );
                                  }
                                )}
                              </Box>
                            </Box>
                          )}

                          {/* Transcript if available */}
                          {question.additionalProps?.transcript && (
                            <Box sx={{ mb: 2 }}>
                              <Typography
                                variant="body2"
                                sx={{ fontWeight: 600, mb: 1 }}
                              >
                                Audio Transcript:
                              </Typography>
                              <Box
                                sx={{
                                  p: 2,
                                  backgroundColor: "#f0f7ff",
                                  borderRadius: 1,
                                  border: "1px solid #e3f2fd",
                                }}
                              >
                                <Typography
                                  variant="body2"
                                  sx={{
                                    fontFamily: "monospace",
                                    lineHeight: 1.6,
                                  }}
                                >
                                  {question.additionalProps.transcript}
                                </Typography>
                              </Box>
                            </Box>
                          )}

                          {/* Audio info */}
                          {question.media?.url && (
                            <Box
                              sx={{
                                mt: 2,
                                p: 2,
                                backgroundColor: "#f0f7ff",
                                borderRadius: 1,
                                border: "1px solid #e3f2fd",
                              }}
                            >
                              <Typography
                                variant="body2"
                                sx={{ color: "#1976d2", fontWeight: 600 }}
                              >
                                🎵 Audio Question
                              </Typography>
                              <Typography
                                variant="caption"
                                sx={{ color: "#666" }}
                              >
                                Student listened to audio with a missing word
                                and selected the correct option
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      </TableCell>
                    ) : (
                      <>
                        <TableCell
                          sx={{
                            color:
                              question.attempted === false
                                ? "#f44336"
                                : "#140342",
                          }}
                          align="right"
                        >
                          {question.score}
                        </TableCell>
                        <TableCell sx={{ color: "#140342" }} align="right">
                          {question.maxScore}
                        </TableCell>
                        <TableCell
                          sx={{
                            color:
                              question.attempted === false
                                ? "#f44336"
                                : question.percentage >= 70
                                ? "#4caf50"
                                : question.percentage >= 50
                                ? "#ff9800"
                                : "#f44336",
                          }}
                          align="right"
                        >
                          {Math.round(question.percentage)}%
                        </TableCell>
                      </>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </StyledCard>
    </Box>
  );
};

ResultSection.propTypes = {
  data: PropTypes.array.isRequired,
  section: PropTypes.string.isRequired,
  onResultsCalculated: PropTypes.func,
};

export default ResultSection;
