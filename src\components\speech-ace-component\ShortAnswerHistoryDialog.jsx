import React, { useState, useEffect } from "react";
import { server } from "@/api/services/server";

import ShortAnswerScoring from "./AnswerScoringDialog";
import { useAuth } from "../others/AuthContext";
import QuestionAnswerIcon from "@mui/icons-material/QuestionAnswer";

const AnswerHistoryDialog = ({ isOpen, onClose, questionId }) => {
  const [answers, setAnswers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [showScoreDialog, setShowScoreDialog] = useState(false);
  const { user } = useAuth();
	// Default to 24 if not found
	


useEffect(() => {
  if (!isOpen || !questionId) return;

  const fetchAnswers = async () => {
    setLoading(true);
    try {
      // Try to fetch answers for this question and user
      const url = `${server.uri}answers?where={"questionId":"${questionId}","user_id":"${user?.id}"}`;
      console.log(
        `Fetching answers for question ID: ${questionId} and user ID: ${user?.id}`
      );
      const response = await fetch(url);
      const data = await response.json();
      console.log("Received data:", data); // Log the data to see what we're getting

      // Filter the answers to only include those matching the user ID and question ID
      const filteredAnswers = data.filter((answer) => {
        // Check all possible variations of user ID fields
        const userIdMatch =
          answer.userId === user?.id?.toString() ||
          answer.userid === user?.id?.toString() ||
          answer.user_id === user?.id?.toString();

        return userIdMatch && answer.questionId === questionId;
      });

      console.log("Filtered answers:", filteredAnswers); // Log filtered results

      // Sort answers by ID or other property since no date is available
      const sortedAnswers = [...filteredAnswers].reverse();

      setAnswers(sortedAnswers);
    } catch (err) {
      console.error("Error fetching answers:", err);
      setError(err.message || "Failed to load answers history");
    } finally {
      setLoading(false);
    }
  };

  fetchAnswers();
}, [isOpen, questionId, user?.id]);
	
	console.log(selectedAnswer)

  const handleViewScore = (answer) => {
    setSelectedAnswer(answer);
    setShowScoreDialog(true);
  };

  const handleCloseScoreDialog = () => {
    setShowScoreDialog(false);
  };

  // Function to create a summary of the transcript
  const getTranscriptSummary = (text, maxLength = 80) => {
    if (!text) return "No transcript available";
    return text.length > maxLength
      ? `${text.substring(0, maxLength)}...`
      : text;
  };

  // Function to format date
  const formatDate = (dateString) => {
    if (!dateString) return "No date";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch (e) {
      return "Unknown date";
    }
  };

  if (!isOpen) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1000,
      }}
    >
      <div
        style={{
          backgroundColor: "white",
          borderRadius: "12px",
          padding: "24px",
          width: "90%",
          maxWidth: "800px",
          maxHeight: "90vh",
          overflowY: "auto",
          boxShadow: "0 4px 15px rgba(0, 0, 0, 0.1)",
        }}
      >
        {/* Header with close button */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "20px",
          }}
        >
          <h2 style={{ fontSize: "24px", margin: 0, color: "#140342" }}>
            Your Previous Answers ({answers?.length})
          </h2>
          <button
            onClick={onClose}
            style={{
              background: "none",
              border: "none",
              fontSize: "24px",
              cursor: "pointer",
              color: "#666",
            }}
          >
            ×
          </button>
        </div>

        {loading ? (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "160px",
            }}
          >
            <div
              style={{
                width: "48px",
                height: "48px",
                border: "4px solid rgba(20, 3, 66, 0.2)",
                borderRadius: "50%",
                borderTop: "4px solid rgba(20, 3, 66, 1)",
                animation: "spin 1s linear infinite",
              }}
            ></div>
            <style jsx>{`
              @keyframes spin {
                0% {
                  transform: rotate(0deg);
                }
                100% {
                  transform: rotate(360deg);
                }
              }
            `}</style>
          </div>
        ) : error ? (
          <div
            style={{
              color: "#f44336",
              padding: "16px",
              border: "1px solid #f44336",
              borderRadius: "8px",
              backgroundColor: "#ffebee",
              marginBottom: "16px",
            }}
          >
            {error}
          </div>
        ) : answers?.length === 0 ? (
          <div
            style={{
              padding: "16px",
              border: "1px solid #e0e0e0",
              borderRadius: "8px",
              backgroundColor: "#f5f5f5",
              textAlign: "center",
            }}
          >
            <p>You haven't submitted any answers for this question yet.</p>
            <p style={{ fontSize: "14px", color: "#666", marginTop: "8px" }}>
              Once you submit an answer, it will appear here for your reference.
            </p>
          </div>
        ) : (
          <div
            style={{
              boxShadow:
                "0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)",
              borderRadius: "8px",
              overflow: "hidden",
            }}
          >
            <table
              style={{
                width: "100%",
                borderCollapse: "collapse",
                fontSize: "14px",
              }}
            >
              <thead style={{ backgroundColor: "#f5f5f5" }}>
                <tr>
                  <th
                    style={{
                      padding: "12px",
                      textAlign: "left",
                      borderBottom: "1px solid #e0e0e0",
                      color: "#333",
                    }}
                  >
                    Date
                  </th>
                  <th
                    style={{
                      padding: "12px",
                      textAlign: "left",
                      borderBottom: "1px solid #e0e0e0",
                      color: "#333",
                    }}
                  >
                    Response
                  </th>
                  <th
                    style={{
                      padding: "12px",
                      textAlign: "center",
                      borderBottom: "1px solid #e0e0e0",
                      color: "#333",
                    }}
                  >
                    Score
                  </th>
                  <th
                    style={{
                      padding: "12px",
                      textAlign: "center",
                      borderBottom: "1px solid #e0e0e0",
                      color: "#333",
                    }}
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {answers?.map((answer, index) => {
                  // Check if keywords matched
                  const matchedKeywords =
                    answer.speechAnalysis?.matchedKeywords || [];
                  const contentScore = matchedKeywords.length > 0 ? 1 : 0;

                  // Get transcript or matched keywords for display
                  const keywordsText =
                    matchedKeywords.length > 0
                      ? `Matched: ${matchedKeywords.join(", ")}`
                      : "Not the expected answer ";

                  return (
                    <tr
                      key={answer.answerId || index}
                      style={{
                        backgroundColor: index % 2 === 0 ? "white" : "#fafafa",
                        borderBottom: "1px solid #e0e0e0",
                      }}
                    >
                      <td
                        style={{
                          padding: "12px",
                          color: "#555",
                          fontSize: "12px",
                        }}
                      >
                        {formatDate(answer.createdAt)}
                      </td>
                      <td
                        style={{
                          padding: "12px",
                          color: "#555",
                        }}
                      >
                        {getTranscriptSummary(keywordsText)}
                      </td>
                      <td
                        style={{
                          padding: "12px",
                          textAlign: "center",
                        }}
                      >
                        <span
                          style={{
                            display: "inline-block",
                            padding: "4px 8px",
                            backgroundColor: "#e5e1f5",
                            color: "#140342",
                            borderRadius: "16px",
                            fontWeight: "600",
                            fontSize: "12px",
                          }}
                        >
                          {contentScore}/1
                        </span>
                      </td>
                      <td
                        style={{
                          padding: "12px",
                          textAlign: "center",
                        }}
                      >
                        <button
                          onClick={() => handleViewScore(answer)}
                          style={{
                            color: "#140342",
                            fontWeight: "500",
                            backgroundColor: "transparent",
                            border: "none",
                            cursor: "pointer",
                            padding: "6px 12px",
                            borderRadius: "4px",
                            transition: "background-color 0.2s",
                            outline: "none",
                          }}
                          onMouseOver={(e) =>
                            (e.target.style.backgroundColor = "#f0f0f0")
                          }
                          onMouseOut={(e) =>
                            (e.target.style.backgroundColor = "transparent")
                          }
                        >
                          View Details
                        </button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}

        {/* Score Dialog */}
        {selectedAnswer && showScoreDialog && (
          <ShortAnswerScoring
            analysisData={selectedAnswer}
            isOpen={showScoreDialog}
            onClose={handleCloseScoreDialog}
            audioUrl={selectedAnswer.media?.url}
          />
        )}
      </div>
    </div>
  );
};

// Main Button Component
const ShortAnswerHistoryButton = ({ questionId }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const toggleDialog = () => {
    setIsDialogOpen(!isDialogOpen);
  };

  return (
    <>
      <button
        style={{
          backgroundColor: "#D9D9D9",
          padding: "10px 20px",
          borderRadius: "5px",
          border: "1px solid #140342",
          cursor: "pointer",
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          gap: "10px",
        }}
        onClick={toggleDialog}
      >
        <QuestionAnswerIcon style={{ color: "#140342" }} />
        <p style={{ color: "#140342", fontSize: "12px" }}>View History</p>
      </button>

      <AnswerHistoryDialog
        isOpen={isDialogOpen}
        onClose={toggleDialog}
        questionId={questionId}
      />
    </>
  );
};

export default ShortAnswerHistoryButton;
