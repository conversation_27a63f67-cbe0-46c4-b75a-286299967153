import React from "react";
import { Box } from "@mui/material";
import BookmarkIcon from "@mui/icons-material/Bookmark";

// Colors matching the SpeechAnalysisDialog theme
const theme = {
  primary: "#140342", // Main purple color
  purple: "#9780ff", // Light purple
};

/**
 * Simple colored bookmark component using MUI
 *
 * @param {Object} props - Component props
 * @param {string} props.color - Color of the bookmark (Orange, Red, Purple, Green or empty string)
 * @returns {JSX.Element} - Colored bookmark component
 */
const SimpleColoredBookmark = ({ color = "" }) => {
  // Get color-specific background and icon color
  const getColorBackground = () => {
    // Normalize the color - convert to proper case to handle case sensitivity issues
    const normalizedColor = color
      ? color.charAt(0).toUpperCase() + color.slice(1).toLowerCase()
      : "";

    switch (normalizedColor) {
      case "Orange":
        return "#FFA500";
      case "Red":
        return "#FF0000";
      case "Purple":
        return "#800080";
      case "Green":
        return "#008000";
      default:
        return "transparent";
    }
  };

  // Color should be empty string, Orange, Red, Purple, or Green
  // Normalize the color to proper case for comparison
  const normalizedColor = color
    ? color.charAt(0).toUpperCase() + color.slice(1).toLowerCase()
    : "";
  const hasColor =
    normalizedColor &&
    ["Orange", "Red", "Purple", "Green"].includes(normalizedColor);

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        borderRadius: "50%",
        width: 32,
        height: 32,
        backgroundColor: getColorBackground(),
        transition: "all 0.2s ease",
        "&:hover": {
          transform: "scale(1.1)",
          boxShadow: hasColor ? "0 2px 4px rgba(0,0,0,0.2)" : "none",
        },
      }}
    >
      <BookmarkIcon
        sx={{
          color: hasColor ? "white" : theme.primary,
          fontSize: 22,
        }}
      />
    </Box>
  );
};

export default SimpleColoredBookmark;