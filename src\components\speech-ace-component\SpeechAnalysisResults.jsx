import { useEffect, useState } from "react";
import PropTypes from "prop-types";
import AudioPlayer from "react-audio-player";
import "react-h5-audio-player/lib/styles.css";

const SpeechAnalysisDialog = ({ analysisData, isOpen, onClose, audioUrl }) => {
  const calculateSkillScores = (analysisData) => {
    const wordScores = analysisData?.speech_score?.word_score_list || [];

    // Calculate pronunciation accuracy
    const pronunciationScore = Math.round(
      wordScores.reduce((acc, word) => acc + word.quality_score, 0) /
        wordScores.length
    );

    // Calculate speed score
    const fluencyMetrics = analysisData?.speech_score?.fluency?.overall_metrics;
    const speedScore = Math.round(
      (fluencyMetrics?.articulation_rate / 6) * 100
    );

    // Calculate fluency score
    const fluencyScore = Math.round(
      (analysisData?.speech_score?.pte_score?.fluency * 100) / 90
    );

    // Calculate stress score from stress pattern accuracy
    const stressScore = Math.round(
      wordScores.reduce((acc, word) => {
        const syllables = word.syllable_score_list || [];
        const correctStress = syllables.filter(
          (s) => s.stress_score >= 80
        ).length;
        return acc + (correctStress / syllables.length) * 100;
      }, 0) / wordScores.length
    );

    return {
      pronunciationAccuracy: Math.min(pronunciationScore, 100),
      speed: Math.min(speedScore, 100),
      fluent: Math.min(fluencyScore, 100),
      stress: Math.min(stressScore, 100),
    };
  };

  // Function to calculate pronunciation score relative to content score
  const calculateRelativeScore = (originalScore) => {
    // Get content score (0-90 scale)
    const contentScore = Math.round(
      (analysisData?.content?.similarity_score || 0) * 90
    );

    // Get content percentage
    const contentPercentage = contentScore / 90;

    // Get original score percentage
    const originalScorePercentage = originalScore / 90;

    // Calculate relative score by multiplying percentages and scaling back to 90
    const relativeScore = Math.round(
      contentPercentage * originalScorePercentage * 90
    );

    return relativeScore;
  };

  const skillScores = calculateSkillScores(analysisData);

  // Get the original scores
  const originalContentScore = Math.round(
    (analysisData?.content?.similarity_score || 0) * 90
  );
  const originalPronunciationScore =
    analysisData?.speech_score?.pte_score?.pronunciation || 0;
  const originalFluencyScore =
    analysisData?.speech_score?.pte_score?.fluency || 0;

  // Calculate relative scores
  const relativePronunciationScore = calculateRelativeScore(
    originalPronunciationScore
  );
  const relativeFluencyScore = calculateRelativeScore(originalFluencyScore);

  if (!isOpen) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1000,
      }}
    >
      <div
        style={{
          backgroundColor: "#f4f0ff",
          borderRadius: "12px",
          padding: "24px",
          maxWidth: "600px",
          width: "90%",
          maxHeight: "90vh",
          overflowY: "auto",
          position: "relative",
        }}
      >
        <button
          onClick={onClose}
          style={{
            position: "absolute",
            right: "16px",
            top: "16px",
            background: "none",
            border: "none",
            fontSize: "24px",
            cursor: "pointer",
          }}
        >
          ×
        </button>

        {/* AI Score Section */}
        <div style={{ marginBottom: "32px" }}>
          <h2
            style={{
              fontSize: "20px",
              fontWeight: "600",
              marginBottom: "16px",
            }}
          >
            AI Score
          </h2>

          <div
            style={{
              padding: "20px",
              backgroundColor: "white",
              boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
              borderRadius: "16px",
              height: "200px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                gap: "16px",
              }}
            >
              <div
                style={{
                  position: "relative",
                  width: "120px",
                  height: "120px",
                }}
              >
                <svg
                  viewBox="0 0 36 36"
                  style={{ width: "100%", height: "100%" }}
                >
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="#E2E8F0"
                    strokeWidth="3"
                  />
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="#140342"
                    strokeWidth="3"
                    strokeDasharray={`${
                      (Math.round(
                        (originalContentScore +
                          relativePronunciationScore +
                          relativeFluencyScore) /
                          3
                      ) *
                        100) /
                      90
                    }, 100`}
                  />
                  <text
                    x="18"
                    y="20.35"
                    style={{ fontSize: "8px", fontWeight: "500" }}
                    textAnchor="middle"
                  >
                    {`${Math.round(
                      (originalContentScore +
                        relativePronunciationScore +
                        relativeFluencyScore) /
                        3
                    )}/90`}
                  </text>
                </svg>
              </div>

              <div
                style={{ display: "flex", flexDirection: "column", gap: "0px" }}
              >
                <ScoreRow label="Content" value={originalContentScore} />
                <ScoreRow
                  label="Pronunciation"
                  value={relativePronunciationScore}
                  originalValue={originalPronunciationScore}
                />
                <ScoreRow
                  label="Fluency"
                  value={relativeFluencyScore}
                  originalValue={originalFluencyScore}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Audio Player */}
        <div style={{ marginBottom: "32px" }}>
          <h2
            style={{
              fontSize: "18px",
              fontWeight: "600",
              marginBottom: "12px",
            }}
          >
            Your Recording
          </h2>
          <div style={{ display: "flex", flexDirection: "row", width: "100%" }}>
            <AudioPlayer src={audioUrl} controls autoPlay={true} />
          </div>
        </div>

        {/* Skill Analysis */}
        <div style={{ marginBottom: "32px" }}>
          <h2
            style={{
              fontSize: "18px",
              fontWeight: "600",
              marginBottom: "12px",
            }}
          >
            Skill Analysis
          </h2>

          <p
            style={{
              fontSize: "14px",
              color: "#666",
              marginBottom: "16px",
              lineHeight: "1.5",
            }}
          >
            The flowing scores show how you have performed in different aspects
            as compared to other users. These scores can be used to analyze how
            you can improve in this question, but they are not directly related
            to the PTE scores.
          </p>

          <div
            style={{
              display: "flex",
              flexDirection: "column",
              gap: "16px",
              border: "2px solid #140342",
              padding: "16px",
              borderRadius: "10px",
            }}
          >
            <SkillBar
              label="Pronunciation Accuracy"
              value={skillScores.pronunciationAccuracy}
            />
            <SkillBar label="Speed" value={skillScores.speed} />
            <SkillBar label="Fluent" value={skillScores.fluent} />
            <SkillBar label="Stress" value={skillScores.stress} />
          </div>
        </div>

        {/* Transcript Analysis */}
        <TranscriptAnalysis analysisData={analysisData} />
      </div>
    </div>
  );
};

SpeechAnalysisDialog.propTypes = {
  analysisData: PropTypes.shape({
    content: PropTypes.shape({
      similarity_score: PropTypes.number,
      transcript: PropTypes.string,
      provided_text: PropTypes.string,
    }),
    speech_score: PropTypes.shape({
      word_score_list: PropTypes.array,
      fluency: PropTypes.shape({
        overall_metrics: PropTypes.shape({
          articulation_rate: PropTypes.number,
        }),
      }),
      pte_score: PropTypes.shape({
        pronunciation: PropTypes.number,
        fluency: PropTypes.number,
      }),
    }),
  }),
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  audioUrl: PropTypes.string,
};

const ScoreRow = ({ label, value, originalValue }) => (
  <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
    <span style={{ fontSize: "18px", color: "#140342", fontWeight: "600" }}>
      {label}:
    </span>
    <span style={{ fontSize: "18px", fontWeight: "600", color: "#140342" }}>
      {value}/90
      {/* {originalValue && originalValue !== value && (
        <span style={{ fontSize: "14px", color: "#666", marginLeft: "5px" }}>
          (Original: {originalValue}/90)
        </span>
      )} */}
    </span>
  </div>
);

ScoreRow.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.number.isRequired,
  originalValue: PropTypes.number,
};

const SkillBar = ({ label, value }) => (
  <div>
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        marginBottom: "4px",
      }}
    >
      <span style={{ fontSize: "14px" }}>{label}</span>
      <span style={{ fontSize: "14px", color: "#666" }}>{value}%</span>
    </div>
    <div
      style={{
        width: "100%",
        height: "16px",
        backgroundColor: "#f0f0f0",
        borderRadius: "43px",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          width: `${value}%`,
          height: "100%",
          backgroundColor:
            value >= 80 ? "#140342" : value >= 60 ? "#140342" : "#140342",
          borderRadius: "4px",
        }}
      ></div>
    </div>
  </div>
);

SkillBar.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.number.isRequired,
};

const TranscriptAnalysis = ({ analysisData }) => {
  const [wordComparisonData, setWordComparisonData] = useState([]);

  useEffect(() => {
    if (
      analysisData?.content?.transcript &&
      analysisData?.content?.provided_text
    ) {
      const comparison = compareTranscripts(
        analysisData.content.transcript,
        analysisData.content.provided_text,
        analysisData.speech_score?.word_score_list || []
      );
      setWordComparisonData(comparison);
    }
  }, [analysisData]);

  // Function to compare transcript with provided text
  const compareTranscripts = (transcript, providedText, wordScoreList) => {
    // Normalize texts for comparison (lowercase, remove extra spaces, etc.)
    const normalizedTranscript = transcript
      .toLowerCase()
      .replace(/[.,!?;:]/g, "");
    const normalizedProvidedText = providedText
      .toLowerCase()
      .replace(/[.,!?;:]/g, "");

    // Split into words
    const transcriptWords = normalizedTranscript.split(/\s+/);
    const providedWords = normalizedProvidedText.split(/\s+/);

    // Create a map of word scores from the word_score_list
    const wordScoreMap = {};
    wordScoreList.forEach((word) => {
      wordScoreMap[word.word.toLowerCase()] = {
        quality_score: word.quality_score,
        syllable_score_list: word.syllable_score_list || [],
      };
    });

    // Create comparison data
    let result = [];

    // For each word in the provided text, find if it exists in the transcript
    providedWords.forEach((word) => {
      const found = transcriptWords.includes(word);
      const matchedScoreData =
        wordScoreMap[word] ||
        wordScoreMap[word + "s"] ||
        wordScoreMap[word.slice(0, -1)];

      result.push({
        word: word,
        foundInTranscript: found,
        qualityScore: found
          ? matchedScoreData
            ? matchedScoreData.quality_score
            : 50
          : 0, // 0 for missing words
        syllableScores: matchedScoreData
          ? matchedScoreData.syllable_score_list
          : [],
        reasons: generateReasons(word, found, matchedScoreData),
      });
    });

    return result;
  };

  // Generate explanation for the tooltip
  const generateReasons = (word, found, scoreData) => {
    if (!found) {
      return `'${word}' was missing in your speech (Score: 0%). You did not pronounce this word.`;
    }

    if (!scoreData) {
      return `'${word}' was detected but no detailed score available. Pronunciation was acceptable.`;
    }

    let reasons = [];

    // Quality score evaluation
    if (scoreData.quality_score >= 90) {
      reasons.push(
        `Excellent pronunciation (${scoreData.quality_score.toFixed(1)}%).`
      );
    } else if (scoreData.quality_score >= 70) {
      reasons.push(
        `Good pronunciation (${scoreData.quality_score.toFixed(1)}%).`
      );
    } else if (scoreData.quality_score >= 50) {
      reasons.push(
        `Fair pronunciation (${scoreData.quality_score.toFixed(1)}%).`
      );
    } else {
      reasons.push(
        `Poor pronunciation (${scoreData.quality_score.toFixed(1)}%).`
      );
    }

    // Syllable analysis if available
    if (
      scoreData.syllable_score_list &&
      scoreData.syllable_score_list.length > 0
    ) {
      const weakSyllables = scoreData.syllable_score_list
        .filter((syllable) => syllable.quality_score < 70)
        .map((syllable) => syllable.letters);

      if (weakSyllables.length > 0) {
        reasons.push(`Focus on syllable(s): ${weakSyllables.join(", ")}`);
      }
    }

    return reasons.join(" ");
  };

  // Style function based on comparison results
  const getWordStyle = (wordData) => {
    const baseStyle = {
      cursor: "pointer",
      padding: "0 2px",
      display: "inline-block",
      transition: "all 0.2s",
      borderBottom: "1px dotted #ccc",
      position: "relative",
    };

    if (!wordData.foundInTranscript) {
      return {
        ...baseStyle,
        color: "#FF3C00",
        fontWeight: "bold",
        textDecoration: "underline",
      }; // Missing - Red, bold, underlined
    }

    if (wordData.qualityScore === null) {
      return { ...baseStyle, color: "#888" }; // No score - Gray
    }

    if (wordData.qualityScore >= 80) {
      return { ...baseStyle, color: "#140342" }; // Good - Purple (Using brand color)
    } else if (wordData.qualityScore >= 60) {
      return { ...baseStyle, color: "#F59E0B" }; // Average - Yellow
    }
    return { ...baseStyle, color: "#FF3C00" }; // Bad - Red
  };

  if (!analysisData?.content?.provided_text || !wordComparisonData.length) {
    return <div>No transcript comparison data available</div>;
  }

  return (
    <div
      style={{
        padding: "20px",
      }}
    >
      <h2
        style={{
          fontSize: "18px",
          fontWeight: "600",
          marginBottom: "12px",
        }}
      >
        Transcript Comparison Analysis
      </h2>

      {/* Legend */}
      <div
        style={{
          display: "flex",
          flexWrap: "wrap",
          gap: "16px",
          marginBottom: "12px",
        }}
      >
        <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <div
            style={{
              width: "12px",
              height: "12px",
              borderRadius: "50%",
              backgroundColor: "#140342",
            }}
          ></div>
          <span style={{ fontSize: "14px" }}>Good (80-100%)</span>
        </div>
        <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <div
            style={{
              width: "12px",
              height: "12px",
              borderRadius: "50%",
              backgroundColor: "#F59E0B",
            }}
          ></div>
          <span style={{ fontSize: "14px" }}>Average (60-79%)</span>
        </div>
        <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <div
            style={{
              width: "12px",
              height: "12px",
              borderRadius: "50%",
              backgroundColor: "#FF3C00",
            }}
          ></div>
          <span style={{ fontSize: "14px" }}>Poor (1-59%)</span>
        </div>
        <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <div
            style={{
              width: "12px",
              height: "12px",
              borderRadius: "50%",
              backgroundColor: "#FF3C00",
            }}
          ></div>
          <span
            style={{
              fontSize: "14px",
              fontWeight: "bold",
              textDecoration: "underline",
            }}
          >
            Missing (0%)
          </span>
        </div>
      </div>

      <p
        style={{
          fontSize: "12px",
          color: "#666",
          fontStyle: "italic",
          marginBottom: "16px",
        }}
      >
        *Hover over the words to see detailed feedback and scores
      </p>

      <div style={{ marginBottom: "20px" }}>
        <h3
          style={{ fontSize: "16px", fontWeight: "600", marginBottom: "10px" }}
        >
          Expected Text
        </h3>
        <div
          style={{
            fontSize: "16px",
            lineHeight: "1.8",
            backgroundColor: "#f5f5f5",
            padding: "15px",
            borderRadius: "8px",
            boxShadow: "inset 0 0 5px rgba(0,0,0,0.1)",
          }}
        >
          {wordComparisonData.map((wordData, index) => (
            <span
              key={index}
              style={getWordStyle(wordData)}
              title={wordData.reasons}
            >
              {wordData.word}
              {index < wordComparisonData.length - 1 ? " " : ""}
            </span>
          ))}
        </div>
      </div>

      <div style={{ marginTop: "20px" }}>
        <h3
          style={{ fontSize: "16px", fontWeight: "600", marginBottom: "10px" }}
        >
          Your Spoken Transcript
        </h3>
        <p
          style={{
            fontSize: "14px",
            lineHeight: "1.6",
            backgroundColor: "#f5f5f5",
            padding: "15px",
            borderRadius: "8px",
          }}
        >
          {analysisData?.content?.transcript}
        </p>
      </div>

      <div style={{ marginTop: "20px" }}>
        <h3
          style={{ fontSize: "16px", fontWeight: "600", marginBottom: "10px" }}
        >
          Performance Summary
        </h3>
        <div
          style={{
            backgroundColor: "#f5f5f5",
            padding: "15px",
            borderRadius: "8px",
          }}
        >
          <table style={{ width: "100%", borderCollapse: "collapse" }}>
            <thead>
              <tr>
                <th
                  style={{
                    textAlign: "left",
                    padding: "8px",
                    borderBottom: "1px solid #ddd",
                  }}
                >
                  Category
                </th>
                <th
                  style={{
                    textAlign: "right",
                    padding: "8px",
                    borderBottom: "1px solid #ddd",
                  }}
                >
                  Count
                </th>
                <th
                  style={{
                    textAlign: "right",
                    padding: "8px",
                    borderBottom: "1px solid #ddd",
                  }}
                >
                  Percentage
                </th>
              </tr>
            </thead>
            <tbody>
              {(() => {
                const totalWords = wordComparisonData.length;
                const goodWords = wordComparisonData.filter(
                  (w) => w.qualityScore >= 80
                ).length;
                const avgWords = wordComparisonData.filter(
                  (w) => w.qualityScore >= 60 && w.qualityScore < 80
                ).length;
                const poorWords = wordComparisonData.filter(
                  (w) => w.qualityScore > 0 && w.qualityScore < 60
                ).length;
                const missingWords = wordComparisonData.filter(
                  (w) => !w.foundInTranscript
                ).length;

                return (
                  <>
                    <tr>
                      <td
                        style={{
                          padding: "8px",
                          borderBottom: "1px solid #ddd",
                        }}
                      >
                        Good Pronunciation (80-100%)
                      </td>
                      <td
                        style={{
                          textAlign: "right",
                          padding: "8px",
                          borderBottom: "1px solid #ddd",
                        }}
                      >
                        {goodWords}
                      </td>
                      <td
                        style={{
                          textAlign: "right",
                          padding: "8px",
                          borderBottom: "1px solid #ddd",
                        }}
                      >
                        {((goodWords / totalWords) * 100).toFixed(1)}%
                      </td>
                    </tr>
                    <tr>
                      <td
                        style={{
                          padding: "8px",
                          borderBottom: "1px solid #ddd",
                        }}
                      >
                        Average Pronunciation (60-79%)
                      </td>
                      <td
                        style={{
                          textAlign: "right",
                          padding: "8px",
                          borderBottom: "1px solid #ddd",
                        }}
                      >
                        {avgWords}
                      </td>
                      <td
                        style={{
                          textAlign: "right",
                          padding: "8px",
                          borderBottom: "1px solid #ddd",
                        }}
                      >
                        {((avgWords / totalWords) * 100).toFixed(1)}%
                      </td>
                    </tr>
                    <tr>
                      <td
                        style={{
                          padding: "8px",
                          borderBottom: "1px solid #ddd",
                        }}
                      >
                        Poor Pronunciation (1-59%)
                      </td>
                      <td
                        style={{
                          textAlign: "right",
                          padding: "8px",
                          borderBottom: "1px solid #ddd",
                        }}
                      >
                        {poorWords}
                      </td>
                      <td
                        style={{
                          textAlign: "right",
                          padding: "8px",
                          borderBottom: "1px solid #ddd",
                        }}
                      >
                        {((poorWords / totalWords) * 100).toFixed(1)}%
                      </td>
                    </tr>
                    <tr>
                      <td
                        style={{
                          padding: "8px",
                          borderBottom: "1px solid #ddd",
                          fontWeight: "bold",
                          color: "#FF3C00",
                        }}
                      >
                        Missing Words (0%)
                      </td>
                      <td
                        style={{
                          textAlign: "right",
                          padding: "8px",
                          borderBottom: "1px solid #ddd",
                          fontWeight: "bold",
                          color: "#FF3C00",
                        }}
                      >
                        {missingWords}
                      </td>
                      <td
                        style={{
                          textAlign: "right",
                          padding: "8px",
                          borderBottom: "1px solid #ddd",
                          fontWeight: "bold",
                          color: "#FF3C00",
                        }}
                      >
                        {((missingWords / totalWords) * 100).toFixed(1)}%
                      </td>
                    </tr>
                    <tr>
                      <td style={{ padding: "8px", fontWeight: "bold" }}>
                        Total Words
                      </td>
                      <td
                        style={{
                          textAlign: "right",
                          padding: "8px",
                          fontWeight: "bold",
                        }}
                      >
                        {totalWords}
                      </td>
                      <td
                        style={{
                          textAlign: "right",
                          padding: "8px",
                          fontWeight: "bold",
                        }}
                      >
                        100%
                      </td>
                    </tr>
                  </>
                );
              })()}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

TranscriptAnalysis.propTypes = {
  analysisData: PropTypes.shape({
    content: PropTypes.shape({
      transcript: PropTypes.string,
      provided_text: PropTypes.string,
    }),
    speech_score: PropTypes.shape({
      word_score_list: PropTypes.array,
    }),
  }),
};

export default SpeechAnalysisDialog;
