import { useState, useMemo, useEffect } from "react";
import { loadStripe } from "@stripe/stripe-js";
import {
  Elements,
  CardElement,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import axios from "axios";
import PropTypes from "prop-types";
import { useAuth } from "../others/AuthContext";
import { server } from "../../api/services/server";

function CheckoutForm({ plan, onSuccess, onClose }) {
  const stripe = useStripe();
  const elements = useElements();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [userId, setUserId] = useState(null);
  const [userLoading, setUserLoading] = useState(true);

  useEffect(() => {
    const fetchUserId = async () => {
      setUserLoading(true);
      setError("");
      try {
        // Get userId from localStorage
        const storedUserId = localStorage.getItem("isUserId");

        if (storedUserId) {
          setUserId(storedUserId);
          setUserLoading(false);
          return;
        }

        // If no userId in storage, try to get from user object
        if (user?.id) {
          setUserId(user.id);
          setUserLoading(false);
          return;
        }

        // If still no userId, try to fetch it
        let email =
          user?.email ||
          user?.emailId ||
          localStorage.getItem("userEmail") ||
          user?.name;
        if (!email) {
          setError("User email not found. Please log in again.");
          setUserLoading(false);
          return;
        }

        // Fetch all users and find by emailId
        const res = await axios.get(`${server.uri}users`);
        const found = res.data.find((u) => u.emailId === email);
        if (found) {
          setUserId(found.userId);
        } else {
          setError("User not found in system. Please contact support.");
        }
      } catch (e) {
        setError("Failed to fetch user info.");
      }
      setUserLoading(false);
    };
    fetchUserId();
    // eslint-disable-next-line
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    if (!userId) {
      setError("User ID not found. Cannot proceed with payment.");
      setLoading(false);
      return;
    }

    const { error: stripeError, paymentMethod } =
      await stripe.createPaymentMethod({
        type: "card",
        card: elements.getElement(CardElement),
      });

    if (stripeError) {
      setError(stripeError.message);
      setLoading(false);
      return;
    }

    try {
      await axios.post(`${server.uri}transactions`, {
        amount: Number(plan.price.replace("$", "")),
        status: "completed",
        createdAt: new Date().toISOString(),
        paymentDetails: {
          amount: Number(plan.price.replace("$", "")),
          paymentMethod: "stripe",
          stripePaymentMethodId: paymentMethod.id,
        },
        userId: userId,
        membershipPlanId: plan.membershipId,
      });

      // Fetch updated user data to get the new membership details
      const userResponse = await axios.get(`${server.uri}users/${userId}`);

      onSuccess(userResponse.data);
    } catch (apiError) {
      setError("Payment succeeded but failed to record transaction.");
    }
    setLoading(false);
  };

  if (userLoading) {
    return (
      <div style={{ padding: 32, textAlign: "center" }}>
        Loading user info...
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} style={{ marginTop: 24 }}>
      <div
        style={{
          background: "#f4f0ff",
          borderRadius: 8,
          padding: 16,
          marginBottom: 16,
          border: "1px solid #eaeaea",
        }}
      >
        <CardElement
          options={{
            style: {
              base: {
                fontSize: "16px",
                color: "#140342",
                "::placeholder": { color: "#b0a8c9" },
                fontFamily: "inherit",
              },
              invalid: { color: "#D92D20" },
            },
          }}
        />
      </div>
      {error && (
        <div style={{ color: "#D92D20", marginBottom: 12, fontWeight: 500 }}>
          {error}
        </div>
      )}
      <div style={{ display: "flex", gap: 12, marginTop: 8 }}>
        <button
          type="submit"
          disabled={!stripe || loading}
          style={{
            flex: 1,
            background: "#140342",
            color: "#fff",
            border: "none",
            borderRadius: 6,
            padding: "12px 0",
            fontWeight: 700,
            fontSize: 16,
            cursor: loading ? "not-allowed" : "pointer",
            boxShadow: "0 2px 8px rgba(106,90,249,0.10)",
            transition: "background 0.2s",
          }}
        >
          {loading ? "Processing..." : `Pay ${plan.price}`}
        </button>
        <button
          type="button"
          onClick={onClose}
          style={{
            background: "#fff",
            color: "#6a5af9",
            border: "1px solid #eaeaea",
            borderRadius: 6,
            padding: "12px 20px",
            fontWeight: 600,
            fontSize: 16,
            cursor: "pointer",
            transition: "background 0.2s",
          }}
        >
          Cancel
        </button>
      </div>
    </form>
  );
}

CheckoutForm.propTypes = {
  plan: PropTypes.shape({
    price: PropTypes.string.isRequired,
    membershipId: PropTypes.string.isRequired,
    name: PropTypes.string,
  }).isRequired,
  onSuccess: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default function StripeCheckoutModal({
  open,
  plan,
  onSuccess,
  onClose,
}) {
  const stripePromise = useMemo(
    () => loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY),
    []
  );

  if (!open || !plan) return null;
  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100vw",
        height: "100vh",
        background: "rgba(20,3,66,0.18)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1000,
        fontFamily: "Inter, sans-serif",
      }}
    >
      <div
        style={{
          background: "#fff",
          padding: "32px 24px 24px 24px",
          borderRadius: 18,
          minWidth: 340,
          maxWidth: 400,
          width: "90vw",
          boxShadow: "0 8px 32px rgba(20,3,66,0.18)",
          position: "relative",
        }}
      >
        {/* Close button */}
        <button
          onClick={onClose}
          style={{
            position: "absolute",
            top: 18,
            right: 18,
            background: "none",
            border: "none",
            fontSize: 22,
            color: "#b0a8c9",
            cursor: "pointer",
            fontWeight: 700,
            lineHeight: 1,
            padding: 0,
          }}
          aria-label="Close"
        >
          ×
        </button>
        <h3
          style={{
            fontSize: 22,
            fontWeight: 800,
            color: "#140342",
            marginBottom: 8,
            textAlign: "center",
            letterSpacing: "-0.5px",
          }}
        >
          Pay for {plan.name} Plan
        </h3>
        <div
          style={{
            textAlign: "center",
            color: "#6a5af9",
            fontWeight: 600,
            fontSize: 18,
            marginBottom: 8,
          }}
        >
          {plan.price}
        </div>
        <div
          style={{
            textAlign: "center",
            color: "#b0a8c9",
            fontSize: 14,
            marginBottom: 12,
          }}
        >
          Enter your card details below
        </div>
        <Elements stripe={stripePromise}>
          <CheckoutForm plan={plan} onSuccess={onSuccess} onClose={onClose} />
        </Elements>
      </div>
    </div>
  );
}

StripeCheckoutModal.propTypes = {
  open: PropTypes.bool.isRequired,
  plan: PropTypes.shape({
    price: PropTypes.string.isRequired,
    membershipId: PropTypes.string.isRequired,
    name: PropTypes.string,
  }),
  onSuccess: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
};
