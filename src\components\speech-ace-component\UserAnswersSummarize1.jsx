import React, { useState, useEffect } from "react";
import SummerizeScoreDialog1 from "../speech-ace-component/SummerizeScoreDialog1";
import { server } from "@/api/services/server";
import { getRequest } from "@/api/services/controller";

const UserAnswersSummarize1 = ({ userId, questionId }) => {
  const [answers, setAnswers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [showScoreDialog, setShowScoreDialog] = useState(false);
  const [apiResponseRaw, setApiResponseRaw] = useState(null);

  useEffect(() => {
    const fetchAnswers = async () => {
      setLoading(true);
      try {
        // Log the input parameters
        console.log(
          `UserAnswersSummarize - Fetching with userId: ${userId}, questionId: ${questionId}`
        );

        // Try different API endpoints and approaches

        // Approach 1: Use the server variable and specific filters
        const uri1 = `${server.uri}answers?where={"questionId":"${questionId}","userId":"${userId}"}`;
        console.log(`Trying endpoint 1: ${uri1}`);

        try {
          const response1 = await getRequest(uri1);
          console.log("API Response 1:", response1);
          if (response1 && response1.data && response1.data.length > 0) {
            setAnswers(response1.data);
            setApiResponseRaw(JSON.stringify(response1.data, null, 2));
            console.log(
              `Found ${response1.data.length} answers with approach 1`
            );
            setLoading(false);
            return;
          }
        } catch (err) {
          console.error("Error with approach 1:", err);
        }

        // Approach 2: Get all answers and filter on client
        const uri2 = `${server.uri}answers`;
        console.log(`Trying endpoint 2: ${uri2}`);

        try {
          const response2 = await getRequest(uri2);
          console.log("API Response 2:", response2);

          if (response2 && response2.data) {
            setApiResponseRaw(JSON.stringify(response2.data, null, 2));

            // Filter on client side
            const filteredAnswers = response2.data.filter((answer) => {
              const answerUserId = String(answer.userId || "");
              const answerQuestionId = String(answer.questionId || "");
              const targetUserId = String(userId || "");
              const targetQuestionId = String(questionId || "");

              const userMatches = answerUserId === targetUserId;
              const questionMatches = answerQuestionId === targetQuestionId;

              return userMatches && questionMatches;
            });

            if (filteredAnswers.length > 0) {
              setAnswers(filteredAnswers);
              setLoading(false);
              return;
            }
          }
        } catch (err) {
          console.error("Error with approach 2:", err);
        }

        // Approach 3: Use fetch directly as a last resort
        const directUrl = `${server.uri}answers`;
        console.log(`Trying direct fetch: ${directUrl}`);

        try {
          const response3 = await fetch(directUrl);
          const data3 = await response3.json();
          console.log("Direct fetch response:", data3);

          if (data3 && Array.isArray(data3)) {
            setApiResponseRaw(JSON.stringify(data3, null, 2));

            // Filter on client side
            const filteredAnswers = data3.filter(
              (answer) =>
                String(answer.userId) === String(userId) &&
                String(answer.questionId) === String(questionId)
            );

            if (filteredAnswers.length > 0) {
              setAnswers(filteredAnswers);
              setLoading(false);
              return;
            }
          }
        } catch (err) {
          console.error("Error with approach 3:", err);
        }

        // If all approaches fail, show error (do not use mock data)
        setError("No answers found for this question.");
        setAnswers([]);
      } catch (err) {
        console.error("Error in fetchAnswers:", err);
        setError(`Failed to load answers: ${err.message}`);
        setAnswers([]); // Do not use mock data
      } finally {
        setLoading(false);
      }
    };

    if (userId && questionId) {
      fetchAnswers();
    } else {
      setLoading(false);
      setError("Missing userId or questionId parameters");
    }
  }, [userId, questionId]);

  const handleViewScore = (answer) => {
    setSelectedAnswer(answer);
    setShowScoreDialog(true);
  };

  const handleCloseDialog = () => {
    setShowScoreDialog(false);
  };

  // Function to create a summary of the answer text
  const getAnswerSummary = (text, maxLength = 100) => {
    if (!text) return "No content";
    return text.length > maxLength
      ? `${text.substring(0, maxLength)}...`
      : text;
  };

  // Function to format date
  const formatDate = (dateString) => {
    if (!dateString) return "No date";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch (e) {
      return "Unknown date";
    }
  };

  if (loading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "160px",
        }}
      >
        <div
          style={{
            width: "48px",
            height: "48px",
            border: "4px solid rgba(20, 3, 66, 0.2)",
            borderRadius: "50%",
            borderTop: "4px solid rgba(20, 3, 66, 1)",
            animation: "spin 1s linear infinite",
          }}
        ></div>
        <style jsx>{`
          @keyframes spin {
            0% {
              transform: rotate(0deg);
            }
            100% {
              transform: rotate(360deg);
            }
          }
        `}</style>
      </div>
    );
  }

  return (
    <div style={{ padding: "16px" }}>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "16px",
        }}
      >
        <h3
          style={{
            fontSize: "20px",
            fontWeight: "600",
            color: "#140342",
            margin: 0,
          }}
        >
          Your Previous Submissions ({answers.length})
        </h3>
      </div>

      {error && (
        <div
          style={{
            color: "#f44336",
            padding: "16px",
            border: "1px solid #f44336",
            borderRadius: "8px",
            backgroundColor: "#ffebee",
            marginBottom: "16px",
          }}
        >
          {error}
        </div>
      )}

      {answers.length === 0 ? (
        <div
          style={{
            padding: "16px",
            border: "1px solid #e0e0e0",
            borderRadius: "8px",
            backgroundColor: "#f5f5f5",
            textAlign: "center",
          }}
        >
          <p>You haven't submitted any summaries for this question yet.</p>
          <p style={{ fontSize: "14px", color: "#666", marginTop: "8px" }}>
            Once you submit a summary, it will appear here for your reference.
          </p>
        </div>
      ) : (
        <div
          style={{
            boxShadow: "0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)",
            borderRadius: "8px",
            overflow: "hidden",
          }}
        >
          <table
            style={{
              width: "100%",
              borderCollapse: "collapse",
              fontSize: "14px",
            }}
          >
            <thead style={{ backgroundColor: "#f5f5f5" }}>
              <tr>
                <th
                  style={{
                    padding: "12px",
                    textAlign: "left",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#333",
                  }}
                >
                  Answer ID
                </th>
                <th
                  style={{
                    padding: "12px",
                    textAlign: "left",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#333",
                  }}
                >
                  Summary Preview
                </th>
                <th
                  style={{
                    padding: "12px",
                    textAlign: "center",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#333",
                  }}
                >
                  Word Count
                </th>
                <th
                  style={{
                    padding: "12px",
                    textAlign: "center",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#333",
                  }}
                >
                  Score
                </th>
                <th
                  style={{
                    padding: "12px",
                    textAlign: "center",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#333",
                  }}
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {answers.map((answer, index) => {
                // Extract scoreResponse from additionalProps if it exists
                const aiScore = answer.additionalProps?.scoreResponse;
                const scoreDisplay = aiScore
                  ? `${aiScore.totalScore}/${aiScore.maxScore}`
                  : answer.score || "N/A";

                const wordCount =
                  aiScore?.wordCount ||
                  (answer.answer
                    ? answer.answer.trim().split(/\s+/).length
                    : 0);

                return (
                  <tr
                    key={answer.answerId || index}
                    style={{
                      backgroundColor: index % 2 === 0 ? "white" : "#fafafa",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    <td
                      style={{
                        padding: "12px",
                        color: "#555",
                        fontSize: "12px",
                      }}
                    >
                      {answer.answerId || "Unknown"}
                    </td>
                    <td
                      style={{
                        padding: "12px",
                        color: "#555",
                      }}
                    >
                      {getAnswerSummary(answer.answer)}
                    </td>
                    <td
                      style={{
                        padding: "12px",
                        textAlign: "center",
                        color:
                          wordCount >= 5 && wordCount <= 75
                            ? "#4caf50"
                            : "#ff3c00",
                        fontWeight: "500",
                      }}
                    >
                      {wordCount}
                    </td>
                    <td
                      style={{
                        padding: "12px",
                        textAlign: "center",
                      }}
                    >
                      <span
                        style={{
                          display: "inline-block",
                          padding: "4px 8px",
                          backgroundColor: "#e5e1f5",
                          color: "#140342",
                          borderRadius: "16px",
                          fontWeight: "600",
                          fontSize: "12px",
                        }}
                      >
                        {scoreDisplay}
                      </span>
                    </td>
                    <td
                      style={{
                        padding: "12px",
                        textAlign: "center",
                      }}
                    >
                      <button
                        onClick={() => handleViewScore(answer)}
                        style={{
                          color: "#140342",
                          fontWeight: "500",
                          backgroundColor: "transparent",
                          border: "none",
                          cursor: "pointer",
                          padding: "6px 12px",
                          borderRadius: "4px",
                          transition: "background-color 0.2s",
                          outline: "none",
                        }}
                        onMouseOver={(e) =>
                          (e.target.style.backgroundColor = "#f0f0f0")
                        }
                        onMouseOut={(e) =>
                          (e.target.style.backgroundColor = "transparent")
                        }
                      >
                        View Details
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      )}

      {/* Score Dialog */}
      {selectedAnswer && (
        <SummerizeScoreDialog1
          aiScore={selectedAnswer.additionalProps?.scoreResponse}
          isOpen={showScoreDialog}
          onClose={handleCloseDialog}
        />
      )}
    </div>
  );
};

export default UserAnswersSummarize1;
