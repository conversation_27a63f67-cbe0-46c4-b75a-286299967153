import React from "react";

/**
 * Demo component showing the enhanced word-by-word comparison feature
 * This demonstrates how the content analysis works in the scoring dialogs
 */
const WordByWordComparisonDemo = () => {
  // Sample data similar to what comes from the API
  const sampleAnalysisData = {
    speech_score: {
      transcript: "Restaurant POS system digital order management table to kitchen communication bar order system food service automation restaurant technology mobile order processing kitchen printer",
      word_score_list: [
        { word: "Restaurant", quality_score: 85, ending_punctuation: " " },
        { word: "POS", quality_score: 90, ending_punctuation: " " },
        { word: "system", quality_score: 78, ending_punctuation: " " },
        { word: "digital", quality_score: 82, ending_punctuation: " " },
        { word: "order", quality_score: 88, ending_punctuation: " " },
        { word: "management", quality_score: 75, ending_punctuation: " " },
        { word: "table", quality_score: 80, ending_punctuation: " " },
        { word: "to", quality_score: 85, ending_punctuation: " " },
        { word: "kitchen", quality_score: 90, ending_punctuation: " " },
        { word: "communication", quality_score: 70, ending_punctuation: " " },
        { word: "bar", quality_score: 85, ending_punctuation: " " },
        { word: "order", quality_score: 88, ending_punctuation: " " },
        { word: "system", quality_score: 78, ending_punctuation: " " },
        { word: "food", quality_score: 92, ending_punctuation: " " },
        { word: "service", quality_score: 85, ending_punctuation: " " },
        { word: "automation", quality_score: 68, ending_punctuation: " " },
        { word: "restaurant", quality_score: 85, ending_punctuation: " " },
        { word: "technology", quality_score: 72, ending_punctuation: " " },
        { word: "mobile", quality_score: 80, ending_punctuation: " " },
        { word: "order", quality_score: 88, ending_punctuation: " " },
        { word: "processing", quality_score: 65, ending_punctuation: " " },
        { word: "kitchen", quality_score: 90, ending_punctuation: " " },
        { word: "printer", quality_score: 75, ending_punctuation: "" }
      ]
    },
    content: {
      score: 80,
      transcript: "Restaurant POS system digital order management table to kitchen communication bar order system food service automation restaurant technology mobile order processing kitchen printer",
      provided_text: "Restaurant POS system, digital order management, table to kitchen communication, bar order system, food service automation, restaurant technology, mobile order processing, kitchen printer",
      similarity_score: 0.89,
      matchedWords: [
        "restaurant", "pos", "system", "digital", "order", "management", 
        "table", "kitchen", "communication", "food", "service", "automation", 
        "technology", "mobile", "processing", "printer"
      ],
      keywords: [
        "restaurant", "pos", "system", "digital", "order", "management", 
        "table", "kitchen", "communication", "food", "service", "automation", 
        "technology", "mobile", "processing", "printer"
      ]
    }
  };

  return (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      <h1 style={{ marginBottom: "20px", color: "#140342" }}>
        Enhanced Word-by-Word Comparison Demo
      </h1>
      
      <div style={{ marginBottom: "30px" }}>
        <h2 style={{ fontSize: "18px", marginBottom: "10px" }}>Features Implemented:</h2>
        <ul style={{ lineHeight: "1.6", color: "#666" }}>
          <li><strong>Content Analysis Section:</strong> Shows word-by-word comparison between user transcript and expected content</li>
          <li><strong>Keyword Matching:</strong> Highlights matched keywords in green with bold text</li>
          <li><strong>Content Word Matching:</strong> Shows words that match expected content in lighter green</li>
          <li><strong>Missing Keywords:</strong> Displays keywords that weren't mentioned in red badges</li>
          <li><strong>Similarity Score:</strong> Shows overall content similarity percentage</li>
          <li><strong>Visual Indicators:</strong> Color-coded legend and tooltips for better understanding</li>
        </ul>
      </div>

      <div style={{ marginBottom: "30px" }}>
        <h2 style={{ fontSize: "18px", marginBottom: "10px" }}>Components Updated:</h2>
        <div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))", gap: "15px" }}>
          <div style={{ padding: "15px", backgroundColor: "#f8f9fa", borderRadius: "8px", border: "1px solid #dee2e6" }}>
            <h3 style={{ fontSize: "16px", marginBottom: "8px", color: "#140342" }}>RepeatSentence.jsx</h3>
            <p style={{ fontSize: "14px", color: "#666", margin: 0 }}>
              Enhanced to show word-by-word comparison with the expected sentence
            </p>
          </div>
          <div style={{ padding: "15px", backgroundColor: "#f8f9fa", borderRadius: "8px", border: "1px solid #dee2e6" }}>
            <h3 style={{ fontSize: "16px", marginBottom: "8px", color: "#140342" }}>Re-tellLecture.jsx</h3>
            <p style={{ fontSize: "14px", color: "#666", margin: 0 }}>
              Shows comparison with lecture keywords and content similarity
            </p>
          </div>
          <div style={{ padding: "15px", backgroundColor: "#f8f9fa", borderRadius: "8px", border: "1px solid #dee2e6" }}>
            <h3 style={{ fontSize: "16px", marginBottom: "8px", color: "#140342" }}>DescribeImage.jsx</h3>
            <p style={{ fontSize: "14px", color: "#666", margin: 0 }}>
              Compares user description with expected image description keywords
            </p>
          </div>
        </div>
      </div>

      <div style={{ marginBottom: "30px" }}>
        <h2 style={{ fontSize: "18px", marginBottom: "10px" }}>Sample Content Analysis:</h2>
        <div style={{ 
          padding: "15px", 
          backgroundColor: "#f8f9fa", 
          borderRadius: "8px",
          border: "1px solid #dee2e6"
        }}>
          <div style={{ marginBottom: "16px" }}>
            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
              <span style={{ fontWeight: "500" }}>Content Similarity:</span>
              <span style={{ fontWeight: "bold", color: "#10B981" }}>
                {Math.round(sampleAnalysisData.content.similarity_score * 100)}%
              </span>
            </div>
          </div>

          <div style={{ marginBottom: "16px" }}>
            <h4 style={{ marginBottom: "8px", fontSize: "16px" }}>Your Transcript:</h4>
            <div style={{ 
              lineHeight: "1.8", 
              padding: "12px", 
              backgroundColor: "#ffffff", 
              borderRadius: "8px",
              border: "1px solid #dee2e6"
            }}>
              {sampleAnalysisData.speech_score.word_score_list.map((word, index) => {
                const isKeyword = sampleAnalysisData.content.keywords.includes(word.word.toLowerCase());
                return (
                  <span
                    key={index}
                    style={{
                      color: isKeyword ? "#10B981" : "#6B7280",
                      backgroundColor: isKeyword ? "#10B98120" : "#F3F4F620",
                      padding: "2px 4px",
                      margin: "0 2px",
                      borderRadius: "3px",
                      fontWeight: isKeyword ? "600" : "normal"
                    }}
                    title={isKeyword ? "Matched keyword" : "Extra word"}
                  >
                    {word.word}{word.ending_punctuation}
                  </span>
                );
              })}
            </div>
          </div>

          <div>
            <h4 style={{ marginBottom: "8px", fontSize: "16px" }}>Expected Keywords:</h4>
            <div style={{ 
              padding: "12px", 
              backgroundColor: "#f0f9ff", 
              borderRadius: "8px",
              border: "1px solid #bfdbfe",
              fontSize: "14px",
              lineHeight: "1.6"
            }}>
              {sampleAnalysisData.content.keywords.join(", ")}
            </div>
          </div>
        </div>
      </div>

      <div style={{ 
        padding: "15px", 
        backgroundColor: "#e8f5e8", 
        borderRadius: "8px",
        border: "1px solid #4caf50"
      }}>
        <h3 style={{ fontSize: "16px", marginBottom: "8px", color: "#2e7d32" }}>✅ Implementation Complete</h3>
        <p style={{ fontSize: "14px", color: "#2e7d32", margin: 0 }}>
          The word-by-word comparison feature has been successfully implemented across all three speaking components. 
          Users can now see detailed analysis of how their spoken responses match the expected content and keywords.
        </p>
      </div>
    </div>
  );
};

export default WordByWordComparisonDemo;
