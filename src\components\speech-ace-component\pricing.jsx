import { useState, useEffect } from "react";
// import { Link } from "react-router-dom";
import Header from "../layout/headers/Header";
import StripeCheckoutModal from "./StripeCheckoutModal";
import FooterOne from "../layout/footers/FooterOne";
import axios from "axios";
import PropTypes from "prop-types";
import { server } from "../../api/services/server";

const PLAN_FEATURES = {
  Basic: [
    "Access to PTE practice questions",
    "Ability to give mock tests",
    "Basic speaking analysis",
    "Limited writing feedback",
    "Access to study materials",
  ],
  Standard: [
    "Everything in Basic",
    "Unlimited PTE practice questions",
    "Advanced speaking analysis",
    "Detailed writing feedback",
    "Progress tracking",
    "Priority support",
  ],
  Premium: [
    "Everything in Standard",
    "Personalized study plan",
    "One-on-one coaching session",
    "Advanced analytics dashboard",
    "Unlimited mock tests",
    "Expert feedback on all sections",
    "24/7 support",
  ],
};

const planOrder = ["Basic", "Standard", "Premium"];

// Success Dialog Component
const SuccessDialog = ({ isOpen, onClose, membershipDetails, planName }) => {
  if (!isOpen) return null;

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100vw",
        height: "100vh",
        background: "rgba(20,3,66,0.18)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1000,
        fontFamily: "Inter, sans-serif",
      }}
    >
      <div
        style={{
          background: "#fff",
          padding: "32px",
          borderRadius: "18px",
          maxWidth: "500px",
          width: "90%",
          boxShadow: "0 8px 32px rgba(20,3,66,0.18)",
          position: "relative",
          textAlign: "center",
        }}
      >
        {/* Success Icon */}
        <div
          style={{
            width: "80px",
            height: "80px",
            borderRadius: "50%",
            background: "#ECFDF3",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            margin: "0 auto 20px",
          }}
        >
          <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
            <path
              d="M20 6L9 17L4 12"
              stroke="#12B76A"
              strokeWidth="3"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>

        <h2
          style={{
            fontSize: "24px",
            fontWeight: "700",
            color: "#140342",
            marginBottom: "16px",
          }}
        >
          Payment Successful!
        </h2>

        <p
          style={{
            fontSize: "16px",
            color: "#666",
            marginBottom: "24px",
          }}
        >
          Thank you for your purchase. Your subscription to the {planName} plan
          has been activated.
        </p>

        <div
          style={{
            background: "#F4F0FF",
            padding: "20px",
            borderRadius: "12px",
            textAlign: "left",
            marginBottom: "24px",
          }}
        >
          <h3
            style={{
              fontSize: "18px",
              fontWeight: "600",
              color: "#140342",
              marginBottom: "12px",
            }}
          >
            Subscription Details
          </h3>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr",
              gap: "12px",
            }}
          >
            <div style={{ color: "#666" }}>Plan:</div>
            <div style={{ fontWeight: "500", color: "#140342" }}>
              {planName}
            </div>

            <div style={{ color: "#666" }}>Start Date:</div>
            <div style={{ fontWeight: "500", color: "#140342" }}>
              {formatDate(membershipDetails?.membershipStartAt)}
            </div>

            <div style={{ color: "#666" }}>End Date:</div>
            <div style={{ fontWeight: "500", color: "#140342" }}>
              {formatDate(membershipDetails?.membershipEndAt)}
            </div>

            <div style={{ color: "#666" }}>Status:</div>
            <div style={{ fontWeight: "500", color: "#140342" }}>
              {membershipDetails?.membershipStatus || "Active"}
            </div>
          </div>
        </div>

        <button
          onClick={onClose}
          style={{
            background: "#140342",
            color: "#fff",
            border: "none",
            padding: "14px 32px",
            borderRadius: "8px",
            fontSize: "16px",
            fontWeight: "600",
            cursor: "pointer",
            transition: "all 0.3s ease",
          }}
        >
          Continue to Dashboard
        </button>
      </div>
    </div>
  );
};

// PropTypes for SuccessDialog
SuccessDialog.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  membershipDetails: PropTypes.shape({
    membershipStartAt: PropTypes.string,
    membershipEndAt: PropTypes.string,
    membershipStatus: PropTypes.string,
  }),
  planName: PropTypes.string,
};

// Current Subscription Component
const CurrentSubscription = ({ subscription, onCancel, loading }) => {
  if (loading)
    return (
      <div className="text-center my-30">Loading your subscription...</div>
    );

  if (!subscription) return null;

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const isCancelled = subscription.membershipStatus === "cancelled";

  return (
    <div className="container">
      <div className="row justify-center mb-30">
        <div className="col-lg-8">
          <div
            style={{
              background: "#fff",
              borderRadius: "16px",
              padding: "32px",
              boxShadow: "0 4px 20px rgba(20, 3, 66, 0.1)",
              border: "1px solid #eaeaea",
              position: "relative",
              overflow: "hidden",
            }}
          >
            <div
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "8px",
                height: "100%",
                background: isCancelled ? "#F79009" : "#140342",
              }}
            ></div>

            <div className="d-flex justify-between items-center">
              <h3
                style={{
                  fontSize: "22px",
                  fontWeight: "700",
                  color: "#140342",
                  marginBottom: "4px",
                }}
              >
                Your Current Subscription
              </h3>

              <div
                style={{
                  background:
                    subscription.membershipStatus === "active"
                      ? "#ECFDF3"
                      : subscription.membershipStatus === "cancelled"
                      ? "#FFF4ED"
                      : "#FEF3F2",
                  color:
                    subscription.membershipStatus === "active"
                      ? "#027A48"
                      : subscription.membershipStatus === "cancelled"
                      ? "#F79009"
                      : "#B42318",
                  padding: "6px 12px",
                  borderRadius: "16px",
                  fontSize: "14px",
                  fontWeight: "600",
                }}
              >
                {subscription.membershipStatus === "active"
                  ? "Active"
                  : subscription.membershipStatus === "cancelled"
                  ? "Cancelled"
                  : "Inactive"}
              </div>
            </div>

            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
                gap: "24px",
                marginTop: "24px",
              }}
            >
              <div>
                <div
                  style={{
                    color: "#667085",
                    fontSize: "14px",
                    marginBottom: "4px",
                  }}
                >
                  Plan
                </div>
                <div
                  style={{
                    fontWeight: "600",
                    fontSize: "18px",
                    color: "#140342",
                  }}
                >
                  {subscription.membershipPlan?.planName || "Standard Plan"}
                </div>
              </div>

              <div>
                <div
                  style={{
                    color: "#667085",
                    fontSize: "14px",
                    marginBottom: "4px",
                  }}
                >
                  Start Date
                </div>
                <div
                  style={{
                    fontWeight: "600",
                    fontSize: "18px",
                    color: "#140342",
                  }}
                >
                  {formatDate(subscription.membershipStartAt)}
                </div>
              </div>

              <div>
                <div
                  style={{
                    color: "#667085",
                    fontSize: "14px",
                    marginBottom: "4px",
                  }}
                >
                  End Date
                </div>
                <div
                  style={{
                    fontWeight: "600",
                    fontSize: "18px",
                    color: "#140342",
                  }}
                >
                  {formatDate(subscription.membershipEndAt)}
                </div>
              </div>
            </div>

            <div
              style={{
                marginTop: "32px",
                borderTop: "1px solid #eaeaea",
                paddingTop: "24px",
              }}
            >
              {isCancelled ? (
                <div
                  style={{
                    background: "#FFF4ED",
                    border: "1px solid #FFEAD5",
                    borderRadius: "8px",
                    padding: "16px",
                    display: "flex",
                    alignItems: "flex-start",
                    gap: "12px",
                  }}
                >
                  <div
                    style={{
                      minWidth: "24px",
                      height: "24px",
                      borderRadius: "50%",
                      background: "#FFEAD5",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <path
                        d="M12 9V12M12 16.5V17M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                        stroke="#F79009"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                  <div>
                    <p
                      style={{
                        color: "#B54708",
                        fontSize: "15px",
                        fontWeight: "500",
                        marginBottom: "4px",
                      }}
                    >
                      Your subscription has been cancelled
                    </p>
                    <p
                      style={{ color: "#B54708", fontSize: "14px", margin: 0 }}
                    >
                      You will lose access to all premium features after{" "}
                      {formatDate(subscription.membershipEndAt)}.
                    </p>
                  </div>
                </div>
              ) : (
                <button
                  onClick={onCancel}
                  style={{
                    background: "#FEF3F2",
                    color: "#B42318",
                    border: "1px solid #FEE4E2",
                    padding: "10px 18px",
                    borderRadius: "8px",
                    fontWeight: "600",
                    fontSize: "16px",
                    cursor: "pointer",
                    transition: "all 0.2s",
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.background = "#FEE4E2";
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.background = "#FEF3F2";
                  }}
                >
                  Cancel Subscription
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

CurrentSubscription.propTypes = {
  subscription: PropTypes.shape({
    membershipPlan: PropTypes.shape({
      planName: PropTypes.string,
    }),
    membershipStartAt: PropTypes.string,
    membershipEndAt: PropTypes.string,
    membershipStatus: PropTypes.string,
  }),
  onCancel: PropTypes.func.isRequired,
  loading: PropTypes.bool.isRequired,
};

// Cancellation Confirmation Dialog
const CancellationDialog = ({ isOpen, onClose, onConfirm, loading }) => {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100vw",
        height: "100vh",
        background: "rgba(20,3,66,0.18)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1000,
        fontFamily: "Inter, sans-serif",
      }}
    >
      <div
        style={{
          background: "#fff",
          padding: "32px",
          borderRadius: "18px",
          maxWidth: "450px",
          width: "90%",
          boxShadow: "0 8px 32px rgba(20,3,66,0.18)",
          position: "relative",
          textAlign: "center",
        }}
      >
        {/* Warning Icon */}
        <div
          style={{
            width: "80px",
            height: "80px",
            borderRadius: "50%",
            background: "#FEF3F2",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            margin: "0 auto 20px",
          }}
        >
          <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
            <path
              d="M12 9V14M12 16.5V17M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
              stroke="#D92D20"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>

        <h2
          style={{
            fontSize: "24px",
            fontWeight: "700",
            color: "#140342",
            marginBottom: "16px",
          }}
        >
          Cancel Subscription?
        </h2>

        <p
          style={{
            fontSize: "16px",
            color: "#666",
            marginBottom: "24px",
            lineHeight: "1.5",
          }}
        >
          Are you sure you want to cancel your subscription? You will lose
          access to premium features at the end of your current billing period.
        </p>

        <div style={{ display: "flex", gap: "12px" }}>
          <button
            onClick={onClose}
            style={{
              flex: "1",
              background: "#fff",
              color: "#140342",
              border: "1px solid #D0D5DD",
              padding: "12px 0",
              borderRadius: "8px",
              fontSize: "16px",
              fontWeight: "600",
              cursor: "pointer",
            }}
          >
            Keep Subscription
          </button>

          <button
            onClick={onConfirm}
            disabled={loading}
            style={{
              flex: "1",
              background: "#B42318",
              color: "#fff",
              border: "none",
              padding: "12px 0",
              borderRadius: "8px",
              fontSize: "16px",
              fontWeight: "600",
              cursor: loading ? "not-allowed" : "pointer",
              opacity: loading ? 0.7 : 1,
            }}
          >
            {loading ? "Cancelling..." : "Yes, Cancel"}
          </button>
        </div>
      </div>
    </div>
  );
};

CancellationDialog.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  loading: PropTypes.bool.isRequired,
};

const PricingPage = () => {
  // const breadcrumbsItems = [
  //   { label: "Home", href: "/" },
  //   { label: "Pricing", href: "/pricing" },
  // ];

  const [showModal, setShowModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);

  // Current subscription states
  const [currentSubscription, setCurrentSubscription] = useState(null);
  const [subscriptionLoading, setSubscriptionLoading] = useState(true);

  // Success dialog states
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [membershipDetails, setMembershipDetails] = useState(null);

  // Cancellation dialog states
  const [showCancellationDialog, setShowCancellationDialog] = useState(false);
  const [cancelLoading, setCancelLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setSubscriptionLoading(true);

      try {
        // Fetch plans
        const plansResponse = await axios.get(`${server.uri}membership-plans`);
        // Sort plans by planOrder
        const sorted = [...plansResponse.data].sort(
          (a, b) =>
            planOrder.indexOf(a.planName) - planOrder.indexOf(b.planName)
        );
        setPlans(sorted);

        // Fetch current subscription
        const userId = localStorage.getItem("isUserId");
        if (userId) {
          const userResponse = await axios.get(`${server.uri}users/${userId}`);
          if (userResponse.data) {
            setCurrentSubscription(userResponse.data);
          }
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
        setSubscriptionLoading(false);
      }
    };

    fetchData();
  }, []);

  // Check if user has an active or cancelled but not expired subscription
  const hasValidSubscription = () => {
    if (!currentSubscription) return false;

    // If subscription is active, show it
    if (currentSubscription.membershipStatus === "active") return true;

    // If subscription is cancelled but not expired yet, show it
    if (currentSubscription.membershipStatus === "cancelled") {
      const endDate = new Date(currentSubscription.membershipEndAt);
      const today = new Date();
      return endDate > today;
    }

    return false;
  };

  const handlePaymentSuccess = (userData) => {
    setShowModal(false);
    setMembershipDetails(userData);
    setCurrentSubscription(userData); // Update current subscription with new data
    setShowSuccessDialog(true);
  };

  const handleCancelSubscription = () => {
    setShowCancellationDialog(true);
  };

  const confirmCancellation = async () => {
    setCancelLoading(true);
    try {
      const userId = localStorage.getItem("isUserId");
      if (!userId) {
        throw new Error("User ID not found");
      }

      // Get current subscription data
      const userData = await axios.get(`${server.uri}users/${userId}`);

      // Update the subscription status to "cancelled"
      await axios.put(`${server.uri}users/${userId}`, {
        ...userData.data,
        membershipStatus: "cancelled",
      });

      // Fetch updated user data
      const updatedUserData = await axios.get(`${server.uri}users/${userId}`);
      setCurrentSubscription(updatedUserData.data);

      setShowCancellationDialog(false);
      // Show success toast or message
      alert("Your subscription has been cancelled successfully.");
    } catch (error) {
      console.error("Error cancelling subscription:", error);
      alert("Failed to cancel subscription. Please try again later.");
    } finally {
      setCancelLoading(false);
    }
  };

  return (
    <>
      <Header />
      <div className="content-wrapper js-content-wrapper mt-80">
        {/* <PageLinks items={breadcrumbsItems} /> */}

        <section
          className="layout-pt-md layout-pb-lg"
          style={{ backgroundColor: "#f4f0ff" }}
        >
          <div className="container">
            <div className="row justify-center text-center">
              <div className="col-auto">
                <div className="sectionTitle">
                  <h2
                    className="sectionTitle__title"
                    style={{
                      color: "#140342",
                      fontSize: "32px",
                      fontWeight: "700",
                    }}
                  >
                    Choose Your PTE Success Plan
                  </h2>
                  <p
                    className="sectionTitle__text"
                    style={{
                      fontSize: "18px",
                      marginTop: "16px",
                      color: "#666",
                    }}
                  >
                    Select the plan that best fits your preparation needs
                  </p>
                </div>
              </div>
            </div>

            {/* Display current subscription if available */}
            {hasValidSubscription() && (
              <CurrentSubscription
                subscription={currentSubscription}
                onCancel={handleCancelSubscription}
                loading={subscriptionLoading}
              />
            )}

            {/* Plans Grid */}
            <div className="row y-gap-30 justify-between pt-60 lg:pt-40">
              {loading ? (
                <div className="col-12 text-center">Loading plans...</div>
              ) : plans.length === 0 ? (
                <div className="col-12 text-center">No plans available.</div>
              ) : (
                plans.map((plan) => {
                  const features = PLAN_FEATURES[plan.planName] || [];
                  const isRecommended = plan.planName === "Standard";
                  // Check if this is the current plan
                  const isCurrentPlan =
                    hasValidSubscription() &&
                    currentSubscription.membershipPlanId ===
                      plan.membershipId &&
                    currentSubscription.membershipStatus === "active";

                  return (
                    <div key={plan.membershipId} className="col-lg-4 col-md-6">
                      <div
                        style={{
                          position: "relative",
                          borderRadius: "16px",
                          backgroundColor: "#fff",
                          boxShadow: isRecommended
                            ? "0 10px 25px rgba(20, 3, 66, 0.15)"
                            : "0 4px 12px rgba(20, 3, 66, 0.08)",
                          border: isCurrentPlan
                            ? "2px solid #027A48"
                            : isRecommended
                            ? "2px solid #140342"
                            : "1px solid #eaeaea",
                          transform: isRecommended ? "scale(1.05)" : "scale(1)",
                        }}
                      >
                        {isCurrentPlan && (
                          <div
                            style={{
                              position: "absolute",
                              top: "0",
                              right: "20px",
                              backgroundColor: "#027A48",
                              color: "#ffffff",
                              padding: "8px 16px",
                              borderBottomLeftRadius: "8px",
                              borderBottomRightRadius: "8px",
                              fontWeight: "600",
                              fontSize: "14px",
                              boxShadow: "0 4px 8px rgba(2, 122, 72, 0.25)",
                              zIndex: 2,
                            }}
                          >
                            Current Plan
                          </div>
                        )}
                        {!isCurrentPlan && isRecommended && (
                          <div
                            style={{
                              position: "absolute",
                              top: "0",
                              right: "20px",
                              backgroundColor: "#140342",
                              color: "#ffffff",
                              padding: "8px 16px",
                              borderBottomLeftRadius: "8px",
                              borderBottomRightRadius: "8px",
                              fontWeight: "600",
                              fontSize: "14px",
                              boxShadow: "0 4px 8px rgba(20, 3, 66, 0.25)",
                              zIndex: 2,
                            }}
                          >
                            Recommended
                          </div>
                        )}
                        <div style={{ padding: "32px" }}>
                          <h4
                            style={{
                              fontSize: "24px",
                              fontWeight: "700",
                              color: "#140342",
                            }}
                          >
                            {plan.planName}
                          </h4>
                          <div
                            style={{
                              backgroundColor: "#f4f0ff",
                              padding: "8px 12px",
                              borderRadius: "8px",
                              fontSize: "14px",
                              fontWeight: "500",
                              color: "#140342",
                              marginBottom: "16px",
                              marginTop: "8px",
                              display: "inline-block",
                            }}
                          >
                            {plan.durationInDays} days
                          </div>
                          <div style={{ margin: "24px 0" }}>
                            <span
                              style={{
                                fontSize: "48px",
                                fontWeight: "800",
                                color: "#140342",
                              }}
                            >
                              ${plan.price}
                            </span>
                            <span
                              style={{
                                color: "#666",
                                fontSize: "16px",
                                fontWeight: "500",
                              }}
                            >
                              /month
                            </span>
                          </div>
                          <ul
                            style={{ padding: 0, margin: 0, listStyle: "none" }}
                          >
                            {features.map((feature, idx) => (
                              <li
                                key={idx}
                                style={{
                                  display: "flex",
                                  alignItems: "center",
                                  marginBottom: "16px",
                                  fontSize: "16px",
                                  color: "#333",
                                }}
                              >
                                <span
                                  style={{
                                    backgroundColor: "#f4f0ff",
                                    borderRadius: "50%",
                                    minWidth: "24px",
                                    height: "24px",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    marginRight: "12px",
                                    marginTop: "2px",
                                  }}
                                >
                                  <svg
                                    width="14"
                                    height="14"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                  >
                                    <path
                                      d="M20 6L9 17L4 12"
                                      stroke="#140342"
                                      strokeWidth="3"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    />
                                  </svg>
                                </span>
                                {feature}
                              </li>
                            ))}
                          </ul>
                          <button
                            style={{
                              display: "block",
                              padding: "16px",
                              borderRadius: "8px",
                              border: isCurrentPlan
                                ? "2px solid #027A48"
                                : isRecommended
                                ? "none"
                                : "2px solid #140342",
                              backgroundColor: isCurrentPlan
                                ? "transparent"
                                : isRecommended
                                ? "#140342"
                                : "transparent",
                              color: isCurrentPlan
                                ? "#027A48"
                                : isRecommended
                                ? "#ffffff"
                                : "#140342",
                              fontWeight: "600",
                              fontSize: "16px",
                              textAlign: "center",
                              width: "100%",
                              cursor: isCurrentPlan ? "default" : "pointer",
                              marginTop: "24px",
                            }}
                            onClick={() => {
                              if (!isCurrentPlan) {
                                setSelectedPlan({
                                  ...plan,
                                  price: `$${plan.price}`,
                                  membershipId: plan.membershipId,
                                  name: plan.planName,
                                });
                                setShowModal(true);
                              }
                            }}
                          >
                            {isCurrentPlan ? "Current Plan" : "Get Started"}
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })
              )}
            </div>

            {/* Bottom FAQs Section */}
            <div className="row justify-center text-center pt-80">
              <div className="col-lg-8">
                <div
                  style={{
                    backgroundColor: "#ffffff",
                    padding: "32px",
                    borderRadius: "16px",
                    boxShadow: "0 4px 12px rgba(20, 3, 66, 0.08)",
                  }}
                >
                  <h3
                    style={{
                      fontSize: "22px",
                      fontWeight: "700",
                      color: "#140342",
                      marginBottom: "16px",
                    }}
                  >
                    Have Questions?
                  </h3>
                  <p
                    style={{
                      fontSize: "16px",
                      color: "#666",
                      marginBottom: "24px",
                    }}
                  >
                    Not sure which plan is right for you? Our support team is
                    ready to help you choose the best option for your PTE exam
                    preparation.
                  </p>
                  <button
                    style={{
                      backgroundColor: "#140342",
                      color: "#ffffff",
                      border: "none",
                      padding: "14px 32px",
                      borderRadius: "8px",
                      fontSize: "16px",
                      fontWeight: "600",
                      cursor: "pointer",
                      transition: "all 0.3s ease",
                    }}
                    onMouseOver={(e) => {
                      e.currentTarget.style.backgroundColor = "#1e065e";
                    }}
                    onMouseOut={(e) => {
                      e.currentTarget.style.backgroundColor = "#140342";
                    }}
                  >
                    Contact Support
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        <FooterOne />
      </div>

      {/* Stripe Checkout Modal */}
      <StripeCheckoutModal
        open={showModal}
        plan={selectedPlan}
        onSuccess={handlePaymentSuccess}
        onClose={() => setShowModal(false)}
      />

      {/* Success Dialog */}
      <SuccessDialog
        isOpen={showSuccessDialog}
        onClose={() => setShowSuccessDialog(false)}
        membershipDetails={membershipDetails}
        planName={selectedPlan?.name}
      />

      {/* Cancellation Dialog */}
      <CancellationDialog
        isOpen={showCancellationDialog}
        onClose={() => setShowCancellationDialog(false)}
        onConfirm={confirmCancellation}
        loading={cancelLoading}
      />
    </>
  );
};

export default PricingPage;
