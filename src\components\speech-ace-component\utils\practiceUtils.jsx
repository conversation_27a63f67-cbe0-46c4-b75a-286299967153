import { useState, useEffect } from "react";
import { server } from "@/api/services/server";
import { useAuth } from "../../others/AuthContext";

// Standalone function to log a practice attempt (user-specific)
export const logPracticeAttempt = async (questionId) => {
  if (!questionId) {
    console.error("No question ID provided");
    return { success: false, error: "No question ID provided" };
  }

  // Get userId from localStorage as a fallback if AuthContext is not available
  const userId = localStorage.getItem("isUserId");

  if (!userId) {
    console.error("No user ID found in localStorage");
    return { success: false, error: "No user ID found" };
  }

  try {
    // Fetch question with user-specific questionmetas
    const filterQuery = {
      include: [
        {
          relation: "questionmetas",
          scope: {
            where: {
              userId: userId,
            },
          },
        },
      ],
    };

    const response = await fetch(
      `${server.uri}questions/${questionId}?filter=${encodeURIComponent(
        JSON.stringify(filterQuery)
      )}`
    );
    if (!response.ok) {
      throw new Error(`Failed to fetch question data (${response.status})`);
    }

    const questionData = await response.json();

    // Check if user has existing questionmeta
    const existingMeta =
      questionData.questionmetas && questionData.questionmetas.length > 0
        ? questionData.questionmetas[0]
        : null;

    if (existingMeta && existingMeta.metaId) {
      // Update existing questionmeta
      const currentCount = parseInt(existingMeta.practiceCount || 0);
      const newCount = currentCount + 1;

      const updatedMeta = {
        ...existingMeta,
        practiceCount: newCount.toString(),
        practiceStatus: "Done",
        createdAt: new Date().toISOString(),
      };

      const updateResponse = await fetch(
        `${server.uri}questionmetas/${existingMeta.metaId}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updatedMeta),
        }
      );

      if (!updateResponse.ok) {
        throw new Error(
          `Failed to update questionmeta (${updateResponse.status})`
        );
      }

      return { practiceCount: newCount, success: true };
    } else {
      // Create new questionmeta
      const newMeta = {
        questionId: questionId,
        userId: userId,
        questionName: questionData.prompt || "",
        practiceCount: "1",
        practiceStatus: "Done",
        markColor: "",
        isShadowed: false,
        enableDiscussion: true,
        keywords: "",
        prepTime: "",
        answerTime: "",
        createdAt: new Date().toISOString(),
        discussion: [],
      };

      const createResponse = await fetch(`${server.uri}questionmetas`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newMeta),
      });

      if (!createResponse.ok) {
        throw new Error(
          `Failed to create questionmeta (${createResponse.status})`
        );
      }

      return { practiceCount: 1, success: true };
    }
  } catch (error) {
    console.error("Error logging practice:", error);
    return { success: false, error: error.message };
  }
};

// Custom hook to fetch user-specific practice info
export const usePracticeInfo = (questionId) => {
  const { user } = useAuth();
  const [practiceCount, setPracticeCount] = useState(0);
  const [isPracticed, setIsPracticed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPracticeInfo = async () => {
      if (!questionId) return;

      // Get userId from AuthContext or localStorage
      const userId = user?.id || localStorage.getItem("isUserId");

      if (!userId) {
        console.error("No user ID found");
        setError("No user ID found");
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        // Fetch question with user-specific questionmetas
        const filterQuery = {
          include: [
            {
              relation: "questionmetas",
              scope: {
                where: {
                  userId: userId,
                },
              },
            },
          ],
        };

        const response = await fetch(
          `${server.uri}questions/${questionId}?filter=${encodeURIComponent(
            JSON.stringify(filterQuery)
          )}`
        );
        if (!response.ok) {
          throw new Error(`Failed to fetch practice info (${response.status})`);
        }

        const questionData = await response.json();

        // Check if user has questionmeta
        const userMeta =
          questionData.questionmetas && questionData.questionmetas.length > 0
            ? questionData.questionmetas[0]
            : null;

        if (userMeta) {
          setPracticeCount(parseInt(userMeta.practiceCount || 0));
          setIsPracticed(userMeta.practiceStatus === "Done");
        } else {
          // No user-specific data exists yet
          setPracticeCount(0);
          setIsPracticed(false);
        }
      } catch (err) {
        console.error("Error fetching practice info:", err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPracticeInfo();
  }, [questionId, user?.id]);

  return {
    practiceCount,
    setPracticeCount,
    isPracticed,
    setIsPracticed,
    isLoading,
    error,
  };
};
