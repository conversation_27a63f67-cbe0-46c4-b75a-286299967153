import React, { useState, useEffect } from "react";
import { CircularProgress } from "@mui/material";
import { toast } from "react-toastify";
import { useAuth } from "../others/AuthContext";
import EssayScoreDialog from "../speech-ace-component/EssayScoreDialog";
import UserAnswersEssay from "../speech-ace-component/UserAnswersEssay";
import { server } from "@/api/services/server";
import { postRequests } from "@/api/services/controller";
import {
  logPracticeAttempt,
  usePracticeInfo,
} from "../speech-ace-component/utils/practiceUtils";
// Subscription imports
import { useSubscriptionCheck } from "../../hooks/useSubscriptionCheck";
import SubscriptionModal from "../others/SubscriptionModal";
import SubscriptionIndicator from "../others/SubscriptionIndicator";
import PropTypes from "prop-types";
import SidebarToggle from "../common/SidebarToggle";

const EssayWriting = ({ question, onQuestionSelect }) => {
  const { prompt, options, duration = 1200 } = question; // 20 minutes = 1200 seconds
  const [essay, setEssay] = useState("");
  const [loading, setLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [timer, setTimer] = useState(duration);
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [aiScore, setAiScore] = useState(null);
  const [showScoreDialog, setShowScoreDialog] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [showPreviousAnswers, setShowPreviousAnswers] = useState(false);
  const {
    practiceCount,
    setPracticeCount,
    isPracticed,
    setIsPracticed,
    error,
  } = usePracticeInfo(question.questionId);

  // Subscription logic
  const {
    eligibility,
    loading: subscriptionLoading,
    showSubscriptionModal,
    canViewAnswer,
    handleSubmitWithCheck,
    closeSubscriptionModal,
    showSubscriptionRequiredModal,
  } = useSubscriptionCheck(question.questionId);

  // Get user ID from localStorage
  const user_id = localStorage.getItem("isUserId");
  const { user } = useAuth();

  // Extract the actual user ID regardless of format
  const getUserId = () => {
    let userInfo = user?.id || user_id || "";
    if (typeof userInfo === "string") {
      try {
        const parsed = JSON.parse(userInfo);
        return parsed.id || parsed || userInfo;
      } catch (e) {
        return userInfo;
      }
    }
    return userInfo;
  };

  // Handle text input
  const handleEssayChange = (e) => {
    const newText = e.target.value;
    setEssay(newText);
    setWordCount(getWordCount(newText));
  };

  // Format time as mm:ss
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Handle form submission with subscription check
  const handleDone = async () => {
    if (essay.trim() === "") {
      toast.error("Please write your essay before submitting");
      return;
    }

    // Check subscription before proceeding
    const success = await handleSubmitWithCheck(async () => {
      setLoading(true);

      const result = await logPracticeAttempt(question.questionId);
      if (result.success) {
        setPracticeCount(result.practiceCount);
        setIsPracticed(true);
      } else {
        toast.error(result.error || "Failed to log practice attempt.");
      }
      try {
        // Get user info
        const userInfo = getUserId();

        // Prepare data to send to the server
        const essayData = {
          text: essay,
          user_id: userInfo,
          category_id: question?.categoryId || "",
          section: question?.section || "",
          prompt: prompt || "Write an essay",
        };

        // Send to server for AI scoring
        const scoringResponse = await fetch(
          "https://deep-ai.up.railway.app/api/essay-scoring",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json",
            },
            body: JSON.stringify(essayData),
            credentials: "include",
          }
        );

        const rawResponse = await scoringResponse.text();

        // Parse response if it's valid
        let scoreResult;
        if (rawResponse && rawResponse.trim()) {
          try {
            scoreResult = JSON.parse(rawResponse);
          } catch (parseError) {
            throw new Error("Invalid response from server");
          }
        } else {
          throw new Error("Empty response from server");
        }

        if (!scoringResponse.ok) {
          throw new Error(
            `Server error: ${scoreResult?.error || scoringResponse.statusText}`
          );
        }

        // Ensure score and userId are strings
        const scoreAsString = String(
          scoreResult.score || scoreResult.totalScore || "0"
        );
        const userIdAsString = String(userInfo);

        // Send to your answers API
        const uri = server.uri + "answers";
        const answersResponse = await postRequests(uri, {
          score: scoreAsString,
          answer: essay,
          questionId: question?.questionId || "",
          userId: userIdAsString,
          section: question?.section || "",

          additionalProps: {
            originalPayload: essayData,
            scoreResponse: scoreResult,
          },
        });

        // Check if the response indicates an error
        if (answersResponse.status >= 400) {
          const errorText =
            answersResponse.data?.error?.message ||
            answersResponse.statusText ||
            "Unknown error";
          throw new Error(`Error saving to answers API: ${errorText}`);
        }

        const answersResult = answersResponse.data;

        setAiScore(scoreResult);
        setIsSubmitted(true);
        setShowScoreDialog(true); // Show the dialog immediately after getting score
        toast.success("Essay submitted successfully");
      } catch (error) {
        toast.error(`Submission error: ${error.message}`);
      } finally {
        setLoading(false);
      }
    });

    if (!success) {
      setLoading(false);
    }
  };

  // Handle reset
  const handleRedo = () => {
    setEssay("");
    setIsSubmitted(false);
    setAiScore(null);
    setTimer(duration);
    setWordCount(0);
  };

  // Countdown timer logic
  useEffect(() => {
    if (timer > 0 && !isSubmitted) {
      const interval = setInterval(() => setTimer((prev) => prev - 1), 1000);
      return () => clearInterval(interval);
    } else if (timer === 0 && !isSubmitted) {
      handleDone(); // Auto-submit when time runs out
    }
  }, [timer, isSubmitted]);

  // Count words in text
  function getWordCount(text) {
    if (typeof text !== "string" || text.trim() === "") return 0;
    const words = text.trim().split(/\s+/);
    return words.length;
  }

  // Update screen width on resize
  useEffect(() => {
    const handleResize = () => setScreenWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const isSmallScreen = screenWidth <= 768;

  // Function to show score dialog
  const handleViewScore = () => {
    setShowScoreDialog(true);
  };

  // Function to close score dialog
  const handleCloseDialog = () => {
    setShowScoreDialog(false);
  };

  // Toggle previous answers view
  const togglePreviousAnswers = () => {
    setShowPreviousAnswers(!showPreviousAnswers);
  };

  // Get word count status color
  const getWordCountColor = () => {
    if (wordCount < 200) return "#ff3c00"; // Red if below minimum
    if (wordCount > 300) return "#ff3c00"; // Red if above maximum
    return "#4caf50"; // Green if within range
  };

  return (
    <div
      style={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: "20px",
        backgroundColor: "#f9f9f9",
        borderRadius: "10px",
        position: "relative",
      }}
      className="question-container"
    >
      {/* Subscription Indicator */}
      <SubscriptionIndicator
        eligibility={eligibility}
        loading={subscriptionLoading}
        position="top-right"
      />

      {/* Score Dialog Component */}
      {aiScore && (
        <EssayScoreDialog
          aiScore={aiScore}
          isOpen={showScoreDialog}
          onClose={handleCloseDialog}
        />
      )}

      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          width: isSmallScreen ? "100%" : "90%",
          marginBottom: "20px",
        }}
      >
        <div
          style={{
            backgroundColor: timer <= 120 ? "#f44336" : "#ff9800",
            color: "white",
            padding: "5px 15px",
            borderRadius: "20px",
            fontWeight: "bold",
          }}
        >
          Remaining: {formatTime(timer)}
        </div>
      </div>

      <div
        style={{
          display: "flex",
          flexDirection: "column",
          width: isSmallScreen ? "100%" : "90%",
          marginBottom: "20px",
        }}
      >
        <p
          className="font-bold my-3"
          style={{
            color: "#333",
            fontWeight: "600",
            fontSize: "18px",
            lineHeight: 1.5,
            textAlign: "justify",
            marginBottom: "10px",
            padding: "15px",
            backgroundColor: "#f0f0f0",
            borderRadius: "5px",
            border: "1px solid #e0e0e0",
          }}
        >
          {prompt}
          <div style={{ marginTop: "10px", fontSize: "14px", color: "#666" }}>
            Write 200-300 words in 20 minutes.
          </div>
        </p>

        <textarea
          value={essay}
          onChange={handleEssayChange}
          disabled={isSubmitted || loading}
          placeholder="Write your essay here..."
          style={{
            minHeight: "40vh",
            borderRadius: 5,
            borderWidth: 2,
            borderColor: isSubmitted ? "#ccc" : "black",
            borderStyle: "solid",
            marginTop: 20,
            fontSize: 15,
            color: "black",
            padding: 15,
            fontWeight: "500",
            fontFamily: "Arial, sans-serif",
            resize: "vertical",
          }}
        />

        <div
          className="controls"
          style={{
            fontSize: "14px",
            marginTop: "10px",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <p style={{ color: getWordCountColor(), fontWeight: "bold" }}>
            Word Count: {wordCount}
            {wordCount < 200 && " (Minimum 200 words required)"}
            {wordCount > 300 && " (Maximum 300 words allowed)"}
          </p>
          <p>Characters: {essay.length}</p>
        </div>
      </div>

      <div
        className="my-7"
        style={{
          display: "flex",
          flexDirection: isSmallScreen ? "column" : "row",
          justifyContent: "space-between",
          alignItems: isSmallScreen ? "stretch" : "center",
          width: isSmallScreen ? "100%" : "90%",
          marginTop: "20px",
          gap: isSmallScreen ? "10px" : "0",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: "12px",
            flexWrap: isSmallScreen ? "wrap" : "nowrap",
          }}
        >
          {!isSubmitted ? (
            <>
              <button
                style={{
                  backgroundColor: loading ? "#a5a5a5" : "#0078d4",
                  padding: "10px 20px",
                  borderRadius: "5px",
                  border: "none",
                  cursor: loading ? "default" : "pointer",
                  fontSize: "14px",
                  color: "white",
                  textAlign: "center",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  minWidth: "100px",
                }}
                onClick={handleDone}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <span style={{ marginRight: "10px" }}>Submitting</span>
                    <CircularProgress
                      size={16}
                      thickness={4}
                      style={{ color: "white" }}
                    />
                  </>
                ) : (
                  "Submit"
                )}
              </button>
              <button
                style={{
                  backgroundColor: "#f5f5f5",
                  padding: "10px 20px",
                  borderRadius: "5px",
                  border: "1px solid #140342",
                  cursor: "pointer",
                  fontSize: "14px",
                  color: "#140342",
                  textAlign: "center",
                }}
                onClick={handleRedo}
                disabled={loading}
              >
                Reset
              </button>
              <div
                style={{ display: "flex", gap: "15px", alignItems: "center" }}
              >
                <button
                  onClick={togglePreviousAnswers}
                  style={{
                    backgroundColor: "#f5f5f5",
                    padding: "10px 20px",
                    borderRadius: "5px",
                    border: "1px solid #140342",
                    cursor: "pointer",
                    fontSize: "14px",
                    color: "#140342",
                    textAlign: "center",
                  }}
                >
                  {showPreviousAnswers
                    ? "Hide Past Submissions"
                    : "Show Past Submissions"}
                </button>
              </div>
            </>
          ) : (
            /* Show "View Score" button after submission */
            <button
              style={{
                backgroundColor: "#140342",
                padding: "10px 20px",
                borderRadius: "5px",
                border: "none",
                cursor: "pointer",
                fontSize: "14px",
                color: "white",
                textAlign: "center",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                minWidth: "100px",
              }}
              onClick={handleViewScore}
            >
              View Score
            </button>
          )}

          {isSubmitted && (
            <button
              style={{
                backgroundColor: "#f5f5f5",
                padding: "10px 20px",
                borderRadius: "5px",
                border: "1px solid #140342",
                cursor: "pointer",
                fontSize: "14px",
                color: "#140342",
                textAlign: "center",
              }}
              onClick={handleRedo}
            >
              Try Again
            </button>
          )}
        </div>
      </div>

      {/* Add keyframe animation for loading spinner */}
      <style>{`
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>

      {/* Subscription Modal */}
      <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={closeSubscriptionModal}
        eligibility={eligibility}
        title="Subscription Required"
        message="Continue practicing with detailed feedback and analysis."
      />

      {/* Sidebar Toggle */}
      <SidebarToggle
        isVisible={true}
        activeCategory="writing"
        currentQuestionType="67d893d673e32a15ba3d88c1"
        onQuestionSelect={onQuestionSelect}
      />

      {/* Show previous answers if toggled */}
      {showPreviousAnswers && (
        <div
          style={{
            width: isSmallScreen ? "100%" : "90%",
            marginBottom: "20px",
          }}
        >
          <UserAnswersEssay
            userId={getUserId()}
            questionId={question?.id || question?.questionId || ""}
          />
        </div>
      )}
    </div>
  );
};

EssayWriting.propTypes = {
  question: PropTypes.shape({
    prompt: PropTypes.string,
    options: PropTypes.array,
    duration: PropTypes.number,
    questionId: PropTypes.string,
    categoryId: PropTypes.string,
    section: PropTypes.string,
    id: PropTypes.string,
  }).isRequired,
  onQuestionSelect: PropTypes.func.isRequired,
};

export default EssayWriting;
