export const requirements = [
  "You will need a copy of Adobe XD 2019 or above. A free trial can be downloaded from Adobe.",
  "No previous design experience is needed.",
  "No previous Adobe XD skills are needed.",
];

export const learnList = [
  "Become a UX designer.",
  "You will be able to add UX designer to your CV",
  "Become a UI designer.",
  "Build & test a full website design.",
  "Create your first UX brief & persona.",
  "How to use premade UI kits.",
  "Create quick wireframes.",
  "Downloadable exercise files",
  "Build a UX project from beginning to end.",
  "Learn to design websites & mobile phone apps.",
  "All the techniques used by UX professionals",
  "You will be able to talk correctly with other UX design.",
];

export const reviews = [
  {
    id: 1,
    avatarSrc: "/assets/img/avatars/1.png",
    name: "<PERSON>",
    date: "3 Days ago",
    rating: 5,
    title: "The best LMS Design",
    comment:
      "This course is a very applicable. Professor <PERSON> explains precisely each algorithm and even tries to give an intuition for mathematical and statistic concepts behind each algorithm. Thank you very much.",
  },
  {
    id: 2,
    avatarSrc: "/assets/img/avatars/1.png",
    name: "<PERSON>",
    date: "3 Days ago",
    rating: 5,
    title: "The best LMS Design",
    comment:
      "This course is a very applicable. Professor <PERSON> explains precisely each algorithm and even tries to give an intuition for mathematical and statistic concepts behind each algorithm. Thank you very much.",
  },
  {
    id: 3,
    avatarSrc: "/assets/img/avatars/1.png",
    name: "Ali Tufan",
    date: "3 Days ago",
    rating: 5,
    title: "The best LMS Design",
    comment:
      "This course is a very applicable. Professor Ng explains precisely each algorithm and even tries to give an intuition for mathematical and statistic concepts behind each algorithm. Thank you very much.",
  },
  // Add more comment objects as needed
];

export const lessonItems = [
  {
    id: 1,
    title: "Course Content",
    duration: "87 min",
    lessons: [
      { id: 1, title: "Introduction to the User", duration: "03:56" },
      { id: 2, title: "Getting started with your", duration: "03:56" },
      {
        id: 3,
        title:
          "What is UI vs UX - User Interface vs User Experience vs Product",
        duration: "03:56",
      },
      { id: 4, title: "Wireframing (low fidelity) in", duration: "03:56" },
      { id: 5, title: "Viewing your prototype on", duration: "03:56" },
      { id: 6, title: "Sharing your design", duration: "03:56" },
    ],
  },
  {
    id: 2,
    title: "The Brief",
    duration: "87 min",
    lessons: [
      {
        id: 1,
        title: "Introduction to the User",
        duration: "03:56",
        questions: 5,
      },
      {
        id: 2,
        title: "Getting started with your",
        duration: "03:56",
        questions: 5,
      },
      {
        id: 3,
        title:
          "What is UI vs UX - User Interface vs User Experience vs Product",
        duration: "03:56",
        questions: 5,
      },
      {
        id: 4,
        title: "Wireframing (low fidelity) in",
        duration: "03:56",
        questions: 5,
      },
      {
        id: 5,
        title: "Viewing your prototype on",
        duration: "03:56",
        questions: 5,
      },
      { id: 6, title: "Sharing your design", duration: "03:56", questions: 5 },
    ],
  },
  {
    id: 3,
    title: "Type, Color & Icon Introduction",
    duration: "87 min",
    lessons: [
      {
        id: 1,
        title: "Introduction to the User",
        duration: "03:56",
        questions: 5,
      },
      {
        id: 2,
        title: "Getting started with your",
        duration: "03:56",
        questions: 5,
      },
      {
        id: 3,
        title:
          "What is UI vs UX - User Interface vs User Experience vs Product",
        duration: "03:56",
        questions: 5,
      },
      {
        id: 4,
        title: "Wireframing (low fidelity) in",
        duration: "03:56",
        questions: 5,
      },
      {
        id: 5,
        title: "Viewing your prototype on",
        duration: "03:56",
        questions: 5,
      },
      { id: 6, title: "Sharing your design", duration: "03:56", questions: 5 },
    ],
  },
  {
    id: 4,
    title: "Prototyping a App - Introduction",
    duration: "87 min",
    lessons: [
      {
        id: 1,
        title: "Introduction to the User",
        duration: "03:56",
        questions: 5,
      },
      {
        id: 2,
        title: "Getting started with your",
        duration: "03:56",
        questions: 5,
      },
      {
        id: 3,
        title:
          "What is UI vs UX - User Interface vs User Experience vs Product",
        duration: "03:56",
        questions: 5,
      },
      {
        id: 4,
        title: "Wireframing (low fidelity) in",
        duration: "03:56",
        questions: 5,
      },
      {
        id: 5,
        title: "Viewing your prototype on",
        duration: "03:56",
        questions: 5,
      },
      { id: 6, title: "Sharing your design", duration: "03:56", questions: 5 },
    ],
  },
  {
    id: 5,
    title: "Wireframe Feedback",
    duration: "87 min",
    lessons: [
      {
        id: 1,
        title: "Introduction to the User",
        duration: "03:56",
        questions: 5,
      },
      {
        id: 2,
        title: "Getting started with your",
        duration: "03:56",
        questions: 5,
      },
      {
        id: 3,
        title:
          "What is UI vs UX - User Interface vs User Experience vs Product",
        duration: "03:56",
        questions: 5,
      },
      {
        id: 4,
        title: "Wireframing (low fidelity) in",
        duration: "03:56",
        questions: 5,
      },
      {
        id: 5,
        title: "Viewing your prototype on",
        duration: "03:56",
        questions: 5,
      },
      { id: 6, title: "Sharing your design", duration: "03:56", questions: 5 },
    ],
  },
];
