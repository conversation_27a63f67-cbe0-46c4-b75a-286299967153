export const coursesData = [
  {
    id: 1,
    imageSrc: "/assets/img/coursesCards/speaking.jpeg",
    authorImageSrc: "/assets/img/general/avatar-1.png",
    title: "Speaking",
    rating: 4.3,
    ratingCount: 1991,
    lessonCount: 6,
    duration: 1320,
    level: "Beginner",
    originalPrice: 199,
    discountedPrice: 79,
    paid: true,
    category: "Design",
    state: "Popular",
    languange: "French",
    authorName: "<PERSON>",
    viewStatus: "Good",
    difficulty: "Easy",
    desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  },
  {
    id: 2,
    imageSrc: "/assets/img/coursesCards/writing.jpeg",
    authorImageSrc: "/assets/img/general/avatar-1.png",
    title: "Writing",
    rating: 4.7,
    ratingCount: 1991,
    lessonCount: 6,
    duration: 410,
    level: "Expert",
    originalPrice: 189,
    discountedPrice: 89,
    paid: true,
    category: "Programming",
    languange: "German",
    authorName: "<PERSON>",
    popular: true,
    new: true,
    bestSeller: true,
    state: "Fetured",
    viewStatus: "Low",
    difficulty: "Easy",
    desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  },
  {
    id: 3,
    imageSrc: "/assets/img/coursesCards/reading.jpeg",
    authorImageSrc: "/assets/img/general/avatar-1.png",
    title: "Reading ",
    rating: 4.5,
    ratingCount: 1991,
    lessonCount: 6,
    duration: 1220,
    level: "Intermediate",
    originalPrice: 249,
    discountedPrice: 129,
    languange: "Italian",
    authorName: "Albert Flores",
    paid: true,
    category: "Programming",
    state: "Trending",
    viewStatus: "Great",
    difficulty: "Easy",
    desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  },
  {
    id: 4,
    imageSrc: "/assets/img/coursesCards/listening_headphone.jpeg",
    authorImageSrc: "/assets/img/general/avatar-1.png",
    title: "Listening ",
    rating: 4.2,
    ratingCount: 1991,
    lessonCount: 6,
    duration: 1020,
    level: "Expert",
    originalPrice: 179,
    discountedPrice: 99,
    languange: "English",
    authorName: "Jacob Jones",
    paid: true,
    category: "Art",
    state: "Trending",
    viewStatus: "Good",
    difficulty: "Easy",
    desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  },
  {
    id: 5,
    imageSrc: "/assets/img/coursesCards/liveClass.jpeg",
    authorImageSrc: "/assets/img/general/avatar-1.png",
    title: "live classes",
    rating: 3.8,
    ratingCount: 1991,
    lessonCount: 6,
    duration: 250,
    level: "Intermediate",
    originalPrice: 169,
    discountedPrice: 79,
    paid: true,
    category: "Photography",
    languange: "French",
    authorName: "Robert Fox",
    state: "Popular",
    viewStatus: "Low",
    difficulty: "Meduium",
    desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  },
  // {
  //   id: 6,
  //   imageSrc: "/assets/img/coursesCards/2.png",
  //   imageAlt: "image",
  //   rating: 4.6,
  //   ratingCount: 1991,
  //   title: "Complete Blender Creator: Learn 3D Modelling for Beginners",
  //   lessonCount: 6,
  //   duration: 800,
  //   level: "Expert",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "German",
  //   authorName: "Jane Cooper",
  //   originalPrice: 209,
  //   discountedPrice: 99,
  //   paid: true,
  //   category: "Animation",
  //   state: "Fetured",
  //   viewStatus: "Good",
  //   difficulty: "Meduium",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 7,
  //   imageSrc: "/assets/img/coursesCards/7.png",
  //   imageAlt: "image",
  //   rating: 3.5,
  //   ratingCount: 1991,
  //   title: "The Complete Financial Analyst Training & Investing Course",
  //   lessonCount: 6,
  //   duration: 490,
  //   level: "Intermediate",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "Italian",
  //   authorName: "Jenny Wilson",
  //   originalPrice: 299,
  //   discountedPrice: 149,
  //   paid: true,
  //   category: "Writing",
  //   state: "Popular",
  //   viewStatus: "Great",
  //   difficulty: "Hard",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 8,
  //   imageSrc: "/assets/img/coursesCards/5.png",
  //   imageAlt: "image",
  //   rating: 4.8,
  //   ratingCount: 1991,
  //   title: "Photography Masterclass: A Complete Guide to Photography",
  //   lessonCount: 6,
  //   duration: 500,
  //   level: "Expert",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "English",
  //   authorName: "Albert Flores",
  //   originalPrice: 219,
  //   discountedPrice: 109,
  //   paid: true,
  //   category: "Photography",
  //   state: "Fetured",
  //   viewStatus: "Medium",
  //   difficulty: "Meduium",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 9,
  //   imageSrc: "/assets/img/coursesCards/6.png",
  //   imageAlt: "image",
  //   rating: 3.9,
  //   ratingCount: 1991,
  //   title: "Complete Python Bootcamp From Zero to Hero in Python",
  //   lessonCount: 6,
  //   duration: 920,
  //   level: "Intermediate",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "French",
  //   authorName: "Jacob Jones",
  //   originalPrice: 199,
  //   discountedPrice: 89,
  //   paid: true,
  //   category: "Programming",
  //   state: "Popular",
  //   viewStatus: "Great",
  //   difficulty: "Hard",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 10,
  //   imageSrc: "/assets/img/coursesCards/5.png",
  //   imageAlt: "image",
  //   rating: 4.2,
  //   ratingCount: 1991,
  //   title: "Angular - The Complete Guide (2022 Edition)",
  //   lessonCount: 6,
  //   duration: 720,
  //   level: "Beginner",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "German",
  //   authorName: "Robert Fox",
  //   originalPrice: 189,
  //   discountedPrice: 99,
  //   paid: true,
  //   category: "Writing",
  //   state: "Trending",
  //   viewStatus: "Medium",
  //   difficulty: "Meduium",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 11,
  //   imageSrc: "/assets/img/coursesCards/2.png",
  //   imageAlt: "image",
  //   rating: 5,
  //   ratingCount: 1991,
  //   title: "Web Design for Beginners: Real World Coding in HTML & CSS",
  //   lessonCount: 6,
  //   duration: 120,
  //   level: "Intermediate",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "Italian",
  //   authorName: "Jane Cooper",
  //   originalPrice: 209,
  //   discountedPrice: 119,
  //   paid: true,
  //   category: "Design",
  //   state: "Popular",
  //   viewStatus: "Medium",
  //   difficulty: "Hard",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 12,
  //   imageSrc: "/assets/img/coursesCards/4.png",
  //   imageAlt: "image",
  //   rating: 5,
  //   ratingCount: 1991,
  //   title: "The Ultimate Drawing Course Beginner to Advanced",
  //   lessonCount: 6,
  //   duration: 1440,
  //   level: "Beginner",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "English",
  //   authorName: "Jenny Wilson",
  //   originalPrice: 209,
  //   discountedPrice: 119,
  //   paid: true,
  //   category: "Design",
  //   state: "Popular",
  //   viewStatus: "Medium",
  //   difficulty: "Hard",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 13,
  //   imageSrc: "/assets/img/coursesCards/10.png",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   title: "Advanced JavaScript: Exploring ES6 and Beyond",
  //   rating: 4.3,
  //   ratingCount: 1991,
  //   lessonCount: 6,
  //   duration: 1320,
  //   level: "Intermediate",
  //   originalPrice: 199,
  //   discountedPrice: 79,
  //   paid: true,
  //   category: "Design",
  //   state: "Popular",
  //   languange: "French",
  //   authorName: "Albert Flores",
  //   viewStatus: "Good",
  //   difficulty: "Easy",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 14,
  //   imageSrc: "/assets/img/coursesCards/8.png",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   title: "Python for Data Analysis: NumPy, Pandas, and Matplotlib",
  //   rating: 4.7,
  //   ratingCount: 1991,
  //   lessonCount: 6,
  //   duration: 410,
  //   level: "Beginner",
  //   originalPrice: 189,
  //   discountedPrice: 89,
  //   paid: true,
  //   category: "Programming",
  //   languange: "German",
  //   authorName: "Jacob Jones",
  //   popular: true,
  //   new: true,
  //   bestSeller: true,
  //   state: "Fetured",
  //   viewStatus: "Low",
  //   difficulty: "Easy",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 15,
  //   imageSrc: "/assets/img/coursesCards/6.png",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   title: "Building Responsive Websites with Bootstrap 5",
  //   rating: 4.5,
  //   ratingCount: 1991,
  //   lessonCount: 6,
  //   duration: 1220,
  //   level: "Intermediate",
  //   originalPrice: 249,
  //   discountedPrice: 129,
  //   languange: "Italian",
  //   authorName: "Robert Fox",
  //   paid: true,
  //   category: "Programming",
  //   state: "Trending",
  //   viewStatus: "Great",
  //   difficulty: "Easy",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 16,
  //   imageSrc: "/assets/img/coursesCards/7.png",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   title: "Social Media Marketing: Strategies for Growing Your Audience",
  //   rating: 4.2,
  //   ratingCount: 1991,
  //   lessonCount: 6,
  //   duration: 1020,
  //   level: "Beginner",
  //   originalPrice: 179,
  //   discountedPrice: 99,
  //   languange: "English",
  //   authorName: "Jane Cooper",
  //   paid: true,
  //   category: "Art",
  //   state: "Trending",
  //   viewStatus: "Good",
  //   difficulty: "Easy",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 17,
  //   imageSrc: "/assets/img/coursesCards/10.png",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   title: "Landscape Photography: Capturing Nature's Beauty",
  //   rating: 3.8,
  //   ratingCount: 1991,
  //   lessonCount: 6,
  //   duration: 250,
  //   level: "Intermediate",
  //   originalPrice: 169,
  //   discountedPrice: 79,
  //   paid: true,
  //   category: "Photography",
  //   languange: "French",
  //   authorName: "Jenny Wilson",
  //   state: "Popular",
  //   viewStatus: "Low",
  //   difficulty: "Meduium",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 18,
  //   imageSrc: "/assets/img/coursesCards/1.png",
  //   imageAlt: "image",
  //   rating: 4.6,
  //   ratingCount: 1991,
  //   title: "Unity 3D Game Development: Create Your First FPS Game",
  //   lessonCount: 6,
  //   duration: 800,
  //   level: "Expert",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "German",
  //   authorName: "Albert Flores",
  //   originalPrice: 209,
  //   discountedPrice: 99,
  //   paid: true,
  //   category: "Animation",
  //   state: "Fetured",
  //   viewStatus: "Good",
  //   difficulty: "Meduium",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 19,
  //   imageSrc: "/assets/img/coursesCards/2.png",
  //   imageAlt: "image",
  //   rating: 3.5,
  //   ratingCount: 1991,
  //   title: "Personal Finance and Investing: Securing Your Financial Future",
  //   lessonCount: 6,
  //   duration: 490,
  //   level: "Intermediate",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "Italian",
  //   authorName: "Jacob Jones",
  //   originalPrice: 299,
  //   discountedPrice: 149,
  //   paid: true,
  //   category: "Writing",
  //   state: "Popular",
  //   viewStatus: "Great",
  //   difficulty: "Hard",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 20,
  //   imageSrc: "/assets/img/coursesCards/4.png",
  //   imageAlt: "image",
  //   rating: 4.8,
  //   ratingCount: 1991,
  //   title: "Graphic Design Principles: Layouts, Typography, and Color Theory",
  //   lessonCount: 6,
  //   duration: 500,
  //   level: "Expert",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "English",
  //   authorName: "Robert Fox",
  //   originalPrice: 219,
  //   discountedPrice: 109,
  //   paid: true,
  //   category: "Photography",
  //   state: "Fetured",
  //   viewStatus: "Medium",
  //   difficulty: "Meduium",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 21,
  //   imageSrc: "/assets/img/coursesCards/2.png",
  //   imageAlt: "image",
  //   rating: 3.9,
  //   ratingCount: 1991,
  //   title: "Node.js and Express.js: Creating Web Applications",
  //   lessonCount: 6,
  //   duration: 920,
  //   level: "Intermediate",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "French",
  //   authorName: "Jane Cooper",
  //   originalPrice: 199,
  //   discountedPrice: 89,
  //   paid: true,
  //   category: "Programming",
  //   state: "Popular",
  //   viewStatus: "Great",
  //   difficulty: "Hard",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 22,
  //   imageSrc: "/assets/img/coursesCards/4.png",
  //   imageAlt: "image",
  //   rating: 4.2,
  //   ratingCount: 1991,
  //   title: "Android App Development: Building Dynamic Mobile Apps",
  //   lessonCount: 6,
  //   duration: 720,
  //   level: "Expert",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "German",
  //   authorName: "Jenny Wilson",
  //   originalPrice: 0,
  //   discountedPrice: 0,
  //   paid: false,
  //   category: "Writing",
  //   state: "Trending",
  //   viewStatus: "Medium",
  //   difficulty: "Meduium",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 23,
  //   imageSrc: "/assets/img/coursesCards/6.png",
  //   imageAlt: "image",
  //   rating: 5,
  //   ratingCount: 1991,
  //   title: "Machine Learning in Python: Hands-On Projects",
  //   lessonCount: 6,
  //   duration: 120,
  //   level: "Intermediate",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "Italian",
  //   authorName: "Albert Flores",
  //   originalPrice: 209,
  //   discountedPrice: 119,
  //   paid: true,
  //   category: "Design",
  //   state: "Popular",
  //   viewStatus: "Medium",
  //   difficulty: "Hard",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 24,
  //   imageSrc: "/assets/img/coursesCards/6.png",
  //   imageAlt: "image",
  //   rating: 5,
  //   ratingCount: 1991,
  //   title: "Creative Writing Masterclass: Crafting Compelling Stories",
  //   lessonCount: 6,
  //   duration: 120,
  //   level: "Beginner",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "French",
  //   authorName: "Jacob Jones",
  //   originalPrice: 0,
  //   discountedPrice: 0,
  //   paid: false,
  //   category: "Design",
  //   state: "Popular",
  //   viewStatus: "Medium",
  //   difficulty: "Hard",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 25,
  //   imageSrc: "/assets/img/coursesCards/2.png",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   title: "Front-End Web Development: HTML, CSS, and JavaScript Fundamentals",
  //   rating: 4.3,
  //   ratingCount: 1991,
  //   lessonCount: 6,
  //   duration: 1320,
  //   level: "Intermediate",
  //   originalPrice: 199,
  //   discountedPrice: 79,
  //   paid: true,
  //   category: "Design",
  //   state: "Popular",
  //   languange: "English",
  //   authorName: "Robert Fox",
  //   viewStatus: "Good",
  //   difficulty: "Easy",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 26,
  //   imageSrc: "/assets/img/coursesCards/10.png",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   title: "Data Visualization with Tableau: Creating Informative Dashboards",
  //   rating: 4.7,
  //   ratingCount: 1991,
  //   lessonCount: 6,
  //   duration: 410,
  //   level: "Expert",
  //   originalPrice: 0,
  //   discountedPrice: 0,
  //   paid: false,
  //   category: "Programming",
  //   languange: "German",
  //   authorName: "Albert Flores",
  //   popular: true,
  //   new: true,
  //   bestSeller: true,
  //   state: "Fetured",
  //   viewStatus: "Low",
  //   difficulty: "Easy",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 27,
  //   imageSrc: "/assets/img/coursesCards/6.png",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   title: "Digital Illustration: Bringing Your Imagination to Life",
  //   rating: 4.5,
  //   ratingCount: 1991,
  //   lessonCount: 6,
  //   duration: 1220,
  //   level: "Expert",
  //   originalPrice: 249,
  //   discountedPrice: 129,
  //   languange: "Italian",
  //   authorName: "Jane Cooper",
  //   paid: true,
  //   category: "Programming",
  //   state: "Trending",
  //   viewStatus: "Great",
  //   difficulty: "Easy",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 28,
  //   imageSrc: "/assets/img/coursesCards/6.png",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   title: "Java Enterprise Edition (EE): Building Scalable Web Applications",
  //   rating: 4.2,
  //   ratingCount: 1991,
  //   lessonCount: 6,
  //   duration: 1020,
  //   level: "Beginner",
  //   originalPrice: 0,
  //   discountedPrice: 0,
  //   languange: "French",
  //   authorName: "Jenny Wilson",
  //   paid: false,
  //   category: "Art",
  //   state: "Trending",
  //   viewStatus: "Good",
  //   difficulty: "Easy",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 29,
  //   imageSrc: "/assets/img/coursesCards/6.png",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   title: "iOS App Design: User Interface and User Experience (UI/UX)",
  //   rating: 3.8,
  //   ratingCount: 1991,
  //   lessonCount: 6,
  //   duration: 250,
  //   level: "Expert",
  //   originalPrice: 169,
  //   discountedPrice: 79,
  //   paid: true,
  //   category: "Photography",
  //   languange: "English",
  //   authorName: "Jacob Jones",
  //   state: "Popular",
  //   viewStatus: "Low",
  //   difficulty: "Meduium",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 30,
  //   imageSrc: "/assets/img/coursesCards/9.png",
  //   imageAlt: "image",
  //   rating: 4.6,
  //   ratingCount: 1991,
  //   title: "Introduction to Artificial Intelligence: Concepts and Applications",
  //   lessonCount: 6,
  //   duration: 800,
  //   level: "Beginner",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "German",
  //   authorName: "Albert Flores",
  //   originalPrice: 0,
  //   discountedPrice: 0,
  //   paid: false,
  //   category: "Animation",
  //   state: "Fetured",
  //   viewStatus: "Good",
  //   difficulty: "Meduium",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 31,
  //   imageSrc: "/assets/img/coursesCards/7.png",
  //   imageAlt: "image",
  //   rating: 3.5,
  //   ratingCount: 1991,
  //   title: "Video Editing with Adobe Premiere Pro: From Shoot to Final Cut",
  //   lessonCount: 6,
  //   duration: 490,
  //   level: "Expert",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "Italian",
  //   authorName: "Albert Flores",
  //   originalPrice: 299,
  //   discountedPrice: 149,
  //   paid: true,
  //   category: "Writing",
  //   state: "Popular",
  //   viewStatus: "Great",
  //   difficulty: "Hard",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 32,
  //   imageSrc: "/assets/img/coursesCards/9.png",
  //   imageAlt: "image",
  //   rating: 4.8,
  //   ratingCount: 1991,
  //   title: "Cybersecurity Essentials: Protecting Your Digital World",
  //   lessonCount: 6,
  //   duration: 500,
  //   level: "Beginner",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "French",
  //   authorName: "Jane Cooper",
  //   originalPrice: 0,
  //   discountedPrice: 0,
  //   paid: false,
  //   category: "Photography",
  //   state: "Fetured",
  //   viewStatus: "Medium",
  //   difficulty: "Meduium",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 33,
  //   imageSrc: "/assets/img/coursesCards/10.png",
  //   imageAlt: "image",
  //   rating: 3.9,
  //   ratingCount: 1991,
  //   title: "Photography Post-Processing: Enhancing and Editing Your Images",
  //   lessonCount: 6,
  //   duration: 920,
  //   level: "Expert",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "English",
  //   authorName: "Jenny Wilson",
  //   originalPrice: 199,
  //   discountedPrice: 89,
  //   paid: true,
  //   category: "Programming",
  //   state: "Popular",
  //   viewStatus: "Great",
  //   difficulty: "Hard",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 34,
  //   imageSrc: "/assets/img/coursesCards/12.png",
  //   imageAlt: "image",
  //   rating: 4.2,
  //   ratingCount: 1991,
  //   title:
  //     "Full-Stack Web Development with MERN: MongoDB, Express.js, React, Node.js",
  //   lessonCount: 6,
  //   duration: 720,
  //   level: "Beginner",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "German",
  //   authorName: "Jacob Jones",
  //   originalPrice: 0,
  //   discountedPrice: 0,
  //   paid: false,
  //   category: "Writing",
  //   state: "Trending",
  //   viewStatus: "Medium",
  //   difficulty: "Meduium",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 35,
  //   imageSrc: "/assets/img/coursesCards/6.png",
  //   imageAlt: "image",
  //   rating: 5,
  //   ratingCount: 1991,
  //   title: "Content Marketing Strategy: Engaging Your Target Audience",
  //   lessonCount: 6,
  //   duration: 120,
  //   level: "Intermediate",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "Italian",
  //   authorName: "Albert Flores",
  //   originalPrice: 209,
  //   discountedPrice: 119,
  //   paid: true,
  //   category: "Design",
  //   state: "Popular",
  //   viewStatus: "Medium",
  //   difficulty: "Hard",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 36,
  //   imageSrc: "/assets/img/coursesCards/1.png",
  //   imageAlt: "image",
  //   rating: 5,
  //   ratingCount: 1991,
  //   title: "Wildlife Photography: Capturing Animals in Their Natural Habitat",
  //   lessonCount: 6,
  //   duration: 120,
  //   level: "Expert",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "French",
  //   authorName: "Albert Flores",
  //   originalPrice: 0,
  //   discountedPrice: 0,
  //   paid: false,
  //   category: "Design",
  //   state: "Popular",
  //   viewStatus: "Medium",
  //   difficulty: "Hard",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 37,
  //   imageSrc: "/assets/img/coursesCards/12.png",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   title:
  //     "Game Design and Storytelling: Creating Immersive Gaming Experiences",
  //   rating: 4.3,
  //   ratingCount: 1991,
  //   lessonCount: 6,
  //   duration: 1320,
  //   level: "Intermediate",
  //   originalPrice: 199,
  //   discountedPrice: 79,
  //   paid: true,
  //   category: "Design",
  //   state: "Popular",
  //   languange: "English",
  //   authorName: "Jane Cooper",
  //   viewStatus: "Good",
  //   difficulty: "Easy",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 38,
  //   imageSrc: "/assets/img/coursesCards/1.png",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   title: "Financial Planning for Retirement: Securing Your Golden Years",
  //   rating: 4.7,
  //   ratingCount: 1991,
  //   lessonCount: 6,
  //   duration: 410,
  //   level: "Expert",
  //   originalPrice: 0,
  //   discountedPrice: 0,
  //   paid: false,
  //   category: "Programming",
  //   languange: "German",
  //   authorName: "Jenny Wilson",
  //   popular: true,
  //   new: true,
  //   bestSeller: true,
  //   state: "Fetured",
  //   viewStatus: "Low",
  //   difficulty: "Easy",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 39,
  //   imageSrc: "/assets/img/coursesCards/11.png",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   title:
  //     "User Interface (UI) Design Fundamentals: Principles and Best Practices",
  //   rating: 4.5,
  //   ratingCount: 1991,
  //   lessonCount: 6,
  //   duration: 1220,
  //   level: "Intermediate",
  //   originalPrice: 249,
  //   discountedPrice: 129,
  //   languange: "Italian",
  //   authorName: "Robert Fox",
  //   paid: true,
  //   category: "Programming",
  //   state: "Trending",
  //   viewStatus: "Great",
  //   difficulty: "Easy",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 40,
  //   imageSrc: "/assets/img/coursesCards/8.png",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   title: "Python Web Scraping: Extracting Data from Websites",
  //   rating: 4.2,
  //   ratingCount: 1991,
  //   lessonCount: 6,
  //   duration: 1020,
  //   level: "Beginner",
  //   originalPrice: 179,
  //   discountedPrice: 99,
  //   languange: "French",
  //   authorName: "Jacob Jones",
  //   paid: true,
  //   category: "Art",
  //   state: "Trending",
  //   viewStatus: "Good",
  //   difficulty: "Easy",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 41,
  //   imageSrc: "/assets/img/coursesCards/3.png",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   title: "Digital Marketing Analytics: Measuring and Optimizing Campaigns",
  //   rating: 3.8,
  //   ratingCount: 1991,
  //   lessonCount: 6,
  //   duration: 250,
  //   level: "Expert",
  //   originalPrice: 169,
  //   discountedPrice: 79,
  //   paid: true,
  //   category: "Photography",
  //   languange: "English",
  //   authorName: "Jacob Jones",
  //   state: "Popular",
  //   viewStatus: "Low",
  //   difficulty: "Meduium",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 42,
  //   imageSrc: "/assets/img/coursesCards/11.png",
  //   imageAlt: "image",
  //   rating: 4.6,
  //   ratingCount: 1991,
  //   title: "Adobe Illustrator Mastery: Creating Vector Artwork",
  //   lessonCount: 6,
  //   duration: 800,
  //   level: "Beginner",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "German",
  //   authorName: "Jane Cooper",
  //   originalPrice: 209,
  //   discountedPrice: 99,
  //   paid: true,
  //   category: "Animation",
  //   state: "Fetured",
  //   viewStatus: "Good",
  //   difficulty: "Meduium",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
  // {
  //   id: 43,
  //   imageSrc: "/assets/img/coursesCards/1.png",
  //   imageAlt: "image",
  //   rating: 3.5,
  //   ratingCount: 1991,
  //   title: "Ruby on Rails: Building Web Applications with Ease",
  //   lessonCount: 6,
  //   duration: 490,
  //   level: "Intermediate",
  //   authorImageSrc: "/assets/img/general/avatar-1.png",
  //   languange: "Italian",
  //   authorName: "Jenny Wilson",
  //   originalPrice: 299,
  //   discountedPrice: 149,
  //   paid: true,
  //   category: "Writing",
  //   state: "Popular",
  //   viewStatus: "Great",
  //   difficulty: "Hard",
  //   desc: "Introductory course on web hosting, domain registration, and how you can easily publish and edit your website online.",
  // },
];

export const catagories = [
  "All Categories",
  "Animation",
  "Design",
  "Photography",
  "Art",
  "Programming",

  "Writing",
];

export const courseStates = ["All", "Fetured", "Popular", "Trending"];

export const viewStatus = ["All", "Great", "Good", "Medium", "Low"];

export const resentCourses = [
  {
    id: 1,
    imageSrc: "/assets/img/dashboard/recent-courses/1.png",
    title: "Complete Python Bootcamp From Zero to Hero in Python",
    author: "Ali Tufan",
    authorImg: `/assets/img/general/avatar-1.png`,
    lessonCount: 6,
    duration: 236,
  },
  {
    id: 2,
    imageSrc: "/assets/img/dashboard/recent-courses/2.png",
    title: "The Ultimate Drawing Course Beginner to Advanced",
    author: "Ali Tufan",
    authorImg: `/assets/img/general/avatar-1.png`,
    lessonCount: 6,
    duration: 236,
  },
  {
    id: 3,
    imageSrc: "/assets/img/dashboard/recent-courses/3.png",
    title: "Instagram Marketing 2021: Complete Guide To Instagram Growth",
    author: "Ali Tufan",
    authorImg: `/assets/img/general/avatar-1.png`,
    lessonCount: 6,
    duration: 236,
  },
];

export const allCategories = [
  "All",
  "Design",
  "Programming",
  "Art",
  "Photography",
  "Animation",

  "Writing",
];

export const difficulty = ["All", "Hard", "Meduium", "Easy"];

export const tags = [
  { id: 1, href: "#", name: "Courses" },
  { id: 2, href: "#", name: "Learn" },
  { id: 3, href: "#", name: "Online" },
  { id: 4, href: "#", name: "Education" },
  { id: 5, href: "#", name: "LMS" },
  { id: 6, href: "#", name: "Training" },
];

export const instractorNames = [
  { id: 1, title: "Jane Cooper" },
  { id: 2, title: "Jenny Wilson" },
  { id: 3, title: "Robert Fox" },
  { id: 4, title: "Jacob Jones" },
  { id: 5, title: "Albert Flores" },
];

export const languages = [
  { id: 1, title: "English" },
  { id: 2, title: "French" },
  { id: 3, title: "German" },
  { id: 4, title: "Italian" },
];

export const levels = [
  { id: 2, title: "Beginner" },
  { id: 3, title: "Intermediate" },
  { id: 4, title: "Expert" },
];
export const prices = [
  { id: 1, title: "All" },
  { id: 2, title: "Free" },
  { id: 3, title: "Paid" },
];

export const rating = [
  { id: 1, star: 5, text: "4.5 & up", range: [4.5, 5] },
  { id: 2, star: 5, text: "4.0 & up", range: [4, 5] },
  { id: 3, star: 5, text: "3.5 & up", range: [3.5, 5] },
  { id: 4, star: 5, text: "3.0 & up", range: [3, 5] },
];

export const categories = [
  { id: 1, title: "Art" },
  { id: 2, title: "Animation" },
  { id: 3, title: "Design" },
  { id: 4, title: "Photography" },
  { id: 5, title: "Programming" },

  { id: 6, title: "Writing" },
];

export const duration = [
  { id: 1, title: "Less than 4 hours", range: [0, 239] },
  { id: 2, title: "4 - 7 hours", range: [240, 419] },
  { id: 3, title: "7 - 18 hours", range: [420, 1079] },
  { id: 4, title: "20 + Hours", range: [1080, 5000] },
];

export const sortingOptions = [
  "Default",
  "Rating (asc)",
  "Rating (dsc)",
  "Price (asc)",
  "Price (dsc)",
  "Duration (asc)",
  "Duration (dsc)",
];
