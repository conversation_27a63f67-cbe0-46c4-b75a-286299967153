export const letters = [
  "A",
  "B",
  "C",
  "D",
  "E",
  "F",
  "G",
  "H",
  "I",
  "J",
  "K",
  "L",
  "M",
  "N",
  "O",
  "P",
  "Q",
  "R",
  "S",
  "T",
  "U",
  "V",
  "W",
  "X",
  "Y",
  "Z",
];

export const alphabetItems = [
  {
    id: 1,
    key: "A",
    title: `Accessibility`,
    description: `The design of apps, devices, materials and environments that support and enable access to content and educational activities for all learners.`,
  },
  {
    id: 2,
    key: "A",
    title: `Artificial Intelligence`,
    description: `The simulation of human intelligence in machines that are programmed to think and learn like humans.`,
  },
  {
    id: 3,
    key: "B",
    title: `Blended learning`,
    description: `The design of apps, devices, materials and environments that support and enable access to content and educational activities for all learners.`,
  },
  {
    id: 4,
    key: "B",
    title: `Big Data`,
    description: `The large volume of structured and unstructured data that organizations can analyze to make better decisions and strategic moves.`,
  },
  {
    id: 5,
    key: "C",
    title: `Cognitive Science`,
    description: `The interdisciplinary study of mind and intelligence, embracing philosophy, psychology, artificial intelligence, neuroscience, linguistics, and anthropology.`,
  },
  {
    id: 6,
    key: "C",
    title: `Collaborative Learning`,
    description: `An instructional approach that involves students working together in groups to solve problems and achieve learning goals.`,
  },
  {
    id: 7,
    key: "D",
    title: `Digital Literacy`,
    description: `The ability to find, evaluate, utilize, share, and create content using information technologies and the Internet.`,
  },
  {
    id: 8,
    key: "D",
    title: `Data Visualization`,
    description: `The representation of data and information through visual elements such as charts, graphs, and maps to facilitate understanding and analysis.`,
  },
  {
    id: 9,
    key: "E",
    title: `E-Learning`,
    description: `The use of electronic technologies to access educational curriculum outside of a traditional classroom.`,
  },
  {
    id: 10,
    key: "E",
    title: `Educational Technology`,
    description: `The use of technology to facilitate and enhance teaching and learning experiences.`,
  },
  {
    id: 11,
    key: "F",
    title: `Formative Assessment`,
    description: `The evaluation of student learning and progress during instruction to provide feedback and guide future instruction.`,
  },
  {
    id: 12,
    key: "F",
    title: `Flipped Classroom`,
    description: `A pedagogical approach where instructional content is delivered online, and in-class time is dedicated to interactive activities and discussions.`,
  },
  {
    id: 13,
    key: "G",
    title: `Gamification`,
    description: `The application of game elements and mechanics in non-game contexts to engage and motivate learners.`,
  },
  {
    id: 14,
    key: "G",
    title: `Global Citizenship`,
    description: `The awareness, understanding, and engagement with global issues, as well as the development of skills to contribute to a more just and sustainable world.`,
  },
  {
    id: 15,
    key: "H",
    title: `Hybrid Learning`,
    description: `An instructional approach that combines online and in-person learning experiences.`,
  },
  {
    id: 16,
    key: "H",
    title: `Higher Order Thinking`,
    description: `The ability to analyze, evaluate, and create, going beyond basic knowledge and comprehension.`,
  },
  {
    id: 17,
    key: "I",
    title: `Instructional Design`,
    description: `The systematic process of creating effective and engaging learning experiences.`,
  },
  {
    id: 18,
    key: "I",
    title: `Inquiry-Based Learning`,
    description: `An approach where students actively explore real-world questions, problems, or scenarios to develop knowledge and skills.`,
  },
  {
    id: 19,
    key: "J",
    title: `Just-in-Time Learning`,
    description: `The delivery of learning content and resources at the moment of need.`,
  },
  {
    id: 20,
    key: "J",
    title: `Job Readiness`,
    description: `The possession of the skills, knowledge, and attitudes required to be prepared for employment and career success.`,
  },
  {
    id: 21,
    key: "K",
    title: `Knowledge Transfer`,
    description: `The process of sharing knowledge and expertise from one individual or group to another.`,
  },
  {
    id: 22,
    key: "K",
    title: `Kinesthetic Learning`,
    description: `A learning style that involves physical activities and movement to enhance learning and understanding.`,
  },
  {
    id: 23,
    key: "L",
    title: `Lifelong Learning`,
    description: `The continuous pursuit of knowledge and skills throughout one's life, beyond formal education.`,
  },
  {
    id: 24,
    key: "L",
    title: `Learning Management System`,
    description: `A software application for the administration, documentation, tracking, reporting, and delivery of educational courses or training programs.`,
  },
  {
    id: 25,
    key: "M",
    title: `Mobile Learning`,
    description: `The use of mobile devices, such as smartphones and tablets, for learning and educational purposes.`,
  },
  {
    id: 26,
    key: "M",
    title: `Microlearning`,
    description: `A learning approach that delivers content in short, focused bursts, typically through multimedia formats.`,
  },
  {
    id: 27,
    key: "N",
    title: `Neuroscience`,
    description: `The scientific study of the nervous system, including the brain, and its impact on behavior and cognition.`,
  },
  {
    id: 28,
    key: "N",
    title: `Non-Formal Learning`,
    description: `Learning that occurs outside of a formal educational institution, often through organized activities and experiences.`,
  },
  {
    id: 29,
    key: "O",
    title: `Online Learning`,
    description: `Education or training delivered over the internet, typically through a learning management system or online platform.`,
  },
  {
    id: 30,
    key: "O",
    title: `Open Educational Resources`,
    description: `Freely accessible and openly licensed educational materials that can be used, shared, and modified for teaching and learning.`,
  },
  {
    id: 31,
    key: "P",
    title: `Personalized Learning`,
    description: `An approach that tailors instruction and learning experiences to meet the unique needs and preferences of individual learners.`,
  },
  {
    id: 32,
    key: "P",
    title: `Project-Based Learning`,
    description: `An instructional approach where students work on projects to gain knowledge and skills through active exploration and problem-solving.`,
  },
  {
    id: 33,
    key: "Q",
    title: `Quantum Computing`,
    description: `A computing technology that leverages the principles of quantum mechanics to perform complex computations at a much faster rate than traditional computers.`,
  },
  {
    id: 34,
    key: "Q",
    title: `Questioning Skills`,
    description: `The ability to ask thoughtful and probing questions to deepen understanding, promote critical thinking, and stimulate discussion.`,
  },
  {
    id: 35,
    key: "R",
    title: `Remote Learning`,
    description: `The delivery of education or training remotely, often through online platforms and virtual classrooms.`,
  },

  {
    id: 35,
    key: "R",
    title: `Remote Learning`,
    description: `The delivery of education or training remotely, often through online platforms and virtual classrooms.`,
  },
  {
    id: 36,
    key: "R",
    title: `Rubrics`,
    description: `Scoring guides or frameworks that outline criteria and levels of performance to assess and evaluate student work.`,
  },
  {
    id: 37,
    key: "S",
    title: `Social Emotional Learning`,
    description: `The process of developing skills, attitudes, and knowledge to understand and manage emotions, establish positive relationships, and make responsible decisions.`,
  },
  {
    id: 38,
    key: "S",
    title: `Synchronous Learning`,
    description: `Real-time online learning where learners and instructors engage with each other simultaneously, often through video conferencing or live chats.`,
  },
  {
    id: 39,
    key: "T",
    title: `Transdisciplinary Skills`,
    description: `Skills that go beyond disciplinary boundaries and can be applied across different subjects and real-world contexts, such as critical thinking, problem-solving, and collaboration.`,
  },
  {
    id: 40,
    key: "T",
    title: `Flipped Classroom`,
    description: `A pedagogical approach where instructional content is delivered online, and in-class time is dedicated to interactive activities and discussions.`,
  },
  {
    id: 41,
    key: "U",
    title: `Universal Design for Learning`,
    description: `An educational framework that promotes inclusive instructional design by providing multiple means of representation, expression, and engagement to accommodate diverse learners.`,
  },
  {
    id: 42,
    key: "U",
    title: `Ubiquitous Learning`,
    description: `The concept of learning anytime and anywhere, facilitated by the pervasive use of digital technologies and mobile devices.`,
  },
  {
    id: 43,
    key: "V",
    title: `Virtual Reality`,
    description: `A computer-generated simulation of a three-dimensional environment that can be interacted with and explored by a user, often through a headset or special devices.`,
  },
  {
    id: 44,
    key: "V",
    title: `Visual Thinking`,
    description: `The use of visual representations, such as diagrams, charts, and mind maps, to organize and process information and enhance understanding.`,
  },
  {
    id: 45,
    key: "W",
    title: `Web-Based Learning`,
    description: `Learning that takes place on the internet through web-based platforms, online courses, and virtual classrooms.`,
  },
  {
    id: 46,
    key: "W",
    title: `Web Accessibility`,
    description: `The practice of designing and developing websites and digital content that can be accessed and used by individuals with disabilities.`,
  },
  {
    id: 47,
    key: "X",
    title: `XAPI (Experience API)`,
    description: `A specification that allows learning experiences and data to be tracked and recorded across different platforms and systems, providing insights into learner performance and behavior.`,
  },
  {
    id: 48,
    key: "X",
    title: `XML (eXtensible Markup Language)`,
    description: `A markup language used for structuring and storing data in a readable format, commonly used in e-learning systems for content organization and interoperability.`,
  },
  {
    id: 49,
    key: "Y",
    title: `Youth Empowerment`,
    description: `The process of equipping young individuals with the knowledge, skills, and resources to participate actively in society, make informed decisions, and effect positive change.`,
  },
  {
    id: 50,
    key: "Y",
    title: `YouTube for Education`,
    description: `The use of YouTube as a platform for educational content, tutorials, lectures, and online learning resources.`,
  },
  {
    id: 51,
    key: "Z",
    title: `Zoom Integration`,
    description: `The seamless integration of Zoom video conferencing software to facilitate online meetings and virtual classrooms.`,
  },
  {
    id: 52,
    key: "Z",
    title: `Zone of Proximal Development`,
    description: `The difference between what a learner can do without help and what they can achieve with guidance and support.`,
  },
];
