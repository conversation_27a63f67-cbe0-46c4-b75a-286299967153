export const events = [
  {
    id: 1,
    imgSrc: "/assets/img/coursesCards/reading.jpeg",
    title: "Reading",
    date: "6 April, 2022",
    location: "London, UK",
    desc: `Educational technology and mobile learning`,
    category: "Design",
    price: 450,
    preDiscount: 650,
  },
  {
    id: 2,
    imgSrc: "/assets/img/coursesCards/listening_headphone.jpeg",
    title: "Listening",
    date: "6 April, 2022",
    location: "London, UK",
    desc: `We are changing the way the world learns`,
    category: "Animation",
    price: 450,
    preDiscount: 650,
  },
  {
    id: 3,
    imgSrc: "/assets/img/coursesCards/liveClass.jpeg",
    title: "live classes",
    date: "6 April, 2022",
    location: "London, UK",
    desc: `Guide to visas and funding to study in the UK`,
    category: "Writing",
    price: 450,
    preDiscount: 650,
  },
  // {
  //   id: 4,
  //   imgSrc: "/assets/img/courses-list/4.png",
  //   title: "Summer School 2022",
  //   date: "6 April, 2022",
  //   location: "London, UK",
  //   desc: `Achieving Advanced in Insights with Big`,
  //   category: "Business",
  //   price: 450,
  //   preDiscount: 650,
  // },
  // {
  //   id: 5,
  //   imgSrc: "/assets/img/courses-list/5.png",
  //   title: "Summer School 2022",
  //   date: "6 April, 2022",
  //   location: "London, UK",
  //   desc: `Educational technology and mobile learning`,
  //   category: "Photo & Film",
  //   price: 450,
  //   preDiscount: 650,
  // },
  // {
  //   id: 6,
  //   imgSrc: "/assets/img/courses-list/6.png",
  //   title: "Summer School 2022",
  //   date: "6 April, 2022",
  //   location: "London, UK",
  //   desc: `We are changing the way the world learns`,
  //   category: "Lifestyle",
  //   price: 450,
  //   preDiscount: 650,
  // },
  // {
  //   id: 7,
  //   imgSrc: "/assets/img/courses-list/7.png",
  //   title: "Summer School 2022",
  //   date: "6 April, 2022",
  //   location: "London, UK",
  //   desc: `We are changing the way the world learns`,
  //   category: "Illustration",
  //   price: 450,
  //   preDiscount: 650,
  // },
  // {
  //   id: 8,
  //   imgSrc: "/assets/img/courses-list/8.png",
  //   title: "Summer School 2022",
  //   date: "6 April, 2022",
  //   location: "London, UK",
  //   desc: `We are changing the way the world learns`,
  //   category: "Design",
  //   price: 450,
  //   preDiscount: 650,
  // },
  // {
  //   id: 9,
  //   imgSrc: "/assets/img/courses-list/9.png",
  //   title: "Summer School 2022",
  //   date: "6 April, 2022",
  //   location: "London, UK",
  //   desc: `We are changing the way the world learns`,
  //   category: "Animation",
  //   price: 450,
  //   preDiscount: 650,
  // },
];

export const categories = [
  "All Categories",

  "Animation",
  "Design",
  "Illustration",
  "Lifestyle",
  "Photo & Film",
  "Business",
  "Writing",
];

export const findEvent = [
  {
    id: 1,
    title: "Event From",
  },
  {
    id: 2,
    title: "All Categories",
  },
  {
    id: 3,
    title: "Location",
  },
  {
    id: 4,
    title: "Keyword",
  },
];

export const tags = [
  { id: 1, href: "#", name: "Courses" },
  { id: 2, href: "#", name: "Learn" },
  { id: 3, href: "#", name: "Online" },
  { id: 4, href: "#", name: "Education" },
  { id: 5, href: "#", name: "LMS" },
  { id: 6, href: "#", name: "Training" },
];

export const eventContent = [
  "You will need a copy of Adobe XD 2019 or above. A free trial can be downloaded from Adobe.",
  "No previous design experience is needed.",
  "No previous Adobe XD skills are needed.",
];

export const learnList = [
  "Become a UX designer.",
  "You will be able to add UX designer to your CV",
  "Become a UI designer.",
  "Build & test a full website design.",
  "Create your first UX brief & persona.",
  "How to use premade UI kits.",
  "Create quick wireframes.",
  "Downloadable exercise files",
  "Build a UX project from beginning to end.",
  "Learn to design websites & mobile phone apps.",
  "All the techniques used by UX professionals",
  "You will be able to talk correctly with other UX design.",
];
