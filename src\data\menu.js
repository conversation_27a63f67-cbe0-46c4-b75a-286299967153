export const menuList = [
  {
    title: "Home",
    links: [
      // { href: "/", label: "Home" },
      // { href: "/home-2", label: "Home 2" },
      // { href: "/home-3", label: "Home 3" },
      // { href: "/home-4", label: "Home 4" },
      // { href: "/home-5", label: "Home 5" },
      // { href: "/home-6", label: "Home 6" },
      // { href: "/home-7", label: "Home 7" },
      // { href: "/home-8", label: "Home 8" },
      // { href: "/home-9", label: "Home 9" },
      // { href: "/home-10", label: "Home 10" },
    ],
  },
  {
    title: "Courses",
    links: [
      {
        title: "Speaking",
        links: [
          { href: "/courses-list-1", label: "Read Aloud" },
          { href: "/courses-list-2", label: "Repeat Sentence" },
          { href: "/courses-list-3", label: "Describe Image" },
          { href: "/courses-list-4", label: "Re-tell Lecture" },
          { href: "/courses-list-5", label: "Answer Short Question" },
        ],
      },
      {
        title: "Writing",
        links: [
          { href: "/courses/3", label: "Summarize Written TextAI Score" },
          { href: "/courses-single-2/2", label: "Write EssayAI Score" },
        ],
      },
      {
        title: "Reading",
        links: [
          { href: "/lesson-single-1", label: "Reading & Writing：Fill in the blanks" },
          { href: "/lesson-single-2", label: "Multiple Choice (Multiple)" },
          { href: "/instructors-list-1", label: "Re-order Paragraphs" },
          { href: "/instructors-list-2", label: "Reading：Fill in the Blanks" },
          { href: "/instructors/1", label: "Multiple Choice (Single)" },
        ],
      },
      {
        title: "Listening",
        links: [
          { href: "/lesson-single-1", label: " Summarize Spoken TextAI Score" },
          { href: "/lesson-single-2", label: "Multiple Choice (Multiple)" },
          { href: "/instructors-list-1", label: "Fill in the Blanks" },
          { href: "/instructors-list-2", label: "Highlight Correct Summary" },
          { href: "/instructors/1", label: "Multiple Choice (Single)" },
          { href: "/instructors-list-1", label: "Select Missing Word" },
          { href: "/instructors-list-2", label: "Highlight Incorrect Words" },
          { href: "/instructors/1", label: " Write From Dictation" },
        ],
      },
      {
        title: "More",
        links: [
          { href: "/lesson-single-1", label: "Vocab Books" },
          { href: "/lesson-single-2", label: "Shadowing" },
          { href: "/instructors-list-1", label: "AI Score Report Analysis" },
          { href: "/instructors-list-2", label: "AI Study Plan" },
          { href: "/instructors/1", label: "Mock Tests" },
          { href: "/instructors/1", label: "Study Materials Download" },
        ],
      },
      {
        title: "Dashboard Pages",
        links: [
          { href: "/dashboard", label: "Dashboard" },
          { href: "/dshb-courses", label: "My Courses" },
          { href: "/dshb-bookmarks", label: "Bookmarks" },
          { href: "/dshb-listing", label: "Add Listing" },
          { href: "/dshb-reviews", label: "Reviews" },
          { href: "/dshb-settings", label: "Settings" },
          { href: "/dshb-administration", label: "Administration" },
          { href: "/dshb-assignment", label: "Assignment" },
          { href: "/dshb-calendar", label: "Calendar" },
          { href: "/dshb-dashboard", label: "Single Dashboard" },
          { href: "/dshb-dictionary", label: "Dictionary" },
          { href: "/dshb-forums", label: "Forums" },
          { href: "/dshb-grades", label: "Grades" },
          { href: "/dshb-messages", label: "Messages" },
          { href: "/dshb-participants", label: "Participants" },
          { href: "/dshb-quiz", label: "Quiz" },
          { href: "/dshb-survey", label: "Survey" },
        ],
      },
      {
        title: "Popular Courses",
        links: [
          { label: "Web Developer", href: "#" },
          { label: "Mobile Developer", href: "#" },
          { label: "Digital Marketing", href: "#" },
          { label: "Development", href: "#" },
          { label: "Finance & Accounting", href: "#" },
          { label: "Design", href: "#" },
          { label: "View All Courses", href: "#" },
        ],
      },
      // {
      //     title : 'Course List Layouts',
      //     links: [
      //     ]
      // },
    ],
  },
  {
    title: "Events",
    links: [
      // { href: "/event-list-1", label: "Event List 1" },
      { href: "/event-list-2", label: "Event List" },
      { href: "/events/1", label: "Event Single" },
      { href: "/event-cart", label: "Event Cart" },
      { href: "/event-checkout", label: "Event Checkout" },
    ],
  },
  {
    title: "Blogs",
    links: [
      { href: "/blog-list-1", label: "Blog List" },
      // { href: "/blog-list-2", label: "Blog List 2" },
      // { href: "/blog-list-3", label: "Blog List 3" },
      { href: "/blogs/3", label: "Blog Single" },
    ],
  },
  {
    title: "Pages",
    links: [
      {
        title: "About Us",
        links: [
          // { href: "/about-1", label: "About 1" },
          { href: "/about-2", label: "About" },
        ],
      },
      {
        title: "Contact",
        links: [
          { href: "/contact-1", label: "Contact 1" },
          { href: "/contact-2", label: "Contact 2" },
        ],
      },
      {
        title: "Shop",
        links: [
          { href: "/shop-cart", label: "Shop Cart" },
          { href: "/shop-checkout", label: "Shop Checkout" },
          { href: "/shop-list", label: "Shop List" },
          { href: "/shop-order", label: "Shop Order" },
          { href: "/shop/3", label: "Shop Single" },
        ],
      },
      { href: "/pricing", label: "Membership plans" },
      // { href: "/not-found", label: "404 Page" },
      { href: "/terms", label: "FAQs" },
      { href: "/help-center", label: "Help Center" },
      // { href: "/login", label: "Login" },
      // { href: "/signup", label: "Register" },
      { href: "/ui-elements", label: "UI Elements" },
    ],
  },
];


// import { useAllCategory } from "@/api/hooks/response";
// import Loader from "@/components/common/Loader";

// export const menuList = () => {
//   const { category, loading, error } = useAllCategory();

//   if(loading) return <Loader />
//   const menuData = [
//     {
//       title: "Home",
//       links: [
//         { href: "/", label: "Home 1" },
//         { href: "/home-2", label: "Home 2" },
//         { href: "/home-3", label: "Home 3" },
//         { href: "/home-4", label: "Home 4" },
//         { href: "/home-5", label: "Home 5" },
//         { href: "/home-6", label: "Home 6" },
//         { href: "/home-7", label: "Home 7" },
//         { href: "/home-8", label: "Home 8" },
//         { href: "/home-9", label: "Home 9" },
//         { href: "/home-10", label: "Home 10" },
//       ],
//     },
//     {
//       title: "Courses",
//       links: category.map((item) => ({
//         title: item.name,
//         // links: item.subItems.map((subItem) => ({
//         //   href: `/courses/${subItem.name}`,
//         //   label: subItem.name,
//         // })),
//         links:[]
//       })),
//     },
//     {
//       title: "Events",
//       links: [
//         { href: "/event-list-2", label: "Event List" },
//         { href: "/events/1", label: "Event Single" },
//         { href: "/event-cart", label: "Event Cart" },
//         { href: "/event-checkout", label: "Event Checkout" },
//       ],
//     },
//     {
//       title: "Blogs",
//       links: [
//         { href: "/blog-list-1", label: "Blog List" },
//         { href: "/blogs/3", label: "Blog Single" },
//       ],
//     },
//     {
//       title: "Pages",
//       links: [
//         {
//           title: "About Us",
//           links: [{ href: "/about-2", label: "About" }],
//         },
//         {
//           title: "Contact",
//           links: [
//             { href: "/contact-1", label: "Contact 1" },
//             { href: "/contact-2", label: "Contact 2" },
//           ],
//         },
//         {
//           title: "Shop",
//           links: [
//             { href: "/shop-cart", label: "Shop Cart" },
//             { href: "/shop-checkout", label: "Shop Checkout" },
//             { href: "/shop-list", label: "Shop List" },
//             { href: "/shop-order", label: "Shop Order" },
//             { href: "/shop/3", label: "Shop Single" },
//           ],
//         },
//         { href: "/pricing", label: "Membership plans" },
//         { href: "/terms", label: "FAQs" },
//         { href: "/help-center", label: "Help Center" },
//         { href: "/ui-elements", label: "UI Elements" },
//       ],
//     },
//   ];

//   // Return or use menuData as needed
//   return menuData;
// };
