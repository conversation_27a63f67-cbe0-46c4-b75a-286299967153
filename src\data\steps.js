export const steps = [
  {
    id: 1,
    // icon: "icon-software text-64 text-green-1",
    icon:"/assets/img/steps/1.png",
    title: "01. Real-time Test Environment",
    text: "Preparing with our best PTE mock test helps you to gain real test experience beforehand.",
  },
  {
    id: 2,
    // icon: "icon-graduation-1 text-64 text-green-1",
    icon:"/assets/img/steps/2.png",
    title: "02. Detailed Analytics",
    text: "After taking the PTE online mock test, the portal provides detailed insights into what went wrong and how you can improve further.",
  },
  {
    id: 3,
    // icon: "icon-working-at-home-2 text-64 text-green-1",
    icon:"/assets/img/steps/3.png",
    title: "03. Accurate Scoring Algorithms",
    text: "The AI-Powered algorithm provides the scorecard within seconds along with the mistakes and corrections.",
  },
  {
    id: 4,
    // icon: "icon-discovery text-64 text-green-1",
    icon:"/assets/img/steps/4.png",
    title: "04. A Unlimited Access",
    text: "The more you practice, the more chances to score better. You can take our PTE Mock Test anytime within the course duration.",
  },
  {
    id: 5,
    // icon: "icon-check text-64 text-green-1",
    icon:"/assets/img/steps/5.png",
    title: "05. Verified Questions",
    text: "Our PTE free complete mock test consists of questions similar to what is asked in the real exam and even in the past exams.",
  },
];



 
export const stepsTwo = [
  {
    id: 1,
    imageSrc: "/assets/img/home-3/works/1.svg",
    text: "Our Mission",
    subText1:'We understand your struggles. We',
    subtext2:'stand to be able to help students’ progress'
  },
  {
    id: 2,
    imageSrc: "/assets/img/home-3/works/2.svg",
    text: "Our Vision",
    subText1:'We aim to create a better life for people',
    subtext2:' struggling to obtain their permanent residency'
  },
  {
    id: 3,
    imageSrc: "/assets/img/home-3/works/3.svg",
    text: "Our Values",
    subText1:'We endeavor to behave professionally,',
    subtext2:' communicate with our team and students'
  },
];
