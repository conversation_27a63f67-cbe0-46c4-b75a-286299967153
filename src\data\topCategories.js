export const topCategories = [
  {
    id: 1,
    iconSrc: "/assets/img/featureCards/1.svg",
    title: "Daily Updated",
    text: "0 Exam Questions",
    text2:'Highly Repeated'
  },
  {
    id: 2,
    iconSrc: "/assets/img/featureCards/2.svg",
    title: " Speaking AI",
    text: "Simulate PTE offical",
    text2:' scoring enging'
  },
  {
    id: 3,
    iconSrc: "/assets/img/featureCards/3.svg",
    title: "Writing AI",
    text: "Accurate Scoring ",
    text2:' Grammar correction'
  },
  {
    id: 4,
    iconSrc: "/assets/img/featureCards/4.svg",
    title: "Answers",
    text: "Quality Answers Provided",
    text2:' by our teaching team'
  },
  {
    id: 5,
    iconSrc: "/assets/img/featureCards/5.svg",
    title: "Community",
    text: "Largest PTE Online",
    text2:' Community'
  },
  {
    id: 6,
    iconSrc: "/assets/img/featureCards/6.svg",
    title: "Classfication",
    text: "Questions Categoried",
    text2:' Practice with ease'
  },
  // {
  //   id:7 ,
  //   iconSrc: "/assets/img/featureCards/4.svg",
  //   title: "Engineering Architecture",
  //   text: "35+ Courses",
  // },
];

export const pTEKnowledge = [
  {
    id: 1,
    // iconSrc: "/assets/img/featureCards/1.svg",
    title: "PTE Academic Guide",
    que: "What is the meaning of PTE?",
    ans:"The full form of PTE is the Pearson Test of English, normally referring to the academic version. It is also known as PTE-A, Pearson Academic, or Pearson Test of English Academic. PTE is a computer-based exam. It also provides online mock tests on Pearson PTE's website.",
    que1:["PTE vs IELTS, what is the difference?","How is PTE test scored?","How to book PTE exams?","How to prepare for PTE exams?"]
  },
  {
    id: 2,
    // iconSrc: "/assets/img/featureCards/2.svg",
    title: "PTE Speaking Guide",
    que: "Read Aloud exam tips",
    ans:"Read Aloud (RA) is one of the most difficult question types in PTE Speaking. Pronunciation and fluency are much more important in scoring than content. Try to keep your tone smooth and do not have unnatural pauses.",
    que1:["Repeat Sentence exam tips","Describe Image exam tips","Re-tell Lecture exam tips","Answer Short Question exam tips"]
  },
  {
    id: 3,
    // iconSrc: "/assets/img/featureCards/3.svg",
    title: "PTE Writing Guide",
    que: "Summarize Written Text exam tips",
    ans:"Summarize Written Text (SWT) is a very simple question type. There are two keys in answering: to find key points in the passage, and to connect the key points into a grammatically correct sentence.",
    que1:["Write Essay exam tips","PTE write essay materials"]
  },
  {
    id: 4,
    // iconSrc: "/assets/img/featureCards/4.svg",
    title: "PTE Reading Guide",
    que: "Multiple Choice Single Answer exam tips",
    ans:"The Reading Multiple Choice Single Answer (MCS) question type accounts for only a very small proportion of marks in the overall Reading score. The most important technique for MCS is time management. Please complete each question of this type within 1 or 2 minutes. Otherwise, you will not have enough time for the more important FIB questions.",
    que1:["Multiple Choice Multiple Answers exam tips","Re-order Paragraphs exam tips","Fill in the Blanks (Reading) exam tips","Fill in the Blanks (Reading & Writing) exam tips"]
  },
  {
    id: 5,
    // iconSrc: "/assets/img/featureCards/5.svg",
    title: "PTE Listening Guide",
    que: "Summarize Spoken Text exam tips",
    ans:"Summarize Spoken Text (SST) is actually a very simple question type in PTE. Focus on the grammar and spelling of your written answer. Content is actually not that important in scoring.",
    que1:["Listening Multiple Choice exam tips","Listening Fill in the Blanks exam tips","Highlight Incorrect Words exam tips","Write from Dictation exam tips"]
  },
  // {
  //   id: 6,
  //   iconSrc: "/assets/img/featureCards/6.svg",
  //   title: "Finance Accounting",
  //   text: "129+ Courses",
  //   text2:' in 14 days'
  // },
  // {
  //   id:7 ,
  //   iconSrc: "/assets/img/featureCards/4.svg",
  //   title: "Engineering Architecture",
  //   text: "35+ Courses",
  // },
];

export const topCategoriesTwo = [
  {
    id: 1,
    imageSrc: "/assets/img/home-3/category/1.png",
    title: "Digital Marketing",
    text: "573+ Courses",
  },
  {
    id: 2,
    imageSrc: "/assets/img/home-3/category/2.png",
    title: "Web Development",
    text: "573+ Courses",
  },
  {
    id: 3,
    imageSrc: "/assets/img/home-3/category/3.png",
    title: "Graphic Design",
    text: "573+ Courses",
  },
  {
    id: 4,
    imageSrc: "/assets/img/home-3/category/4.png",
    title: "Social Sciences",
    text: "573+ Courses",
  },
  {
    id: 5,
    imageSrc: "/assets/img/home-3/category/5.png",
    title: "Photography",
    text: "573+ Courses",
  },
  {
    id: 6,
    imageSrc: "/assets/img/home-3/category/6.png",
    title: "Art & Humanities",
    text: "573+ Courses",
  },
  {
    id: 7,
    imageSrc: "/assets/img/home-3/category/7.png",
    title: "Personal Development",
    text: "573+ Courses",
  },
  {
    id: 8,
    imageSrc: "/assets/img/home-3/category/8.png",
    title: "IT and Software",
    text: "573+ Courses",
  },
];
export const topCatagoriesThree = [
  {
    id: 1,
    imageSrc: "/assets/img/home-2/categories/1.png",
    title: "Digital Marketing",
    courses: "573+ Courses",
  },
  {
    id: 2,
    imageSrc: "/assets/img/home-2/categories/2.png",
    title: "Web Development",
    courses: "473+ Courses",
  },
  {
    id: 3,
    imageSrc: "/assets/img/home-2/categories/3.png",
    title: "Graphic Design",
    courses: "583+ Courses",
  },
  {
    id: 4,
    imageSrc: "/assets/img/home-2/categories/4.png",
    title: "Social Sciences",
    courses: "373+ Courses",
  },
  {
    id: 5,
    imageSrc: "/assets/img/home-2/categories/5.png",
    title: "Personal Development",
    courses: "373+ Courses",
  },
  {
    id: 6,
    imageSrc: "/assets/img/home-2/categories/6.png",
    title: "Photography",
    courses: "393+ Courses",
  },
  {
    id: 7,
    imageSrc: "/assets/img/home-2/categories/7.png",
    title: "Arts and Humanities",
    courses: "373+ Courses",
  },
];

export const topCatagoriesFour = [
  {
    id: 1,
    title: "Digital Marketing",
    iconClass: "icon icon-announcement text-35",
    courses: "553+ Courses",
  },
  {
    id: 2,
    title: "Web Development",
    iconClass: "icon icon-web-programming text-35",
    courses: "573+ Courses",
  },
  {
    id: 3,
    title: "Graphic Design",
    iconClass: "icon icon-design text-35",
    courses: "823+ Courses",
  },
  {
    id: 4,
    title: "Social Sciences",
    iconClass: "icon icon-social-media text-35",
    courses: "573+ Courses",
  },
  {
    id: 5,
    title: "Photography",
    iconClass: "icon icon-photo-camera text-35",
    courses: "563+ Courses",
  },
  {
    id: 6,
    title: "Art & Humanities",
    iconClass: "icon icon-digital-art text-35",
    courses: "573+ Courses",
  },
  {
    id: 7,
    title: "Personal Development",
    iconClass: "icon icon-person text-35",
    courses: "571+ Courses",
  },
  {
    id: 8,
    title: "IT and Software",
    iconClass: "icon icon-tech text-35",
    courses: "593+ Courses",
  },
];

export const topCatagoriesFive = [
  {
    id: 1,
    title: "Digtal Marketing",
    icon: "icon icon-creative-announcement",
    delay: 1,
    courses: "553+ Courses",
  },
  {
    id: 2,
    title: "Web Development",
    icon: "icon icon-creative-web",
    delay: 2,
    courses: "553+ Courses",
  },
  {
    id: 3,
    title: "Graphic Design",
    icon: "icon icon-creative-design",
    delay: 3,
    courses: "553+ Courses",
  },
  {
    id: 4,
    title: "Social Sciences",
    icon: "icon icon-creative-photography",
    delay: 4,
    courses: "553+ Courses",
  },
  {
    id: 5,
    title: "Photohraphy",
    icon: "icon icon-creative-social",
    delay: 5,
    courses: "553+ Courses",
  },
  {
    id: 6,
    title: "Art & Humanities",
    icon: "icon icon-creative-art",
    delay: 6,
    courses: "553+ Courses",
  },
  {
    id: 7,
    title: "Personal Development",
    icon: "icon icon-creative-personal",
    delay: 7,
    courses: "553+ Courses",
  },
  {
    id: 8,
    title: "IT and Software",
    icon: "icon icon-creative-software",
    delay: 8,
    courses: "553+ Courses",
  },
];

export const topCatagoriesSix = [
  {
    id: 1,
    icon: "icon-architecture text-40",
    title: "Design Creative",
    courses: 573,
  },
  {
    id: 2,
    icon: "icon-megaphone text-40",
    title: "Sales Marketing",
    courses: 565,
  },
  {
    id: 3,
    icon: "icon-save-money text-40",
    title: "Development IT",
    courses: 126,
  },
  {
    id: 4,
    icon: "icon-software text-40",
    title: "Engineering Architecture",
    courses: 35,
  },
  {
    id: 5,
    icon: "icon-tools text-40",
    title: "Personal Development",
    courses: 908,
  },
  {
    id: 6,
    icon: "icon-person-2 text-40",
    title: "Finance Accounting",
    courses: 129,
  },
];

export const topCatagoriesSeven = [
  {
    id: 1,
    title: "Creative",
    items: [
      { id: 1, href: "#", title: "Animation" },
      { id: 2, href: "#", title: "Drawing" },
      { id: 3, href: "#", title: "Graphic Design" },
      { id: 4, href: "#", title: "Illustration" },
      { id: 5, href: "#", title: "Photography" },
    ],
  },
  {
    id: 2,
    title: "Business",
    items: [
      { id: 1, href: "#", title: "Animation" },
      { id: 2, href: "#", title: "Drawing" },
      { id: 3, href: "#", title: "Graphic Design" },
      { id: 4, href: "#", title: "Illustration" },
      { id: 5, href: "#", title: "Photography" },
    ],
  },
  {
    id: 3,
    title: "Creative",
    items: [
      { id: 1, href: "#", title: "Animation" },
      { id: 2, href: "#", title: "Drawing" },
      { id: 3, href: "#", title: "Graphic Design" },
      { id: 4, href: "#", title: "Illustration" },
      { id: 5, href: "#", title: "Photography" },
    ],
  },
  {
    id: 4,
    title: "Business",
    items: [
      { id: 1, href: "#", title: "Animation" },
      { id: 2, href: "#", title: "Drawing" },
      { id: 3, href: "#", title: "Graphic Design" },
      { id: 4, href: "#", title: "Illustration" },
      { id: 5, href: "#", title: "Photography" },
    ],
  },
  {
    id: 5,
    title: "Business",
    items: [
      { id: 1, href: "#", title: "Animation" },
      { id: 2, href: "#", title: "Drawing" },
      { id: 3, href: "#", title: "Graphic Design" },
      { id: 4, href: "#", title: "Illustration" },
      { id: 5, href: "#", title: "Photography" },
    ],
  },
];

export const topCatagoriesEight = [
  {
    id: 1,
    icon: "/assets/img/home-8/categories/1.svg",
    courses: 573,
    title: "Digital Marketing",
    backgroundColor: "bg-orange-2",
  },
  {
    id: 2,
    icon: "/assets/img/home-8/categories/2.svg",
    courses: 573,
    title: "Web Development",
    backgroundColor: "bg-green-2",
  },
  {
    id: 3,
    icon: "/assets/img/home-8/categories/3.svg",
    courses: 573,
    title: "Art & Humanities",
    backgroundColor: "bg-purple-2",
  },
  {
    id: 4,
    icon: "/assets/img/home-8/categories/4.svg",
    courses: 573,
    title: "Personal Development",
    backgroundColor: "bg-yellow-5",
  },
  {
    id: 5,
    icon: "/assets/img/home-8/categories/5.svg",
    courses: 573,
    title: "IT and Software",
    backgroundColor: "bg-green-6",
  },
  {
    id: 6,
    icon: "/assets/img/home-8/categories/6.svg",
    courses: 573,
    title: "Social Sciences",
    backgroundColor: "bg-light-10",
  },
  {
    id: 7,
    icon: "/assets/img/home-8/categories/7.svg",
    courses: 573,
    title: "Graphic Design",
    backgroundColor: "bg-green-7",
  },
];
