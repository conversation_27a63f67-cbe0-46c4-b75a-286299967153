import { useState, useCallback, useRef } from "react";
import { FFmpeg } from "@ffmpeg/ffmpeg";
import { fetchFile, toBlobURL } from "@ffmpeg/util";

const useAudioCompression = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState(null);
  const ffmpegRef = useRef(null);
  const isLoadedRef = useRef(false);

  // Initialize FFmpeg
  const initFFmpeg = useCallback(async () => {
    if (isLoadedRef.current) return;

    try {
      setIsLoading(true);
      setError(null);

      const ffmpeg = new FFmpeg();
      ffmpegRef.current = ffmpeg;

      // Progress tracking
      ffmpeg.on("progress", ({ progress }) => {
        setProgress(Math.round(progress * 100));
      });

      // Load FFmpeg
      const baseURL = "https://unpkg.com/@ffmpeg/core-mt@0.12.6/dist/esm";
      await ffmpeg.load({
        coreURL: await toBlobURL(
          `${baseURL}/ffmpeg-core.js`,
          "text/javascript"
        ),
        wasmURL: await toBlobURL(
          `${baseURL}/ffmpeg-core.wasm`,
          "application/wasm"
        ),
        workerURL: await toBlobURL(
          `${baseURL}/ffmpeg-core.worker.js`,
          "text/javascript"
        ),
      });

      isLoadedRef.current = true;
      console.log("✅ FFmpeg loaded successfully for frontend compression");
    } catch (err) {
      console.error("❌ Failed to load FFmpeg:", err);
      setError(`Failed to load FFmpeg: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Compress audio file
  const compressAudio = useCallback(
    async (audioBlob, options = {}) => {
      const {
        bitrate = "64k",
        sampleRate = 16000,
        channels = 1,
        format = "mp3",
        quality = "high", // 'high', 'medium', 'low'
      } = options;

      try {
        setIsLoading(true);
        setProgress(0);
        setError(null);

        // Initialize FFmpeg if not already loaded
        if (!isLoadedRef.current) {
          await initFFmpeg();
        }

        const ffmpeg = ffmpegRef.current;
        if (!ffmpeg) {
          throw new Error("FFmpeg not initialized");
        }

        console.log("🎵 Starting frontend audio compression...", {
          originalSize: `${(audioBlob.size / 1024 / 1024).toFixed(2)}MB`,
          targetBitrate: bitrate,
          targetFormat: format,
        });

        // Write input file
        const inputName = "input.wav";
        const outputName = `output.${format}`;

        await ffmpeg.writeFile(inputName, await fetchFile(audioBlob));

        // Prepare compression command based on quality
        let compressionArgs = ["-i", inputName];

        // Quality-based settings
        switch (quality) {
          case "high":
            compressionArgs.push(
              "-codec:a",
              format === "mp3" ? "libmp3lame" : "aac",
              "-b:a",
              bitrate,
              "-ar",
              sampleRate.toString(),
              "-ac",
              channels.toString(),
              "-q:a",
              "2" // High quality
            );
            break;
          case "medium":
            compressionArgs.push(
              "-codec:a",
              format === "mp3" ? "libmp3lame" : "aac",
              "-b:a",
              "48k",
              "-ar",
              "16000",
              "-ac",
              "1",
              "-q:a",
              "4" // Medium quality
            );
            break;
          case "low":
            compressionArgs.push(
              "-codec:a",
              format === "mp3" ? "libmp3lame" : "aac",
              "-b:a",
              "32k",
              "-ar",
              "16000",
              "-ac",
              "1",
              "-q:a",
              "6" // Lower quality, smaller size
            );
            break;
        }

        compressionArgs.push(outputName);

        // Execute compression
        await ffmpeg.exec(compressionArgs);

        // Read compressed file
        const compressedData = await ffmpeg.readFile(outputName);
        const compressedBlob = new Blob([compressedData], {
          type: format === "mp3" ? "audio/mpeg" : "audio/mp4",
        });

        // Calculate compression ratio
        const compressionRatio = (
          ((audioBlob.size - compressedBlob.size) / audioBlob.size) *
          100
        ).toFixed(1);

        console.log("✅ Frontend compression completed:", {
          originalSize: `${(audioBlob.size / 1024 / 1024).toFixed(2)}MB`,
          compressedSize: `${(compressedBlob.size / 1024 / 1024).toFixed(2)}MB`,
          compressionRatio: `${compressionRatio}%`,
          quality,
        });

        // Clean up temporary files
        try {
          await ffmpeg.deleteFile(inputName);
          await ffmpeg.deleteFile(outputName);
        } catch (cleanupError) {
          console.warn("Cleanup warning:", cleanupError.message);
        }

        return {
          compressedBlob,
          originalSize: audioBlob.size,
          compressedSize: compressedBlob.size,
          compressionRatio: parseFloat(compressionRatio),
          format,
        };
      } catch (err) {
        console.error("❌ Frontend compression failed:", err);
        setError(`Compression failed: ${err.message}`);
        throw err;
      } finally {
        setIsLoading(false);
        setProgress(0);
      }
    },
    [initFFmpeg]
  );

  // Compress with automatic quality selection based on file size
  const smartCompress = useCallback(
    async (audioBlob) => {
      const sizeInMB = audioBlob.size / 1024 / 1024;

      let quality, bitrate;
      if (sizeInMB > 5) {
        quality = "low";
        bitrate = "32k";
      } else if (sizeInMB > 2) {
        quality = "medium";
        bitrate = "48k";
      } else {
        quality = "high";
        bitrate = "64k";
      }

      console.log(
        `🤖 Smart compression: ${quality} quality selected for ${sizeInMB.toFixed(
          2
        )}MB file`
      );

      return compressAudio(audioBlob, { quality, bitrate });
    },
    [compressAudio]
  );

  return {
    compressAudio,
    smartCompress,
    initFFmpeg,
    isLoading,
    progress,
    error,
    isFFmpegLoaded: isLoadedRef.current,
  };
};

export default useAudioCompression;
