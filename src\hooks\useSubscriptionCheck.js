import { useState, useEffect, useCallback } from "react";
import { useAuth } from "../components/others/AuthContext";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import {
  checkSubmissionEligibility,
  processAnswerSubmission,
  getUserId,
} from "../utils/subscriptionUtils";

/**
 * Custom hook for managing subscription checks and answer submissions
 * @param {string} questionId - Optional question ID for tracking
 * @returns {object} - Object containing subscription state and methods
 */
export const useSubscriptionCheck = (questionId = null) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [eligibility, setEligibility] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [canSubmitAnswer, setCanSubmitAnswer] = useState(true);
  const [canViewAnswer, setCanViewAnswer] = useState(true);

  // Get user ID
  const userId = getUserId(user);

  /**
   * Check if user is authenticated
   */
  const checkAuthentication = useCallback(() => {
    const token = localStorage.getItem("token");
    const isAuthenticated = localStorage.getItem("isAuthenticated");
    return user && token && isAuthenticated === "true";
  }, [user]);

  /**
   * Check subscription eligibility
   */
  const checkEligibility = useCallback(async () => {
    // If user is not authenticated, don't check subscription eligibility
    if (!checkAuthentication()) {
      setEligibility({
        canSubmit: false,
        canViewAnswer: false,
        reason: "unauthenticated",
        userData: null,
        answersRemaining: 0,
      });
      setCanSubmitAnswer(false);
      setCanViewAnswer(false);
      setLoading(false);
      return;
    }

    if (!userId) {
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      const result = await checkSubmissionEligibility(userId);
      setEligibility(result);
      setCanSubmitAnswer(result.canSubmit);
      setCanViewAnswer(result.canViewAnswer);
    } catch (error) {
      console.error("Error checking subscription eligibility:", error);
      setEligibility({
        canSubmit: false,
        canViewAnswer: false,
        reason: "error",
        userData: null,
        answersRemaining: 0,
      });
      setCanSubmitAnswer(false);
      setCanViewAnswer(false);
    } finally {
      setLoading(false);
    }
  }, [userId, checkAuthentication]);

  /**
   * Handle answer submission with authentication and subscription check
   * @param {function} submitCallback - Function to call when submission is allowed
   * @returns {Promise<boolean>} - Whether submission was successful
   */
  const handleSubmitWithCheck = useCallback(
    async (submitCallback) => {
      // First check if user is authenticated
      if (!checkAuthentication()) {
        toast.warning("Please sign in to submit your answer", {
          position: "top-center",
          autoClose: 3000,
        });

        // Redirect to login page after a short delay
        setTimeout(() => {
          navigate("/login");
        }, 1000);

        return false;
      }

      if (!userId) {
        console.error("No user ID found");
        return false;
      }

      const result = await processAnswerSubmission(
        userId,
        async (eligibilityData) => {
          // User can submit - call the provided callback
          if (submitCallback) {
            await submitCallback(eligibilityData);
          }
          // Refresh eligibility after successful submission
          await checkEligibility();
        },
        (eligibilityData) => {
          // User needs subscription - show modal
          setEligibility(eligibilityData);
          setShowSubscriptionModal(true);
        }
      );

      return result.success;
    },
    [userId, checkEligibility, checkAuthentication, navigate]
  );

  /**
   * Force show subscription modal with authentication check
   */
  const showSubscriptionRequiredModal = useCallback(
    (customEligibility = null) => {
      // First check if user is authenticated
      if (!checkAuthentication()) {
        toast.warning("Please sign in to view answers", {
          position: "top-center",
          autoClose: 3000,
        });

        // Redirect to login page after a short delay
        setTimeout(() => {
          navigate("/login");
        }, 1000);

        return false;
      }

      setEligibility(customEligibility || eligibility);
      setShowSubscriptionModal(true);
      return true;
    },
    [eligibility, checkAuthentication, navigate]
  );

  /**
   * Close subscription modal
   */
  const closeSubscriptionModal = useCallback(() => {
    setShowSubscriptionModal(false);
  }, []);

  /**
   * Refresh eligibility data
   */
  const refreshEligibility = useCallback(async () => {
    await checkEligibility();
  }, [checkEligibility]);

  // Check eligibility on mount and when userId changes
  useEffect(() => {
    checkEligibility();
  }, [checkEligibility]);

  return {
    // State
    eligibility,
    loading,
    showSubscriptionModal,
    canSubmitAnswer,
    canViewAnswer,
    userId,

    // Methods
    handleSubmitWithCheck,
    showSubscriptionRequiredModal,
    closeSubscriptionModal,
    refreshEligibility,
    checkEligibility,

    // Computed values
    hasActiveSubscription: eligibility?.reason === "active_subscription",
    answersRemaining: eligibility?.answersRemaining || 0,
    isWithinFreeLimit: eligibility?.reason === "within_free_limit",
    isFreeLimitExceeded: eligibility?.reason === "free_limit_exceeded",
    isAuthenticated: checkAuthentication(),
    isUnauthenticated: eligibility?.reason === "unauthenticated",
  };
};
