import { fetchSubCategoryDetail } from "@/api/services/services";
import React, { useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import { MediaRecorder } from "extendable-media-recorder";
import AudioPlayer from "react-audio-player";
// import AudioPlayer from 'react-h5-audio-player';
import "react-h5-audio-player/lib/styles.css";
import api from "@/api/services/api";
import AiScoreModal from "@/components/AiScoreModal";
import dummy1 from "../../../public/assets/dummy3.mp3";
import { toast, ToastContainer } from "react-toastify";
import localStorage from "@react-native-async-storage/async-storage";
import Loader from "react-js-loader";
import Header from "@/components/layout/headers/Header";
import SubCategoryModal from "@/components/SubCategoryModal";

const getAbbreviation = (name) => {
  const words = name.split(" ").filter((word) => word.length > 0);
  return words.map((word) => word.charAt(0).toUpperCase()).join("");
};

const convertTimeToSeconds = (time) => {
  const [hours, minutes, seconds] = time.split(":").map(Number);
  return hours * 3600 + minutes * 60 + seconds;
};

const formatTime = (seconds) => {
  const hours = String(Math.floor(seconds / 3600)).padStart(2, "0");
  const minutes = String(Math.floor((seconds % 3600) / 60)).padStart(2, "0");
  const secs = String(seconds % 60).padStart(2, "0");
  return `${hours}:${minutes}:${secs}`;
};

export const CateogoryDetail = (props) => {
  const { practiceId } = useParams();
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timer, setTimer] = useState(null);
  const [testTime, setTestTime] = useState(null);
  const [mediaRecorder, setMediaRecorder] = useState(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordedBlob, setRecordedBlob] = useState(null);
  const [audioUrl, setAudioUrl] = useState("");
  const abbreviation = getAbbreviation(data?.sub_category_details?.name || "");
  const [AiScoreData, SetAiScoreData] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedData, setSelectedData] = useState(null);
  // const userData = localStorage.getItem("user")
  const [userData, setuserData] = useState();
  const [user_id, setuserId] = useState();
  const [playbackRate, setPlaybackRate] = useState(1);
  const [activeDropdown, setActiveDropdown] = useState(null);
  // console.log(userData,"userData...")
  const safeParse = (data) => {
    try {
      return JSON.parse(data);
    } catch (e) {
      console.error("Parsing error:", e);
      return null; // or some default value
    }
  };

  useEffect(() => {
    const getStoredValue = async () => {
      const value = await new Promise((resolve) => {
        resolve(localStorage.getItem("user"));
      });
      console.log(value, "valueee");
      setuserData(value);
    };

    getStoredValue();
  }, []);

  useEffect(() => {
    const getStoredValue = async () => {
      const value = await new Promise((resolve) => {
        resolve(localStorage.getItem("isUserId"));
      });
      console.log(value, "valueee");
      setuserId(value);
    };

    getStoredValue();
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await fetchSubCategoryDetail(practiceId);
        setData(result.data);
        if (result.data.prepare_time) {
          setTimer(convertTimeToSeconds(result.data.prepare_time));
        }
        if (result.data.test_time) {
          setTestTime(convertTimeToSeconds(result.data.test_time));
        }
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [practiceId]);

  // const handleScoreInfoClick = (id) => {
  //     const selectedItem = AiScoreData.find(item => item.id === id);
  //     setSelectedData(selectedItem);
  //     setIsModalOpen(true);
  // };
  const handleScoreInfoClick = (id) => {
    const selectedItem = AiScoreData.find((item) => item.id === id);
    if (selectedItem) {
      setSelectedData(selectedItem);
      setIsModalOpen(true);
    }
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedData(null);
  };

  useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => setTimer((prev) => prev - 1), 1000);
      return () => clearInterval(interval);
    } else if (timer === 0 && testTime > 0) {
      const interval = setInterval(() => setTestTime((prev) => prev - 1), 1000);
      return () => clearInterval(interval);
    }
  }, [timer, testTime]);

  useEffect(() => {
    const initializeMediaRecorder = async () => {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const recorder = new MediaRecorder(stream, {
        mimeType: "audio/webm",
      });
      setMediaRecorder(recorder);

      recorder.ondataavailable = (event) => {
        setRecordedBlob(event.data);
      };
    };

    initializeMediaRecorder();
  }, []);

  const startRecording = () => {
    if (mediaRecorder) {
      mediaRecorder.start();
      setIsRecording(true);
    }
  };

  const stopRecording = () => {
    if (mediaRecorder) {
      mediaRecorder.stop();
      setIsRecording(false);
    }
  };

  useEffect(() => {
    if (recordedBlob) {
      const url = URL.createObjectURL(recordedBlob);
      setAudioUrl(url);
    }
  }, [recordedBlob]);

  const handleDownload = (url) => {
    console.log(url, "urrlll");
    if (url) {
      const link = document.createElement("a");
      link.href = url;
      link.download = `recording_${Date.now()}.wav`;
      document.body.appendChild(link);
      link.click();
      console.log(link, "..");
      document.body.removeChild(link);
    } else {
      if (audioUrl) {
        const link = document.createElement("a");
        link.href = audioUrl;
        link.download = `recording_${Date.now()}.wav`; // Change to .mp3 if needed
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    }
  };

  const handleDownloadurl = (url) => {
    const a = document.createElement("a");
    a.href = url; // Set the href to the audio file URL
    a.download = url.split("/").pop(); // Use the filename from the URL
    // document.body.appendChild(a);
    // a.click(); // Simulate a click to trigger the download
    // document.body.removeChild(a); // Clean up by removing the element
  };

  const toggleDropdown = (index) => {
    // Toggle the dropdown for the clicked item
    setActiveDropdown(activeDropdown === index ? null : index);
  };

  const changePlaybackRate = (rate) => {
    setPlaybackRate(rate);
    setActiveDropdown(false);
  };
  console.log(data, "...");

  const handleDone = async () => {
    // const user_id = localStorage.getItem("isUserId");

    if (!recordedBlob) {
      console.error("No recorded blob available.");
      return;
    }

    const url = URL.createObjectURL(recordedBlob);
    console.log("Blob URL:", url);

    const file = await fetch(url).then((r) => r.blob());
    if (!file) {
      console.error("Failed to create blob from URL.");
      return;
    }
    setLoading(true);

    console.log(data, "...");
    const dataToSend = {
      file: `recording_${Date.now()}.wav`,
      user_id,
      practice_id: data.id,
      category_id: data.main_category_id,
      sub_category_id: data.sub_category_id,
      // original_text: data.paragraph
      ...(data.paragraph ? { original_text: data.paragraph } : {}),
      ...(data.audio ? { original_audio: data.audio } : {}),
      ...(data.image ? { original_image: data.image } : {}),
    };
    console.log(dataToSend, "....dataToSend");
    const formData = new FormData();
    formData.append("file", file, dataToSend.file);

    for (const [key, value] of Object.entries(dataToSend)) {
      formData.append(key, value);
    }

    // Log each entry in FormData
    for (let [key, value] of formData.entries()) {
      console.log(`${key}: ${value}`);
    }

    try {
      const response = await fetch(
        "https://srv625589.hstgr.cloud/practice_score",
        {
          method: "POST",
          body: formData,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
        setLoading(false);
      }
      setLoading(false);
      const result = await response.json();
      console.log(result);
      toast.success("Test successfully save");
      getAIScoreApi();
    } catch (error) {
      setLoading(false);
      console.error("Error uploading file:", error, error.message);
      toast.error("Something went wrong");
    }
  };

  const getAIScoreApi = async () => {
    const practice_id = data.id;
    const url = `https://srv625589.hstgr.cloud/practice_score?user_id=${user_id}&practice_id=${practice_id}`;

    try {
      const response = await fetch(url, {
        method: "GET",
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      SetAiScoreData(result);
      console.log(result, "...");
    } catch (error) {
      console.error("Error uploading file:", error, error.message);
    }
  };

  useEffect(() => getAIScoreApi, [data]);

  const [subCategoryData, setSubCategoryData] = useState([]);
  const [isModalOpen1, setIsModalOpen1] = useState(false);

  const handleSubCategoryOpen = (data) => {
    setSubCategoryData(data);
    setIsModalOpen1(true);
  };

  const [screenWidth, setScreenWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => {
      setScreenWidth(window.innerWidth);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const isSmallScreen = screenWidth <= 768;

  if (error) return <p>Error fetching data: {error.message}</p>;
  if (!data)
    return (
      <Loader
        type="spinner-circle"
        bgColor="#140342"
        color="#140342"
        size={100}
      />
    );

  return (
    <>
      <Header onSubCategoryOpen={handleSubCategoryOpen} />

      <SubCategoryModal
        isOpen={isModalOpen1}
        onClose={() => setIsModalOpen1(false)}
        data={subCategoryData}
      />
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: isSmallScreen ? "flex-start" : "center",
          flexDirection: "column",
          backgroundImage: "url(/assets/img/bgImg.png)",
          backgroundSize: "cover",
          backgroundPosition: "center",
          minHeight: "100vh",
          width: "full",
          paddingBottom: 50,
          overflowX: "hidden",
          overflowY: "hidden",
          flexWrap: "wrap",
          marginTop: 100,
          paddingLeft: isSmallScreen ? 10 : 0,
          marginRight: isSmallScreen ? 10 : 0,
        }}
      >
        <ToastContainer />
        {loading && (
          <div
            style={{
              display: "flex",
              position: "fixed",
              top: 0,
              left: 0,
              width: "100vw",
              height: "100vh",
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "rgba(255, 255, 255, 0.5)",
              zIndex: 1000,
            }}
          >
            <Loader
              type="spinner-circle"
              bgColor="#140342"
              color="#140342"
              size={100}
            />
          </div>
        )}
        <div
          className="main-content"
          style={{
            display: "flex",
            justifyContent: "center",
            flexDirection: "column",
            alignItems: "flex-start",
            maxWidth: "1200px",
            padding: "20px",
            flexWrap: "wrap",
          }}
        >
          <div
            style={{
              display: "flex",
              flexDirection: isSmallScreen ? "column" : "row",
              gap: 20,
              alignItems: isSmallScreen ? "flex-start" : "center",
              marginTop: isSmallScreen ? 30 : 20,
              width: isSmallScreen ? "100vw" : "1200px",
              flexWrap: "wrap",
            }}
          >
            <div
              style={{
                display: "flex",
                height: "8vh",
                width: isSmallScreen ? "13vw" : "4vw",
                backgroundColor: "#0e3d31",
                alignItems: "center",
                justifyContent: "center",
                borderTopRightRadius: 50,
                position: "relative",
              }}
            >
              <h3 style={{ color: "white", fontWeight: "600" }}>
                {abbreviation}
              </h3>
              {data && data.sub_category_details.ai_score === 1 && (
                <p
                  style={{
                    color: "black",
                    fontSize: 15,
                    fontWeight: "600",
                    position: "absolute",
                    top: -5,
                    right: 0,
                  }}
                >
                  AI
                </p>
              )}
            </div>

            <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: 10,
                marginTop: isSmallScreen ? 20 : 50,
                flexWrap: "wrap",
              }}
            >
              <div
                style={{
                  display: "flex",
                  gap: isSmallScreen ? 10 : 20,
                  flexDirection: "row",
                  alignItems: "center",
                }}
              >
                <h2
                  style={{
                    color: "#140342",
                    fontWeight: "500",
                    fontSize: isSmallScreen ? "20px" : "32px",
                    textAlign: "center",
                  }}
                >
                  {data && data.sub_category_details.name}
                </h2>
                <div
                  style={{
                    backgroundColor: "#140342",
                    display: "flex",
                    flexDirection: "row",
                    padding: 5,
                    alignItems: "center",
                    justifyContent: "center",
                    gap: 5,
                    borderRadius: 5,
                  }}
                >
                  <img
                    style={{ height: 20, width: 20 }}
                    src="/assets/img/education.png"
                    alt="separator line"
                  />
                  <p
                    style={{ color: "white", fontWeight: "600", fontSize: 15 }}
                  >
                    Study Guide
                  </p>
                </div>
              </div>

              <h6 style={{ color: "#5B5B5B", fontWeight: "500" }}>
                {data && data.sub_category_details.description}
              </h6>
            </div>
          </div>

          <img
            style={{
              height: 1,
              width: "100%",
              marginTop: 10,
              marginBottom: 10,
            }}
            src="/assets/img/line_black.png"
            alt="separator line"
          />
        </div>

        <div
          style={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            width: "1200px",
            paddingLeft: 20,
            paddingRight: 20,
            flexWrap: "wrap",
          }}
        >
          <div style={{ display: "flex", flexDirection: "row", gap: 20 }}>
            <div style={{ display: "flex", flexDirection: "row", gap: 10 }}>
              <p style={{ color: "#140342", fontWeight: "500", fontSize: 20 }}>
                #{data && data.q_num}
              </p>
              <p style={{ color: "#140342", fontWeight: "500", fontSize: 20 }}>
                {data && data.title}
              </p>
            </div>
            {data.difficulty && (
              <div
                style={{
                  borderWidth: 1,
                  borderRadius: 2,
                  borderColor: "#140342",
                  padding: 5,
                  backgroundColor: "#D9D9D9",
                }}
              >
                {data.difficulty}
              </div>
            )}
          </div>
          <div style={{ display: "flex", flexDirection: "row", gap: 10 }}>
            <div
              style={{
                backgroundColor: "#140342",
                display: "flex",
                flexDirection: "row",
                padding: 5,
                alignItems: "center",
                justifyContent: "center",
                gap: 5,
                borderRadius: 5,
              }}
            >
              <img
                style={{ height: 20, width: 20 }}
                src="/assets/img/education.png"
                alt="separator line"
              />
              <p style={{ color: "white", fontWeight: "600", fontSize: 15 }}>
                Tested
              </p>
            </div>
            <img
              style={{ height: 30, width: 30 }}
              src="/assets/img/bookMark.png"
              alt="separator line"
            />
          </div>
        </div>

        {timer !== null && (
          <div style={{ width: "1200px", marginTop: 40 }}>
            <p
              style={{
                color: timer === 0 ? "red" : "black",
                fontWeight: "500",
                fontSize: "15px",
                lineHeight: 1.5,
                textAlign: "justify",
              }}
            >
              {timer === 0 ? "Test Time: " : "Prepare Time: "}{" "}
              {formatTime(timer === 0 ? testTime : timer)}
            </p>
          </div>
        )}

        {data && data.sub_category_details.content_type == "text" && (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              width: isSmallScreen ? "100vw" : "1200px",
              marginTop: 40,
              marginBottom: 40,
              flexWrap: "wrap",
            }}
          >
            <p
              style={{
                color: "black",
                fontWeight: "500",
                fontSize: "24px",
                lineHeight: 1.5,
                textAlign: "justify",
                marginBottom: 40,
              }}
            >
              {data.paragraph}
            </p>

            {data && data.sub_category_details.input_type == "text" ? (
              <textarea
                style={{
                  minHeight: "30vh",
                  borderRadius: 5,
                  borderWidth: 2,
                  borderColor: "black",
                  borderStyle: "solid",
                  marginTop: 40,
                  fontSize: 15,
                  color: "black",
                  padding: 10,
                  fontWeight: "500",
                }}
              />
            ) : (
              <>
                {" "}
                {data && data.sub_category_details.input_type == "audio" ? (
                  <div>
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        justifyContent: "center",
                        backgroundColor: "#D3D3D3",
                        paddingTop: 30,
                        paddingBottom: 30,
                        paddingLeft: 5,
                        paddingRight: 5,
                      }}
                    >
                      <div
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "center",
                          justifyContent: "center",
                          backgroundColor: "#D3D3D3",
                        }}
                      >
                        <div
                          style={{
                            marginTop: "20px",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            flexDirection: "column",
                            gap: 10,
                          }}
                        >
                          {isRecording ? (
                            <p
                              style={{
                                color: "#000000",
                                fontSize: 20,
                                fontWeight: "500",
                              }}
                            >
                              Click To Stop
                            </p>
                          ) : (
                            <p
                              style={{
                                color: "#000000",
                                fontSize: 20,
                                fontWeight: "500",
                              }}
                            >
                              Click To Start
                            </p>
                          )}
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              width: "60px",
                              height: "60px",
                              borderRadius: "50%",
                              backgroundColor: isRecording
                                ? "#8055f6"
                                : "lightgray",
                              cursor: "pointer",
                            }}
                            onClick={
                              isRecording ? stopRecording : startRecording
                            }
                          >
                            <img
                              src="/assets/img/microphone.png"
                              alt={
                                isRecording
                                  ? "Stop Recording"
                                  : "Start Recording"
                              }
                              style={{ width: "30px", height: "30px" }}
                            />
                          </div>
                          {/* {recordedBlob && <p>Recorded audio is ready to download!</p>} */}
                        </div>
                        {/* <div style={{ display: 'flex', backgroundColor: '#140342', borderRadius: 10, padding: 10, margin: 10 }} onClick={handleDone} >
                                    <p style={{ color: 'white' }}>Done</p>
                                </div> */}
                        <div
                          style={{
                            display: "flex",
                            flex: 1,
                            alignItems: "center",
                            justifyContent: "flex-end",
                            flexDirection: "row",
                            marginLeft: "30vw",
                            gap: 5,
                            marginTop: 20,
                          }}
                        >
                          <div
                            style={{
                              backgroundColor: "#9780ff",
                              borderRadius: "50%",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              width: "30px",
                              height: "30px",
                            }}
                          >
                            <p
                              style={{
                                color: "#000000",
                                fontSize: 18,
                                fontWeight: "600",
                              }}
                            >
                              i
                            </p>
                          </div>
                          <p
                            style={{
                              color: "#000000",
                              fontSize: 15,
                              fontWeight: "600",
                            }}
                          >
                            Use a headset with inline microphone to get accurate
                            AI scores
                          </p>
                        </div>
                      </div>
                    </div>
                    {recordedBlob && (
                      <div style={{ marginTop: 20 }}>
                        <div style={{ display: "flex", flexDirection: "row" }}>
                          {/* <AudioPlayer
                                            src={audioUrl}
                                            autoPlay={false}
                                            controls
                                            showSkipControls={false}
                                            showJumpControls={false}
                                            showlo
                                            onVolumeChanged={(e) => console.log(e)}
                                            loop={false}
                                            style={{ width: '40vw', backgroundColor: '#D3D3D3', borderRadius: 50, padding: 20 }}

                                               /> */}
                          <AudioPlayer
                            src={audioUrl}
                            // autoPlay={false}
                            controls
                            // loop={false}
                            // style={{ width: '40vw', backgroundColor: '#D3D3D3', borderRadius: 50, padding: 20 }}
                            // style={{ width: '40vw' }}
                            // onVolumeChanged={(e) => console.log(e)}
                            // playbackRate={playbackRate}
                            // showJumpControls={false}
                          />

                          <div
                            style={{
                              position: "relative",
                              display: "inline-block",
                            }}
                          >
                            {/* <button onClick={toggleDropdown} style={{ backgroundColor: '#9780ff', color: '#fff', border: 'none', borderRadius: 5, padding: '10px' }}>
                                                Options
                                            </button> */}
                            <img
                              src="/assets/img/dots.png"
                              alt={"translate"}
                              onClick={toggleDropdown}
                              style={{
                                width: "20px",
                                height: "20px",
                                marginTop: 20,
                                marginLeft: 20,
                              }}
                            />
                            {activeDropdown && (
                              <div
                                style={{
                                  position: "absolute",
                                  backgroundColor: "#fff",
                                  border: "1px solid #ccc",
                                  borderRadius: 5,
                                  boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                                  zIndex: 1,
                                  marginTop: 5,
                                  width: 150,
                                }}
                              >
                                <div
                                  style={{
                                    padding: "10px",
                                    cursor: "pointer",
                                    color: "black",
                                  }}
                                  onClick={handleDownload}
                                >
                                  Download
                                </div>
                                <div
                                  style={{
                                    padding: "10px",
                                    cursor: "pointer",
                                    color: "black",
                                  }}
                                >
                                  Playback Speed
                                  <div style={{ marginTop: 5 }}>
                                    <div
                                      onClick={() => changePlaybackRate(0.5)}
                                      style={{ cursor: "pointer" }}
                                    >
                                      0.5x
                                    </div>
                                    <div
                                      onClick={() => changePlaybackRate(1)}
                                      style={{ cursor: "pointer" }}
                                    >
                                      1x
                                    </div>
                                    <div
                                      onClick={() => changePlaybackRate(1.5)}
                                      style={{ cursor: "pointer" }}
                                    >
                                      1.5x
                                    </div>
                                    <div
                                      onClick={() => changePlaybackRate(2)}
                                      style={{ cursor: "pointer" }}
                                    >
                                      2x
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                        <p
                          style={{
                            color: "#000000",
                            fontSize: 15,
                            fontWeight: "600",
                          }}
                        >
                          AI Scoring and Audio Answer Download is available
                          after submission.
                        </p>
                      </div>
                    )}
                  </div>
                ) : (
                  <></>
                )}
              </>
            )}
          </div>
        )}

        {data.sub_category_details.content_type == "audio" && (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              width: isSmallScreen ? "100vw" : "1200px",
              marginTop: 40,
              marginBottom: 40,
            }}
          >
            {/* <AudioPlayer
                        src={data.audio}
                        autoPlay={false}
                        controls
                    /> */}
            <AudioPlayer
              src={`https://www.certifit.in/deep_insight_academy/${data.audio}`}
              controls
              // autoPlay={false}
              // loop={false}
              // // style={{ width: '40vw', backgroundColor: '#D3D3D3', borderRadius: 50, padding: 20 }}
              // style={{ width: '40vw' }}
              // onVolumeChanged={(e) => console.log(e)}
              // playbackRate={playbackRate}
              // showJumpControls={false}
            />

            {data.sub_category_details.input_type == "text" ? (
              <textarea
                style={{
                  minHeight: "30vh",
                  borderRadius: 5,
                  borderWidth: 2,
                  borderColor: "black",
                  borderStyle: "solid",
                  marginTop: 40,
                  fontSize: 15,
                  color: "black",
                  padding: 10,
                  fontWeight: "500",
                }}
              />
            ) : (
              <>
                {" "}
                {data.sub_category_details.input_type == "audio" ? (
                  <div>
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        justifyContent: "center",
                        backgroundColor: "#D3D3D3",
                        paddingTop: 30,
                        paddingBottom: 30,
                        paddingLeft: 5,
                        paddingRight: 5,
                      }}
                    >
                      <div
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "center",
                          justifyContent: "center",
                          backgroundColor: "#D3D3D3",
                        }}
                      >
                        <div
                          style={{
                            marginTop: "20px",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            flexDirection: "column",
                            gap: 10,
                          }}
                        >
                          {isRecording ? (
                            <p
                              style={{
                                color: "#000000",
                                fontSize: 20,
                                fontWeight: "500",
                              }}
                            >
                              Click To Stop
                            </p>
                          ) : (
                            <p
                              style={{
                                color: "#000000",
                                fontSize: 20,
                                fontWeight: "500",
                              }}
                            >
                              Click To Start
                            </p>
                          )}
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              width: "60px",
                              height: "60px",
                              borderRadius: "50%",
                              backgroundColor: isRecording
                                ? "#8055f6"
                                : "lightgray",
                              cursor: "pointer",
                            }}
                            onClick={
                              isRecording ? stopRecording : startRecording
                            }
                          >
                            <img
                              src="/assets/img/microphone.png"
                              alt={
                                isRecording
                                  ? "Stop Recording"
                                  : "Start Recording"
                              }
                              style={{ width: "30px", height: "30px" }}
                            />
                          </div>
                          {/* {recordedBlob && <p>Recorded audio is ready to download!</p>} */}
                        </div>
                        {/* <div style={{ display: 'flex', backgroundColor: '#140342', borderRadius: 10, padding: 10, margin: 10 }} onClick={handleDone} >
                                            <p style={{ color: 'white' }}>Done</p>
                                        </div> */}
                        <div
                          style={{
                            display: "flex",
                            flex: 1,
                            alignItems: "center",
                            justifyContent: "flex-end",
                            flexDirection: "row",
                            marginLeft: "30vw",
                            gap: 5,
                            marginTop: 20,
                          }}
                        >
                          <div
                            style={{
                              backgroundColor: "#9780ff",
                              borderRadius: "50%",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              width: "30px",
                              height: "30px",
                            }}
                          >
                            <p
                              style={{
                                color: "#000000",
                                fontSize: 18,
                                fontWeight: "600",
                              }}
                            >
                              i
                            </p>
                          </div>
                          <p
                            style={{
                              color: "#000000",
                              fontSize: 15,
                              fontWeight: "600",
                            }}
                          >
                            Use a headset with inline microphone to get accurate
                            AI scores
                          </p>
                        </div>
                      </div>
                    </div>
                    {recordedBlob && (
                      // <div style={{ marginTop: 20 }}>

                      //     <AudioPlayer
                      //         src={audioUrl}
                      //         autoPlay={false}
                      //         controls
                      //     />

                      //     <p style={{ color: '#000000', fontSize: 15, fontWeight: '600' }}>AI Scoring and Audio Answer Download is available after submission.</p>
                      // </div>
                      <div style={{ marginTop: 20 }}>
                        <div style={{ display: "flex", flexDirection: "row" }}>
                          {/* <AudioPlayer
                                        src={audioUrl}
                                        autoPlay={false}
                                        controls
                                        showSkipControls={false}
                                        showJumpControls={false}
                                        showlo
                                        onVolumeChanged={(e) => console.log(e)}
                                        loop={false}
                                        style={{ width: '40vw', backgroundColor: '#D3D3D3', borderRadius: 50, padding: 20 }}

                                           /> */}
                          <AudioPlayer
                            src={audioUrl}
                            controls
                            // autoPlay={false}
                            // loop={false}
                            // // style={{ width: '40vw', backgroundColor: '#D3D3D3', borderRadius: 50, padding: 20 }}
                            // style={{ width: '40vw' }}
                            // onVolumeChanged={(e) => console.log(e)}
                            // playbackRate={playbackRate}
                            // showJumpControls={false}
                          />

                          <div
                            style={{
                              position: "relative",
                              display: "inline-block",
                            }}
                          >
                            {/* <button onClick={toggleDropdown} style={{ backgroundColor: '#9780ff', color: '#fff', border: 'none', borderRadius: 5, padding: '10px' }}>
                                            Options
                                        </button> */}
                            <img
                              src="/assets/img/dots.png"
                              alt={"translate"}
                              onClick={toggleDropdown}
                              style={{
                                width: "20px",
                                height: "20px",
                                marginTop: 20,
                                marginLeft: 20,
                              }}
                            />
                            {activeDropdown && (
                              <div
                                style={{
                                  position: "absolute",
                                  backgroundColor: "#fff",
                                  border: "1px solid #ccc",
                                  borderRadius: 5,
                                  boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                                  zIndex: 1,
                                  marginTop: 5,
                                  width: 150,
                                }}
                              >
                                <div
                                  style={{
                                    padding: "10px",
                                    cursor: "pointer",
                                    color: "black",
                                  }}
                                  onClick={handleDownload}
                                >
                                  Download
                                </div>
                                <div
                                  style={{
                                    padding: "10px",
                                    cursor: "pointer",
                                    color: "black",
                                  }}
                                >
                                  Playback Speed
                                  <div style={{ marginTop: 5 }}>
                                    <div
                                      onClick={() => changePlaybackRate(0.5)}
                                      style={{ cursor: "pointer" }}
                                    >
                                      0.5x
                                    </div>
                                    <div
                                      onClick={() => changePlaybackRate(1)}
                                      style={{ cursor: "pointer" }}
                                    >
                                      1x
                                    </div>
                                    <div
                                      onClick={() => changePlaybackRate(1.5)}
                                      style={{ cursor: "pointer" }}
                                    >
                                      1.5x
                                    </div>
                                    <div
                                      onClick={() => changePlaybackRate(2)}
                                      style={{ cursor: "pointer" }}
                                    >
                                      2x
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                        <p
                          style={{
                            color: "#000000",
                            fontSize: 15,
                            fontWeight: "600",
                          }}
                        >
                          AI Scoring and Audio Answer Download is available
                          after submission.
                        </p>
                      </div>
                    )}
                  </div>
                ) : (
                  <></>
                )}
              </>
            )}
          </div>
        )}

        {data.sub_category_details.content_type == "image" && (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              width: isSmallScreen ? "100vw" : "1200px",
              marginTop: 40,
              marginBottom: 40,
              flexWrap: "wrap",
            }}
          >
            <img
              src={`https://www.certifit.in/deep_insight_academy/${data.image}`}
              alt="data img"
              style={{ height: "500px", width: "450px", marginBottom: 20 }}
            />

            {data.sub_category_details.input_type == "text" ? (
              <textarea
                style={{
                  minHeight: "30vh",
                  borderRadius: 5,
                  borderWidth: 2,
                  borderColor: "black",
                  borderStyle: "solid",
                  marginTop: 40,
                  fontSize: 15,
                  color: "black",
                  padding: 10,
                  fontWeight: "500",
                }}
              />
            ) : (
              <>
                {" "}
                {data.sub_category_details.input_type == "audio" ? (
                  <div>
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        justifyContent: "center",
                        backgroundColor: "#D3D3D3",
                        paddingTop: 30,
                        paddingBottom: 30,
                        paddingLeft: 5,
                        paddingRight: 5,
                      }}
                    >
                      <div
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "center",
                          justifyContent: "center",
                          backgroundColor: "#D3D3D3",
                        }}
                      >
                        <div
                          style={{
                            marginTop: "20px",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            flexDirection: "column",
                            gap: 10,
                          }}
                        >
                          {isRecording ? (
                            <p
                              style={{
                                color: "#000000",
                                fontSize: 20,
                                fontWeight: "500",
                              }}
                            >
                              Click To Stop
                            </p>
                          ) : (
                            <p
                              style={{
                                color: "#000000",
                                fontSize: 20,
                                fontWeight: "500",
                              }}
                            >
                              Click To Start
                            </p>
                          )}
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              width: "60px",
                              height: "60px",
                              borderRadius: "50%",
                              backgroundColor: isRecording
                                ? "#8055f6"
                                : "lightgray",
                              cursor: "pointer",
                            }}
                            onClick={
                              isRecording ? stopRecording : startRecording
                            }
                          >
                            <img
                              src="/assets/img/microphone.png"
                              alt={
                                isRecording
                                  ? "Stop Recording"
                                  : "Start Recording"
                              }
                              style={{ width: "30px", height: "30px" }}
                            />
                          </div>
                          {/* {recordedBlob && <p>Recorded audio is ready to download!</p>} */}
                        </div>
                        {/* <div style={{ display: 'flex', backgroundColor: '#140342', borderRadius: 10, padding: 10, margin: 10 }} onClick={handleDone} >
                                            <p style={{ color: 'white' }}>Done</p>
                                        </div> */}
                        <div
                          style={{
                            display: "flex",
                            flex: 1,
                            alignItems: "center",
                            justifyContent: "flex-end",
                            flexDirection: "row",
                            marginLeft: "30vw",
                            gap: 5,
                            marginTop: 20,
                          }}
                        >
                          <div
                            style={{
                              backgroundColor: "#9780ff",
                              borderRadius: "50%",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              width: "30px",
                              height: "30px",
                            }}
                          >
                            <p
                              style={{
                                color: "#000000",
                                fontSize: 18,
                                fontWeight: "600",
                              }}
                            >
                              i
                            </p>
                          </div>
                          <p
                            style={{
                              color: "#000000",
                              fontSize: 15,
                              fontWeight: "600",
                            }}
                          >
                            Use a headset with inline microphone to get accurate
                            AI scores
                          </p>
                        </div>
                      </div>
                    </div>
                    {recordedBlob && (
                      // <div style={{ marginTop: 20 }}>

                      //     <AudioPlayer
                      //         src={audioUrl}
                      //         autoPlay={false}
                      //         controls
                      //     />

                      //     <p style={{ color: '#000000', fontSize: 15, fontWeight: '600' }}>AI Scoring and Audio Answer Download is available after submission.</p>
                      // </div>
                      <div style={{ marginTop: 20 }}>
                        <div style={{ display: "flex", flexDirection: "row" }}>
                          {/* <AudioPlayer
                                        src={audioUrl}
                                        autoPlay={false}
                                        controls
                                        showSkipControls={false}
                                        showJumpControls={false}
                                        showlo
                                        onVolumeChanged={(e) => console.log(e)}
                                        loop={false}
                                        style={{ width: '40vw', backgroundColor: '#D3D3D3', borderRadius: 50, padding: 20 }}

                                           /> */}
                          <AudioPlayer
                            src={audioUrl}
                            controls
                            // autoPlay={false}
                            // loop={false}
                            // // style={{ width: '40vw', backgroundColor: '#D3D3D3', borderRadius: 50, padding: 20 }}
                            // style={{ width: '40vw' }}
                            // onVolumeChanged={(e) => console.log(e)}
                            // playbackRate={playbackRate}
                            // showJumpControls={false}
                          />

                          <div
                            style={{
                              position: "relative",
                              display: "inline-block",
                            }}
                          >
                            {/* <button onClick={toggleDropdown} style={{ backgroundColor: '#9780ff', color: '#fff', border: 'none', borderRadius: 5, padding: '10px' }}>
                                            Options
                                        </button> */}
                            <img
                              src="/assets/img/dots.png"
                              alt={"translate"}
                              onClick={toggleDropdown}
                              style={{
                                width: "20px",
                                height: "20px",
                                marginTop: 20,
                                marginLeft: 20,
                              }}
                            />
                            {activeDropdown && (
                              <div
                                style={{
                                  position: "absolute",
                                  backgroundColor: "#fff",
                                  border: "1px solid #ccc",
                                  borderRadius: 5,
                                  boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                                  zIndex: 1,
                                  marginTop: 5,
                                  width: 150,
                                }}
                              >
                                <div
                                  style={{
                                    padding: "10px",
                                    cursor: "pointer",
                                    color: "black",
                                  }}
                                  onClick={handleDownload}
                                >
                                  Download
                                </div>
                                <div
                                  style={{
                                    padding: "10px",
                                    cursor: "pointer",
                                    color: "black",
                                  }}
                                >
                                  Playback Speed
                                  <div style={{ marginTop: 5 }}>
                                    <div
                                      onClick={() => changePlaybackRate(0.5)}
                                      style={{ cursor: "pointer" }}
                                    >
                                      0.5x
                                    </div>
                                    <div
                                      onClick={() => changePlaybackRate(1)}
                                      style={{ cursor: "pointer" }}
                                    >
                                      1x
                                    </div>
                                    <div
                                      onClick={() => changePlaybackRate(1.5)}
                                      style={{ cursor: "pointer" }}
                                    >
                                      1.5x
                                    </div>
                                    <div
                                      onClick={() => changePlaybackRate(2)}
                                      style={{ cursor: "pointer" }}
                                    >
                                      2x
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                        <p
                          style={{
                            color: "#000000",
                            fontSize: 15,
                            fontWeight: "600",
                          }}
                        >
                          AI Scoring and Audio Answer Download is available
                          after submission.
                        </p>
                      </div>
                    )}
                  </div>
                ) : (
                  <></>
                )}
              </>
            )}
          </div>
        )}

        <div
          style={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            width: isSmallScreen ? "100vw" : "1200px",
          }}
        >
          <div style={{ display: "flex", gap: "10" }}>
            <button
              style={{
                backgroundColor: "#D9D9D9",
                marginRight: 10,
                paddingLeft: 20,
                paddingRight: 20,
              }}
              onClick={handleDone}
            >
              <p style={{ color: "black", fontSize: 12 }}>Submit</p>
            </button>
            <button
              style={{
                backgroundColor: "#D9D9D9",
                borderWidth: 1,
                borderColor: "#140342",
                borderStyle: "solid",
                marginRight: 10,
                paddingLeft: 20,
                paddingRight: 20,
              }}
            >
              <p style={{ color: "black", fontSize: 12 }}>Re-do</p>
            </button>
            <button
              style={{
                backgroundColor: "#D9D9D9",
                borderWidth: 1,
                borderColor: "#140342",
                borderStyle: "solid",
                marginRight: 10,
                paddingLeft: 20,
                paddingRight: 20,
                display: "flex",
                flexDirection: "row",
                justifyItems: "center",
                alignItems: "center",
                gap: 10,
              }}
            >
              <img
                src="/assets/img/translate.png"
                alt={"translate"}
                style={{ width: "20px", height: "20px" }}
              />
              <p style={{ color: "#522CFF", fontSize: 12 }}> Translation</p>
            </button>
            <button
              style={{
                backgroundColor: "#D9D9D9",
                borderWidth: 1,
                borderColor: "#140342",
                borderStyle: "solid",
                marginRight: 10,
                paddingLeft: 20,
                paddingRight: 20,
                display: "flex",
                flexDirection: "row",
                justifyItems: "center",
                alignItems: "center",
                gap: 10,
              }}
            >
              <img
                src="/assets/img/shadowing.png"
                alt={"translate"}
                style={{ width: "20px", height: "20px" }}
              />
              <p style={{ color: "#522CFF", fontSize: 12 }}>Shadowing</p>
            </button>
            <button
              style={{
                backgroundColor: "#D9D9D9",
                borderWidth: 1,
                borderColor: "#140342",
                borderStyle: "solid",
                marginRight: 10,
                paddingLeft: 20,
                paddingRight: 20,
                display: "flex",
                flexDirection: "row",
                justifyItems: "center",
                alignItems: "center",
                gap: 10,
              }}
            >
              <img
                src="/assets/img/oneLineMode.png"
                alt={"translate"}
                style={{ width: "20px", height: "20px" }}
              />
              <p style={{ color: "#522CFF", fontSize: 12 }}>One line mode</p>
            </button>
          </div>
          <div style={{ display: "flex", gap: "10" }}>
            <button
              style={{
                backgroundColor: "#D9D9D9",
                borderWidth: 1,
                borderColor: "#FF3C00",
                borderStyle: "solid",
                marginRight: 10,
                paddingLeft: 20,
                paddingRight: 20,
                display: "flex",
                flexDirection: "row",
                justifyItems: "center",
                alignItems: "center",
                gap: 10,
              }}
            >
              <img
                src="/assets/img/ticket.png"
                alt={"translate"}
                style={{ width: "20px", height: "20px" }}
              />
              <p style={{ color: "#FF3C00", fontSize: 12 }}>X 3</p>
            </button>
            <div
              style={{
                backgroundColor: "#D9D9D9",
                borderWidth: 1,
                borderColor: "#000",
                borderStyle: "solid",
                marginRight: 10,
                display: "flex",
                flexDirection: "row",
                justifyItems: "center",
                alignItems: "center",
              }}
            >
              <input
                style={{
                  border: "none",
                  color: "#5B5B5B",
                  fontSize: 15,
                  width: "60%",
                }}
                placeholder="Question Co... "
              />
              <div
                style={{
                  backgroundColor: "#2F00FF",
                  margin: 0,
                  padding: 0,
                  width: "50%",
                  display: "flex",
                  justifyContent: "center",
                  height: "100%",
                  alignItems: "center",
                }}
              >
                <img
                  src="/assets/img/search.png"
                  alt={"translate"}
                  style={{ width: "20px", height: "20px", filter: "invert(1)" }}
                />
              </div>
            </div>
            <button
              style={{
                backgroundColor: "#5B5B5B",
                marginRight: 10,
                paddingLeft: 20,
                paddingRight: 20,
              }}
            >
              <p style={{ color: "white", fontSize: 12 }}>Previous</p>
            </button>
            <button
              style={{
                backgroundColor: "#140342",
                marginRight: 10,
                paddingLeft: 20,
                paddingRight: 20,
              }}
            >
              <p style={{ color: "white", fontSize: 12 }}>Next</p>
            </button>
          </div>
        </div>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            width: isSmallScreen ? "100vw" : "1200px",
            marginTop: 40,
          }}
        >
          <div style={{ position: "relative" }}>
            <img
              style={{
                height: 1,
                width: "100%",
                marginTop: 10,
                marginBottom: 10,
                position: "relative",
              }}
              src="/assets/img/line_black.png"
              alt="separator line"
            />
            <p
              style={{
                padding: 2,
                color: "black",
                backgroundColor: "white",
                position: "absolute",
                left: 180,
                top: 0,
                fontWeight: "bold",
              }}
            >
              Discussion
            </p>
          </div>

          {/* <div style={{ display: 'flex', gap: '10', marginTop: 40 }}>
                        <button style={{ backgroundColor: '#D9D9D9', marginRight: 10, paddingLeft: 20, paddingRight: 20, display: 'flex', flexDirection: 'row', justifyItems: 'center', alignItems: 'center', gap: 10 }} onClick={() => SetAiScoreData([])}>

                            <img
                                src="/assets/img/notification.png"
                                alt={'translate'}
                                style={{ width: '20px', height: '20px' }}
                            />
                            <p style={{ color: '#140342', fontSize: 12 }}>Discussion</p>
                        </button >
                        <button style={{ backgroundColor: '#D9D9D9', borderWidth: 1, borderColor: '#140342', borderStyle: 'solid', marginRight: 10, paddingLeft: 20, paddingRight: 20, display: 'flex', flexDirection: 'row', justifyItems: 'center', alignItems: 'center', gap: 10 }} onClick={() => SetAiScoreData([])}>

                            <img
                                src="/assets/img/menu.png"
                                alt={'translate'}
                                style={{ width: '20px', height: '20px' }}
                            />
                            <p style={{ color: '#140342', fontSize: 12 }}>Board</p>
                        </button >
                        <button style={{ backgroundColor: '#D9D9D9', borderWidth: 1, borderColor: '#140342', borderStyle: 'solid', marginRight: 10, paddingLeft: 20, paddingRight: 20, display: 'flex', flexDirection: 'row', justifyItems: 'center', alignItems: 'center', gap: 10 }} onClick={getAIScoreApi}>

                            <img
                                src="/assets/img/person.png"
                                alt={'translate'}
                                style={{ width: '20px', height: '20px' }}
                            />
                            <p style={{ color: '#140342', fontSize: 12 }}>Me</p>
                        </button >

                    </div> */}
          <img
            style={{ height: 1, width: "100%", marginBottom: 40 }}
            src="/assets/img/line_black.png"
            alt="separator line"
          />
          {AiScoreData &&
            AiScoreData.length > 0 &&
            AiScoreData.map((item, index) => (
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  marginTop: 20,
                  alignItems: "center",
                }}
                key={index}
              >
                <img
                  style={{ height: 50, width: 50 }}
                  src="/assets/img/user.png"
                  alt="separator line"
                />
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    gap: 5,
                    marginLeft: 30,
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      gap: 20,
                      alignItems: "center",
                    }}
                  >
                    <p
                      style={{
                        color: "black",
                        fontSize: 20,
                        fontWeight: "500",
                      }}
                    >
                      {userData}
                    </p>
                    <p
                      style={{
                        color: "#5B5B5B",
                        fontSize: 15,
                        fontWeight: "500",
                      }}
                    >
                      {new Date(item.timestamp).toISOString().split("T")[0]}
                    </p>
                  </div>

                  {/* <AudioPlayer
                                        src={audioUrl}
                                        autoPlay={false}
                                        controls

                                    /> */}

                  <div style={{ marginTop: 20 }}>
                    <div style={{ display: "flex", flexDirection: "row" }}>
                      <AudioPlayer
                        src={item.audio_url}
                        controls
                        // autoPlay={false}
                        // loop={false}
                        // style={{ width: '30vw' }}
                        // showJumpControls={false}
                      />
                      {/* <div style={{ position: 'relative', display: 'inline-block' }}>
                                                <img
                                                    src="/assets/img/dots.png"
                                                    alt={'options'}
                                                    onClick={() => toggleDropdown(index)}
                                                    style={{ width: '20px', height: '20px', marginTop: 20, marginLeft: 20 }}
                                                />
                                                {activeDropdown === index && ( // Show dropdown only for the active index
                                                    <div style={{
                                                        position: 'absolute',
                                                        backgroundColor: '#fff',
                                                        border: '1px solid #ccc',
                                                        borderRadius: 5,
                                                        boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                                                        zIndex: 1,
                                                        marginTop: 5,
                                                        width: 150
                                                    }}>
                                                        <div style={{ padding: '10px', cursor: 'pointer', color: 'black' }} onClick={handleDownloadurl(item.audio_url)}>
                                                            Download
                                                        </div>
                                                        <div style={{ padding: '10px', cursor: 'pointer', color: 'black' }}>
                                                            Playback Speed
                                                            <div style={{ marginTop: 5 }}>
                                                                <div onClick={() => changePlaybackRate(0.5)} style={{ cursor: 'pointer' }}>0.5x</div>
                                                                <div onClick={() => changePlaybackRate(1)} style={{ cursor: 'pointer' }}>1x</div>
                                                                <div onClick={() => changePlaybackRate(1.5)} style={{ cursor: 'pointer' }}>1.5x</div>
                                                                <div onClick={() => changePlaybackRate(2)} style={{ cursor: 'pointer' }}>2x</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}
                                            </div> */}
                    </div>
                  </div>

                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "center",
                    }}
                  >
                    <button
                      style={{
                        backgroundColor: "#D9D9D9",
                        borderWidth: 1,
                        borderColor: "#140342",
                        borderStyle: "solid",
                        display: "flex",
                        flexDirection: "row",
                        justifyItems: "center",
                        alignItems: "center",
                        borderRadius: 20,
                        padding: 5,
                        justifyContent: "center",
                      }}
                    >
                      <p style={{ color: "#140342", fontSize: 12 }}>
                        RA S-ASIA V4i
                      </p>
                    </button>
                    <button
                      style={{
                        backgroundColor: "#D9D9D9",
                        borderWidth: 1,
                        borderColor: "#140342",
                        borderStyle: "solid",
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        borderRadius: 20,
                        padding: 5,
                        justifyContent: "center",
                        marginLeft: 10,
                      }}
                      onClick={() => handleScoreInfoClick(item.id)}
                    >
                      <p
                        style={{
                          color: "#2F00FF",
                          fontSize: 12,
                          textAlign: "center",
                        }}
                      >
                        Score Info {item.total_score}/100
                      </p>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          <AiScoreModal
            isOpen={isModalOpen}
            onClose={closeModal}
            data={selectedData}
          />
        </div>
      </div>
    </>
  );
};
