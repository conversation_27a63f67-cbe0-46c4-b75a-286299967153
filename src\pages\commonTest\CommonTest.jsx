import { useEffect, useRef, useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import "react-h5-audio-player/lib/styles.css";
import { toast, ToastContainer } from "react-toastify";
import Loader from "react-js-loader";
import Header from "@/components/layout/headers/Header";
import SubCategoryModal from "@/components/SubCategoryModal";
import { server } from "@/api/services/server";
import { getRequest } from "@/api/services/controller";
import { ComponentData } from "../componentData";
import BookmarkDropdown from "@/components/speech-ace-component/BookMarkDropdown";
import { useAuth } from "@/components/others/AuthContext";
import axios from "axios";

const theme = {
  primary: "#140342", // Main purple color
  lightPurple: "#f4f0ff", // Light purple background
  purple: "#9780ff", // Secondary purple
  green: "#4CAF50", // Green for success
  red: "#FF3C00", // Red for error/warning
  amber: "#F59E0B", // Amber/orange for warnings
  lightGray: "#E2E8F0", // Light gray for backgrounds
  gray: "#666", // Gray for text
  borderRadius: "10px", // Common border radius
  transition: "all 0.2s ease", // Common transition
};

const getDifficultyColor = (difficulty) => {
  switch (difficulty?.toLowerCase()) {
    case "easy":
      return theme.green;
    case "medium":
      return theme.amber;
    case "hard":
      return theme.red;
    default:
      return theme.lightGray;
  }
};

const CommonTest = () => {
  const { user } = useAuth(); // Get user data from AuthContext
  const [subCategoryData, setSubCategoryData] = useState([]);
  const getAbbreviation = (name) => {
    const words = name.split(" ").filter((word) => word.length > 0);
    return words.map((word) => word.charAt(0).toUpperCase()).join("");
  };

  const params = useParams();
  const navigate = useNavigate();
  const { practiceId } = params;
  console.log(practiceId);
  // const [ComponentData, setComponentData] = useState(null)
  const [data, setdata] = useState([]);
  const [isModalOpen1, setIsModalOpen1] = useState(false);
  const [screenWidth] = useState(window.innerWidth);
  const [loading, setloading] = useState(false);
  const [timer, setTimer] = useState(null);
  const abbreviation = getAbbreviation(data?.category?.name || "");
  const [testTime, setTestTime] = useState(null);
  const [currentCategoryId, setCurrentCategoryId] = useState(null);
  const [bookmarkColor, setBookmarkColor] = useState("");
  // Navigation states
  const [categoryQuestions, setCategoryQuestions] = useState([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(-1);

  // Discussion related states
  const [showDiscussion, setShowDiscussion] = useState(false);
  const [likedComments, setLikedComments] = useState(new Set());
  const [replyingTo, setReplyingTo] = useState(null);
  const [replyText, setReplyText] = useState("");
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState("");
  const commentInputRef = useRef(null);

  useEffect(() => {
    let mounted = true;

    const fetchQuestionData = async () => {
      if (!practiceId || !mounted) return;

      try {
        setloading(true);
        const uri = `${server.uri}questions/${practiceId}?filter={"include":[{"relation":"category"}]}`;
        const datadb = await getRequest(uri);

        if (mounted && datadb) {
          setdata(datadb);
          setCurrentCategoryId(datadb.category?.categoryId);

          // Load discussion comments if they exist
          if (datadb.additionalProp1?.discussion) {
            setComments(datadb.additionalProp1.discussion);
          } else {
            setComments([]);
          }

          // Always reset timers when ID changes
          setTimer(40);
          setTestTime(40);

          // After getting current question data, fetch all questions in this category
          if (datadb.category?.categoryId) {
            fetchCategoryQuestions(
              datadb.category.categoryId,
              datadb.questionId || datadb.id
            );
          }
        }
      } catch (error) {
        if (mounted) {
          console.error("Error fetching question data:", error);
        }
      } finally {
        if (mounted) {
          setloading(false);
        }
      }
    };

    fetchQuestionData();

    // Cleanup: Clear intervals and mounted flag
    return () => {
      mounted = false;
    };
  }, [practiceId]);

  // Fetch all questions for the current category
  const fetchCategoryQuestions = async (categoryId, currentQuestionId) => {
    try {
      const uri = `${server.uri}questions?filter={"where":{"categoryId":"${categoryId}"},"order":"questionNumber ASC"}`;
      const response = await getRequest(uri);

      if (response && Array.isArray(response)) {
        setCategoryQuestions(response);

        // Find the index of the current question
        const index = response.findIndex(
          (q) => (q.questionId || q.id) === currentQuestionId
        );
        setCurrentQuestionIndex(index !== -1 ? index : 0);
      }
    } catch (error) {
      console.error("Error fetching category questions:", error);
    }
  };

  // Navigation handlers
  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      const prevQuestion = categoryQuestions[currentQuestionIndex - 1];
      const prevQuestionId = prevQuestion.questionId || prevQuestion.id;
      navigate(`/commonTest/${prevQuestionId}`);
    } else {
      toast.info("You are at the first question of this category.");
    }
  };

  const handleNext = () => {
    if (currentQuestionIndex < categoryQuestions.length - 1) {
      const nextQuestion = categoryQuestions[currentQuestionIndex + 1];
      const nextQuestionId = nextQuestion.questionId || nextQuestion.id;
      navigate(`/commonTest/${nextQuestionId}`);
    } else {
      toast.info("You are at the last question of this category.");
    }
  };

  const handleQuestionSearch = (e) => {
    if (e.key === "Enter") {
      const searchValue = e.target.value.trim();
      if (!searchValue) return;

      // Try to match by question number
      const foundQuestion = categoryQuestions.find(
        (q) =>
          String(q.questionNumber) === searchValue ||
          q.prompt?.toLowerCase().includes(searchValue.toLowerCase()) ||
          q.questionName?.toLowerCase().includes(searchValue.toLowerCase())
      );

      if (foundQuestion) {
        const foundQuestionId = foundQuestion.questionId || foundQuestion.id;
        navigate(`/commonTest/${foundQuestionId}`);
      } else {
        toast.error("Question not found. Try another search term.");
      }
    }
  };

  // Handle question selection from sidebar
  const handleQuestionSelect = (questionId) => {
    // Always navigate to the questionId, regardless of questionType
    if (questionId) {
      navigate(`/commonTest/${questionId}`);
    } else {
      console.error("No questionId provided to handleQuestionSelect");
    }
  };

  useEffect(() => {
    let timerInterval;

    if (timer > 0) {
      timerInterval = setInterval(() => setTimer((prev) => prev - 1), 1000);
    } else if (timer === 0 && testTime > 0) {
      timerInterval = setInterval(() => setTestTime((prev) => prev - 1), 1000);
    }

    // Cleanup function to clear interval
    return () => {
      if (timerInterval) {
        clearInterval(timerInterval);
      }
    };
  }, [timer, testTime]);

  const handleSubCategoryOpen = async (categoryId) => {
    console.log("CommonTest receiving categoryId:", categoryId);

    try {
      setloading(true);
      // If categoryId is provided, use it; otherwise use the current category's ID
      const idToUse = categoryId || currentCategoryId;

      if (!idToUse) {
        console.error("No category ID available");
        return;
      }

      setSubCategoryData(idToUse);
      setIsModalOpen1(true);
    } catch (error) {
      console.error("Error in handleSubCategoryOpen:", error);
    } finally {
      setloading(false);
    }
  };

  // Handle submitting a new comment
  // Enhanced handleSubmitComment function with better error handling
  const handleSubmitComment = async () => {
    if (!newComment.trim()) {
      toast.error("Please enter a comment");
      return;
    }

    try {
      setloading(true);

      // Create new comment object with user info
      const newCommentObj = {
        id: Date.now().toString(), // Simple unique ID
        text: newComment.trim(),
        userId: user.id,
        userName: user.name,
        timestamp: new Date().toISOString(),
        likes: 0,
      };

      // Add to local state
      const updatedComments = [...comments, newCommentObj];
      setComments(updatedComments);

      // Clear comment input
      setNewComment("");

      // Update the question with new comments
      const questionId = data.questionId || data.id;

      // Create a cleaner data structure to send to the server
      const updatePayload = {
        additionalProp1: {
          ...data.additionalProp1,
          discussion: updatedComments,
        },
      };

      // Use the enhanced postRequests function for better error handling
      const response = await axios.patch(
        `${server.uri}questions/${questionId}`,
        updatePayload,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status >= 200 && response.status < 300) {
        // Show success message
        toast.success("Comment added successfully");
      } else {
        throw new Error(`Server returned ${response.status}`);
      }
    } catch (error) {
      console.error("Error adding comment:", error);
      toast.error("Failed to add comment. Please try again.");

      // Revert the local state since the server update failed
      setComments(comments);
    } finally {
      setloading(false);
    }
  };

  // Handle liking a comment
  const handleLikeComment = async (commentId) => {
    // Check if user has already liked this comment
    if (likedComments.has(commentId)) {
      toast.info("You've already liked this comment");
      return;
    }

    try {
      setloading(true);

      // Find and update the comment
      const updatedComments = comments.map((comment) => {
        if (comment.id === commentId) {
          return { ...comment, likes: comment.likes + 1 };
        } else if (comment.replies) {
          // Check for the comment in replies
          const updatedReplies = comment.replies.map((reply) => {
            if (reply.id === commentId) {
              return { ...reply, likes: reply.likes + 1 };
            }
            return reply;
          });
          return { ...comment, replies: updatedReplies };
        }
        return comment;
      });

      // Update local state optimistically
      setComments(updatedComments);
      // Add commentId to liked comments set
      setLikedComments((prev) => new Set([...prev, commentId]));

      // Update in the database
      const questionId = data.questionId || data.id;

      // Create a cleaner payload for updating
      const updatePayload = {
        additionalProp1: {
          ...data.additionalProp1,
          discussion: updatedComments,
        },
      };

      // Use axios directly for better error handling
      const response = await axios.patch(
        `${server.uri}questions/${questionId}`,
        updatePayload,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!(response.status >= 200 && response.status < 300)) {
        throw new Error(`Server returned ${response.status}`);
      }
    } catch (error) {
      console.error("Error liking comment:", error);
      toast.error("Failed to like comment. Please try again.");

      // Revert the local state since the server update failed
      setComments(comments);
      // Remove commentId from liked comments set
      setLikedComments((prev) => {
        const newSet = new Set([...prev]);
        newSet.delete(commentId);
        return newSet;
      });
    } finally {
      setloading(false);
    }
  };

  // Function to handle replying to comments
  const handleReplyToComment = (commentId) => {
    setReplyingTo(commentId);
    setReplyText("");
  };

  // Function to submit a reply
  const handleSubmitReply = async (parentId) => {
    if (!replyText.trim()) {
      toast.error("Please enter a reply");
      return;
    }

    try {
      setloading(true);

      // Create new reply object
      const newReply = {
        id: Date.now().toString(),
        text: replyText.trim(),
        userId: user.id,
        userName: user.name,
        timestamp: new Date().toISOString(),
        likes: 0,
        isReply: true,
      };

      // Find the parent comment and add the reply
      const updatedComments = comments.map((comment) => {
        if (comment.id === parentId) {
          return {
            ...comment,
            replies: [...(comment.replies || []), newReply],
          };
        }
        return comment;
      });

      // Update local state
      setComments(updatedComments);
      // Clear reply state
      setReplyingTo(null);
      setReplyText("");

      // Update in database
      const questionId = data.questionId || data.id;

      const updatePayload = {
        additionalProp1: {
          ...data.additionalProp1,
          discussion: updatedComments,
        },
      };

      const response = await axios.patch(
        `${server.uri}questions/${questionId}`,
        updatePayload,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status >= 200 && response.status < 300) {
        toast.success("Reply added successfully");
      } else {
        throw new Error(`Server returned ${response.status}`);
      }
    } catch (error) {
      console.error("Error adding reply:", error);
      toast.error("Failed to add reply. Please try again.");

      // Revert to original comments
      setComments(comments);
    } finally {
      setloading(false);
    }
  };

  // Enhanced handleDeleteComment to handle deleting replies
  const handleDeleteComment = async (commentId, parentId = null) => {
    if (!window.confirm("Are you sure you want to delete this comment?")) {
      return;
    }

    try {
      setloading(true);

      // Store original comments in case we need to revert

      let updatedComments;

      if (parentId) {
        // This is a reply - find the parent comment and remove the reply
        updatedComments = comments.map((comment) => {
          if (comment.id === parentId && comment.replies) {
            return {
              ...comment,
              replies: comment.replies.filter(
                (reply) => reply.id !== commentId
              ),
            };
          }
          return comment;
        });
      } else {
        // This is a top-level comment
        updatedComments = comments.filter(
          (comment) => comment.id !== commentId
        );
      }

      // Update local state optimistically
      setComments(updatedComments);

      // Update in the database
      const questionId = data.questionId || data.id;

      const updatePayload = {
        additionalProp1: {
          ...data.additionalProp1,
          discussion: updatedComments,
        },
      };

      const response = await axios.patch(
        `${server.uri}questions/${questionId}`,
        updatePayload,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status >= 200 && response.status < 300) {
        toast.success("Comment deleted successfully");
        // Also remove from liked comments if it was there
        if (likedComments.has(commentId)) {
          setLikedComments((prev) => {
            const newSet = new Set([...prev]);
            newSet.delete(commentId);
            return newSet;
          });
        }
      } else {
        throw new Error(`Server returned ${response.status}`);
      }
    } catch (error) {
      console.error("Error deleting comment:", error);
      toast.error("Failed to delete comment. Please try again.");

      // Revert the local state since the server update failed
      setComments(comments);
    } finally {
      setloading(false);
    }
  };

  // Generate initials from username
  const getUserInitials = (name) => {
    if (!name) return "?";
    return name
      .split(" ")
      .map((part) => part[0]?.toUpperCase() || "")
      .join("")
      .slice(0, 2);
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const Component = ComponentData?.find(
    (item) => item.categoryId == data?.categoryId
  )?.Component;
  const isSmallScreen = screenWidth <= 768;

  return (
    <>
      <Header onSubCategoryOpen={handleSubCategoryOpen} />

      {isModalOpen1 && (
        <SubCategoryModal
          isOpen={isModalOpen1}
          onClose={() => setIsModalOpen1(false)}
          data={subCategoryData || currentCategoryId}
        />
      )}
      <div
        style={{
          display: "flex",
          alignItems: "center",
          flexDirection: "column",
          backgroundImage: "url(/assets/img/bgImg.png)",
          backgroundSize: "cover",
          backgroundPosition: "center",
          minHeight: "100vh",
          width: "100%",
          paddingBottom: 80,
          overflowX: "hidden",
          overflowY: "hidden",
          flexWrap: "wrap",
          marginTop: isSmallScreen ? 80 : 80,
          paddingLeft: isSmallScreen ? 10 : 0,
          marginRight: isSmallScreen ? 10 : 0,
        }}
      >
        <ToastContainer />
        {loading && (
          <div
            style={{
              display: "flex",
              position: "fixed",
              top: 0,
              left: 0,
              width: "100vw",
              height: "100vh",
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "rgba(255, 255, 255, 0.5)",
              zIndex: 1000,
            }}
          >
            <Loader
              type="spinner-circle"
              bgColor="#140342"
              color="#140342"
              size={100}
            />
          </div>
        )}
        <div
          className="main-content"
          style={{
            display: "flex",
            justifyContent: "center",
            flexDirection: "column",
            alignItems: "flex-start",
            maxWidth: isSmallScreen ? "90%" : "100%",
          }}
        >
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              gap: 20,
              alignItems: "center",
              marginTop: isSmallScreen ? 30 : 20,
              width: isSmallScreen ? "100vw" : "86vw",
            }}
          >
            <div
              style={{
                display: isSmallScreen ? "none" : "flex",
                height: "8vh",
                width: isSmallScreen ? "13vw" : "4vw",
                backgroundColor: "#0e3d31",
                alignItems: "center",
                justifyContent: "center",
                borderTopRightRadius: 50,
                position: "relative",

              }}
            >
              <h3 style={{ color: "white", fontWeight: "600" }}>
                {abbreviation}
              </h3>
              {data?.category?.isAiBased && (
                <p
                  style={{
                    color: "black",
                    fontSize: 15,
                    fontWeight: "600",
                    position: "absolute",
                    top: -5,
                    right: 0,
                  }}
                >
                  AI
                </p>
              )}
            </div>

            <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: 10,
                marginTop: isSmallScreen ? 20 : 50,
                flexWrap: "wrap",
                width: "100%",
              }}
            >
              <div
                style={{
                  display: "flex",
                  gap: isSmallScreen ? 10 : 20,
                  flexDirection: "row",
                  alignItems: "center",
                }}
              >
                <h2
                  style={{
                    color: "#140342",
                    fontWeight: "500",
                    fontSize: isSmallScreen ? "20px" : "32px",
                    textAlign: "center",
                  }}
                >
                  {data && data?.category?.name}
                </h2>
                <div
                  style={{
                    backgroundColor: "#140342",
                    display: "flex",
                    flexDirection: "row",
                    padding: 5,
                    alignItems: "center",
                    justifyContent: "center",
                    gap: 5,
                    borderRadius: 5,
                  }}
                >
                  <img
                    style={{ height: 20, width: 20 }}
                    src="/assets/img/education.png"
                    alt="separator line"
                  />
                  <p
                    style={{ color: "white", fontWeight: "600", fontSize: 15 }}
                  >
                    Study Guide
                  </p>
                </div>
              </div>

              <h6 style={{ color: "#5B5B5B", fontWeight: "500" }}>
                {data && data?.category?.description}
              </h6>
            </div>
          </div>

          <img
            style={{
              height: 1,
              width: "100%",
              marginTop: 10,
              marginBottom: 10,
            }}
            src="/assets/img/line_black.png"
            alt="separator line"
          />
        </div>

        <div
          style={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            width: "86%",
            marginTop: isSmallScreen ? 10 : 10,
          }}
        >
          <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
            <div
              style={{
                backgroundColor: theme.lightPurple,
                padding: "5px 0px",
                borderRadius: "8px",
                fontWeight: "600",
                color: theme.primary,
              }}
            >
              #{data?.questionNumber || ""}
            </div>
            <h3
              style={{
                color: theme.primary,
                fontWeight: "500",
                margin: 0,
                fontSize: "18px",
              }}
            >
              {data?.additionalProp1?.questionName ||
                data?.prompt?.split(" ").slice(0, 5).join(" ") ||
                ""}
            </h3>
            {data?.difficulty && (
              <div
                style={{
                  backgroundColor: getDifficultyColor(data.difficulty),
                  color: "white",
                  padding: "4px 12px",
                  borderRadius: "6px",
                  fontSize: "12px",
                  fontWeight: "600",
                  textTransform: "capitalize",
                }}
              >
                {data.difficulty}
              </div>
            )}
          </div>
          <div style={{ display: "flex", flexDirection: "row", gap: 10 }}>
            {/* <div
              style={{
                backgroundColor: "#140342",
                display: "flex",
                flexDirection: "row",
                padding: 5,
                alignItems: "center",
                justifyContent: "center",
                gap: 5,
                borderRadius: 5,
              }}
            >
              <img
                style={{ height: 20, width: 20 }}
                src="/assets/img/education.png"
                alt="separator line"
              />
              <p style={{ color: "white", fontWeight: "600", fontSize: 15 }}>
                Tested
              </p>
            </div> */}
            <BookmarkDropdown
              questionId={practiceId}
              initialLabel={bookmarkColor}
              onLabelChange={(label) => {
                console.log("Label changed to:", label);
                setBookmarkColor(label);
              }}
            />
          </div>
        </div>

        {/* Question component */}
        {data &&
          (Component ? (
            <Component
              question={data}
              onQuestionSelect={handleQuestionSelect}
            />
          ) : (
            <div>Loading...</div>
          ))}

        {/* Navigation buttons */}
        <div
          style={{
            display: "flex",
            width: "90%",
            justifyContent: "right",
            alignItems: "center",
            marginTop: "20px",
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "15px",
            }}
          >
            {/* Question search */}
            <div
              style={{
                display: "flex",
                borderRadius: "8px",
                overflow: "hidden",
                border: `1px solid ${theme.lightGray}`,
              }}
            >
              <input
                placeholder="Search question..."
                onKeyDown={handleQuestionSearch}
                style={{
                  border: "none",
                  padding: "8px 12px",
                  outline: "none",
                  fontSize: "14px",
                  width: "140px",
                }}
              />
              <button
                style={{
                  backgroundColor: theme.purple,
                  border: "none",
                  padding: "0 15px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  cursor: "pointer",
                }}
              >
                <img
                  src="/assets/img/search.png"
                  alt="Search"
                  style={{
                    width: "16px",
                    height: "16px",
                    filter: "brightness(0) invert(1)",
                  }}
                />
              </button>
            </div>

            {/* Navigation buttons */}
            <button
              onClick={handlePrevious}
              disabled={currentQuestionIndex <= 0}
              style={{
                backgroundColor:
                  currentQuestionIndex <= 0 ? theme.lightGray : theme.primary,
                color: currentQuestionIndex <= 0 ? theme.gray : "white",
                border: "none",
                padding: "8px 20px",
                borderRadius: "8px",
                cursor: currentQuestionIndex <= 0 ? "not-allowed" : "pointer",
                fontWeight: "500",
                fontSize: "14px",
                display: "flex",
                alignItems: "center",
                gap: "8px",
                transition: theme.transition,
              }}
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M15 6L9 12L15 18"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              Previous
            </button>

            <button
              onClick={handleNext}
              disabled={currentQuestionIndex >= categoryQuestions.length - 1}
              style={{
                backgroundColor:
                  currentQuestionIndex >= categoryQuestions.length - 1
                    ? theme.lightGray
                    : theme.primary,
                color:
                  currentQuestionIndex >= categoryQuestions.length - 1
                    ? theme.gray
                    : "white",
                border: "none",
                padding: "8px 20px",
                borderRadius: "8px",
                cursor:
                  currentQuestionIndex >= categoryQuestions.length - 1
                    ? "not-allowed"
                    : "pointer",
                fontWeight: "500",
                fontSize: "14px",
                display: "flex",
                alignItems: "center",
                gap: "8px",
                transition: theme.transition,
              }}
            >
              Next
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9 18L15 12L9 6"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Question Component */}
        {/* {Component && data && (
          <div style={{ width: "90%", marginTop: "20px" }}>
            <Component
              question={data}
              onQuestionSelect={handleQuestionSelect}
            />
          </div>
        )} */}

        {/* Discussion section */}
        <div
          style={{
            width: "90%",
            marginTop: "30px",
          }}
        >
          <div style={{ position: "relative" }}>
            <img
              style={{
                height: 1,
                width: "100%",
                marginTop: 10,
                marginBottom: 30,
                position: "relative",
              }}
              src="/assets/img/line_black.png"
              alt="separator line"
            />
            <p
              style={{
                padding: "0 10px",
                color: "black",
                backgroundColor: "white",
                position: "absolute",
                left: "45%",
                top: -10,
                fontWeight: "bold",
                border: "1px solid #ddd",
                borderRadius: "15px",
                cursor: "pointer",
                display: "flex",
                alignItems: "center",
                gap: "5px",
              }}
              onClick={() => setShowDiscussion(!showDiscussion)}
            >
              Discussion
              {comments.length > 0 && (
                <span
                  style={{
                    background: theme.primary,
                    color: "white",
                    borderRadius: "50%",
                    width: "20px",
                    height: "20px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    fontSize: "12px",
                  }}
                >
                  {comments.length}
                </span>
              )}
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                style={{
                  transform: showDiscussion ? "rotate(180deg)" : "rotate(0deg)",
                  transition: "transform 0.3s ease",
                }}
              >
                <path
                  d="M19 9L12 16L5 9"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </p>
          </div>

          {showDiscussion && (
            <div
              style={{
                background: "white",
                borderRadius: "10px",
                padding: "20px",
                boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                marginBottom: "30px",
              }}
            >
              {/* Comment input */}
              <div
                style={{
                  marginBottom: "25px",
                  background: "#f8f9fa",
                  padding: "15px",
                  borderRadius: "10px",
                }}
              >
                <h3
                  style={{
                    margin: "0 0 15px 0",
                    fontSize: "18px",
                    color: theme.primary,
                    fontWeight: "600",
                  }}
                >
                  Add your comment
                </h3>
                <textarea
                  ref={commentInputRef}
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="Share your thoughts about this question..."
                  style={{
                    width: "100%",
                    padding: "12px 15px",
                    borderRadius: "8px",
                    border: "1px solid #ddd",
                    fontSize: "14px",
                    outline: "none",
                    marginBottom: "10px",
                    minHeight: "80px",
                    resize: "vertical",
                  }}
                />
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <div style={{ color: theme.gray, fontSize: "14px" }}>
                    Posting as: <strong>{user?.name || "Anonymous"}</strong>
                  </div>
                  <button
                    onClick={handleSubmitComment}
                    style={{
                      backgroundColor: theme.primary,
                      color: "white",
                      border: "none",
                      borderRadius: "8px",
                      padding: "10px 20px",
                      fontSize: "14px",
                      fontWeight: "500",
                      cursor: "pointer",
                      transition: "background-color 0.2s ease",
                    }}
                  >
                    Post Comment
                  </button>
                </div>
              </div>

              {/* Comments list */}
              <div>
                <h3
                  style={{
                    margin: "0 0 15px 0",
                    fontSize: "18px",
                    color: theme.primary,
                    fontWeight: "600",
                    display: "flex",
                    alignItems: "center",
                    gap: "10px",
                  }}
                >
                  Discussion
                  {comments.length > 0 && (
                    <span
                      style={{
                        background: theme.lightPurple,
                        color: theme.primary,
                        borderRadius: "20px",
                        padding: "2px 10px",
                        fontSize: "14px",
                      }}
                    >
                      {comments.length} comment
                      {comments.length !== 1 ? "s" : ""}
                    </span>
                  )}
                </h3>

                {comments.length === 0 ? (
                  <div
                    style={{
                      textAlign: "center",
                      padding: "30px",
                      background: "#f8f9fa",
                      borderRadius: "10px",
                      color: theme.gray,
                    }}
                  >
                    <p style={{ margin: 0 }}>
                      Be the first to comment on this question!
                    </p>
                  </div>
                ) : (
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      gap: "15px",
                    }}
                  >
                    {comments.map((comment) => (
                      <div
                        key={comment.id}
                        style={{
                          border: "1px solid #eee",
                          borderRadius: "10px",
                          padding: "15px",
                          display: "flex",
                          flexDirection: "column",
                          gap: "15px",
                          marginBottom: "15px",
                        }}
                      >
                        <div style={{ display: "flex", gap: "15px" }}>
                          {/* User avatar */}
                          <div
                            style={{
                              width: "40px",
                              height: "40px",
                              borderRadius: "50%",
                              backgroundColor: theme.primary,
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              color: "white",
                              fontWeight: "600",
                              fontSize: "16px",
                              flexShrink: 0,
                            }}
                          >
                            {getUserInitials(comment.userName)}
                          </div>

                          {/* Comment content */}
                          <div style={{ flex: 1 }}>
                            <div
                              style={{
                                display: "flex",
                                justifyContent: "space-between",
                                marginBottom: "5px",
                                alignItems: "center",
                              }}
                            >
                              <div>
                                <span
                                  style={{
                                    fontWeight: "600",
                                    color: theme.primary,
                                  }}
                                >
                                  {comment.userName}
                                </span>
                                <span
                                  style={{
                                    color: theme.gray,
                                    fontSize: "12px",
                                    marginLeft: "10px",
                                  }}
                                >
                                  {formatDate(comment.timestamp)}
                                </span>
                              </div>

                              {/* Delete button (only for current user's comments) */}
                              {user && comment.userId === user.id && (
                                <button
                                  onClick={() =>
                                    handleDeleteComment(comment.id)
                                  }
                                  style={{
                                    background: "none",
                                    border: "none",
                                    cursor: "pointer",
                                    color: theme.red,
                                    display: "flex",
                                    alignItems: "center",
                                    padding: "0",
                                    fontSize: "12px",
                                  }}
                                >
                                  <svg
                                    width="16"
                                    height="16"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      d="M3 6H5H21"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    />
                                    <path
                                      d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    />
                                  </svg>
                                  <span style={{ marginLeft: "5px" }}>
                                    Delete
                                  </span>
                                </button>
                              )}
                            </div>

                            <p
                              style={{
                                margin: "10px 0",
                                color: "#333",
                                lineHeight: "1.5",
                                fontSize: "14px",
                              }}
                            >
                              {comment.text}
                            </p>

                            <div style={{ display: "flex", gap: "15px" }}>
                              {/* Like button */}
                              <button
                                onClick={() => handleLikeComment(comment.id)}
                                disabled={likedComments.has(comment.id)}
                                style={{
                                  background: "none",
                                  border: "none",
                                  cursor: likedComments.has(comment.id)
                                    ? "default"
                                    : "pointer",
                                  color: likedComments.has(comment.id)
                                    ? theme.purple
                                    : theme.gray,
                                  display: "flex",
                                  alignItems: "center",
                                  padding: "0",
                                  fontSize: "12px",
                                  opacity: likedComments.has(comment.id)
                                    ? 0.7
                                    : 1,
                                }}
                              >
                                <svg
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill={
                                    likedComments.has(comment.id)
                                      ? "currentColor"
                                      : "none"
                                  }
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M7 22H4C3.46957 22 2.96086 21.7893 2.58579 21.4142C2.21071 21.0391 2 20.5304 2 20V13C2 12.4696 2.21071 11.9609 2.58579 11.5858C2.96086 11.2107 3.46957 11 4 11H7M14 9V5C14 4.20435 13.6839 3.44129 13.1213 2.87868C12.5587 2.31607 11.7956 2 11 2L7 11V22H18.28C18.7623 22.0055 19.2304 21.8364 19.5979 21.524C19.9654 21.2116 20.2077 20.7769 20.28 20.3L21.66 11.3C21.7035 11.0134 21.6842 10.7207 21.6033 10.4423C21.5225 10.1638 21.3821 9.90629 21.1919 9.68751C21.0016 9.46873 20.7661 9.29393 20.5016 9.17522C20.2371 9.0565 19.9499 8.99672 19.66 9H14Z"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                </svg>
                                <span style={{ marginLeft: "5px" }}>
                                  {comment.likes > 0
                                    ? `${comment.likes} ${
                                        comment.likes === 1 ? "like" : "likes"
                                      }`
                                    : "Like"}
                                </span>
                              </button>

                              {/* Reply button (only show if this is not already a reply) */}
                              {!comment.isReply && (
                                <button
                                  onClick={() =>
                                    handleReplyToComment(comment.id)
                                  }
                                  style={{
                                    background: "none",
                                    border: "none",
                                    cursor: "pointer",
                                    color: theme.gray,
                                    display: "flex",
                                    alignItems: "center",
                                    padding: "0",
                                    fontSize: "12px",
                                  }}
                                >
                                  <svg
                                    width="16"
                                    height="16"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      d="M3 10H13C17.4183 10 21 13.5817 21 18V20M3 10L9 16M3 10L9 4"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    />
                                  </svg>
                                  <span style={{ marginLeft: "5px" }}>
                                    Reply
                                  </span>
                                </button>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Reply input field */}
                        {replyingTo === comment.id && (
                          <div
                            style={{
                              marginLeft: "55px",
                              marginTop: "10px",
                              marginBottom: "10px",
                              padding: "10px",
                              backgroundColor: "#f8f9fa",
                              borderRadius: "8px",
                            }}
                          >
                            <textarea
                              value={replyText}
                              onChange={(e) => setReplyText(e.target.value)}
                              placeholder="Write a reply..."
                              style={{
                                width: "100%",
                                padding: "10px 15px",
                                borderRadius: "8px",
                                border: "1px solid #ddd",
                                fontSize: "14px",
                                outline: "none",
                                marginBottom: "10px",
                                minHeight: "60px",
                                resize: "vertical",
                              }}
                            />
                            <div
                              style={{
                                display: "flex",
                                justifyContent: "flex-end",
                                gap: "10px",
                              }}
                            >
                              <button
                                onClick={() => setReplyingTo(null)}
                                style={{
                                  backgroundColor: "white",
                                  color: theme.gray,
                                  border: `1px solid ${theme.lightGray}`,
                                  borderRadius: "6px",
                                  padding: "5px 12px",
                                  fontSize: "12px",
                                  fontWeight: "500",
                                  cursor: "pointer",
                                }}
                              >
                                Cancel
                              </button>
                              <button
                                onClick={() => handleSubmitReply(comment.id)}
                                style={{
                                  backgroundColor: theme.primary,
                                  color: "white",
                                  border: "none",
                                  borderRadius: "6px",
                                  padding: "5px 12px",
                                  fontSize: "12px",
                                  fontWeight: "500",
                                  cursor: "pointer",
                                }}
                              >
                                Reply
                              </button>
                            </div>
                          </div>
                        )}

                        {/* Display replies if any */}
                        {comment.replies && comment.replies.length > 0 && (
                          <div style={{ marginLeft: "55px" }}>
                            {comment.replies.map((reply) => (
                              <div
                                key={reply.id}
                                style={{
                                  display: "flex",
                                  gap: "15px",
                                  marginBottom: "15px",
                                  padding: "10px",
                                  backgroundColor: "#f8f9fa",
                                  borderRadius: "8px",
                                }}
                              >
                                {/* Reply avatar */}
                                <div
                                  style={{
                                    width: "30px",
                                    height: "30px",
                                    borderRadius: "50%",
                                    backgroundColor: theme.purple,
                                    display: "flex",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    color: "white",
                                    fontWeight: "600",
                                    fontSize: "14px",
                                    flexShrink: 0,
                                  }}
                                >
                                  {getUserInitials(reply.userName)}
                                </div>

                                {/* Reply content */}
                                <div style={{ flex: 1 }}>
                                  <div
                                    style={{
                                      display: "flex",
                                      justifyContent: "space-between",
                                      marginBottom: "5px",
                                      alignItems: "center",
                                    }}
                                  >
                                    <div>
                                      <span
                                        style={{
                                          fontWeight: "600",
                                          color: theme.primary,
                                          fontSize: "13px",
                                        }}
                                      >
                                        {reply.userName}
                                      </span>
                                      <span
                                        style={{
                                          color: theme.gray,
                                          fontSize: "11px",
                                          marginLeft: "10px",
                                        }}
                                      >
                                        {formatDate(reply.timestamp)}
                                      </span>
                                    </div>

                                    {/* Delete reply button (only for current user's replies) */}
                                    {user && reply.userId === user.id && (
                                      <button
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleDeleteComment(
                                            reply.id,
                                            comment.id
                                          );
                                        }}
                                        style={{
                                          background: "none",
                                          border: "none",
                                          cursor: "pointer",
                                          color: theme.red,
                                          display: "flex",
                                          alignItems: "center",
                                          padding: "0",
                                          fontSize: "11px",
                                        }}
                                      >
                                        <svg
                                          width="14"
                                          height="14"
                                          viewBox="0 0 24 24"
                                          fill="none"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M3 6H5H21"
                                            stroke="currentColor"
                                            strokeWidth="2"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                          />
                                          <path
                                            d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z"
                                            stroke="currentColor"
                                            strokeWidth="2"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                          />
                                        </svg>
                                        <span style={{ marginLeft: "5px" }}>
                                          Delete
                                        </span>
                                      </button>
                                    )}
                                  </div>

                                  <p
                                    style={{
                                      margin: "8px 0",
                                      color: "#333",
                                      lineHeight: "1.4",
                                      fontSize: "13px",
                                    }}
                                  >
                                    {reply.text}
                                  </p>

                                  {/* Like button for reply */}
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleLikeComment(reply.id);
                                    }}
                                    disabled={likedComments.has(reply.id)}
                                    style={{
                                      background: "none",
                                      border: "none",
                                      cursor: likedComments.has(reply.id)
                                        ? "default"
                                        : "pointer",
                                      color: likedComments.has(reply.id)
                                        ? theme.purple
                                        : theme.gray,
                                      display: "flex",
                                      alignItems: "center",
                                      padding: "0",
                                      fontSize: "11px",
                                      opacity: likedComments.has(reply.id)
                                        ? 0.7
                                        : 1,
                                    }}
                                  >
                                    <svg
                                      width="14"
                                      height="14"
                                      viewBox="0 0 24 24"
                                      fill={
                                        likedComments.has(reply.id)
                                          ? "currentColor"
                                          : "none"
                                      }
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M7 22H4C3.46957 22 2.96086 21.7893 2.58579 21.4142C2.21071 21.0391 2 20.5304 2 20V13C2 12.4696 2.21071 11.9609 2.58579 11.5858C2.96086 11.2107 3.46957 11 4 11H7M14 9V5C14 4.20435 13.6839 3.44129 13.1213 2.87868C12.5587 2.31607 11.7956 2 11 2L7 11V22H18.28C18.7623 22.0055 19.2304 21.8364 19.5979 21.524C19.9654 21.2116 20.2077 20.7769 20.28 20.3L21.66 11.3C21.7035 11.0134 21.6842 10.7207 21.6033 10.4423C21.5225 10.1638 21.3821 9.90629 21.1919 9.68751C21.0016 9.46873 20.7661 9.29393 20.5016 9.17522C20.2371 9.0565 19.9499 8.99672 19.66 9H14Z"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                      />
                                    </svg>
                                    <span style={{ marginLeft: "5px" }}>
                                      {reply.likes > 0
                                        ? `${reply.likes} ${
                                            reply.likes === 1 ? "like" : "likes"
                                          }`
                                        : "Like"}
                                    </span>
                                  </button>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default CommonTest;
