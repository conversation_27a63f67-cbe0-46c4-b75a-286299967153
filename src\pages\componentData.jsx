// import ReadWritingFillInBlanks from "@/components/components/ReadWritingFillInBlanks";

import FillIntheBlanks from "@/components/listening/FillintheBlanks";
import HighLightWords from "@/components/listening/HighLightIncorrectWord";
import MultipleChoiceMultiple from "@/components/listening/MultiQnaMultiAns";
import MultipleOneSingle from "@/components/listening/MultiQnaoneAns";
import WriteFromDiction from "@/components/listening/WriteFromDiction";

import HighlightCorrectSummary from "@/components/listening/HighlightCorrectSummary";
import SelectMissingWord from "@/components/listening/SelectMissingWord";
import MultipleChoiceMultipl from "@/components/questionComponent/MultipleQaMultiAns";
import MultipleChoiceSingle from "@/components/questionComponent/MultipleQuOneAns";
import ReadWritingFillInBlanks from "@/components/questionComponent/ReadWritingFillInBlanks";
import ReadingFillInBlanks from "@/components/questionComponent/ReadingFillInBlanks";
import ReorderParagraphs from "@/components/questionComponent/ReOrderANs";
import AnswerShort from "@/components/speaking/AnswerShort";
import DescribeImage from "@/components/speaking/DescribeImage";

import ReTellLecture from "@/components/speaking/Re-tellLecture";
import ReadAloud from "@/components/speaking/ReadAloud";
import RepeatSentence from "@/components/speaking/RepeatSentence";
import Respond from "@/components/speaking/Respond";
import EssayWriting from "@/components/writing/EssayWriting";
import SummarizeTextA from "@/components/writing/SummerizeTextA";
import SummurizeText from "@/components/writing/SummurizeText";
import WriteEmail from "@/components/writing/WriteEmail";
import React from "react";
import SummarizeSpokenText from "@/components/listening/SummarizeSpokenText";

export const ComponentData = [
  {
    categoryId: "67801456e0dfdc154eff11ba",
    Component: ReadWritingFillInBlanks,
  },

  {
    categoryId: "67800fc7d4e7d9147dd9525d",
    Component: MultipleChoiceSingle,
  },
  {
    categoryId: "6780113ae0dfdc154eff11b6",
    Component: MultipleChoiceMultipl,
  },
  {
    categoryId: "678011ade0dfdc154eff11b8",
    Component: ReorderParagraphs,
  },
  {
    categoryId: "67801bda382bce18e30d11ca",
    Component: MultipleChoiceMultiple,
  },
  {
    categoryId: "67801ec5382bce18e30d11ce",
    Component: MultipleOneSingle,
  },
  {
    categoryId: "678022c1382bce18e30d11d0",
    Component: SelectMissingWord,
  },
  {
    categoryId: "678023f5382bce18e30d11d2",
    Component: HighLightWords,
  },
  {
    categoryId: "68556cb5d0001a608d091719",
    Component: SummarizeSpokenText,
  },
  {
    categoryId: "68556cb6d0001a608d09171a",
    Component: HighlightCorrectSummary,
  },
  {
    categoryId: "68556cb8d0001a608d09171b",
    Component: FillIntheBlanks,
  },
  {
    categoryId: "678024ac382bce18e30d11d3",
    Component: WriteFromDiction,
  },
  {
    categoryId: "6787cc2b486e6c04269a34e1",
    Component: ReadAloud,
  },
  {
    categoryId: "6783923483cd4009d9cddafa",
    Component: AnswerShort,
  },
  {
    categoryId: "67838fcf2e266d08ba713eed",
    Component: RepeatSentence,
  },
  {
    categoryId: "678391852e266d08ba713eee",
    Component: DescribeImage,
  },

  {
    categoryId: "67b34f839ab23dd6196d1a91",
    Component: Respond,
  },
  {
    categoryId: "6783926b83cd4009d9cddafb",
    Component: SummurizeText,
  },
  {
    categoryId: "67e5af2b73e32a15ba3d88c9",
    Component: SummarizeTextA,
  },
  {
    categoryId: "678392a883cd4009d9cddafc",
    Component: WriteEmail,
  },
  {
    categoryId: "67d893d673e32a15ba3d88c1",
    Component: EssayWriting,
  },
  {
    categoryId: "678392a883cd4009d9cddafc",
    Component: React.lazy(() => import("@/components/writing/WriteEmail")),
  },
  {
    categoryId: "67894ed2102a6d6548ceec90",
    Component: ReadingFillInBlanks,
  },
  {
    categoryId: "67e5acdd73e32a15ba3d88c8",
    Component: ReTellLecture,
  },
  //   {
  //       categoryId: "67801d77382bce18e30d11cc",
  //       Component: FillTheBlanks,
  // },
];
