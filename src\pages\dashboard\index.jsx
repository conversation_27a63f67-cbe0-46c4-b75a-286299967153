import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Header from "@/components/layout/headers/Header";
import FooterOne from "@/components/layout/footers/FooterOne";
import Preloader from "@/components/common/Preloader";
import MetaComponent from "@/components/common/MetaComponent";
import SubCategoryModal from "@/components/SubCategoryModal";

const metadata = {
  title: "PTE Practice - Deep Insight Academy",
  description:
    "Practice PTE questions with our comprehensive test preparation platform.",
};

export default function PTEPracticePage() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [subCategoryData, setSubCategoryData] = useState([]);
  const [selectedTab, setSelectedTab] = useState("PTE Core");
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [activeCategory, setActiveCategory] = useState(null);
  const [isExamModalOpen, setIsExamModalOpen] = useState(false);

  // Categories data matching Menu.jsx
  const categories = [
    {
      name: "speaking",
      icon: "🎤",
      color: "#E8F5E8",
      borderColor: "#4CAF50",
      data: [
        {
          categoryId: "6787cc2b486e6c04269a34e1",
          name: "Read Aloud",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "67838fcf2e266d08ba713eed",
          name: "Repeat Sentence",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "678391852e266d08ba713eee",
          name: "Describe Image",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "67e5acdd73e32a15ba3d88c8",
          name: "Re-tell Lecture",
          isAiBased: true,
          activeInCore: false,
          activeInAcademic: true,
        },
        {
          categoryId: "67b34f839ab23dd6196d1a91",
          name: "Respond to a situation",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: false,
        },
        {
          categoryId: "6783923483cd4009d9cddafa",
          name: "Answer Short Question",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: true,
        },
      ],
    },
    {
      name: "writing",
      icon: "✍️",
      color: "#FFF3E0",
      borderColor: "#FF9800",
      data: [
        {
          categoryId: "6783926b83cd4009d9cddafb",
          name: "Summarize Written Text",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: false,
        },
        {
          categoryId: "67d893d673e32a15ba3d88c1",
          name: "Write Essay",
          isAiBased: true,
          activeInCore: false,
          activeInAcademic: true,
        },
        {
          categoryId: "67e5af2b73e32a15ba3d88c9",
          name: "Summarize Written Text (A)",
          isAiBased: true,
          activeInCore: false,
          activeInAcademic: true,
        },
        {
          categoryId: "678392a883cd4009d9cddafc",
          name: "Write Email",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: false,
        },
      ],
    },
    {
      name: "reading",
      icon: "📖",
      color: "#E3F2FD",
      borderColor: "#2196F3",
      data: [
        {
          categoryId: "67801456e0dfdc154eff11ba",
          name: "Reading & Writing: Fill in the blanks",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "6780113ae0dfdc154eff11b6",
          name: "Multiple Choice (Multiple)",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "678011ade0dfdc154eff11b8",
          name: "Re-order Paragraphs",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "67894ed2102a6d6548ceec90",
          name: "Reading: Fill in the Blanks",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "67800fc7d4e7d9147dd9525d",
          name: "Multiple Choice (Single)",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
      ],
    },
    {
      name: "listening",
      icon: "🎧",
      color: "#F3E5F5",
      borderColor: "#9C27B0",
      data: [
        {
          categoryId: "68556cb5d0001a608d091719",
          name: "Summarize Spoken Text",
          isAiBased: true,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "67801bda382bce18e30d11ca",
          name: "Multiple Choice (Multiple)",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "68556cb8d0001a608d09171b",
          name: "Fill in the Blanks",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "68556cb6d0001a608d09171a",
          name: "Highlight Correct Summary",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "67801ec5382bce18e30d11ce",
          name: "Multiple Choice (Single)",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "678022c1382bce18e30d11d0",
          name: "Select Missing Word",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "678023f5382bce18e30d11d2",
          name: "Highlight Incorrect Words",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
        {
          categoryId: "678024ac382bce18e30d11d3",
          name: "Write from Dictation",
          isAiBased: false,
          activeInCore: true,
          activeInAcademic: true,
        },
      ],
    },
  ];

  useEffect(() => {
    const savedTab = localStorage.getItem("selectedPteTab");
    if (savedTab) {
      setSelectedTab(savedTab);
    }
  }, []);

  const handleSubCategoryOpen = (categoryId) => {
    setSubCategoryData(categoryId);
    setIsModalOpen(true);
  };

  const handleCategoryClick = (category) => {
    setActiveCategory(category);
    setIsSidebarOpen(true);
  };

  const handleExamTypeChange = (examType) => {
    setSelectedTab(examType);
    localStorage.setItem("selectedPteTab", examType);
    setIsExamModalOpen(false);
  };

  const getFilteredQuestions = (category) => {
    return category.data.filter((item) =>
      selectedTab === "PTE Core" ? item.activeInCore : item.activeInAcademic
    );
  };

  return (
    <>
      <Preloader />
      <MetaComponent meta={metadata} />
      <Header onSubCategoryOpen={handleSubCategoryOpen} />

      {/* Exam Type Modal */}
      <AnimatePresence>
        {isExamModalOpen && (
          <motion.div
            className="modal"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: "rgba(0,0,0,0.5)",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              zIndex: 1000,
              backdropFilter: "blur(2px)",
            }}
            onClick={() => setIsExamModalOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              style={{
                background: "#fff",
                padding: "24px",
                borderRadius: "16px",
                width: "500px",
                maxWidth: "90%",
                boxShadow: "0 20px 40px rgba(0,0,0,0.15)",
                position: "relative",
              }}
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={() => setIsExamModalOpen(false)}
                style={{
                  position: "absolute",
                  right: "16px",
                  top: "16px",
                  background: "none",
                  border: "none",
                  fontSize: "24px",
                  cursor: "pointer",
                  color: "#666",
                  width: "32px",
                  height: "32px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  borderRadius: "50%",
                  transition: "background 0.2s ease",
                }}
                onMouseOver={(e) => (e.target.style.background = "#f5f5f5")}
                onMouseOut={(e) => (e.target.style.background = "none")}
              >
                ×
              </button>
              <h3
                style={{
                  color: "#140342",
                  fontWeight: "600",
                  marginBottom: "16px",
                  fontSize: "20px",
                  fontFamily: "Poppins, sans-serif",
                }}
              >
                Choose Your Exam Type
              </h3>
              <div
                style={{
                  padding: "16px",
                  borderRadius: "12px",
                  background: "#f8f7fc",
                  marginBottom: "12px",
                  border: "1px solid #e0e0e0",
                }}
              >
                <p
                  style={{
                    color: "#140342",
                    marginBottom: "0",
                    fontSize: "14px",
                  }}
                >
                  <strong>PTE Academic / UKVI:</strong> Used for global
                  university applications, various Australian and New Zealand
                  visa applications.
                </p>
              </div>
              <div
                style={{
                  padding: "16px",
                  borderRadius: "12px",
                  background: "#f8f7fc",
                  marginBottom: "20px",
                  border: "1px solid #e0e0e0",
                }}
              >
                <p
                  style={{
                    color: "#140342",
                    marginBottom: "0",
                    fontSize: "14px",
                  }}
                >
                  <strong>PTE Core:</strong> Used for Canadian immigration or
                  work visa applications.
                </p>
              </div>
              <div
                style={{
                  display: "flex",
                  gap: "12px",
                  justifyContent: "center",
                }}
              >
                <button
                  onClick={() => handleExamTypeChange("PTE Academic")}
                  style={{
                    background: "#140342",
                    color: "#fff",
                    padding: "12px 24px",
                    border: "none",
                    borderRadius: "8px",
                    cursor: "pointer",
                    fontWeight: "500",
                    fontSize: "14px",
                    fontFamily: "Poppins, sans-serif",
                    boxShadow: "0 4px 12px rgba(20, 3, 66, 0.2)",
                    transition: "all 0.2s ease",
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.background = "#1d0659";
                    e.currentTarget.style.transform = "translateY(-1px)";
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.background = "#140342";
                    e.currentTarget.style.transform = "translateY(0)";
                  }}
                >
                  PTE Academic / UKVI
                </button>
                <button
                  onClick={() => handleExamTypeChange("PTE Core")}
                  style={{
                    background: "#140342",
                    color: "#fff",
                    padding: "12px 24px",
                    border: "none",
                    borderRadius: "8px",
                    cursor: "pointer",
                    fontWeight: "500",
                    fontSize: "14px",
                    fontFamily: "Poppins, sans-serif",
                    boxShadow: "0 4px 12px rgba(20, 3, 66, 0.2)",
                    transition: "all 0.2s ease",
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.background = "#1d0659";
                    e.currentTarget.style.transform = "translateY(-1px)";
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.background = "#140342";
                    e.currentTarget.style.transform = "translateY(0)";
                  }}
                >
                  PTE Core
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Question Modal */}
      {isModalOpen && (
        <SubCategoryModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          data={subCategoryData}
        />
      )}

      {/* Sidebar */}
      <AnimatePresence>
        {isSidebarOpen && (
          <>
            <motion.div
              className="sidebar-overlay"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              style={{
                position: "fixed",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: "rgba(0,0,0,0.5)",
                zIndex: 999,
                backdropFilter: "blur(2px)",
              }}
              onClick={() => setIsSidebarOpen(false)}
            />
            <motion.div
              className="sidebar"
              initial={{ x: -400 }}
              animate={{ x: 0 }}
              exit={{ x: -400 }}
              transition={{ type: "spring", damping: 25, stiffness: 200 }}
              style={{
                position: "fixed",
                top: 0,
                left: 0,
                width: "380px",
                height: "100vh",
                background: "#fff",
                zIndex: 1000,
                boxShadow: "4px 0 20px rgba(0,0,0,0.1)",
                display: "flex",
                flexDirection: "column",
              }}
            >
              {/* Sidebar Header */}
              <div
                style={{
                  padding: "24px",
                  borderBottom: "1px solid #E5E7EB",
                  background:
                    "linear-gradient(135deg, #140342 0%, #1d0659 100%)",
                  color: "#fff",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <div>
                    <h3
                      style={{
                        margin: 0,
                        fontSize: "18px",
                        fontWeight: "600",
                        fontFamily: "Poppins, sans-serif",
                      }}
                    >
                      {activeCategory?.name.toUpperCase()}{" "}
                      {activeCategory?.icon}
                    </h3>
                    <p
                      style={{
                        margin: "4px 0 0 0",
                        fontSize: "14px",
                        opacity: 0.8,
                      }}
                    >
                      {selectedTab}
                    </p>
                  </div>
                  <button
                    onClick={() => setIsSidebarOpen(false)}
                    style={{
                      background: "rgba(255,255,255,0.1)",
                      border: "none",
                      color: "#fff",
                      width: "32px",
                      height: "32px",
                      borderRadius: "50%",
                      cursor: "pointer",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      fontSize: "18px",
                      transition: "background 0.2s ease",
                    }}
                    onMouseOver={(e) =>
                      (e.target.style.background = "rgba(255,255,255,0.2)")
                    }
                    onMouseOut={(e) =>
                      (e.target.style.background = "rgba(255,255,255,0.1)")
                    }
                  >
                    ×
                  </button>
                </div>
              </div>

              {/* Sidebar Content */}
              <div style={{ flex: 1, padding: "16px", overflowY: "auto" }}>
                {activeCategory &&
                  getFilteredQuestions(activeCategory).map(
                    (question, index) => (
                      <motion.div
                        key={question.categoryId}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        style={{
                          padding: "16px",
                          marginBottom: "12px",
                          border: "1px solid #E5E7EB",
                          borderRadius: "12px",
                          cursor: "pointer",
                          transition: "all 0.2s ease",
                          background: "#fff",
                        }}
                        whileHover={{
                          scale: 1.02,
                          boxShadow: "0 8px 25px rgba(20, 3, 66, 0.1)",
                          borderColor: "#140342",
                        }}
                        onClick={() =>
                          handleSubCategoryOpen(question.categoryId)
                        }
                      >
                        <div
                          style={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                          }}
                        >
                          <h4
                            style={{
                              margin: 0,
                              fontSize: "14px",
                              fontWeight: "500",
                              color: "#140342",
                              fontFamily: "Poppins, sans-serif",
                            }}
                          >
                            {question.name}
                          </h4>
                          {question.isAiBased && (
                            <span
                              style={{
                                background: "rgba(20, 3, 66, 0.1)",
                                color: "#140342",
                                padding: "4px 8px",
                                borderRadius: "6px",
                                fontSize: "10px",
                                fontWeight: "600",
                              }}
                            >
                              AI Score
                            </span>
                          )}
                        </div>
                      </motion.div>
                    )
                  )}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      <div className="content-wrapper js-content-wrapper overflow-hidden">
        {/* Hero Section */}
        <section
          style={{
            background: "linear-gradient(135deg, #140342 0%, #1d0659 100%)",
            padding: "120px 0 80px 0",
            position: "relative",
            overflow: "hidden",
          }}
        >
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="row justify-center text-center"
            >
              <div className="col-lg-8">
                <h1
                  style={{
                    color: "#fff",
                    fontSize: "clamp(32px, 5vw, 48px)",
                    fontWeight: "700",
                    marginBottom: "20px",
                    fontFamily: "Poppins, sans-serif",
                    lineHeight: "1.2",
                  }}
                >
                  PTE Practice Platform
                </h1>
                <p
                  style={{
                    color: "rgba(255,255,255,0.8)",
                    fontSize: "18px",
                    marginBottom: "30px",
                    maxWidth: "600px",
                    margin: "0 auto 30px auto",
                    lineHeight: "1.6",
                  }}
                >
                  Master your PTE skills with our comprehensive practice
                  questions and AI-powered scoring system.
                </p>

                {/* Exam Type Selector */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  style={{
                    display: "inline-flex",
                    background: "rgba(255,255,255,0.1)",
                    borderRadius: "12px",
                    padding: "8px",
                    marginBottom: "40px",
                    backdropFilter: "blur(10px)",
                    border: "1px solid rgba(255,255,255,0.2)",
                  }}
                >
                  <button
                    onClick={() => setIsExamModalOpen(true)}
                    style={{
                      background: "rgba(255,255,255,0.9)",
                      color: "#140342",
                      border: "none",
                      padding: "12px 24px",
                      borderRadius: "8px",
                      fontSize: "14px",
                      fontWeight: "600",
                      cursor: "pointer",
                      transition: "all 0.2s ease",
                      fontFamily: "Poppins, sans-serif",
                    }}
                    onMouseOver={(e) => {
                      e.target.style.background = "#fff";
                      e.target.style.transform = "translateY(-1px)";
                    }}
                    onMouseOut={(e) => {
                      e.target.style.background = "rgba(255,255,255,0.9)";
                      e.target.style.transform = "translateY(0)";
                    }}
                  >
                    {selectedTab} ⚙️
                  </button>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Categories Grid */}
        <section style={{ padding: "80px 0", background: "#f8f9fa" }}>
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="row justify-center text-center mb-60"
            >
              <div className="col-lg-8">
                <h2
                  style={{
                    color: "#140342",
                    fontSize: "clamp(28px, 4vw, 36px)",
                    fontWeight: "700",
                    marginBottom: "16px",
                    fontFamily: "Poppins, sans-serif",
                  }}
                >
                  Choose Your Practice Category
                </h2>
                <p
                  style={{
                    color: "#666",
                    fontSize: "16px",
                    maxWidth: "500px",
                    margin: "0 auto",
                    lineHeight: "1.6",
                  }}
                >
                  Select a category to start practicing questions tailored for
                  your exam type.
                </p>
              </div>
            </motion.div>

            <div className="row justify-center">
              {categories.map((category, index) => {
                const filteredQuestions = getFilteredQuestions(category);
                return (
                  <motion.div
                    key={category.name}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.6 }}
                    className="col-lg-6 col-md-6 mb-30"
                  >
                    <motion.div
                      style={{
                        background: "#fff",
                        borderRadius: "20px",
                        padding: "32px",
                        height: "100%",
                        cursor: "pointer",
                        border: `2px solid ${category.borderColor}20`,
                        position: "relative",
                        overflow: "hidden",
                      }}
                      whileHover={{
                        y: -8,
                        boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
                        borderColor: category.borderColor,
                      }}
                      onClick={() => handleCategoryClick(category)}
                    >
                      {/* Background Pattern */}
                      <div
                        style={{
                          position: "absolute",
                          top: 0,
                          right: 0,
                          width: "100px",
                          height: "100px",
                          background: category.color,
                          borderRadius: "50%",
                          transform: "translate(30px, -30px)",
                          opacity: 0.3,
                        }}
                      />

                      <div style={{ position: "relative", zIndex: 2 }}>
                        <div
                          style={{
                            fontSize: "48px",
                            marginBottom: "16px",
                            display: "block",
                          }}
                        >
                          {category.icon}
                        </div>
                        <h3
                          style={{
                            color: "#140342",
                            fontSize: "24px",
                            fontWeight: "600",
                            marginBottom: "12px",
                            fontFamily: "Poppins, sans-serif",
                            textTransform: "uppercase",
                          }}
                        >
                          {category.name}
                        </h3>
                        <p
                          style={{
                            color: "#666",
                            fontSize: "14px",
                            marginBottom: "20px",
                            lineHeight: "1.5",
                          }}
                        >
                          {filteredQuestions.length} questions available for{" "}
                          {selectedTab}
                        </p>

                        {/* Question Types Preview */}
                        <div style={{ marginBottom: "20px" }}>
                          {filteredQuestions
                            .slice(0, 3)
                            .map((question, idx) => (
                              <div
                                key={idx}
                                style={{
                                  fontSize: "12px",
                                  color: "#888",
                                  marginBottom: "4px",
                                  display: "flex",
                                  alignItems: "center",
                                  gap: "8px",
                                }}
                              >
                                <span
                                  style={{
                                    width: "4px",
                                    height: "4px",
                                    background: category.borderColor,
                                    borderRadius: "50%",
                                  }}
                                />
                                {question.name}
                                {question.isAiBased && (
                                  <span
                                    style={{
                                      background: "rgba(20, 3, 66, 0.1)",
                                      color: "#140342",
                                      padding: "2px 6px",
                                      borderRadius: "4px",
                                      fontSize: "10px",
                                      fontWeight: "600",
                                    }}
                                  >
                                    AI
                                  </span>
                                )}
                              </div>
                            ))}
                          {filteredQuestions.length > 3 && (
                            <div
                              style={{
                                fontSize: "12px",
                                color: "#888",
                                fontStyle: "italic",
                              }}
                            >
                              +{filteredQuestions.length - 3} more questions
                            </div>
                          )}
                        </div>

                        <motion.div
                          style={{
                            background: category.borderColor,
                            color: "#fff",
                            padding: "12px 24px",
                            borderRadius: "8px",
                            fontSize: "14px",
                            fontWeight: "600",
                            textAlign: "center",
                            fontFamily: "Poppins, sans-serif",
                          }}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          Start Practice →
                        </motion.div>
                      </div>
                    </motion.div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </section>

        <FooterOne />
      </div>
    </>
  );
}
