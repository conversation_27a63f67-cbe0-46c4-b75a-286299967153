import { useState } from "react";
import Header from "@/components/layout/headers/Header";
import HomeHero from "@/components/homes/heros/HomeHero";
import Categories from "@/components/homes/categories/Why-us";
import Courses from "@/components/homes/courses/Top-experience";
import TestimonialsOne from "@/components/common/WallandVoice";
import Mission from "@/components/common/Mission";
import Instructors from "@/components/common/Join&FAQ";

import FooterOne from "@/components/layout/footers/FooterOne";
import Preloader from "@/components/common/Preloader";
import MetaComponent from "@/components/common/MetaComponent";
import SubCategoryModal from "@/components/SubCategoryModal";

const metadata = {
  title: "Deep Insight Academy",
  description:
    "Elevate your e-learning content with Deep Insight Academy, the most impressive LMS template for online courses, education and LMS platforms.",
};

export default function HomePage1() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [subCategoryData, setSubCategoryData] = useState([]);

  const handleSubCategoryOpen = (data) => {
    setSubCategoryData(data);
    setIsModalOpen(true);
  };

  return (
    <>
      <Preloader />
      <MetaComponent meta={metadata} />
      <Header onSubCategoryOpen={handleSubCategoryOpen} />

      {isModalOpen && (
        <SubCategoryModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          data={subCategoryData}
        />
      )}

      <div className="content-wrapper js-content-wrapper overflow-hidden">
        <HomeHero />
        <Categories />
        <Courses />
        <Mission />
        <TestimonialsOne />
        {/* <FeaturesOne /> */}
        {/* <WhyCourse /> */}
        <Instructors />
        {/* <GetApp />
        <Blog /> */}
        {/* <Join /> */}
        <FooterOne />
      </div>
    </>
  );
}
