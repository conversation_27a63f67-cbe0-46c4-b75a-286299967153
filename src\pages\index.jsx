import React from "react";
import Wrapper from "./Wrapper";
import HomePage1 from "./homes/home-1";
import MetaComponent from "@/components/common/MetaComponent";

const metadata = {
  title:
    "Deep Insight Academy",
  description:
    "Elevate your e-learning content with Deep Insight Academy, the most impressive LMS template for online courses, education and LMS platforms.",
};


export default function index() {
  const logged = localStorage.getItem("isAuthenticated")
  console.log(logged, "...logged")
  if (logged) {
    history.pushState(null, null, location.href);
    window.onpopstate = function (event) {
      history.go(1);
    };
  }

  return (
    <Wrapper>
      <MetaComponent meta={metadata} />
      <HomePage1 />
    </Wrapper>
  );
}
