import { useEffect, useState } from "react";
import axios from "axios";
import { useAuth } from "../../components/others/AuthContext";
import PropTypes from "prop-types";
import { server } from "../../api/services/server";

const FEATURES = [
  "Access to PTE practice questions",
  "Ability to give mock tests",
  "Basic speaking analysis",
  "Limited writing feedback",
  "Access to study materials",
  "Everything in Basic",
  "Unlimited PTE practice questions",
  "Advanced speaking analysis",
  "Detailed writing feedback",
  "Progress tracking",
  "Priority support",
  "Everything in Standard",
  "Personalized study plan",
  "One-on-one coaching session",
  "Advanced analytics dashboard",
  "Unlimited mock tests",
  "Expert feedback on all sections",
  "24/7 support",
];

const API = `${server.uri}membership-plans`;

function PlanForm({ initial, onSubmit, onCancel, loading }) {
  const [planName, setPlanName] = useState(initial?.planName || "");
  const [durationInDays, setDurationInDays] = useState(
    initial?.durationInDays || ""
  );
  const [price, setPrice] = useState(initial?.price || "");
  const [status, setStatus] = useState(initial?.status || "active");

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit({
      planName,
      durationInDays,
      price: Number(price),
      status,
    });
  };

  return (
    <form
      onSubmit={handleSubmit}
      style={{
        background: "#f4f0ff",
        padding: 20,
        borderRadius: 10,
        marginBottom: 24,
      }}
    >
      <div style={{ marginBottom: 12 }}>
        <label>
          Plan Name:{" "}
          <input
            value={planName}
            onChange={(e) => setPlanName(e.target.value)}
            required
          />
        </label>
      </div>
      <div style={{ marginBottom: 12 }}>
        <label>
          Duration (days):{" "}
          <input
            value={durationInDays}
            onChange={(e) => setDurationInDays(e.target.value)}
            required
          />
        </label>
      </div>
      <div style={{ marginBottom: 12 }}>
        <label>
          Price ($):{" "}
          <input
            type="number"
            value={price}
            onChange={(e) => setPrice(e.target.value)}
            required
            min="0"
          />
        </label>
      </div>
      <div style={{ marginBottom: 12 }}>
        <label>
          Status:
          <select value={status} onChange={(e) => setStatus(e.target.value)}>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </label>
      </div>

      <div style={{ marginTop: 16, display: "flex", gap: 8 }}>
        <button
          type="submit"
          disabled={loading}
          style={{
            background: "#6a5af9",
            color: "#fff",
            border: "none",
            borderRadius: 6,
            padding: "8px 20px",
            fontWeight: 600,
            cursor: loading ? "not-allowed" : "pointer",
          }}
        >
          {loading ? "Saving..." : "Save"}
        </button>
        {onCancel && (
          <button
            type="button"
            onClick={onCancel}
            style={{
              background: "#fff",
              color: "#6a5af9",
              border: "1px solid #eaeaea",
              borderRadius: 6,
              padding: "8px 20px",
              fontWeight: 600,
              cursor: "pointer",
            }}
          >
            Cancel
          </button>
        )}
      </div>
    </form>
  );
}

PlanForm.propTypes = {
  initial: PropTypes.shape({
    planName: PropTypes.string,
    durationInDays: PropTypes.string,
    price: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    status: PropTypes.string,
  }),
  onSubmit: PropTypes.func.isRequired,
  onCancel: PropTypes.func,
  loading: PropTypes.bool,
};

export default function ManagePlans() {
  const { user } = useAuth();
  const userId = user?.id;
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [creating, setCreating] = useState(false);
  const [editing, setEditing] = useState(null);
  const [saving, setSaving] = useState(false);

  const fetchPlans = async () => {
    setLoading(true);
    setError("");
    try {
      const res = await axios.get(API);
      setPlans(res.data);
    } catch (e) {
      setError("Failed to fetch plans");
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchPlans();
  }, []);

  const handleCreate = async (data) => {
    setSaving(true);
    setError("");
    try {
      await axios.post(API, { ...data, userId });
      setCreating(false);
      fetchPlans();
    } catch (e) {
      setError("Failed to create plan");
    }
    setSaving(false);
  };

  const handleUpdate = async (id, data) => {
    setSaving(true);
    setError("");
    try {
      await axios.put(`${API}/${id}`, data);
      setEditing(null);
      fetchPlans();
    } catch (e) {
      setError("Failed to update plan");
    }
    setSaving(false);
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this plan?")) return;
    setSaving(true);
    setError("");
    try {
      await axios.delete(`${API}/${id}`);
      fetchPlans();
    } catch (e) {
      setError("Failed to delete plan");
    }
    setSaving(false);
  };

  return (
    <div
      style={{
        maxWidth: 900,
        margin: "40px auto",
        padding: 24,
        background: "#fff",
        borderRadius: 12,
        boxShadow: "0 4px 24px rgba(20,3,66,0.08)",
      }}
    >
      <h2 style={{ fontWeight: 800, color: "#140342", marginBottom: 24 }}>
        Manage Membership Plans
      </h2>
      {error && (
        <div style={{ color: "#D92D20", marginBottom: 16 }}>{error}</div>
      )}
      {creating ? (
        <PlanForm
          onSubmit={handleCreate}
          onCancel={() => setCreating(false)}
          loading={saving}
        />
      ) : (
        <button
          onClick={() => setCreating(true)}
          style={{
            background: "#6a5af9",
            color: "#fff",
            border: "none",
            borderRadius: 6,
            padding: "10px 28px",
            fontWeight: 700,
            marginBottom: 24,
            cursor: "pointer",
          }}
        >
          + Create New Plan
        </button>
      )}
      {loading ? (
        <div>Loading plans...</div>
      ) : plans.length === 0 ? (
        <div>No plans found.</div>
      ) : (
        <table
          style={{ width: "100%", borderCollapse: "collapse", marginTop: 12 }}
        >
          <thead>
            <tr style={{ background: "#f4f0ff" }}>
              <th style={{ padding: 8, border: "1px solid #eaeaea" }}>Name</th>
              <th style={{ padding: 8, border: "1px solid #eaeaea" }}>
                Duration
              </th>
              <th style={{ padding: 8, border: "1px solid #eaeaea" }}>Price</th>
              <th style={{ padding: 8, border: "1px solid #eaeaea" }}>
                Status
              </th>

              <th style={{ padding: 8, border: "1px solid #eaeaea" }}>
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {plans.map((plan) => (
              <tr
                key={plan.membershipId}
                style={{ borderBottom: "1px solid #eaeaea" }}
              >
                <td style={{ padding: 8 }}>{plan.planName}</td>
                <td style={{ padding: 8 }}>{plan.durationInDays}</td>
                <td style={{ padding: 8 }}>${plan.price}</td>
                <td style={{ padding: 8 }}>{plan.status}</td>

                <td style={{ padding: 8 }}>
                  <button
                    onClick={() => setEditing(plan)}
                    style={{
                      background: "#fff",
                      color: "#6a5af9",
                      border: "1px solid #eaeaea",
                      borderRadius: 6,
                      padding: "6px 16px",
                      fontWeight: 600,
                      marginRight: 8,
                      cursor: "pointer",
                    }}
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDelete(plan.membershipId)}
                    style={{
                      background: "#fff",
                      color: "#D92D20",
                      border: "1px solid #eaeaea",
                      borderRadius: 6,
                      padding: "6px 16px",
                      fontWeight: 600,
                      cursor: "pointer",
                    }}
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
      {editing && (
        <div style={{ marginTop: 32 }}>
          <h3 style={{ fontWeight: 700, color: "#140342", marginBottom: 12 }}>
            Edit Plan
          </h3>
          <PlanForm
            initial={editing}
            onSubmit={(data) => handleUpdate(editing.membershipId, data)}
            onCancel={() => setEditing(null)}
            loading={saving}
          />
        </div>
      )}
    </div>
  );
}
