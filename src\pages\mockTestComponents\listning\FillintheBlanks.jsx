import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  Button,
  Typography,
  Box,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Divider,
} from "@mui/material";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import PauseIcon from "@mui/icons-material/Pause";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import VolumeUpIcon from "@mui/icons-material/VolumeUp";
import HearingIcon from "@mui/icons-material/Hearing";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";

const ListeningFillInTheBlanks = ({
  setAnswer,
  onNext,
  setGoNext,
  question,
  handleUploadAnswer,
  onAnswerSubmitted,
  mockTestId,
  attemptNumber,
}) => {
  const questionData = question;

  // State management
  const [answers, setAnswers] = useState({});
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isAudioPlaying, setIsAudioPlaying] = useState(false);
  const [audioCurrentTime, setAudioCurrentTime] = useState(0);
  const [audioDuration, setAudioDuration] = useState(0);
  const [hasAudioEnded, setHasAudioEnded] = useState(false);
  const [phase, setPhase] = useState("listening");

  // Refs
  const audioRef = useRef(null);

  // Reset state when question changes
  useEffect(() => {
    setAnswers({});
    setIsSubmitted(false);
    setAudioCurrentTime(0);
    setAudioDuration(0);
    setHasAudioEnded(false);
    setIsAudioPlaying(false);
    setPhase("listening");
  }, [questionData?.questionId]);

  // Set up next functionality
  useEffect(() => {
    setGoNext(() => handleNextQuestion);
  }, [setGoNext]);

  // Memoize audio control functions to prevent re-creation
  const handlePlayPause = useCallback(() => {
    if (audioRef.current) {
      if (isAudioPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
    }
  }, [isAudioPlaying]);

  const handleRestart = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.currentTime = 0;
      audioRef.current.play();
    }
  }, []);

  const handleAudioLoadedMetadata = useCallback(() => {
    if (audioRef.current) {
      setAudioDuration(audioRef.current.duration);
    }
  }, []);

  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current) {
      setAudioCurrentTime(audioRef.current.currentTime);
    }
  }, []);

  const handleAudioPlay = useCallback(() => {
    setIsAudioPlaying(true);
  }, []);

  const handleAudioPause = useCallback(() => {
    setIsAudioPlaying(false);
  }, []);

  const handleAudioEnded = useCallback(() => {
    setIsAudioPlaying(false);
    setHasAudioEnded(true);
    setPhase("writing");
  }, []);

  // Format time for display
  const formatTime = useCallback((seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secondsRemaining = Math.floor(seconds % 60);
    return `${minutes}:${secondsRemaining < 10 ? "0" : ""}${secondsRemaining}`;
  }, []);

  // Handle text input change
  const handleInputChange = useCallback(
    (position, value) => {
      setAnswers((prev) => {
        const newAnswers = { ...prev, [position]: value };

        // Update parent immediately
        const formattedAnswers = Object.entries(newAnswers)
          .filter(([, val]) => val && val.trim())
          .map(([pos, val]) => ({
            position: parseInt(pos),
            answer: val.trim(),
            optionText: val.trim(),
          }));

        const answerData = {
          mockTestId: mockTestId || questionData?.mocktestId,
          mocktestId: mockTestId || questionData?.mocktestId,
          questionId: questionData?.questionId,
          prompt: questionData?.prompt,
          section: questionData?.section,
          categoryId: questionData?.categoryId,
          userId: localStorage.getItem("userId"),
          useranswers: formattedAnswers,
          correctAnswer: questionData?.blanks,
          maxScoreIfCorrect: questionData?.maxScore,
          type: "listening：fill in the blanks",
          attemptNumber: attemptNumber,
          answers: newAnswers,
          media: questionData?.media,
        };

        setAnswer(answerData);
        return newAnswers;
      });

      if (phase === "listening") {
        setPhase("writing");
      }
    },
    [questionData, phase, mockTestId, attemptNumber, setAnswer]
  );

  // Handle question submission
  const handleSubmit = useCallback(async () => {
    if (isSubmitted) return;

    const formattedAnswers = questionData?.blanks?.map((blank) => ({
      position: blank.position,
      answer: answers[blank.position] || "",
      optionText: answers[blank.position] || "",
    }));

    const finalAnswerData = {
      mockTestId: mockTestId || questionData?.mocktestId,
      mocktestId: mockTestId || questionData?.mocktestId,
      questionId: questionData?.questionId,
      prompt: questionData?.prompt,
      section: questionData?.section,
      categoryId: questionData?.categoryId,
      userId: localStorage.getItem("userId"),
      useranswers: formattedAnswers,
      correctAnswer: questionData?.blanks,
      maxScoreIfCorrect: questionData?.maxScore,
      type: "listening：fill in the blanks",
      attemptNumber: attemptNumber,
      answer: JSON.stringify(answers),
      media: questionData?.media,
    };

    try {
      await handleUploadAnswer(finalAnswerData);
      setIsSubmitted(true);
      setPhase("submitted");
      if (onAnswerSubmitted) onAnswerSubmitted();
    } catch (error) {
      console.error("Error submitting answer:", error);
    }
  }, [
    isSubmitted,
    questionData,
    answers,
    mockTestId,
    attemptNumber,
    handleUploadAnswer,
    onAnswerSubmitted,
  ]);

  // Handle moving to next question
  const handleNextQuestion = useCallback(() => {
    if (!isSubmitted) {
      handleSubmit();
    }
    if (onNext) {
      onNext();
    }
  }, [isSubmitted, handleSubmit, onNext]);

  // Computed values
  const filledBlanksCount = Object.keys(answers).filter(
    (key) => answers[key] && answers[key].trim()
  ).length;
  const allBlanksFilled = filledBlanksCount === questionData?.blanks?.length;

  if (!question) {
    return <div>Loading...</div>;
  }

  // Render audio player
  const renderAudioPlayer = () => {
    if (!questionData?.media?.url) return null;

    return (
      <Paper
        elevation={2}
        sx={{
          p: { xs: 2, sm: 3 },
          mb: 3,
          borderRadius: 2,
          backgroundColor: "#f8f9fa",
          border: "1px solid #e6e8eb",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 500,
              display: "flex",
              alignItems: "center",
              color: "text.primary",
            }}
          >
            <HearingIcon sx={{ mr: 1, color: "primary.main" }} />
            Listen to the audio
          </Typography>

          <Chip
            icon={<VolumeUpIcon />}
            label={hasAudioEnded ? "Completed" : "Listening"}
            color={hasAudioEnded ? "success" : "primary"}
            size="small"
            variant={hasAudioEnded ? "filled" : "outlined"}
            sx={{ fontWeight: 500 }}
          />
        </Box>

        <Divider sx={{ mb: 2 }} />

        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            p: 2,
            backgroundColor: "white",
            borderRadius: 2,
            border: "1px solid #e0e0e0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              mb: 1.5,
              width: "100%",
              justifyContent: "space-between",
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <IconButton
                onClick={handlePlayPause}
                disabled={isSubmitted}
                color="primary"
                sx={{
                  mr: 1,
                  backgroundColor: "rgba(25, 118, 210, 0.08)",
                  "&:hover": {
                    backgroundColor: "rgba(25, 118, 210, 0.12)",
                  },
                  "&:disabled": {
                    backgroundColor: "#ccc",
                    color: "#666",
                  },
                }}
              >
                {isAudioPlaying ? <PauseIcon /> : <PlayArrowIcon />}
              </IconButton>

              <Tooltip title="Restart audio">
                <IconButton
                  onClick={handleRestart}
                  disabled={isSubmitted}
                  sx={{
                    color: "text.secondary",
                    "&:hover": {
                      color: "primary.main",
                    },
                    "&:disabled": {
                      color: "#ccc",
                    },
                  }}
                >
                  <RestartAltIcon />
                </IconButton>
              </Tooltip>
            </Box>

            <Typography variant="body2" color="text.secondary">
              {formatTime(audioCurrentTime)} / {formatTime(audioDuration)}
            </Typography>
          </Box>

          <Box
            sx={{
              width: "100%",
              height: "6px",
              backgroundColor: "#f0f0f0",
              borderRadius: "3px",
              position: "relative",
              overflow: "hidden",
            }}
          >
            <Box
              sx={{
                position: "absolute",
                height: "100%",
                width: `${(audioCurrentTime / audioDuration) * 100}%`,
                backgroundColor: "primary.main",
                borderRadius: "3px",
                transition: "width 0.1s linear",
              }}
            />
          </Box>

          <audio
            ref={audioRef}
            onLoadedMetadata={handleAudioLoadedMetadata}
            onTimeUpdate={handleTimeUpdate}
            onPlay={handleAudioPlay}
            onPause={handleAudioPause}
            onEnded={handleAudioEnded}
            autoPlay
            src={questionData?.media?.url}
            style={{ display: "none" }}
          />
        </Box>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            p: 1.5,
            mt: 2,
            backgroundColor: "rgba(25, 118, 210, 0.05)",
            borderRadius: 1,
          }}
        >
          <InfoOutlinedIcon color="primary" sx={{ mr: 1, fontSize: 20 }} />
          <Typography variant="body2" color="text.secondary">
            Listen carefully and type the missing words in the blanks below.
          </Typography>
        </Box>
      </Paper>
    );
  };

  // Render instructions box
  const renderInstructionsBox = () => {
    return (
      <Paper
        elevation={2}
        sx={{
          p: { xs: 2, sm: 3 },
          mb: 3,
          borderRadius: 2,
          backgroundColor: "#f8f9fa",
          border: "1px solid #e0e0e0",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            mb: 2,
            gap: 1,
          }}
        >
          <InfoOutlinedIcon color="primary" />
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: "text.primary",
            }}
          >
            Instructions
          </Typography>
        </Box>

        <Divider sx={{ mb: 2 }} />

        <Box
          sx={{
            p: 2,
            backgroundColor: "#e3f2fd",
            borderRadius: 1,
            border: "1px solid #1976d2",
          }}
        >
          <Typography
            variant="body1"
            color="primary"
            sx={{
              fontWeight: 500,
              display: "flex",
              alignItems: "center",
              gap: 1,
              mb: 1,
            }}
          >
            <HearingIcon fontSize="small" />
            Listen to the audio and type the missing words in the blanks below.
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ ml: 3 }}>
            Type your answers directly into the text fields where you see the
            blanks.
          </Typography>
        </Box>
      </Paper>
    );
  };

  // Render content with input fields (similar to practice component)
  const renderContentWithBlanks = () => {
    if (!questionData?.content) return null;

    const parts = questionData.content.split("_____");
    return parts.map((part, idx) => (
      <span key={idx} style={{ fontSize: "18px", lineHeight: "2.5" }}>
        {part}
        {idx < (questionData?.blanks?.length || 0) && (
          <span
            style={{
              display: "inline-flex",
              alignItems: "center",
              justifyContent: "center",
              minWidth: "120px",
              height: "45px",
              border: `2px ${
                answers[questionData.blanks[idx]?.position] ? "solid" : "dashed"
              } ${
                answers[questionData.blanks[idx]?.position] ? "#140342" : "#ccc"
              }`,
              borderRadius: "8px",
              margin: "0 6px",
              padding: "8px 12px",
              backgroundColor: answers[questionData.blanks[idx]?.position]
                ? "#f8f9ff"
                : "white",
              transition: "all 0.3s ease",
              verticalAlign: "middle",
              boxShadow: answers[questionData.blanks[idx]?.position]
                ? "0 2px 8px rgba(20, 3, 66, 0.15)"
                : "0 1px 3px rgba(0,0,0,0.1)",
              position: "relative",
            }}
          >
            <input
              type="text"
              value={answers[questionData.blanks[idx]?.position] || ""}
              onChange={(e) =>
                handleInputChange(
                  questionData.blanks[idx]?.position,
                  e.target.value
                )
              }
              disabled={isSubmitted}
              placeholder=""
              style={{
                width: "100%",
                height: "100%",
                border: "none",
                background: "transparent",
                fontSize: "16px",
                fontWeight: "700",
                fontStyle: "italic",
                textAlign: "center",
                color: "#140342",
                outline: "none",
                textDecoration: answers[questionData.blanks[idx]?.position]
                  ? "underline"
                  : "none",
                textDecorationColor: "#140342",
                textDecorationStyle: "solid",
                textUnderlineOffset: "2px",
              }}
            />
          </span>
        )}
      </span>
    ));
  };

  return (
    <Box sx={{ maxWidth: "1000px", mx: "auto", p: { xs: 2, sm: 3 } }}>
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          mb: 3,
          pb: 2,
          borderBottom: "2px solid #e0e0e0",
        }}
      >
        <Typography
          variant="h4"
          sx={{
            mb: 1,
            fontWeight: 700,
            color: "#1976d2",
            textAlign: "center",
          }}
        >
          Listening: Fill in the Blanks
        </Typography>

        <Typography
          variant="subtitle1"
          color="text.secondary"
          sx={{
            fontWeight: 500,
            maxWidth: "600px",
            textAlign: "center",
            fontSize: "1.1rem",
          }}
        >
          {questionData?.prompt}
        </Typography>
      </Box>

      {/* Phase status indicator */}
      <Box sx={{ display: "flex", justifyContent: "center", mb: 3 }}>
        <Chip
          icon={
            phase === "submitted" ? (
              <CheckCircleOutlineIcon />
            ) : phase === "writing" ? (
              <CheckCircleOutlineIcon />
            ) : (
              <HearingIcon />
            )
          }
          label={
            phase === "submitted"
              ? "Answer Submitted"
              : phase === "writing"
              ? "Fill the blanks using options below"
              : hasAudioEnded
              ? "Audio completed - Now fill the blanks"
              : "Listening to audio..."
          }
          color={
            phase === "submitted"
              ? "success"
              : phase === "writing"
              ? "secondary"
              : hasAudioEnded
              ? "success"
              : "primary"
          }
          size="large"
          sx={{ fontWeight: 600, fontSize: "1rem", py: 2 }}
        />
      </Box>

      {/* Audio Player */}
      {renderAudioPlayer()}

      {/* Instructions Box */}
      {renderInstructionsBox()}

      {/* Main Content with Blanks */}
      <Paper
        elevation={2}
        sx={{
          p: { xs: 3, sm: 4 },
          mb: 3,
          borderRadius: 2,
          backgroundColor: "white",
          border: "1px solid #e0e0e0",
        }}
      >
        <div style={{ marginBottom: "30px", lineHeight: "2" }}>
          {renderContentWithBlanks()}
        </div>

        <Box
          sx={{
            mt: 4,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            flexWrap: "wrap",
            gap: 2,
            p: 2,
            backgroundColor: "#f8f9fa",
            borderRadius: 1,
          }}
        >
          <Typography
            variant="body1"
            color="text.secondary"
            sx={{ fontWeight: 500 }}
          >
            Progress: {filledBlanksCount} of {questionData?.blanks?.length || 0}{" "}
            blanks filled
          </Typography>

          {allBlanksFilled && !isSubmitted && (
            <Chip
              icon={<CheckCircleOutlineIcon />}
              label="Ready to submit!"
              color="success"
              size="medium"
              sx={{ fontWeight: 600 }}
            />
          )}
        </Box>
      </Paper>

      {/* Submit Button */}
      {!isSubmitted && (
        <Box sx={{ display: "flex", justifyContent: "center", gap: 3, mt: 4 }}>
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={!allBlanksFilled}
            size="large"
            sx={{
              backgroundColor: "#140342",
              "&:hover": { backgroundColor: "#281C63" },
              "&:disabled": { backgroundColor: "#ccc" },
              px: 6,
              py: 2,
              fontSize: "1.1rem",
              fontWeight: 700,
              borderRadius: 2,
            }}
          >
            Submit Answer
          </Button>

          <Button
            variant="outlined"
            onClick={handleNextQuestion}
            size="large"
            sx={{
              borderColor: "#140342",
              color: "#140342",
              "&:hover": {
                borderColor: "#281C63",
                backgroundColor: "rgba(20, 3, 66, 0.04)",
              },
              px: 4,
              py: 2,
              fontSize: "1.1rem",
              fontWeight: 600,
              borderRadius: 2,
            }}
          >
            Skip Question
          </Button>
        </Box>
      )}

      {/* Completion Message */}
      {isSubmitted && (
        <Paper
          elevation={3}
          sx={{
            mt: 4,
            p: 4,
            borderRadius: 2,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            backgroundColor: "#f0f7ff",
            border: "2px solid #4caf50",
          }}
        >
          <CheckCircleOutlineIcon
            sx={{ color: "success.main", fontSize: 48, mb: 2 }}
          />
          <Typography
            variant="h5"
            sx={{ fontWeight: 600, color: "success.dark", mb: 1 }}
          >
            Answer Submitted Successfully!
          </Typography>
          <Typography
            variant="body1"
            color="text.secondary"
            sx={{ textAlign: "center", maxWidth: "400px" }}
          >
            Your answers have been saved and will be scored. You can now proceed
            to the next question.
          </Typography>
        </Paper>
      )}
    </Box>
  );
};

export default ListeningFillInTheBlanks;
