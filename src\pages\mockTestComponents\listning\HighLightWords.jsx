import React, { useEffect, useState, useRef } from "react";
import {
  Typography,
  Box,
  Paper,
  Divider,
  Chip,
  IconButton,
  Tooltip,
} from "@mui/material";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import VolumeUpIcon from "@mui/icons-material/VolumeUp";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import PauseIcon from "@mui/icons-material/Pause";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import HearingIcon from "@mui/icons-material/Hearing";
import FormatQuoteIcon from "@mui/icons-material/FormatQuote";

const HighlightIncorrectWords = ({
  setAnswer,
  onNext,
  setGoNext,
  question,
  handleUploadAnswer,
}) => {
  const [selectedWords, setSelectedWords] = useState([]);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioDuration, setAudioDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);

  const audioRef = useRef(null);

  // Reset state when question changes
  useEffect(() => {
    setSelectedWords([]);
    setCurrentTime(0);
    setIsPlaying(false);
  }, [question?.questionId]);

  useEffect(() => {
    setGoNext();
  }, []);

  // Handle word click in the paragraph
  const handleWordClick = (wordId) => {
    // Allow clicking during audio playback - no restriction needed

    const newSelectedWords = selectedWords.includes(wordId)
      ? selectedWords.filter((selectedWord) => selectedWord !== wordId)
      : [...selectedWords, wordId];

    setSelectedWords(newSelectedWords);

    // Format answer data with correct structure for new API
    // Convert wordIds back to actual words for the API
    const selectedWordTexts = newSelectedWords.map((wordId) => {
      // Extract word text from wordId (format: "word_index")
      const parts = wordId.split("_");
      return parts[0]; // Get the word part before the underscore
    });

    const answerData = {
      mockTestId: question?.mocktestId,
      questionId: question?.questionId,
      section: question?.section || question?.category?.section,
      prompt: question?.prompt,
      categoryId: question?.categoryId,
      userId: localStorage.getItem("userId"),
      useranswers: selectedWordTexts.map((word) => ({
        word: word, // Use 'word' instead of 'optionText' for highlight-incorrect-words
        optionText: word, // Keep both for backward compatibility
      })),
      correctAnswer: {
        incorrectWords: question?.incorrectWords || [], // Use new API structure
      },
      maxScoreIfCorrect: question?.maxScore,
      type: question?.type || question?.category?.name,
    };

    setAnswer(answerData);
  };

  // Audio control functions
  const handlePlayPause = () => {
    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleRestart = () => {
    audioRef.current.currentTime = 0;
    audioRef.current.play();
    setIsPlaying(true);
  };

  const handleAudioLoadedMetadata = () => {
    setAudioDuration(audioRef.current.duration);
  };

  const handleTimeUpdate = () => {
    setCurrentTime(audioRef.current.currentTime);
  };

  const handleAudioEnd = () => {
    setIsPlaying(false);
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? "0" : ""}${secs}`;
  };

  // Count selected words
  const selectedCount = selectedWords.length;

  // Get the text to display - use incorrectTranscript from new API structure
  const getDisplayText = () => {
    return question?.incorrectTranscript || question?.prompt || "";
  };

  // Render the paragraph with clickable words
  const renderParagraph = () => {
    const displayText = getDisplayText();
    if (!displayText) return null;

    // Split the paragraph into words with spaces preserved
    const words = displayText.split(/(\s+)/);

    return (
      <Box
        sx={{
          lineHeight: 2,
          fontSize: "1.1rem",
          textAlign: "left",
        }}
      >
        {words.map((part, index) => {
          // Skip rendering for whitespace
          if (part.trim() === "") {
            return <React.Fragment key={index}>{part}</React.Fragment>;
          }

          // Clean word for comparison (remove punctuation)
          const cleanWord = part.replace(/[.,!?;:'"()]/g, "");

          // Only make actual words (not punctuation) clickable
          const isWord = /^[a-zA-Z0-9''-]+$/.test(cleanWord);

          // Check if this word should be highlightable (is in incorrectWords list)
          const incorrectWords = question?.incorrectWords || [];
          const isIncorrectWord = incorrectWords.some(
            (item) => item.incorrect === cleanWord || item.incorrect === part
          );

          // Create unique ID for this word instance
          const wordId = `${cleanWord}_${index}`;

          return isWord ? (
            <Box
              component="span"
              key={index}
              onClick={() => handleWordClick(wordId)}
              sx={{
                padding: "2px 4px",
                margin: "0 2px",
                borderRadius: "4px",
                cursor: "pointer",
                backgroundColor: selectedWords.includes(wordId)
                  ? "rgba(25, 118, 210, 0.12)"
                  : "rgba(0, 0, 0, 0.02)",
                border: "1px solid",
                borderColor: selectedWords.includes(wordId)
                  ? "primary.main"
                  : "rgba(0, 0, 0, 0.1)",
                color: selectedWords.includes(wordId)
                  ? "primary.dark"
                  : "inherit",
                fontWeight: selectedWords.includes(wordId) ? 600 : 400,
                transition: "all 0.2s ease",
                position: "relative",
                // Add subtle indication for words that are actually incorrect (for testing purposes)
                boxShadow: isIncorrectWord
                  ? "inset 0 0 0 1px rgba(244, 67, 54, 0.2)"
                  : "none",
                "&:hover": {
                  backgroundColor: selectedWords.includes(wordId)
                    ? "rgba(25, 118, 210, 0.2)"
                    : "rgba(0, 0, 0, 0.05)",
                },
              }}
            >
              {part}
            </Box>
          ) : (
            <React.Fragment key={index}>{part}</React.Fragment>
          );
        })}
      </Box>
    );
  };

  return (
    <Box
      sx={{
        maxWidth: "800px",
        mx: "auto",
        p: { xs: 2, sm: 3 },
        minHeight: "70vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Task header */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          mb: 3,
          pb: 2,
          borderBottom: "1px solid rgba(0,0,0,0.08)",
        }}
      >
        <Typography
          variant="h5"
          sx={{
            mb: 1,
            fontWeight: 600,
            color: "text.primary",
          }}
        >
          Highlight Incorrect Words
        </Typography>

        <Typography
          variant="subtitle1"
          color="text.secondary"
          sx={{ fontWeight: 500 }}
        >
          {question?.category?.description ||
            "Listen and identify the incorrect words"}
        </Typography>
      </Box>

      {/* Audio player card */}
      <Paper
        elevation={2}
        sx={{
          p: { xs: 2, sm: 3 },
          mb: 3,
          borderRadius: 2,
          backgroundColor: "#f8f9fa",
          border: "1px solid #e6e8eb",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 500,
              display: "flex",
              alignItems: "center",
              color: "text.primary",
            }}
          >
            <HearingIcon sx={{ mr: 1, color: "primary.main" }} />
            Listen to the audio
          </Typography>

          <Chip
            icon={<VolumeUpIcon />}
            label="Highlight while listening"
            color="success"
            size="small"
            variant="filled"
            sx={{ fontWeight: 500 }}
          />
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Custom audio player */}
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            p: 2,
            backgroundColor: "white",
            borderRadius: 2,
            border: "1px solid #e0e0e0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              mb: 1.5,
              width: "100%",
              justifyContent: "space-between",
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <IconButton
                onClick={handlePlayPause}
                color="primary"
                sx={{
                  mr: 1,
                  backgroundColor: "rgba(25, 118, 210, 0.08)",
                  "&:hover": {
                    backgroundColor: "rgba(25, 118, 210, 0.12)",
                  },
                }}
              >
                {isPlaying ? <PauseIcon /> : <PlayArrowIcon />}
              </IconButton>

              <Tooltip title="Restart audio">
                <IconButton
                  onClick={handleRestart}
                  sx={{
                    color: "text.secondary",
                    "&:hover": {
                      color: "primary.main",
                    },
                  }}
                >
                  <RestartAltIcon />
                </IconButton>
              </Tooltip>
            </Box>

            <Typography variant="body2" color="text.secondary">
              {formatTime(currentTime)} / {formatTime(audioDuration)}
            </Typography>
          </Box>

          {/* Progress bar */}
          <Box
            sx={{
              width: "100%",
              height: "6px",
              backgroundColor: "#f0f0f0",
              borderRadius: "3px",
              position: "relative",
              overflow: "hidden",
            }}
          >
            <Box
              sx={{
                position: "absolute",
                height: "100%",
                width: `${(currentTime / audioDuration) * 100}%`,
                backgroundColor: "primary.main",
                borderRadius: "3px",
                transition: "width 0.1s linear",
              }}
            />
          </Box>

          <audio
            ref={audioRef}
            key={question?.questionId}
            onEnded={handleAudioEnd}
            onLoadedMetadata={handleAudioLoadedMetadata}
            onTimeUpdate={handleTimeUpdate}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            autoPlay
            src={question?.media?.url}
            style={{ display: "none" }}
          />
        </Box>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            p: 1.5,
            mt: 2,
            backgroundColor: "rgba(25, 118, 210, 0.05)",
            borderRadius: 1,
          }}
        >
          <InfoOutlinedIcon color="primary" sx={{ mr: 1, fontSize: 20 }} />
          <Typography variant="body2" color="text.secondary">
            Listen to the audio and click on the words in the text that don't
            match what you hear. You can highlight words while listening.
          </Typography>
        </Box>
      </Paper>

      {/* Paragraph with highlightable words */}
      <Paper
        elevation={1}
        sx={{
          p: { xs: 2, sm: 3 },
          borderRadius: 2,
          backgroundColor: "white",
          flex: 1,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 500,
              display: "flex",
              alignItems: "center",
            }}
          >
            <FormatQuoteIcon sx={{ mr: 1 }} />
            Text Transcript
          </Typography>

          <Chip
            label={`${selectedCount} word${
              selectedCount !== 1 ? "s" : ""
            } highlighted`}
            color={selectedCount > 0 ? "primary" : "default"}
            size="small"
            variant={selectedCount > 0 ? "filled" : "outlined"}
            sx={{ fontWeight: 500 }}
          />
        </Box>

        <Divider sx={{ mb: 3 }} />

        {/* Show question instructions */}
        {question?.prompt && (
          <Box
            sx={{ mb: 2, p: 2, backgroundColor: "#f5f5f5", borderRadius: 1 }}
          >
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ fontStyle: "italic" }}
            >
              <strong>Instructions:</strong> {question.prompt}
            </Typography>
          </Box>
        )}

        {/* Paragraph content with highlightable words */}
        <Box
          sx={{
            p: 2,
            backgroundColor: "white",
            borderRadius: 2,
            border: "1px solid",
            borderColor: "#f0f0f0",
            transition: "all 0.3s ease",
            position: "relative",
          }}
        >
          {renderParagraph()}
        </Box>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            p: 1.5,
            mt: 2,
            backgroundColor: "rgba(0, 0, 0, 0.02)",
            borderRadius: 1,
          }}
        >
          <Typography variant="body2" color="text.secondary">
            Click on any word to highlight or unhighlight it. Only highlight
            words that don't match what you heard in the audio.
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default HighlightIncorrectWords;
