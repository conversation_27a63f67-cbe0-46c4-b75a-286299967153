import React, { useState, useEffect, useRef } from "react";
import {
  Typography,
  Box,
  Radio,
  RadioGroup,
  FormControlLabel,
  Paper,
  Divider,
  Chip,
  FormControl,
  IconButton,
  Tooltip,
} from "@mui/material";
import VolumeUpIcon from "@mui/icons-material/VolumeUp";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import PauseIcon from "@mui/icons-material/Pause";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import HearingIcon from "@mui/icons-material/Hearing";
import PropTypes from "prop-types";

const HighlightCorrectSummary = ({
  setAnswer,
  onNext,
  setGoNext,
  question,
}) => {
  const questionData = question;
  const [selectedOption, setSelectedOption] = useState("");
  const [onMediaEnd, setOnMediaEnd] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioDuration, setAudioDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);

  const audioRef = useRef(null);

  // Reset selected option when question changes
  useEffect(() => {
    setSelectedOption("");
    setOnMediaEnd(false);
    setCurrentTime(0);
    setIsPlaying(false);
  }, [question?.questionId]);

  useEffect(() => {
    setGoNext();
  }, [setGoNext]);

  // Handle radio button change
  const handleChange = (event) => {
    const selectedValue = event.target.value;
    setSelectedOption(selectedValue);

    // Prepare answer data for mocktest
    const answerData = {
      mockTestId: questionData?.mocktestId,
      questionId: questionData?.questionId,
      section: questionData?.category?.section,
      categoryId: questionData?.categoryId,
      userId: localStorage.getItem("userId"),
      prompt: questionData?.prompt,
      useranswers: [selectedValue],
      correctAnswer: questionData?.options,
      media: {
        url: questionData?.media?.url,
        type: "audio",
      },
      maxScoreIfCorrect: questionData?.maxScore,
      type: "highlight correct summary",
    };

    setAnswer(answerData);
  };

  // Audio control functions
  const handlePlayPause = () => {
    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleRestart = () => {
    audioRef.current.currentTime = 0;
    audioRef.current.play();
    setIsPlaying(true);
  };

  const handleAudioLoadedMetadata = () => {
    setAudioDuration(audioRef.current.duration);
     if (audioRef.current) {
       audioRef.current.playbackRate = 1.5;
     
     }
  };

  const handleTimeUpdate = () => {
    setCurrentTime(audioRef.current.currentTime);
  };

  const handleAudioEnded = () => {
    setOnMediaEnd(true);
    setIsPlaying(false);
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? "0" : ""}${secs}`;
  };

  // Get option letter (A, B, C, D)
  const getOptionLetter = (index) => {
    return String.fromCharCode(65 + index); // A, B, C, D...
  };

  // Has an option been selected
  const hasSelection = selectedOption !== "";

  return (
    <Box
      sx={{
        maxWidth: "800px",
        mx: "auto",
        p: { xs: 2, sm: 3 },
        minHeight: "70vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Task header */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          mb: 3,
          pb: 2,
          borderBottom: "1px solid rgba(0,0,0,0.08)",
        }}
      >
        <Typography
          variant="h5"
          sx={{
            mb: 1,
            fontWeight: 600,
            color: "text.primary",
          }}
        >
          Listening: Highlight Correct Summary
        </Typography>

        <Typography
          variant="subtitle1"
          color="text.secondary"
          sx={{ fontWeight: 500 }}
        >
          {questionData?.category?.description ||
            "Listen and select the summary that best represents the content"}
        </Typography>
      </Box>

      {/* Audio player card */}
      <Paper
        elevation={2}
        sx={{
          p: { xs: 2, sm: 3 },
          mb: 3,
          borderRadius: 2,
          backgroundColor: "#f8f9fa",
          border: "1px solid #e6e8eb",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 500,
              display: "flex",
              alignItems: "center",
              color: "text.primary",
            }}
          >
            <HearingIcon sx={{ mr: 1, color: "primary.main" }} />
            Listen to the audio
          </Typography>

          <Chip
            icon={<VolumeUpIcon />}
            label={onMediaEnd ? "Completed" : "Listening"}
            color={onMediaEnd ? "success" : "primary"}
            size="small"
            variant={onMediaEnd ? "filled" : "outlined"}
            sx={{ fontWeight: 500 }}
          />
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Custom audio player */}
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            p: 2,
            backgroundColor: "white",
            borderRadius: 2,
            border: "1px solid #e0e0e0",
          }}
        >
          <audio
            ref={audioRef}
            onLoadedMetadata={handleAudioLoadedMetadata}
            onTimeUpdate={handleTimeUpdate}
            onEnded={handleAudioEnded}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            style={{ display: "none" }}
           
          >
            <source src={questionData?.media?.url} type="audio/mpeg" />
            Your browser does not support the audio element.
          </audio>

          {/* Audio controls */}
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 2,
              mb: 2,
            }}
          >
            <Tooltip title={isPlaying ? "Pause" : "Play"}>
              <IconButton
                onClick={handlePlayPause}
                color="primary"
                size="large"
                sx={{
                  backgroundColor: "primary.main",
                  color: "white",
                  "&:hover": {
                    backgroundColor: "primary.dark",
                  },
                }}
              >
                {isPlaying ? <PauseIcon /> : <PlayArrowIcon />}
              </IconButton>
            </Tooltip>

            <Tooltip title="Restart">
              <IconButton onClick={handleRestart} color="primary">
                <RestartAltIcon />
              </IconButton>
            </Tooltip>
          </Box>

          {/* Audio progress */}
          <Box sx={{ width: "100%", textAlign: "center" }}>
            <Typography variant="body2" color="text.secondary">
              {formatTime(currentTime)} / {formatTime(audioDuration)}
            </Typography>
            <Box
              sx={{
                width: "100%",
                height: 4,
                backgroundColor: "#e0e0e0",
                borderRadius: 2,
                mt: 1,
                overflow: "hidden",
              }}
            >
              <Box
                sx={{
                  height: "100%",
                  backgroundColor: "primary.main",
                  width: `${(currentTime / audioDuration) * 100 || 0}%`,
                  transition: "width 0.1s ease",
                }}
              />
            </Box>
          </Box>
        </Box>
      </Paper>

      {/* Question prompt */}
      {/* {questionData?.prompt && (
        <Paper
          elevation={1}
          sx={{
            p: { xs: 2, sm: 3 },
            mb: 3,
            borderRadius: 2,
            backgroundColor: "#f0f7ff",
            border: "1px solid #2196f3",
          }}
        >
          <Typography
            variant="body1"
            sx={{
              fontWeight: 500,
              lineHeight: 1.6,
              color: "text.primary",
            }}
          >
            {questionData.prompt}
          </Typography>
        </Paper>
      )} */}

      {/* Answer options */}
      <Paper
        elevation={1}
        sx={{
          p: { xs: 2, sm: 3 },
          borderRadius: 2,
          backgroundColor: "white",
          flex: 1,
        }}
      >
        <Typography
          variant="subtitle1"
          color="text.secondary"
          sx={{ mb: 2, fontWeight: 500 }}
        >
          Select the best summary:
        </Typography>

        <Divider sx={{ mb: 2 }} />

        <FormControl component="fieldset" sx={{ width: "100%" }}>
          <RadioGroup
            value={selectedOption}
            onChange={handleChange}
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: 1,
            }}
          >
            {questionData?.options?.map((option, index) => (
              <Paper
                key={index}
                elevation={0}
                sx={{
                  border: "1px solid",
                  borderColor:
                    selectedOption === option.text ? "primary.main" : "#e0e0e0",
                  borderRadius: 1.5,
                  transition: "all 0.2s ease",
                  backgroundColor:
                    selectedOption === option.text
                      ? "rgba(25, 118, 210, 0.04)"
                      : "white",
                  "&:hover": {
                    backgroundColor:
                      selectedOption === option.text
                        ? "rgba(25, 118, 210, 0.08)"
                        : "rgba(0, 0, 0, 0.01)",
                    borderColor:
                      selectedOption === option.text
                        ? "primary.main"
                        : "#bdbdbd",
                  },
                }}
              >
                <FormControlLabel
                  value={option.text}
                  control={
                    <Radio
                      color="primary"
                      sx={{
                        "& .MuiSvgIcon-root": {
                          fontSize: 20,
                        },
                      }}
                    />
                  }
                  label={
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "flex-start",
                        py: 0.5,
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: 600,
                          minWidth: "24px",
                          color: "primary.main",
                          mr: 1,
                        }}
                      >
                        {getOptionLetter(index)}.
                      </Typography>
                      <Typography variant="body1" sx={{ lineHeight: 1.5 }}>
                        {option.text}
                      </Typography>
                    </Box>
                  }
                  sx={{
                    m: 0,
                    p: 2,
                    width: "100%",
                    alignItems: "flex-start",
                    "& .MuiFormControlLabel-label": {
                      width: "100%",
                      fontWeight: selectedOption === option.text ? 500 : 400,
                    },
                  }}
                />
              </Paper>
            ))}
          </RadioGroup>
        </FormControl>

        {/* Selection status */}
        <Box
          sx={{
            mt: 3,
            p: 2,
            backgroundColor: hasSelection ? "#e8f5e8" : "#fff3e0",
            borderRadius: 1,
            border: `1px solid ${hasSelection ? "#4caf50" : "#ff9800"}`,
          }}
        >
          <Typography
            variant="body2"
            sx={{
              color: hasSelection ? "#2e7d32" : "#f57c00",
              fontWeight: 500,
              textAlign: "center",
            }}
          >
            {hasSelection
              ? "✓ Answer selected"
              : "⚠ Please select an answer before proceeding"}
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

HighlightCorrectSummary.propTypes = {
  setAnswer: PropTypes.func.isRequired,
  onNext: PropTypes.func,
  setGoNext: PropTypes.func.isRequired,
  question: PropTypes.shape({
    questionId: PropTypes.string.isRequired,
    questionNumber: PropTypes.string,
    prompt: PropTypes.string,
    section: PropTypes.string,
    difficulty: PropTypes.string,
    media: PropTypes.shape({
      url: PropTypes.string,
    }),
    options: PropTypes.arrayOf(
      PropTypes.shape({
        text: PropTypes.string.isRequired,
        isCorrect: PropTypes.bool.isRequired,
      })
    ).isRequired,
    maxScore: PropTypes.number,
    categoryId: PropTypes.string,
    mocktestId: PropTypes.string,
    category: PropTypes.shape({
      section: PropTypes.string,
      name: PropTypes.string,
      description: PropTypes.string,
    }),
  }).isRequired,
};

export default HighlightCorrectSummary;
