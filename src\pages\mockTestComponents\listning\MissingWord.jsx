import React, { useEffect, useState, useRef } from "react";
import {
  Typography,
  Box,
  Radio,
  RadioGroup,
  FormControlLabel,
  Paper,
  Divider,
  Chip,
  FormControl,
  IconButton,
  Tooltip,
} from "@mui/material";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import VolumeUpIcon from "@mui/icons-material/VolumeUp";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import PauseIcon from "@mui/icons-material/Pause";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import HearingIcon from "@mui/icons-material/Hearing";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import TextFieldsIcon from "@mui/icons-material/TextFields";

const MissingWord = ({
  setAnswer,
  onNext,
  setGoNext,
  question,
  handleUploadAnswer,
}) => {
  const questionData = question;
  const [selectedOption, setSelectedOption] = useState("");
  const [onMediaEnd, setOnMediaEnd] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioDuration, setAudioDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [hasListenedOnce, setHasListenedOnce] = useState(false);

  const audioRef = useRef(null);

  // Reset selected option when question changes
  useEffect(() => {
    setSelectedOption("");
    setOnMediaEnd(false);
    setCurrentTime(0);
    setIsPlaying(false);
    setHasListenedOnce(false);
  }, [question?.questionId]);

  useEffect(() => {
    setGoNext();
  }, []);

  // Handle radio button change
  const handleChange = (event) => {
    const selectedValue = event.target.value;
    setSelectedOption(selectedValue);

    // Prepare answer data with updated structure for new API
    const answerData = {
      mockTestId: questionData?.mocktestId,
      questionId: questionData?.questionId,
      section: questionData?.section || questionData?.category?.section,
      prompt: questionData?.prompt,
      categoryId: questionData?.categoryId,
      userId: localStorage.getItem("userId"),
      useranswers: [selectedValue],
      correctAnswer: questionData?.options || [],
      maxScoreIfCorrect: questionData?.maxScore || 1,
      media: questionData?.media ? { url: questionData.media.url } : null,
      type: questionData?.type || questionData?.category?.name,
      // Include additional data for results display
      transcript: questionData?.transcript,
      questionName: questionData?.questionName,
    };

    // Send answer data to parent component
    setAnswer(answerData);
  };

  // Audio control functions
  const handlePlayPause = () => {
    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleRestart = () => {
    audioRef.current.currentTime = 0;
    audioRef.current.play();
    setIsPlaying(true);
  };

  const handleAudioLoadedMetadata = () => {
    setAudioDuration(audioRef.current.duration);
  };

  const handleTimeUpdate = () => {
    setCurrentTime(audioRef.current.currentTime);
  };

  const handleAudioEnd = () => {
    setOnMediaEnd(true);
    setIsPlaying(false);
    setHasListenedOnce(true);
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? "0" : ""}${secs}`;
  };

  // Has an option been selected
  const hasSelection = selectedOption !== "";

  // Render the prompt text (no need for {{blank}} replacement in this question type)
  const renderPrompt = () => {
    if (!questionData?.prompt) return null;

    return (
      <Typography
        variant="body1"
        sx={{
          fontSize: "1.1rem",
          lineHeight: 1.6,
          fontWeight: 400,
          textAlign: "center",
          fontStyle: "italic",
        }}
      >
        {questionData.prompt}
      </Typography>
    );
  };

  return (
    <Box
      sx={{
        maxWidth: "800px",
        mx: "auto",
        p: { xs: 2, sm: 3 },
        minHeight: "70vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Task header */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          mb: 3,
          pb: 2,
          borderBottom: "1px solid rgba(0,0,0,0.08)",
        }}
      >
        <Typography
          variant="h5"
          sx={{
            mb: 1,
            fontWeight: 600,
            color: "text.primary",
          }}
        >
          Select Missing Word
        </Typography>

        <Typography
          variant="subtitle1"
          color="text.secondary"
          sx={{ fontWeight: 500, textAlign: "center" }}
        >
          {questionData?.category?.description ||
            "Listen to the recording and select the missing word"}
        </Typography>
      </Box>

      {/* Audio player card */}
      {questionData?.media?.url && (
        <Paper
          elevation={2}
          sx={{
            p: { xs: 2, sm: 3 },
            mb: 3,
            borderRadius: 2,
            backgroundColor: "#f8f9fa",
            border: "1px solid #e6e8eb",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              mb: 2,
            }}
          >
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 500,
                display: "flex",
                alignItems: "center",
                color: "text.primary",
              }}
            >
              <HearingIcon sx={{ mr: 1, color: "primary.main" }} />
              Listen to the recording
            </Typography>

            <Chip
              icon={<VolumeUpIcon />}
              label={hasListenedOnce ? "Ready to select" : "Listen first"}
              color={hasListenedOnce ? "success" : "primary"}
              size="small"
              variant={hasListenedOnce ? "filled" : "outlined"}
              sx={{ fontWeight: 500 }}
            />
          </Box>

          <Divider sx={{ mb: 2 }} />

          {/* Custom audio player */}
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              p: 2,
              backgroundColor: "white",
              borderRadius: 2,
              border: "1px solid #e0e0e0",
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                mb: 1.5,
                width: "100%",
                justifyContent: "space-between",
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <IconButton
                  onClick={handlePlayPause}
                  color="primary"
                  sx={{
                    mr: 1,
                    backgroundColor: "rgba(25, 118, 210, 0.08)",
                    "&:hover": {
                      backgroundColor: "rgba(25, 118, 210, 0.12)",
                    },
                  }}
                >
                  {isPlaying ? <PauseIcon /> : <PlayArrowIcon />}
                </IconButton>

                <Tooltip title="Restart audio">
                  <IconButton
                    onClick={handleRestart}
                    sx={{
                      color: "text.secondary",
                      "&:hover": {
                        color: "primary.main",
                      },
                    }}
                  >
                    <RestartAltIcon />
                  </IconButton>
                </Tooltip>
              </Box>

              <Typography variant="body2" color="text.secondary">
                {formatTime(currentTime)} / {formatTime(audioDuration)}
              </Typography>
            </Box>

            {/* Progress bar */}
            <Box
              sx={{
                width: "100%",
                height: "6px",
                backgroundColor: "#f0f0f0",
                borderRadius: "3px",
                position: "relative",
                overflow: "hidden",
              }}
            >
              <Box
                sx={{
                  position: "absolute",
                  height: "100%",
                  width: `${(currentTime / audioDuration) * 100}%`,
                  backgroundColor: "primary.main",
                  borderRadius: "3px",
                  transition: "width 0.1s linear",
                }}
              />
            </Box>

            <audio
              ref={audioRef}
              key={question?.questionId}
              onEnded={handleAudioEnd}
              onLoadedMetadata={handleAudioLoadedMetadata}
              onTimeUpdate={handleTimeUpdate}
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
              autoPlay
              src={questionData?.media?.url}
              style={{ display: "none" }}
            />
          </Box>

          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              p: 1.5,
              mt: 2,
              backgroundColor: "rgba(25, 118, 210, 0.05)",
              borderRadius: 1,
            }}
          >
            <InfoOutlinedIcon color="primary" sx={{ mr: 1, fontSize: 20 }} />
            <Typography variant="body2" color="text.secondary">
              Listen carefully to the recording. The last word has been replaced
              by a beep. Select the correct word from the options below.
            </Typography>
          </Box>
        </Paper>
      )}

      {/* Instructions and question info */}
      <Paper
        elevation={1}
        sx={{
          p: { xs: 2, sm: 3 },
          mb: 3,
          borderRadius: 2,
          backgroundColor: "white",
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 500,
              display: "flex",
              alignItems: "center",
            }}
          >
            <TextFieldsIcon sx={{ mr: 1 }} />
            Question Instructions
          </Typography>

          {questionData?.questionName && (
            <Chip
              label={questionData.questionName}
              color="info"
              size="small"
              variant="outlined"
              sx={{ fontWeight: 500 }}
            />
          )}
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Question prompt */}
        <Box
          sx={{
            p: 2,
            backgroundColor: "#f0f7ff",
            borderRadius: 1,
            border: "1px solid #e3f2fd",
          }}
        >
          {renderPrompt()}
        </Box>

        {/* Question metadata */}
        {(questionData?.additionalProp1?.prepTime ||
          questionData?.additionalProp1?.answerTime) && (
          <Box sx={{ mt: 2, display: "flex", gap: 2, flexWrap: "wrap" }}>
            {questionData?.additionalProp1?.prepTime && (
              <Typography variant="caption" color="text.secondary">
                <strong>Prep Time:</strong>{" "}
                {questionData.additionalProp1.prepTime}
              </Typography>
            )}
            {questionData?.additionalProp1?.answerTime && (
              <Typography variant="caption" color="text.secondary">
                <strong>Answer Time:</strong>{" "}
                {questionData.additionalProp1.answerTime}
              </Typography>
            )}
          </Box>
        )}
      </Paper>

      {/* Options selection card */}
      <Paper
        elevation={1}
        sx={{
          p: { xs: 2, sm: 3 },
          borderRadius: 2,
          backgroundColor: "white",
          flex: 1,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 500,
              display: "flex",
              alignItems: "center",
            }}
          >
            <CheckCircleOutlineIcon sx={{ mr: 1 }} />
            Select the missing word
          </Typography>

          {hasSelection && (
            <Chip
              icon={<CheckCircleOutlineIcon />}
              label="Option selected"
              color="success"
              size="small"
              sx={{ fontWeight: 500 }}
            />
          )}
        </Box>

        <Divider sx={{ mb: 3 }} />

        {/* Options section */}
        <FormControl component="fieldset" sx={{ width: "100%" }}>
          <RadioGroup
            value={selectedOption}
            onChange={handleChange}
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: 1.5,
            }}
          >
            {questionData?.options?.map((option, index) => (
              <Paper
                key={index}
                elevation={0}
                sx={{
                  border: "1px solid",
                  borderColor:
                    selectedOption === option.optionText
                      ? "primary.main"
                      : "#e0e0e0",
                  borderRadius: 1.5,
                  transition: "all 0.2s ease",
                  backgroundColor:
                    selectedOption === option.optionText
                      ? "rgba(25, 118, 210, 0.04)"
                      : "white",
                  position: "relative",
                  "&:hover": {
                    backgroundColor:
                      selectedOption === option.optionText
                        ? "rgba(25, 118, 210, 0.08)"
                        : "rgba(0, 0, 0, 0.01)",
                    borderColor:
                      selectedOption === option.optionText
                        ? "primary.main"
                        : "#bdbdbd",
                  },
                }}
              >
                <FormControlLabel
                  value={option.optionText}
                  control={
                    <Radio
                      color="primary"
                      sx={{
                        "& .MuiSvgIcon-root": {
                          fontSize: 20,
                        },
                      }}
                    />
                  }
                  label={
                    <Typography
                      variant="body1"
                      sx={{
                        py: 0.5,
                        fontSize: "1.1rem",
                        fontWeight:
                          selectedOption === option.optionText ? 500 : 400,
                      }}
                    >
                      {option.optionText}
                    </Typography>
                  }
                  sx={{
                    m: 0,
                    p: 2,
                    width: "100%",
                    "& .MuiFormControlLabel-label": {
                      width: "100%",
                    },
                  }}
                />
                {selectedOption === option.optionText && (
                  <Box
                    sx={{
                      position: "absolute",
                      right: 16,
                      top: "50%",
                      transform: "translateY(-50%)",
                      display: { xs: "none", sm: "block" },
                    }}
                  >
                    <CheckCircleOutlineIcon color="primary" fontSize="small" />
                  </Box>
                )}
              </Paper>
            ))}
          </RadioGroup>
        </FormControl>

        {/* Selection status */}
        {!hasSelection && hasListenedOnce && (
          <Box
            sx={{
              mt: 2,
              p: 1.5,
              backgroundColor: "rgba(255, 152, 0, 0.05)",
              borderRadius: 1,
              border: "1px solid rgba(255, 152, 0, 0.2)",
            }}
          >
            <Typography variant="body2" color="text.secondary">
              💡 Please select one of the options above to complete the missing
              word.
            </Typography>
          </Box>
        )}


      </Paper>
    </Box>
  );
};

export default MissingWord;
