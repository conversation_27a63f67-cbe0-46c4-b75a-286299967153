import React, { useEffect, useState, useRef } from "react";
import {
  Typo<PERSON>,
  Box,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Paper,
  Divider,
  Chip,
  FormControl,
  IconButton,
  Tooltip,
} from "@mui/material";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import VolumeUpIcon from "@mui/icons-material/VolumeUp";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import PauseIcon from "@mui/icons-material/Pause";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import HearingIcon from "@mui/icons-material/Hearing";

const MultipleChoiceListen = ({ setAnswer, onNext, setGoNext, question }) => {
  const questionData = question;
  const [selectedOptions, setSelectedOptions] = useState({});
  const [onMediaEnd, setOnMediaEnd] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioDuration, setAudioDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);

  const audioRef = useRef(null);

  // Reset selected options when question changes
  useEffect(() => {
    setSelectedOptions({});
    setOnMediaEnd(false);
    setCurrentTime(0);
    setIsPlaying(false);
  }, [question?.questionId]);

  useEffect(() => {
    setGoNext();
  }, []);

  // Handle checkbox changes
  const handleChange = (event) => {
    const { name, checked } = event.target;

    // Create new selected options with current change
    const newSelectedOptions = {
      ...selectedOptions,
      [name]: checked,
    };

    // Update local state
    setSelectedOptions(newSelectedOptions);

    const formattedUserAnswers = Object.entries(newSelectedOptions)
      .filter(([_, isSelected]) => isSelected)
      .map(([optionText]) => ({ optionText }));

    // Prepare answer data
    const answerData = {
      mockTestId: questionData?.mocktestId,
      questionId: questionData?.questionId,
      section: questionData?.category?.section,
      prompt: questionData?.prompt,
      categoryId: questionData?.categoryId,
      userId: localStorage.getItem("userId"),
      useranswers: formattedUserAnswers,
      correctAnswer: questionData?.options,
      media: {
        url: questionData?.media?.url,
        type: "audio",
      },
      type: questionData?.category?.name,
    };

    // Store the answer in parent component
    setAnswer(answerData);
  };

  // Audio control functions
  const handlePlayPause = () => {
    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleRestart = () => {
    audioRef.current.currentTime = 0;
    audioRef.current.play();
    setIsPlaying(true);
  };

  const handleAudioLoadedMetadata = () => {
    setAudioDuration(audioRef.current.duration);
  };

  const handleTimeUpdate = () => {
    setCurrentTime(audioRef.current.currentTime);
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? "0" : ""}${secs}`;
  };

  // Calculate completion status
  const selectedCount = Object.values(selectedOptions).filter(Boolean).length;
  const totalOptions = questionData?.options?.length || 0;

  return (
    <Box
      sx={{
        maxWidth: "800px",
        mx: "auto",
        p: { xs: 2, sm: 3 },
        minHeight: "70vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Task header */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          mb: 3,
          pb: 2,
          borderBottom: "1px solid rgba(0,0,0,0.08)",
        }}
      >
        <Typography
          variant="h5"
          sx={{
            mb: 1,
            fontWeight: 600,
            color: "text.primary",
          }}
        >
          Listening: Multiple Choice
        </Typography>

        <Typography
          variant="subtitle1"
          color="text.secondary"
          sx={{ fontWeight: 500 }}
        >
          {questionData?.category?.description ||
            "Listen and select the correct answers"}
        </Typography>
      </Box>

      {/* Audio player card */}
      <Paper
        elevation={2}
        sx={{
          p: { xs: 2, sm: 3 },
          mb: 3,
          borderRadius: 2,
          backgroundColor: "#f8f9fa",
          border: "1px solid #e6e8eb",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 500,
              display: "flex",
              alignItems: "center",
              color: "text.primary",
            }}
          >
            <HearingIcon sx={{ mr: 1, color: "primary.main" }} />
            Listen to the audio
          </Typography>

          <Chip
            icon={<VolumeUpIcon />}
            label={onMediaEnd ? "Completed" : "Listening"}
            color={onMediaEnd ? "success" : "primary"}
            size="small"
            variant={onMediaEnd ? "filled" : "outlined"}
            sx={{ fontWeight: 500 }}
          />
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Custom audio player */}
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            p: 2,
            backgroundColor: "white",
            borderRadius: 2,
            border: "1px solid #e0e0e0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              mb: 1.5,
              width: "100%",
              justifyContent: "space-between",
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <IconButton
                onClick={handlePlayPause}
                color="primary"
                sx={{
                  mr: 1,
                  backgroundColor: "rgba(25, 118, 210, 0.08)",
                  "&:hover": {
                    backgroundColor: "rgba(25, 118, 210, 0.12)",
                  },
                }}
              >
                {isPlaying ? <PauseIcon /> : <PlayArrowIcon />}
              </IconButton>

              <Tooltip title="Restart audio">
                <IconButton
                  onClick={handleRestart}
                  sx={{
                    color: "text.secondary",
                    "&:hover": {
                      color: "primary.main",
                    },
                  }}
                >
                  <RestartAltIcon />
                </IconButton>
              </Tooltip>
            </Box>

            <Typography variant="body2" color="text.secondary">
              {formatTime(currentTime)} / {formatTime(audioDuration)}
            </Typography>
          </Box>

          {/* Progress bar */}
          <Box
            sx={{
              width: "100%",
              height: "6px",
              backgroundColor: "#f0f0f0",
              borderRadius: "3px",
              position: "relative",
              overflow: "hidden",
            }}
          >
            <Box
              sx={{
                position: "absolute",
                height: "100%",
                width: `${(currentTime / audioDuration) * 100}%`,
                backgroundColor: "primary.main",
                borderRadius: "3px",
                transition: "width 0.1s linear",
              }}
            />
          </Box>

          <audio
            ref={audioRef}
            onEnded={() => {
              setOnMediaEnd(true);
              setIsPlaying(false);
            }}
            onLoadedMetadata={handleAudioLoadedMetadata}
            onTimeUpdate={handleTimeUpdate}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            src={questionData?.media?.url}
            style={{ display: "none" }}
          />
        </Box>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            p: 1.5,
            mt: 2,
            backgroundColor: "rgba(25, 118, 210, 0.05)",
            borderRadius: 1,
          }}
        >
          <InfoOutlinedIcon color="primary" sx={{ mr: 1, fontSize: 20 }} />
          <Typography variant="body2" color="text.secondary">
            Listen carefully to the audio and select all answers that apply to
            the question.
          </Typography>
        </Box>
      </Paper>

      {/* Questions card */}
      <Paper
        elevation={1}
        sx={{
          p: { xs: 2, sm: 3 },
          borderRadius: 2,
          backgroundColor: "white",
          flex: 1,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
            {questionData.prompt}
          </Typography>

          <Chip
            label={`${selectedCount} selected`}
            color={selectedCount > 0 ? "primary" : "default"}
            size="small"
            variant="outlined"
            sx={{ fontWeight: 500 }}
          />
        </Box>

        <Divider sx={{ mb: 3 }} />

        <FormControl component="fieldset" sx={{ width: "100%" }}>
          <FormGroup sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
            {questionData?.options?.map((option, index) => (
              <Paper
                key={index}
                elevation={0}
                sx={{
                  border: "1px solid",
                  borderColor: selectedOptions[option.optionText]
                    ? "primary.main"
                    : "#e0e0e0",
                  borderRadius: 1.5,
                  transition: "all 0.2s ease",
                  backgroundColor: selectedOptions[option.optionText]
                    ? "rgba(25, 118, 210, 0.04)"
                    : "white",
                  "&:hover": {
                    backgroundColor: selectedOptions[option.optionText]
                      ? "rgba(25, 118, 210, 0.08)"
                      : "rgba(0, 0, 0, 0.01)",
                    borderColor: selectedOptions[option.optionText]
                      ? "primary.main"
                      : "#bdbdbd",
                  },
                }}
              >
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={selectedOptions[option.optionText] || false}
                      onChange={handleChange}
                      name={option.optionText}
                      icon={<CheckBoxOutlineBlankIcon />}
                      checkedIcon={<CheckBoxIcon />}
                      sx={{
                        "&.Mui-checked": { color: "primary.main" },
                        "& .MuiSvgIcon-root": { fontSize: 22 },
                      }}
                    />
                  }
                  label={
                    <Typography variant="body1" sx={{ py: 0.5 }}>
                      {option.optionText}
                    </Typography>
                  }
                  sx={{
                    m: 0,
                    p: 1,
                    width: "100%",
                    "& .MuiFormControlLabel-label": {
                      width: "100%",
                      fontWeight: selectedOptions[option.optionText]
                        ? 500
                        : 400,
                    },
                  }}
                />
              </Paper>
            ))}
          </FormGroup>
        </FormControl>
      </Paper>
    </Box>
  );
};

export default MultipleChoiceListen;
