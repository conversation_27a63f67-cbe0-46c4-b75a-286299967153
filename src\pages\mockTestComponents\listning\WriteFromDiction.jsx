import React, { useState, useEffect, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>Field,
  Typography,
  Box,
  Paper,
  IconButton,
  Toolt<PERSON>,
  Chip,
  Divider,
} from "@mui/material";
import VolumeUpIcon from "@mui/icons-material/VolumeUp";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import PauseIcon from "@mui/icons-material/Pause";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import HearingIcon from "@mui/icons-material/Hearing";
import EditIcon from "@mui/icons-material/Edit";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";

const WriteFromDictation = ({
  setAnswer,
  onNext,
  setGoNext,
  question,
  handleUploadAnswer,
}) => {
  const [answer, setAnswerText] = useState("");
  const [audioEnded, setAudioEnded] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioDuration, setAudioDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);

  const audioRef = useRef(null);

  // Reset state when question changes
  useEffect(() => {
    setAnswerText("");
    setAudioEnded(false);
    setIsPlaying(false);
    setCurrentTime(0);
  }, [question?.questionId]);

  // Set initial state
  useEffect(() => {
    if (setGoNext) {
      setGoNext();
    }
  }, [setGoNext]);

  // Handle input changes
  const handleInputChange = (event) => {
    const newValue = event.target.value;
    setAnswerText(newValue);

    // Prepare answer object in the correct format for new API
    const answerObj = {
      questionId: question?.questionId,
      mocktestId: question?.mocktestId,
      categoryId: question?.categoryId,
      section: question?.section,
      userId: localStorage.getItem("userId"),
      useranswers: newValue, // Store the typed answer directly
      correctAnswer: {
        transcript: question?.transcript,
        answer: question?.answer,
      },
      maxScoreIfCorrect: question?.maxScore,
      type: question?.type || "write from dictation",
      answer: newValue, // Also store in answer field for display
    };

    // Update parent component state
    setAnswer(answerObj);
  };

  // Audio control functions
  const handlePlayPause = () => {
    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleRestart = () => {
    audioRef.current.currentTime = 0;
    audioRef.current.play();
    setIsPlaying(true);
  };

  const handleAudioLoadedMetadata = () => {
    setAudioDuration(audioRef.current.duration);
  };

  const handleTimeUpdate = () => {
    setCurrentTime(audioRef.current.currentTime);
  };

  const handleAudioEnd = () => {
    setAudioEnded(true);
    setIsPlaying(false);
  };

  // Format time display
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secondsRemaining = seconds % 60;
    return `${minutes}:${secondsRemaining < 10 ? "0" : ""}${secondsRemaining}`;
  };

  // Word count calculation
  const getWordCount = (text) => {
    if (typeof text !== "string") return 0;
    const words = text.trim().split(/\s+/);
    return words.length === 1 && words[0] === "" ? 0 : words.length;
  };

  return (
    <Box
      sx={{
        maxWidth: "800px",
        mx: "auto",
        p: { xs: 2, sm: 3 },
        minHeight: "70vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Task header */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          mb: 3,
          pb: 2,
          borderBottom: "1px solid rgba(0,0,0,0.08)",
        }}
      >
        <Typography
          variant="h5"
          sx={{
            mb: 1,
            fontWeight: 600,
            color: "text.primary",
          }}
        >
          Write from Dictation
        </Typography>

        <Typography
          variant="subtitle1"
          color="text.secondary"
          sx={{ fontWeight: 500, textAlign: "center" }}
        >
          {question?.prompt ||
            "Listen carefully and type exactly what you hear"}
        </Typography>
      </Box>

      {/* Audio player card */}
      <Paper
        elevation={2}
        sx={{
          p: { xs: 2, sm: 3 },
          mb: 3,
          borderRadius: 2,
          backgroundColor: "#f8f9fa",
          border: "1px solid #e6e8eb",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 500,
              display: "flex",
              alignItems: "center",
              color: "text.primary",
            }}
          >
            <HearingIcon sx={{ mr: 1, color: "primary.main" }} />
            Listen to the sentence
          </Typography>

          <Chip
            icon={<VolumeUpIcon />}
            label={audioEnded ? "Ready to type" : "Listen carefully"}
            color={audioEnded ? "success" : "primary"}
            size="small"
            variant={audioEnded ? "filled" : "outlined"}
            sx={{ fontWeight: 500 }}
          />
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Custom audio player */}
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            p: 2,
            backgroundColor: "white",
            borderRadius: 2,
            border: "1px solid #e0e0e0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              mb: 1.5,
              width: "100%",
              justifyContent: "space-between",
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <IconButton
                onClick={handlePlayPause}
                color="primary"
                sx={{
                  mr: 1,
                  backgroundColor: "rgba(25, 118, 210, 0.08)",
                  "&:hover": {
                    backgroundColor: "rgba(25, 118, 210, 0.12)",
                  },
                }}
              >
                {isPlaying ? <PauseIcon /> : <PlayArrowIcon />}
              </IconButton>

              <Tooltip title="Restart audio">
                <IconButton
                  onClick={handleRestart}
                  sx={{
                    color: "text.secondary",
                    "&:hover": {
                      color: "primary.main",
                    },
                  }}
                >
                  <RestartAltIcon />
                </IconButton>
              </Tooltip>
            </Box>

            {/* <Typography variant="body2" color="text.secondary">
              {formatTime(currentTime)} / {formatTime(audioDuration)}
            </Typography> */}
          </Box>

          {/* Progress bar */}
          <Box
            sx={{
              width: "100%",
              height: "6px",
              backgroundColor: "#f0f0f0",
              borderRadius: "3px",
              position: "relative",
              overflow: "hidden",
            }}
          >
            <Box
              sx={{
                position: "absolute",
                height: "100%",
                width: `${(currentTime / audioDuration) * 100}%`,
                backgroundColor: "primary.main",
                borderRadius: "3px",
                transition: "width 0.1s linear",
              }}
            />
          </Box>

          <audio
            ref={audioRef}
            key={question?.questionId}
            onEnded={handleAudioEnd}
            onLoadedMetadata={handleAudioLoadedMetadata}
            onTimeUpdate={handleTimeUpdate}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            autoPlay
            src={question?.media?.url}
            style={{ display: "none" }}
          />
        </Box>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            p: 1.5,
            mt: 2,
            backgroundColor: "rgba(25, 118, 210, 0.05)",
            borderRadius: 1,
          }}
        >
          <InfoOutlinedIcon color="primary" sx={{ mr: 1, fontSize: 20 }} />
          <Typography variant="body2" color="text.secondary">
            You will hear the sentence only once. Listen carefully and type
            exactly what you hear.
          </Typography>
        </Box>
      </Paper>

      {/* Writing area */}
      <Paper
        elevation={1}
        sx={{
          p: { xs: 2, sm: 3 },
          borderRadius: 2,
          backgroundColor: "white",
          flex: 1,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 500,
              display: "flex",
              alignItems: "center",
            }}
          >
            <EditIcon sx={{ mr: 1 }} />
            Type your answer
          </Typography>
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Text input field */}
        <TextField
          label="Write your answer here"
          multiline
          rows={4}
          fullWidth
          value={answer}
          onChange={handleInputChange}
          placeholder="Type exactly what you heard..."
          sx={{
            marginBottom: 2,
            "& .MuiOutlinedInput-root": {
              backgroundColor: "#f5f5f5",
              fontSize: "1.1rem",
            },
            "& .MuiInputLabel-root.Mui-disabled": {
              color: "rgba(0, 0, 0, 0.38)",
            },
          }}
        />

        {/* Status and stats */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            flexWrap: "wrap",
            gap: 2,
            p: 2,
            backgroundColor: "rgba(0, 0, 0, 0.02)",
            borderRadius: 1,
          }}
        >
          <Box sx={{ display: "flex", gap: 3, flexWrap: "wrap" }}>
            <Typography variant="body2" color="text.secondary">
              <strong>Word Count:</strong> {getWordCount(answer)}
            </Typography>

            <Typography variant="body2" color="text.secondary">
              <strong>Characters:</strong> {answer.length}
            </Typography>
          </Box>
        </Box>

        {/* Instructions */}
        <Box
          sx={{
            mt: 2,
            p: 1.5,
            backgroundColor: "rgba(76, 175, 80, 0.05)",
            borderRadius: 1,
            border: "1px solid",
            borderColor: "rgba(76, 175, 80, 0.2)",
          }}
        >
          <Typography variant="body2" color="text.secondary">
            ✏️ Type exactly what you heard. Pay attention to spelling,
            punctuation, and capitalization. You can listen to the audio while
            typing.
          </Typography>
        </Box>

        {/* Question name and metadata */}
        {/* {question?.questionName && (
          <Box
            sx={{ mt: 2, p: 1, backgroundColor: "#f5f5f5", borderRadius: 1 }}
          >
            <Typography variant="caption" color="text.secondary">
              <strong>Question:</strong> {question.questionName}
            </Typography>
            {question?.additionalProp1?.prepTime && (
              <Typography
                variant="caption"
                color="text.secondary"
                sx={{ ml: 2 }}
              >
                <strong>Prep Time:</strong> {question.additionalProp1.prepTime}
              </Typography>
            )}
            {question?.additionalProp1?.answerTime && (
              <Typography
                variant="caption"
                color="text.secondary"
                sx={{ ml: 2 }}
              >
                <strong>Answer Time:</strong>{" "}
                {question.additionalProp1.answerTime}
              </Typography>
            )}
          </Box>
        )} */}
      </Paper>
    </Box>
  );
};

export default WriteFromDictation;
