import React, { useEffect, useState } from "react";
import { <PERSON>, Typography, Paper, Button, Divider, Chip } from "@mui/material";
import RefreshIcon from "@mui/icons-material/Refresh";
import DragIndicatorIcon from "@mui/icons-material/DragIndicator";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import TextFormatIcon from "@mui/icons-material/TextFormat";

const FillTheBlanks = ({ setAnswer, onNext, setGoNext, question }) => {
  // Initialize answers array based on number of options
  const [answers, setAnswers] = useState(
    new Array(question?.options?.length || 0).fill(null)
  );

  // Create pool of all available options
  const [pool, setPool] = useState(() => {
    const allOptions = question?.options?.reduce((acc, blank) => {
      // Add all options from each blank to the pool
      return [...acc, ...blank.options];
    }, []);
    return allOptions || [];
  });

  const [shouldUpdateAnswer, setShouldUpdateAnswer] = useState(false);
  const [draggedWord, setDraggedWord] = useState(null);

  useEffect(() => {
    setGoNext();
  }, []);

  useEffect(() => {
    // Reset state when question changes
    if (question?.questionId) {
      const newAnswerArray = new Array(question?.options?.length || 0).fill(
        null
      );
      setAnswers(newAnswerArray);

      const allOptions = question?.options?.reduce((acc, blank) => {
        return [...acc, ...blank.options];
      }, []);
      setPool(allOptions || []);
    }
  }, [question?.questionId]);

  useEffect(() => {
    if (shouldUpdateAnswer && answers.some((answer) => answer !== null)) {
      const answerObj = {
        questionId: question?.questionId,
        mocktestId: question?.mocktestId,
        useranswers: answers
          .filter((a) => a !== null)
          .map((answer) => ({
            optionText: answer,
          })),
        section: question?.category?.section,
        categoryId: question?.categoryId,
        userId: localStorage.getItem("userId"),
        correctAnswer: question?.options,
        maxScoreIfCorrect: question?.maxScore,
        type: question?.category?.name,
      };
      setAnswer(answerObj);
      setShouldUpdateAnswer(false);
    }
  }, [shouldUpdateAnswer, answers, question]);

  const handleDrop = (word, index) => {
    const newAnswers = [...answers];
    const newPool = [...pool];

    if (newAnswers[index]) {
      // If there was a previous answer, add it back to the pool
      newPool.push(newAnswers[index]);
    }

    newAnswers[index] = word;
    setAnswers(newAnswers);
    setPool(newPool.filter((w) => w !== word));
    setShouldUpdateAnswer(true);
    setDraggedWord(null);
  };

  const handleDragStart = (e, word) => {
    e.dataTransfer.setData("text/plain", word);
    setDraggedWord(word);
  };

  const handleDragEnd = () => {
    setDraggedWord(null);
  };

  const handleReset = () => {
    // Reset answers and restore all options to the pool
    setAnswers(new Array(question?.options?.length || 0).fill(null));
    const allOptions = question?.options?.reduce((acc, blank) => {
      return [...acc, ...blank.options];
    }, []);
    setPool(allOptions || []);
    setShouldUpdateAnswer(true);
  };

  // Calculate completion status
  const filledBlanks = answers.filter((a) => a !== null).length;
  const totalBlanks = answers.length;
  const completionPercentage =
    totalBlanks > 0 ? Math.round((filledBlanks / totalBlanks) * 100) : 0;

  // Split the prompt into parts based on underscores
  const parts = question?.prompt.split(/_+/g) || [];

  return (
    <Box
      sx={{
        maxWidth: "800px",
        mx: "auto",
        p: { xs: 2, sm: 3 },
        minHeight: "70vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Task header */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          mb: 3,
          pb: 2,
          borderBottom: "1px solid rgba(0,0,0,0.08)",
        }}
      >
        <Typography
          variant="h5"
          sx={{
            mb: 1,
            fontWeight: 600,
            color: "text.primary",
          }}
        >
          Fill In The Blanks
        </Typography>

        <Typography
          variant="subtitle1"
          color="text.secondary"
          sx={{ fontWeight: 500 }}
        >
          {question?.category?.description ||
            "Complete the text with the appropriate words"}
        </Typography>
      </Box>

      {/* Instructions */}
      <Paper
        elevation={2}
        sx={{
          p: { xs: 2, sm: 3 },
          mb: 3,
          borderRadius: 2,
          backgroundColor: "#f8f9fa",
          border: "1px solid #e6e8eb",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            p: 1.5,
            backgroundColor: "rgba(25, 118, 210, 0.05)",
            borderRadius: 1,
          }}
        >
          <InfoOutlinedIcon color="primary" sx={{ mr: 1, fontSize: 20 }} />
          <Typography variant="body2" color="text.secondary">
            Drag words from the word pool and drop them into the appropriate
            blanks. Click "Reset" to start over.
          </Typography>
        </Box>
      </Paper>

      {/* Main content area */}
      <Paper
        elevation={1}
        sx={{
          p: { xs: 2, sm: 3 },
          borderRadius: 2,
          backgroundColor: "white",
          flex: 1,
          mb: 3,
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            color="text.secondary"
            sx={{
              fontWeight: 500,
              display: "flex",
              alignItems: "center",
            }}
          >
            <TextFormatIcon sx={{ mr: 1 }} />
            Complete the text:
          </Typography>

          <Chip
            label={`${filledBlanks}/${totalBlanks} filled`}
            color={completionPercentage === 100 ? "success" : "primary"}
            variant={completionPercentage === 100 ? "filled" : "outlined"}
            size="small"
            sx={{ fontWeight: 500 }}
          />
        </Box>

        <Divider sx={{ mb: 3 }} />

        <Box
          sx={{
            fontSize: "16px",
            lineHeight: 1.6,
            color: "text.primary",
            mb: 4,
            p: 2,
            backgroundColor: "#fafafa",
            borderRadius: 2,
            border: "1px solid #f0f0f0",
          }}
        >
          {parts.map((part, index) => (
            <React.Fragment key={index}>
              {part}
              {index < answers.length && (
                <Box
                  component="span"
                  sx={{
                    display: "inline-block",
                    minWidth: "120px",
                    height: "40px",
                    lineHeight: "40px",
                    textAlign: "center",
                    m: "0 8px",
                    backgroundColor: answers[index]
                      ? "rgba(25, 118, 210, 0.08)"
                      : "#f5f5f5",
                    border: "2px",
                    borderStyle: answers[index] ? "solid" : "dashed",
                    borderColor: answers[index] ? "primary.main" : "#ccc",
                    borderRadius: "4px",
                    cursor: "pointer",
                    transition: "all 0.2s ease",
                    color: answers[index] ? "primary.dark" : "text.secondary",
                    fontWeight: answers[index] ? 500 : 400,
                    position: "relative",
                    "&:hover": {
                      backgroundColor: answers[index]
                        ? "rgba(25, 118, 210, 0.12)"
                        : "#f0f0f0",
                    },
                  }}
                  onDragOver={(e) => e.preventDefault()}
                  onDrop={(e) => {
                    e.preventDefault();
                    const word = e.dataTransfer.getData("text/plain");
                    handleDrop(word, index);
                  }}
                >
                  {answers[index] || "_____"}
                </Box>
              )}
            </React.Fragment>
          ))}
        </Box>
      </Paper>

      {/* Word pool */}
      <Paper
        elevation={1}
        sx={{
          p: { xs: 2, sm: 3 },
          borderRadius: 2,
          backgroundColor: "#f8f9fa",
          mb: 3,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 500,
              display: "flex",
              alignItems: "center",
            }}
          >
            <DragIndicatorIcon sx={{ mr: 1 }} />
            Word Pool:
          </Typography>

          <Button
            variant="outlined"
            size="small"
            startIcon={<RefreshIcon />}
            onClick={handleReset}
            sx={{ borderRadius: 2 }}
          >
            Reset
          </Button>
        </Box>

        <Divider sx={{ mb: 2 }} />

        <Box
          sx={{
            display: "flex",
            flexWrap: "wrap",
            gap: 1.5,
            minHeight: "80px",
            p: 2,
            backgroundColor: "white",
            borderRadius: 1,
            border: "1px solid #e0e0e0",
          }}
        >
          {pool.length === 0 ? (
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ p: 2, fontStyle: "italic" }}
            >
              All words have been used
            </Typography>
          ) : (
            pool.map((word, index) => (
              <Box
                key={`${word}-${index}`}
                draggable
                onDragStart={(e) => handleDragStart(e, word)}
                onDragEnd={handleDragEnd}
                sx={{
                  py: 1,
                  px: 2,
                  backgroundColor:
                    draggedWord === word ? "rgba(25, 118, 210, 0.12)" : "white",
                  border: "1px solid",
                  borderColor:
                    draggedWord === word ? "primary.main" : "#e0e0e0",
                  borderRadius: "4px",
                  cursor: "grab",
                  userSelect: "none",
                  transition: "all 0.15s ease",
                  boxShadow:
                    draggedWord === word
                      ? "0 2px 4px rgba(0,0,0,0.1)"
                      : "0 1px 2px rgba(0,0,0,0.05)",
                  "&:hover": {
                    backgroundColor: "#f5f5f5",
                    transform: "translateY(-1px)",
                    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                  },
                  "&:active": {
                    cursor: "grabbing",
                    transform: "translateY(0px)",
                  },
                }}
              >
                {word}
              </Box>
            ))
          )}
        </Box>
      </Paper>
    </Box>
  );
};

export default FillTheBlanks;
