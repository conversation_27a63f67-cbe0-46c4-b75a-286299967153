import { useEffect, useState } from "react";
import {
  Typography,
  Box,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Paper,
  Divider,
  FormControl,
  Chip,
} from "@mui/material";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import PropTypes from "prop-types";

const MultipleChoiceQuestion = ({
  setAnswer,
  onNext,
  setGoNext,
  question,
  handleUploadAnswer,
}) => {
  const questionData = question;
  const [selectedOptions, setSelectedOptions] = useState({});

  // Reset selected options when question changes
  useEffect(() => {
    setSelectedOptions({});
  }, [question?.questionId]);

  useEffect(() => {
    setGoNext();
  }, [setGoNext]);

  // Handle checkbox changes - only updates local state
  const handleChange = (event) => {
    const { name, checked } = event.target;
    const newSelectedOptions = {
      ...selectedOptions,
      [name]: checked,
    };

    // Update local state
    setSelectedOptions(newSelectedOptions);

    const formattedUserAnswers = Object.entries(newSelectedOptions)
      .filter(([_, isSelected]) => isSelected)
      .map(([optionText]) => ({ text: optionText }));

    // Prepare and store answer data for next click
    const answerData = {
      mockTestId: questionData?.mocktestId,
      questionId: questionData?.questionId,
      section: questionData?.category?.section,
      prompt: questionData?.prompt,
      categoryId: questionData?.categoryId,
      userId: localStorage.getItem("userId"),
      useranswers: formattedUserAnswers,
      correctAnswer: questionData?.options,
      maxScoreIfCorrect: questionData?.maxScore,
      type: questionData?.category.name,
    };

    // Only store the answer in parent component, don't send to backend yet
    setAnswer(answerData);
  };

  // Count selected options
  const selectedCount = Object.values(selectedOptions).filter(Boolean).length;

  return (
    <Box
      sx={{
        maxWidth: "800px",
        mx: "auto",
        p: { xs: 2, sm: 3 },
        minHeight: "70vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Task header */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          mb: 3,
          pb: 2,
          borderBottom: "1px solid rgba(0,0,0,0.08)",
        }}
      >
        <Typography
          variant="h5"
          sx={{
            mb: 1,
            fontWeight: 600,
            color: "text.primary",
          }}
        >
          Multiple Choice
        </Typography>

        <Typography
          variant="subtitle1"
          color="text.secondary"
          sx={{ fontWeight: 500 }}
        >
          {questionData?.category?.description || "Select all correct answers"}
        </Typography>
      </Box>

      {/* Question content */}
      <Paper
        elevation={2}
        sx={{
          p: { xs: 2, sm: 3 },
          mb: { xs: 3, sm: 4 },
          borderRadius: 2,
          backgroundColor: "#f8f9fa",
          border: "1px solid #e6e8eb",
        }}
      >
        <Typography
          variant="body1"
          sx={{
            fontWeight: 500,
            lineHeight: 1.6,
            mb: 2,
          }}
        >
          {questionData.prompt}
        </Typography>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            p: 1.5,
            backgroundColor: "rgba(25, 118, 210, 0.05)",
            borderRadius: 1,
            mt: 2,
          }}
        >
          <InfoOutlinedIcon color="primary" sx={{ mr: 1, fontSize: 20 }} />
          <Typography variant="body2" color="text.secondary">
            Select all options that apply. There may be more than one correct
            answer.
          </Typography>
        </Box>
      </Paper>

      {/* Answer options with selection counter */}
      <Paper
        elevation={1}
        sx={{
          p: { xs: 2, sm: 3 },
          borderRadius: 2,
          backgroundColor: "white",
          flex: 1,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            color="text.secondary"
            sx={{ fontWeight: 500 }}
          >
            Select all correct options:
          </Typography>

          <Chip
            label={`${selectedCount} selected`}
            color={selectedCount > 0 ? "primary" : "default"}
            size="small"
            sx={{ fontWeight: 500 }}
          />
        </Box>

        <Divider sx={{ mb: 2 }} />

        <FormControl component="fieldset" sx={{ width: "100%" }}>
          <FormGroup sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
            {questionData?.options?.map((option, index) => (
              <Paper
                key={index}
                elevation={0}
                sx={{
                  border: "1px solid",
                  borderColor: selectedOptions[option.text || option.optionText]
                    ? "primary.main"
                    : "#e0e0e0",
                  borderRadius: 1.5,
                  transition: "all 0.2s ease",
                  backgroundColor: selectedOptions[option.text || option.optionText] 
                    ? "rgba(25, 118, 210, 0.04)"
                    : "white",
                  "&:hover": {
                    backgroundColor: selectedOptions[option.text || option.optionText]
                      ? "rgba(25, 118, 210, 0.08)"
                      : "rgba(0, 0, 0, 0.01)",
                    borderColor: selectedOptions[option.text || option.optionText]
                      ? "primary.main"
                      : "#bdbdbd",
                  },
                }}
              >
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={selectedOptions[option.text || option.optionText] || false}
                      onChange={handleChange}
                      name={option.text || option.optionText}
                      icon={<CheckBoxOutlineBlankIcon />}
                      checkedIcon={<CheckBoxIcon />}
                      sx={{
                        "&.Mui-checked": { color: "primary.main" },
                        "& .MuiSvgIcon-root": { fontSize: 22 },
                      }}
                    />
                  }
                  label={
                    <Typography variant="body1" sx={{ py: 0.5 }}>
                      {option.text || option.optionText}
                    </Typography>
                  }
                  sx={{
                    m: 0,
                    p: 1,
                    width: "100%",
                    "& .MuiFormControlLabel-label": {
                      width: "100%",
                      fontWeight: selectedOptions[option.text || option.optionText] ? 500 : 400,
                    },
                  }}
                />
              </Paper>
            ))}
          </FormGroup>
        </FormControl>
      </Paper>
    </Box>
  );
};

MultipleChoiceQuestion.propTypes = {
  setAnswer: PropTypes.func.isRequired,
  onNext: PropTypes.func,
  setGoNext: PropTypes.func.isRequired,
  question: PropTypes.shape({
    questionId: PropTypes.string,
    mocktestId: PropTypes.string,
    prompt: PropTypes.string,
    maxScore: PropTypes.number,
    categoryId: PropTypes.string,
    options: PropTypes.arrayOf(
      PropTypes.shape({
        text: PropTypes.string,
        isCorrect: PropTypes.bool,
      })
    ),
    category: PropTypes.shape({
      name: PropTypes.string,
      description: PropTypes.string,
      section: PropTypes.string,
    }),
  }).isRequired,
  handleUploadAnswer: PropTypes.func,
};

export default MultipleChoiceQuestion;
