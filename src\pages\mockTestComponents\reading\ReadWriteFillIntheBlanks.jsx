import React, { useState, useEffect } from "react";
import {
  Typo<PERSON>,
  Box,
  Select,
  MenuItem,
  Paper,
  Divider,
  Chip,
  FormControl,
} from "@mui/material";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import TextFormatIcon from "@mui/icons-material/TextFormat";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";

const ReadingWritingFillInTheBlanks = ({
  setAnswer,
  onNext,
  setGoNext,
  question,
  handleUploadAnswer,
}) => {
  const [localAnswers, setLocalAnswers] = useState({});

  // Reset answers when question changes
  useEffect(() => {
    setLocalAnswers({});
  }, [question?.questionId]);

  useEffect(() => {
    setGoNext();
  }, []);

  const handleChange = (position, event) => {
    const value = event.target.value;
    setLocalAnswers((prev) => ({
      ...prev,
      [position]: value,
    }));

    const formattedUserAnswers = Object.entries({
      ...localAnswers,
      [position]: value,
    })
      .filter(([_, value]) => value !== "")
      .map(([_, value]) => ({
        optionText: value,
      }));

    const answerData = {
      mockTestId: question?.mocktestId,
      questionId: question?.questionId,
      prompt: question?.prompt,
      section: question?.category?.section,
      categoryId: question?.categoryId,
      userId: localStorage.getItem("userId"),
      useranswers: formattedUserAnswers,
      correctAnswer: question?.options,
      maxScoreIfCorrect: question?.maxScore,
      type: question?.category?.name,
    };

    setAnswer(answerData);
  };

  // Calculate completion status
  const totalBlanks = question?.options?.length || 0;
  const filledBlanks = Object.values(localAnswers).filter(
    (value) => value && value !== ""
  ).length;
  const completionPercentage =
    totalBlanks > 0 ? Math.round((filledBlanks / totalBlanks) * 100) : 0;

  const renderContent = () => {
    if (!question?.prompt) {
      return (
        <Box sx={{ p: 3, textAlign: "center" }}>
          <Typography color="text.secondary">Loading question...</Typography>
        </Box>
      );
    }

    // Split by ___
    const parts = question.prompt.split(/___/g);
    const content = [];

    parts.forEach((part, index) => {
      // Add the text part
      content.push(
        <Typography
          key={`text-${index}`}
          component="span"
          sx={{
            lineHeight: "inherit",
            display: "inline",
          }}
        >
          {part}
        </Typography>
      );

      // Add select box after each part except the last one
      if (index < parts.length - 1) {
        const blankData = question.options[index];

        if (blankData) {
          content.push(
            <FormControl
              key={`select-${index}`}
              variant="outlined"
              size="small"
              sx={{
                margin: "0 6px",
                display: "inline-block",
                verticalAlign: "middle",
                minWidth: "160px",
              }}
            >
              <Select
                value={localAnswers[blankData.index] || ""}
                onChange={(e) => handleChange(blankData.index, e)}
                displayEmpty
                IconComponent={KeyboardArrowDownIcon}
                sx={{
                  backgroundColor: localAnswers[blankData.index]
                    ? "rgba(25, 118, 210, 0.04)"
                    : "white",
                  borderRadius: "4px",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: localAnswers[blankData.index]
                      ? "primary.main"
                      : "#cccccc",
                    borderWidth: localAnswers[blankData.index] ? "1px" : "1px",
                    borderStyle: localAnswers[blankData.index]
                      ? "solid"
                      : "dashed",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: localAnswers[blankData.index]
                      ? "primary.dark"
                      : "#aaaaaa",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "primary.main",
                    borderWidth: "1px",
                  },
                  "& .MuiSelect-select": {
                    padding: "8px 14px",
                    fontWeight: localAnswers[blankData.index] ? 500 : 400,
                    color: localAnswers[blankData.index]
                      ? "primary.dark"
                      : "text.secondary",
                  },
                  transition: "all 0.2s ease",
                }}
                MenuProps={{
                  PaperProps: {
                    sx: {
                      borderRadius: "8px",
                      marginTop: "4px",
                      boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
                    },
                  },
                }}
              >
                <MenuItem value="" disabled sx={{ fontStyle: "italic" }}>
                  Select an option...
                </MenuItem>
                {blankData.options.map((option, optIndex) => (
                  <MenuItem
                    key={optIndex}
                    value={option}
                    sx={{
                      transition: "all 0.1s ease",
                      "&.Mui-selected": {
                        backgroundColor: "rgba(25, 118, 210, 0.08)",
                        fontWeight: 500,
                      },
                      "&.Mui-selected:hover": {
                        backgroundColor: "rgba(25, 118, 210, 0.12)",
                      },
                    }}
                  >
                    {option}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          );
        }
      }
    });

    return content;
  };

  return (
    <Box
      sx={{
        maxWidth: "800px",
        mx: "auto",
        p: { xs: 2, sm: 3 },
        minHeight: "70vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Task header */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          mb: 3,
          pb: 2,
          borderBottom: "1px solid rgba(0,0,0,0.08)",
        }}
      >
        <Typography
          variant="h5"
          sx={{
            mb: 1,
            fontWeight: 600,
            color: "text.primary",
          }}
        >
          Reading & Writing: Fill In The Blanks
        </Typography>

        <Typography
          variant="subtitle1"
          color="text.secondary"
          sx={{ fontWeight: 500 }}
        >
          {question?.category?.description ||
            "Select the appropriate options to complete the text"}
        </Typography>
      </Box>

      {/* Instructions */}
      <Paper
        elevation={2}
        sx={{
          p: { xs: 2, sm: 3 },
          mb: 3,
          borderRadius: 2,
          backgroundColor: "#f8f9fa",
          border: "1px solid #e6e8eb",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            p: 1.5,
            backgroundColor: "rgba(25, 118, 210, 0.05)",
            borderRadius: 1,
          }}
        >
          <InfoOutlinedIcon color="primary" sx={{ mr: 1, fontSize: 20 }} />
          <Typography variant="body2" color="text.secondary">
            Read the text and select the most appropriate word from each
            dropdown menu to complete the sentence.
          </Typography>
        </Box>
      </Paper>

      {/* Main content area */}
      <Paper
        elevation={1}
        sx={{
          p: { xs: 2, sm: 3 },
          borderRadius: 2,
          backgroundColor: "white",
          flex: 1,
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            color="text.secondary"
            sx={{
              fontWeight: 500,
              display: "flex",
              alignItems: "center",
            }}
          >
            <TextFormatIcon sx={{ mr: 1 }} />
            Complete the text:
          </Typography>

          <Chip
            label={`${filledBlanks}/${totalBlanks} filled`}
            color={completionPercentage === 100 ? "success" : "primary"}
            variant={completionPercentage === 100 ? "filled" : "outlined"}
            size="small"
            sx={{ fontWeight: 500 }}
          />
        </Box>

        <Divider sx={{ mb: 3 }} />

        <Box
          sx={{
            fontSize: "17px",
            lineHeight: 1.8,
            color: "text.primary",
            mb: 3,
            p: { xs: 2, sm: 3 },
            backgroundColor: "#fafafa",
            borderRadius: 2,
            border: "1px solid #f0f0f0",
            "& > *": {
              verticalAlign: "middle",
            },
          }}
        >
          {renderContent()}
        </Box>
      </Paper>
    </Box>
  );
};

export default ReadingWritingFillInTheBlanks;
