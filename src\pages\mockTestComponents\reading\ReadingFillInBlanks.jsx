import { useState, useEffect } from "react";
import {
  DndContext,
  closestCenter,
  MouseSensor,
  TouchSensor,
  DragOverlay,
  useSensor,
  useSensors,
  useDraggable,
  useDroppable,
} from "@dnd-kit/core";
import { CSS } from "@dnd-kit/utilities";
import { Box, Typography, Paper } from "@mui/material";
import PropTypes from "prop-types";

const DraggableOption = ({ id, text }) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useDraggable({
      id: id,
    });

  const style = {
    transform: CSS.Transform.toString(transform),
    padding: "10px 16px",
    backgroundColor: "#f0f0f0",
    border: "1px solid #ccc",
    borderRadius: "4px",
    cursor: "grab",
    margin: "4px",
    display: "inline-block",
    touchAction: "none",
    opacity: isDragging ? 0.5 : 1,
    width: "fit-content",
    minWidth: "100px",
    textAlign: "center",
    fontSize: "16px",
    fontWeight: "500",
  };

  return (
    <div ref={setNodeRef} style={style} {...listeners} {...attributes}>
      {text}
    </div>
  );
};

DraggableOption.propTypes = {
  id: PropTypes.string.isRequired,
  text: PropTypes.string.isRequired,
};

const DroppableBlank = ({ id, value, children }) => {
  const { setNodeRef, isOver: droppingOver } = useDroppable({
    id: id,
  });

  return (
    <div
      ref={setNodeRef}
      style={{
        display: "inline-flex",
        alignItems: "center",
        justifyContent: "center",
        minWidth: "120px",
        height: "45px",
        border: `2px ${droppingOver ? "solid" : "dashed"} ${
          droppingOver ? "#140342" : value ? "#140342" : "#ccc"
        }`,
        borderRadius: "8px",
        margin: "0 6px",
        padding: "8px 12px",
        backgroundColor: droppingOver ? "#e8f0fe" : value ? "#f8f9ff" : "white",
        transition: "all 0.3s ease",
        verticalAlign: "middle",
        boxShadow: value
          ? "0 2px 8px rgba(20, 3, 66, 0.15)"
          : droppingOver
          ? "0 2px 8px rgba(20, 3, 66, 0.25)"
          : "0 1px 3px rgba(0,0,0,0.1)",
      }}
    >
      {children}
    </div>
  );
};

DroppableBlank.propTypes = {
  id: PropTypes.string.isRequired,
  value: PropTypes.string,
  children: PropTypes.node,
};

const ReadingFillInBlanks = ({ setAnswer, onNext, setGoNext, question }) => {
  // Create draggable options from all dropdown options
  const createOptions = () => {
    if (!question?.dropdownOptions) return [];

    // Get all unique options from all dropdown sets
    const allOptions = new Set();
    question.dropdownOptions.forEach((dropdown) => {
      dropdown.options.forEach((option) => {
        allOptions.add(option);
      });
    });

    // Convert to array and create option objects
    return Array.from(allOptions).map((text, index) => ({
      id: `option-${index}-${text}`,
      text,
      isUsed: false,
      blankIndex: null,
    }));
  };

  // Parse blanks from prompt - looking for patterns like "(1) ___", "(2) ___"
  const parsePromptForBlanks = () => {
    if (!question?.prompt) return [];

    const blankPattern = /\((\d+)\)\s*___/g;
    const blanks = [];
    let match;

    while ((match = blankPattern.exec(question.prompt)) !== null) {
      const blankNumber = parseInt(match[1]);
      const dropdownOption = question.dropdownOptions?.find(
        (opt) => opt.blank === blankNumber
      );
      if (dropdownOption) {
        blanks.push({
          position: blankNumber,
          correctAnswer: dropdownOption.correctAnswer,
          originalIndex: blanks.length, // For ordering in the UI
        });
      }
    }

    // Sort by position to maintain correct order
    return blanks.sort((a, b) => a.position - b.position);
  };

  const [options, setOptions] = useState([]);
  const [filledAnswers, setFilledAnswers] = useState([]);
  const [activeId, setActiveId] = useState(null);
  const blanks = parsePromptForBlanks();

  // Reset state when question changes
  useEffect(() => {
    const newOptions = createOptions();
    setOptions(newOptions);
    setFilledAnswers(new Array(blanks.length).fill(null));
    setActiveId(null);
  }, [question?.questionId]);

  useEffect(() => {
    setGoNext();
  }, [setGoNext]);

  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 100,
        tolerance: 5,
      },
    })
  );

  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over) return;

    if (over.id.startsWith("blank-")) {
      const blankIndex = parseInt(over.id.split("-")[1]);

      // If there's already a word in the target blank, swap it back to options
      if (filledAnswers[blankIndex]) {
        setOptions(
          options.map((opt) =>
            opt.id === filledAnswers[blankIndex]
              ? { ...opt, isUsed: false, blankIndex: null }
              : opt
          )
        );
      }

      // Remove word from previous position if it exists
      if (filledAnswers.includes(active.id)) {
        const oldIndex = filledAnswers.indexOf(active.id);
        const newFilledAnswers = [...filledAnswers];
        newFilledAnswers[oldIndex] = null;
        setFilledAnswers(newFilledAnswers);
      }

      // Update options state
      setOptions(
        options.map((opt) =>
          opt.id === active.id ? { ...opt, isUsed: true, blankIndex } : opt
        )
      );

      // Update filled answers
      const newFilledAnswers = [...filledAnswers];
      newFilledAnswers[blankIndex] = active.id;
      setFilledAnswers(newFilledAnswers);

      // Update answer for mocktest
      updateAnswer(newFilledAnswers);
    } else if (over.id === "options-box") {
      // Return word to options box
      const blankIndex = options.find(
        (opt) => opt.id === active.id
      )?.blankIndex;
      if (blankIndex !== null) {
        const newFilledAnswers = [...filledAnswers];
        newFilledAnswers[blankIndex] = null;
        setFilledAnswers(newFilledAnswers);
        updateAnswer(newFilledAnswers);
      }

      setOptions(
        options.map((opt) =>
          opt.id === active.id
            ? { ...opt, isUsed: false, blankIndex: null }
            : opt
        )
      );
    }
  };

  // Update answer in mocktest format
  const updateAnswer = (currentFilledAnswers) => {
    // Format user answers for mocktest API
    const formattedUserAnswers = currentFilledAnswers
      .map((answerId, index) => {
        if (answerId) {
          const selectedOption = options.find((opt) => opt.id === answerId);
          return {
            optionText: selectedOption?.text || "",
            position: index,
          };
        }
        return null;
      })
      .filter((answer) => answer !== null);

    // Format correct answers for mocktest API
    const formattedCorrectAnswers = blanks.map((blank, index) => ({
      correctAnswer: blank.correctAnswer,
      position: index,
    }));

    const answerData = {
      mockTestId: question?.mocktestId,
      questionId: question?.questionId,
      prompt: question?.prompt,
      section: question?.category?.section,
      categoryId: question?.categoryId,
      userId: localStorage.getItem("userId"),
      useranswers: formattedUserAnswers,
      correctAnswer: formattedCorrectAnswers,
      maxScoreIfCorrect: question?.maxScore,
      type: "reading:fill in the blanks",
    };

    setAnswer(answerData);
  };

  // Render content with blanks - parse the prompt and replace numbered blanks
  const renderContentWithBlanks = () => {
    if (!question?.prompt) return null;

    const parts = [];
    const blankPattern = /\((\d+)\)\s*___/g;
    let lastIndex = 0;
    let match;
    let blankIndex = 0;

    while ((match = blankPattern.exec(question.prompt)) !== null) {
      // Add text before the blank
      if (match.index > lastIndex) {
        parts.push(
          <span key={`text-${parts.length}`}>
            {question.prompt.slice(lastIndex, match.index)}
          </span>
        );
      }

      // Add the droppable blank
      parts.push(
        <DroppableBlank
          key={`blank-${blankIndex}`}
          id={`blank-${blankIndex}`}
          value={filledAnswers[blankIndex]}
        >
          {filledAnswers[blankIndex] && (
            <div
              style={{
                padding: "2px 4px",
                margin: "0",
                backgroundColor: "transparent",
                color: "#140342",
                borderRadius: "0",
                width: "auto",
                maxWidth: "100%",
                textAlign: "center",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
                fontSize: "16px",
                fontWeight: "700",
                fontStyle: "italic",
                display: "inline-flex",
                alignItems: "center",
                justifyContent: "center",
                minHeight: "auto",
                border: "none",
                textDecoration: "underline",
                textDecorationColor: "#140342",
                textDecorationStyle: "solid",
                textUnderlineOffset: "2px",
              }}
            >
              {options.find((opt) => opt.id === filledAnswers[blankIndex])
                ?.text || ""}
            </div>
          )}
        </DroppableBlank>
      );

      lastIndex = match.index + match[0].length;
      blankIndex++;
    }

    // Add remaining text after the last blank
    if (lastIndex < question.prompt.length) {
      parts.push(
        <span key={`text-${parts.length}`}>
          {question.prompt.slice(lastIndex)}
        </span>
      );
    }

    return parts;
  };

  const { setNodeRef: setOptionsBoxRef } = useDroppable({
    id: "options-box",
  });

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <Box
        sx={{
          maxWidth: "800px",
          mx: "auto",
          p: { xs: 2, sm: 3 },
          minHeight: "70vh",
          display: "flex",
          flexDirection: "column",
        }}
      >
        {/* Task header */}
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            position: "relative",
            mb: 3,
            pb: 2,
            borderBottom: "1px solid rgba(0,0,0,0.08)",
          }}
        >
          <Typography
            variant="h5"
            sx={{
              mb: 1,
              fontWeight: 600,
              color: "text.primary",
            }}
          >
            Reading: Fill In The Blanks
          </Typography>

          <Typography
            variant="subtitle1"
            color="text.secondary"
            sx={{ fontWeight: 500 }}
          >
            {question?.category?.description ||
              "Fill in the blanks by dragging the words from the options below"}
          </Typography>
        </Box>

        {/* Instructions */}
        <Paper
          elevation={2}
          sx={{
            p: { xs: 2, sm: 3 },
            mb: 3,
            borderRadius: 2,
            backgroundColor: "#f8f9fa",
            border: "1px solid #e6e8eb",
          }}
        >
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ textAlign: "center" }}
          >
            Drag words from the options below to fill in the blanks in the text.
          </Typography>
        </Paper>

        {/* Main content area */}
        <Paper
          elevation={1}
          sx={{
            p: { xs: 2, sm: 3 },
            borderRadius: 2,
            backgroundColor: "white",
            flex: 1,
            mb: 3,
          }}
        >
          {/* Content with blanks */}
          <Box
            sx={{
              marginBottom: "30px",
              lineHeight: "2.5",
              fontSize: "18px",
            }}
          >
            {renderContentWithBlanks()}
          </Box>
        </Paper>

        {/* Draggable options */}
        <Paper
          elevation={1}
          sx={{
            p: 3,
            borderRadius: 2,
            backgroundColor: "#f8f8f8",
            border: "2px solid #140342",
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              textAlign: "center",
              margin: "0 0 15px 0",
              fontWeight: "bold",
              fontSize: "16px",
              color: "#666",
            }}
          >
            Drag words from here to fill the blanks:
          </Typography>

          <Box
            ref={setOptionsBoxRef}
            sx={{
              display: "flex",
              flexWrap: "wrap",
              gap: "10px",
              alignItems: "center",
              justifyContent: "center",
              minHeight: "60px",
            }}
          >
            {options
              ?.filter((opt) => !opt.isUsed)
              .map((option) => (
                <DraggableOption
                  key={option.id}
                  id={option.id}
                  text={option.text}
                />
              ))}
          </Box>
        </Paper>
      </Box>

      <DragOverlay>
        {activeId ? (
          <div
            style={{
              padding: "8px 16px",
              backgroundColor: "#f0f0f0",
              border: "1px solid #ccc",
              borderRadius: "4px",
              cursor: "grab",
              width: "fit-content",
              minWidth: "100px",
              textAlign: "center",
              fontSize: "16px",
            }}
          >
            {options.find((opt) => opt.id === activeId)?.text || activeId}
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  );
};

ReadingFillInBlanks.propTypes = {
  setAnswer: PropTypes.func.isRequired,
  onNext: PropTypes.func,
  setGoNext: PropTypes.func.isRequired,
  question: PropTypes.shape({
    questionId: PropTypes.string.isRequired,
    questionNumber: PropTypes.string,
    prompt: PropTypes.string.isRequired,
    section: PropTypes.string,
    difficulty: PropTypes.string,
    maxScore: PropTypes.number,
    duration: PropTypes.number,
    dropdownOptions: PropTypes.arrayOf(
      PropTypes.shape({
        blank: PropTypes.number.isRequired,
        correctAnswer: PropTypes.string.isRequired,
        options: PropTypes.arrayOf(PropTypes.string).isRequired,
      })
    ).isRequired,
    categoryId: PropTypes.string,
    mocktestId: PropTypes.string,
    type: PropTypes.string,
    questionName: PropTypes.string,
    category: PropTypes.shape({
      section: PropTypes.string,
      name: PropTypes.string,
      description: PropTypes.string,
    }),
  }).isRequired,
};

export default ReadingFillInBlanks;
