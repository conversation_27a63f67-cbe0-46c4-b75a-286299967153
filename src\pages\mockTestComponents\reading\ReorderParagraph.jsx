import React, { useEffect, useState } from "react";
import { Typo<PERSON>, Box, Paper, Divider } from "@mui/material";
import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
  arrayMove,
} from "@dnd-kit/sortable";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import DragIndicatorIcon from "@mui/icons-material/DragIndicator";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import SwapVertIcon from "@mui/icons-material/SwapVert";

const ReorderParagraphs = ({ setAnswer, onNext, setGoNext, question }) => {
  // Initialize sentences from the question prop
  const [sentences, setSentences] = useState([]);

  useEffect(() => {
    // Update sentences when question changes
    if (question?.sentences && Array.isArray(question.sentences)) {
      // Shuffle the sentences for initial random order
      const shuffled = [...question.sentences].sort(() => Math.random() - 0.5);
      setSentences(shuffled);
    } else {
      setSentences([]);
    }
  }, [question?.questionId]);

  useEffect(() => {
    if (typeof setGoNext === "function") {
      setGoNext(false);
    }
  }, [setGoNext]);

  // Configure DnD sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5, // Minimum drag distance before activation
      },
    })
  );

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (!active || !over || active.id === over.id) return;

    const activeIndex = sentences.findIndex(
      (sentence) => sentence.id === active.id
    );
    const overIndex = sentences.findIndex(
      (sentence) => sentence.id === over.id
    );
    if (activeIndex === -1 || overIndex === -1) return;

    const newOrder = arrayMove(sentences, activeIndex, overIndex);
    setSentences(newOrder);

    // Format user answers as array of sentence IDs in current order
    const userAnswerIds = newOrder.map((sentence) => sentence.id);

    const answerData = {
      mockTestId: question?.mocktestId,
      questionId: question?.questionId,
      prompt: question?.prompt,
      section: question?.category?.section,
      categoryId: question?.categoryId,
      userId: localStorage.getItem("userId"),
      useranswers: userAnswerIds,
      correctAnswer: question?.correctOrder || [],
      maxScoreIfCorrect: question?.maxScore,
      type: "re-order paragraphs",
    };

    setAnswer(answerData);
  };

  const SortableItem = ({ sentence, index }) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging,
    } = useSortable({
      id: sentence.id,
    });

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
      zIndex: isDragging ? 1000 : 1,
    };

    return (
      <Paper
        ref={setNodeRef}
        style={style}
        sx={{
          p: 0,
          mb: 1.5,
          borderRadius: "6px",
          backgroundColor: isDragging ? "rgba(25, 118, 210, 0.08)" : "white",
          border: "1px solid",
          borderColor: isDragging ? "primary.main" : "#e0e0e0",
          boxShadow: isDragging ? 3 : 0,
          display: "flex",
          overflow: "hidden",
          transition:
            "background-color 0.2s, border-color 0.2s, box-shadow 0.2s",
          "&:hover": {
            borderColor: "primary.light",
            backgroundColor: "rgba(25, 118, 210, 0.04)",
          },
        }}
      >
        {/* Drag handle */}
        <Box
          {...attributes}
          {...listeners}
          sx={{
            backgroundColor: isDragging ? "primary.main" : "grey.100",
            color: isDragging ? "white" : "text.secondary",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            p: 1.5,
            cursor: "grab",
            transition: "background-color 0.2s, color 0.2s",
            "&:active": {
              cursor: "grabbing",
            },
          }}
        >
          <DragIndicatorIcon />
          <Typography variant="caption" sx={{ mt: 0.5, fontWeight: "medium" }}>
            {index + 1}
          </Typography>
        </Box>

        {/* Sentence content */}
        <Box
          sx={{
            py: 2,
            px: 3,
            flex: 1,
            display: "flex",
            alignItems: "center",
          }}
        >
          <Typography
            variant="body1"
            sx={{
              lineHeight: 1.6,
              textAlign: "left",
            }}
          >
            {sentence.text}
          </Typography>
        </Box>
      </Paper>
    );
  };

  // Only show no data message if truly no sentences
  if (!Array.isArray(sentences) || sentences.length === 0) {
    return (
      <Box
        sx={{
          maxWidth: "800px",
          mx: "auto",
          p: { xs: 2, sm: 3 },
          minHeight: "70vh",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Paper
          elevation={1}
          sx={{
            p: 4,
            borderRadius: 2,
            textAlign: "center",
            backgroundColor: "#f8f9fa",
          }}
        >
          <Typography color="text.secondary">
            No sentences available to reorder.
          </Typography>
        </Paper>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        maxWidth: "800px",
        mx: "auto",
        p: { xs: 2, sm: 3 },
        minHeight: "70vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Task header */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          mb: 3,
          pb: 2,
          borderBottom: "1px solid rgba(0,0,0,0.08)",
        }}
      >
        <Typography
          variant="h5"
          sx={{
            mb: 1,
            fontWeight: 600,
            color: "text.primary",
          }}
        >
          Reorder Paragraphs
        </Typography>

        <Typography
          variant="subtitle1"
          color="text.secondary"
          sx={{ fontWeight: 500 }}
        >
          {question?.category?.description ||
            "Arrange the sentences in the correct order"}
        </Typography>
      </Box>

      {/* Instructions */}
      <Paper
        elevation={2}
        sx={{
          p: { xs: 2, sm: 3 },
          mb: 3,
          borderRadius: 2,
          backgroundColor: "#f8f9fa",
          border: "1px solid #e6e8eb",
        }}
      >
        {question?.prompt && (
          <Typography
            variant="body1"
            sx={{
              fontWeight: 500,
              lineHeight: 1.6,
              mb: 2,
            }}
          >
            {question.prompt}
          </Typography>
        )}

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            p: 1.5,
            backgroundColor: "rgba(25, 118, 210, 0.05)",
            borderRadius: 1,
          }}
        >
          <InfoOutlinedIcon color="primary" sx={{ mr: 1, fontSize: 20 }} />
          <Typography variant="body2" color="text.secondary">
            Drag and drop the sentences to arrange them in the correct order.
            The first sentence should be at the top.
          </Typography>
        </Box>
      </Paper>

      {/* Drag and drop area */}
      <Paper
        elevation={1}
        sx={{
          p: { xs: 2, sm: 3 },
          borderRadius: 2,
          backgroundColor: "white",
          flex: 1,
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            color="text.secondary"
            sx={{
              fontWeight: 500,
              display: "flex",
              alignItems: "center",
            }}
          >
            <SwapVertIcon sx={{ mr: 1 }} />
            Arrange the sentences:
          </Typography>

          <Typography
            variant="caption"
            color="text.secondary"
            sx={{
              backgroundColor: "grey.100",
              px: 1,
              py: 0.5,
              borderRadius: 1,
              fontWeight: 500,
            }}
          >
            {sentences.length} sentences
          </Typography>
        </Box>

        <Divider sx={{ mb: 3 }} />

        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={sentences.map((sentence) => sentence.id)}
            strategy={verticalListSortingStrategy}
          >
            {sentences.map((sentence, index) => (
              <SortableItem
                key={sentence.id}
                sentence={sentence}
                index={index}
              />
            ))}
          </SortableContext>
        </DndContext>
      </Paper>
    </Box>
  );
};

export default ReorderParagraphs;
