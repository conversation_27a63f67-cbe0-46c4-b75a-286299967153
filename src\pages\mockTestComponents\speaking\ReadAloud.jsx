import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  Button,
  Typography,
  CircularProgress,
  Snackbar,
  Alert,
  Paper,
  IconButton,
} from "@mui/material";
import MicIcon from "@mui/icons-material/Mic";
import StopIcon from "@mui/icons-material/Stop";
import DoneIcon from "@mui/icons-material/Done";
import TimerIcon from "@mui/icons-material/Timer";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import LinearProgress from "@mui/material/LinearProgress";
import { uploadFile } from "@/api/services/controller";

function ReadAloud({
  setAnswer,
  onNext,
  setGoNext,
  question,
  onProcessingStateChange,
  handleUploadAnswer,
  onSpeakingStateChange,
  mockTestId,
  attemptNumber,
  onAnswerSubmitted,
}) {
  const [isPreparing, setIsPreparing] = useState(false);
  const [timer, setTimer] = useState(35); // 35 seconds preparation time as per PTE guide
  const [phase, setPhase] = useState("preparation");
  const [recordingTimer, setRecordingTimer] = useState(32); // 30-35 seconds recording time
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [audioURL, setAudioURL] = useState(null);
  const [canMoveToNext, setCanMoveToNext] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingComplete, setProcessingComplete] = useState(false);
  const [processingInBackground, setProcessingInBackground] = useState(false);
  const [showBackgroundProcessingNotice, setShowBackgroundProcessingNotice] =
    useState(false);
  const [recordingComplete, setRecordingComplete] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [processingStatus, setProcessingStatus] = useState("");

  // Refs
  const mediaRecorderRef = useRef(null);
  const chunksRef = useRef([]);
  const processingRef = useRef(false);
  const loadingIntervalRef = useRef(null);
  const answerSubmittedRef = useRef(false);

  const analyzeSpeech = async (audioBlob, text) => {
    try {
      setProcessingStatus("Analyzing pronunciation and fluency...");

      const formData = new FormData();
      formData.append("audio", audioBlob, "recording.wav");
      formData.append("text", text);

      const response = await fetch(
        "https://deep-ai.up.railway.app/api/speech-analysis",
        {
          method: "POST",
          body: formData,
          headers: {
            Accept: "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Speech analysis failed");
      }

      const data = await response.json();
      console.log("Full AI response:", data);

      if (!data?.speech_score?.pte_score) {
        // Handle the error case by returning the full response structure with zero scores
        return {
          fullResponse: data,
          pronunciation: 0,
          fluency: 0,
          similarity_score: 0,
          content_accuracy: data?.content?.content_accuracy || 0,
          word_count: data?.content?.word_count || 0,
          missing_words: data?.content?.missing_words || [],
          extra_words: data?.content?.extra_words || [],
          pronunciation_details: data?.speech_score?.pronunciation || {},
          fluency_details: data?.speech_score?.fluency || {},
        };
      }

      // Extract complete scores and details
      const { pte_score, pronunciation, fluency } = data.speech_score;
      const contentData = data.content || {};

      // Create comprehensive scores object with full AI response
      const completeScores = {
        // Main PTE scores
        pronunciation: pte_score.pronunciation,
        fluency: pte_score.fluency,
        similarity_score: contentData.similarity_score || 0,

        // Content analysis
        content_accuracy: contentData.content_accuracy || 0,
        word_count: contentData.word_count || 0,
        missing_words: contentData.missing_words || [],
        extra_words: contentData.extra_words || [],

        // Detailed pronunciation breakdown
        pronunciation_details: {
          overall_score: pronunciation?.overall_score || 0,
          vowel_accuracy: pronunciation?.vowel_accuracy || 0,
          consonant_accuracy: pronunciation?.consonant_accuracy || 0,
          word_stress: pronunciation?.word_stress || 0,
          intonation: pronunciation?.intonation || 0,
          problematic_words: pronunciation?.problematic_words || [],
        },

        // Detailed fluency breakdown
        fluency_details: {
          overall_score: fluency?.overall_score || 0,
          speaking_rate: fluency?.speaking_rate || 0,
          rhythm: fluency?.rhythm || 0,
          pauses: fluency?.pauses || 0,
          word_emphasis: fluency?.word_emphasis || 0,
          pause_analysis: fluency?.pause_analysis || {},
        },

        // Store the complete raw response for future reference
        fullResponse: data,
      };

      return completeScores;
    } catch (error) {
      console.error("Speech analysis error:", error);
      // Return comprehensive zero scores structure if there's an error
      return {
        pronunciation: 0,
        fluency: 0,
        similarity_score: 0,
        content_accuracy: 0,
        word_count: 0,
        missing_words: [],
        extra_words: [],
        pronunciation_details: {
          overall_score: 0,
          vowel_accuracy: 0,
          consonant_accuracy: 0,
          word_stress: 0,
          intonation: 0,
          problematic_words: [],
        },
        fluency_details: {
          overall_score: 0,
          speaking_rate: 0,
          rhythm: 0,
          pauses: 0,
          word_emphasis: 0,
          pause_analysis: {},
        },
        fullResponse: { error: error.message },
      };
    }
  };

  const simulateLoadingProgress = () => {
    // Clear any existing interval
    if (loadingIntervalRef.current) {
      clearInterval(loadingIntervalRef.current);
    }

    // Reset progress
    setLoadingProgress(0);

    // Simulate progress that never quite reaches 100% until complete
    loadingIntervalRef.current = setInterval(() => {
      setLoadingProgress((prev) => {
        if (prev < 90) {
          // Move faster at the beginning, slower as it approaches 90%
          const increment = (90 - prev) / 10;
          return prev + Math.max(0.5, increment);
        }
        return prev;
      });
    }, 300);
  };

  const processRecording = async (blob) => {
    if (processingRef.current) return;
    processingRef.current = true;
    setIsProcessing(true);

    // Only block UI navigation if we're not submitting in background
    if (!processingInBackground) {
      onProcessingStateChange(true);
    }

    simulateLoadingProgress();

    try {
      const uri = URL.createObjectURL(blob);
      setAudioURL(uri);

      setProcessingStatus("Analyzing pronunciation and fluency...");

      // Process speech analysis with original audio (no compression)
      const scores = await analyzeSpeech(blob, question.prompt);
      setProcessingStatus("Uploading recording...");

      // Handle file upload
      const fileUrl = await blobToBase64(blob);
      const uploadedUrl = await uploadFile("", fileUrl, "audio/wav");

      const userId = localStorage.getItem("userId");

      const answerData = {
        score: String(
          scores.pronunciation + scores.fluency + scores.similarity_score * 90
        ), // Total score calculation
        answer: "", // No text answer for read aloud
        questionId: question?.questionId || "",
        userId: userId,
        section: question?.section || "speaking",
        mockTestId: mockTestId || question?.mocktestId || "",
        mocktestId: mockTestId || question?.mocktestId || "",
        attemptNumber: attemptNumber,
        type: question?.type || "read-aloud",
        prompt: question?.prompt,
        categoryId: question?.categoryId,
        useranswers: [], // No text answers for speaking
        correctAnswer: question?.options || [],
        maxScoreIfCorrect: question?.maxScore || 1,
        media: {
          url: uploadedUrl,
          type: "audio",
        },
        pteScores: scores, // Store complete scores with all details
        taskType: "read-aloud",
        additionalProps: {
          speechAnalysis: scores.fullResponse, // Store complete AI response
          taskSpecificData: {
            textToRead: question?.prompt,
            recordingDuration: blob.size, // Approximate duration info
          },
        },
      };

      setProcessingStatus("Saving results...");

      // Use the handleUploadAnswer function from parent component
      const uploadResult = await handleUploadAnswer(answerData);

      if (uploadResult) {
        answerSubmittedRef.current = true;
        if (onAnswerSubmitted) onAnswerSubmitted();

        // Complete the loading animation
        clearInterval(loadingIntervalRef.current);
        setLoadingProgress(100);
        setProcessingStatus("Processing complete.");

        setTimeout(() => {
          setProcessingComplete(true);
          setCanMoveToNext(true);
          setProcessingInBackground(false);
        }, 500);
      } else {
        throw new Error("Failed to upload answer");
      }
    } catch (error) {
      console.error("Error processing recording:", error);
      clearInterval(loadingIntervalRef.current);
      setLoadingProgress(0);
      setProcessingStatus("Error processing. Please try again.");

      // Still submit the answer with zero scores
      try {
        const userId = localStorage.getItem("userId");
        const zeroScores = {
          pronunciation: 0,
          fluency: 0,
          similarity_score: 0,
          content_accuracy: 0,
          word_count: 0,
          missing_words: [],
          extra_words: [],
          pronunciation_details: {},
          fluency_details: {},
          fullResponse: { error: error.message },
        };

        const errorAnswerData = {
          score: "0",
          answer: "",
          questionId: question?.questionId || "",
          userId: userId,
          section: question?.section || "speaking",
          mockTestId: mockTestId || question?.mocktestId || "",
          mocktestId: mockTestId || question?.mocktestId || "",
          attemptNumber: attemptNumber,
          type: question?.type || "read-aloud",
          prompt: question?.prompt,
          categoryId: question?.categoryId,
          useranswers: [],
          correctAnswer: question?.options || [],
          maxScoreIfCorrect: question?.maxScore || 1,
          pteScores: zeroScores,
          taskType: "read-aloud",
          additionalProps: {
            error: error.message,
            speechAnalysis: zeroScores.fullResponse,
          },
        };

        await handleUploadAnswer(errorAnswerData);
        if (onAnswerSubmitted) onAnswerSubmitted();
        setProcessingComplete(true);
        setCanMoveToNext(true);
      } catch (submitError) {
        console.error("Error submitting zero scores:", submitError);
      }
    } finally {
      setIsProcessing(false);
      processingRef.current = false;
      onProcessingStateChange(false);
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorderRef.current.onstop = async () => {
        const blob = new Blob(chunksRef.current, { type: "audio/wav" });
        setRecordingComplete(true);
        processRecording(blob);
        chunksRef.current = [];
      };

      mediaRecorderRef.current.start();
      setIsSpeaking(true);

      // Notify parent component
      if (onSpeakingStateChange) {
        onSpeakingStateChange(true);
      }
    } catch (error) {
      console.error("Error starting recording:", error);
    }
  };

  const stopRecording = () => {
    if (
      mediaRecorderRef.current &&
      mediaRecorderRef.current.state === "recording"
    ) {
      mediaRecorderRef.current.stop();
      setIsSpeaking(false);

      // Notify parent component
      if (onSpeakingStateChange) {
        onSpeakingStateChange(false);
      }
    }
  };

  const handleEarlySubmission = () => {
    if (isSpeaking) {
      stopRecording();
      // No popup needed for early submission
    }
  };

  const handleMoveToNextQuestion = () => {
    // If still processing, switch to background mode
    if (processingRef.current && !answerSubmittedRef.current) {
      setProcessingInBackground(true);
      setShowBackgroundProcessingNotice(true);
      onProcessingStateChange(false); // Don't block UI navigation
    }

    // Always allow moving to next question
    if (onNext && typeof onNext === "function") {
      onNext();
    }
  };

  function blobToBase64(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onloadend = () => resolve(reader.result.split(",")[1]);
      reader.onerror = (error) => reject(error);
    });
  }

  useEffect(() => {
    setGoNext(() => handleMoveToNextQuestion);
  }, [setGoNext]);

  useEffect(() => {
    if (timer === 0) {
      if (phase === "preparation") {
        setPhase("speaking");
        setIsPreparing(false);
        setTimer(recordingTimer); // Use PTE recording time (32 seconds)
        startRecording();
      } else {
        stopRecording();
      }
    } else if (timer > 0) {
      const interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [timer, phase, recordingTimer]);

  useEffect(() => {
    // Reset timer and phase for new question
    setTimer(35); // Reset to preparation time
    setPhase("preparation");
    setIsPreparing(true);
    setIsSpeaking(false);
    setIsProcessing(false);
    setProcessingComplete(false);
    setProcessingInBackground(false);
    setRecordingComplete(false);
    setAudioURL(null);
    setCanMoveToNext(false);
    setLoadingProgress(0);
    setProcessingStatus("");
    answerSubmittedRef.current = false;

    if (loadingIntervalRef.current) {
      clearInterval(loadingIntervalRef.current);
    }
  }, [question?.questionId]);

  // Enhanced Interactive Speaking Circle UI
  const renderSpeakingCircle = () => {
    if (!isSpeaking && !isPreparing) return null;

    const phaseMaxTime = phase === "preparation" ? 35 : recordingTimer;
    const progress = ((phaseMaxTime - timer) / phaseMaxTime) * 100;

    // Determine colors based on phase and progress
    const getCircleColor = () => {
      if (phase === "preparation") return "#3f51b5"; // Preparation blue

      // For speaking phase, change color as time runs out
      if (timer > 30) return "#4caf50"; // Plenty of time - green
      if (timer > 15) return "#ff9800"; // Getting low - orange
      return "#f44336"; // Running out - red
    };

    const circleColor = getCircleColor();
    const isNearlyDone = phase === "speaking" && timer <= 10;

    return (
      <Box
        sx={{
          position: "relative",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          cursor: isSpeaking ? "pointer" : "default",
          mt: 3,
        }}
      >
        {/* Phase indicator */}
        <Paper
          elevation={1}
          sx={{
            px: 3,
            py: 1,
            mb: 3,
            borderRadius: 2,
            backgroundColor: phase === "preparation" ? "#e3f2fd" : "#fff8e1",
            border: "1px solid",
            borderColor: phase === "preparation" ? "#90caf9" : "#ffe082",
          }}
        >
          <Typography
            variant="body1"
            sx={{ fontWeight: "medium", display: "flex", alignItems: "center" }}
          >
            {phase === "preparation" ? (
              <>
                <TimerIcon sx={{ mr: 1, color: "#1976d2" }} />
                Prepare your response
              </>
            ) : (
              <>
                <MicIcon sx={{ mr: 1, color: "#d32f2f" }} />
                Speaking in progress
              </>
            )}
          </Typography>
        </Paper>

        {/* Interactive circle */}
        <Box
          sx={{
            position: "relative",
            display: "inline-flex",
            boxShadow: isSpeaking ? 2 : 0,
            borderRadius: "50%",
            p: 2,
            backgroundColor: "rgba(255, 255, 255, 0.8)",
            animation: isNearlyDone ? "pulse 1.5s infinite" : "none",
            "@keyframes pulse": {
              "0%": { boxShadow: `0 0 0 0 rgba(244, 67, 54, 0.4)` },
              "70%": { boxShadow: `0 0 0 10px rgba(244, 67, 54, 0)` },
              "100%": { boxShadow: `0 0 0 0 rgba(244, 67, 54, 0)` },
            },
          }}
          onClick={isSpeaking ? handleEarlySubmission : undefined}
        >
          <CircularProgress
            variant="determinate"
            value={progress}
            size={120}
            thickness={5}
            sx={{
              color: circleColor,
              transition: "transform 0.3s, box-shadow 0.3s",
              "&:hover": isSpeaking
                ? {
                    transform: "scale(1.05)",
                  }
                : {},
            }}
          />

          {/* Icon in center */}
          {isSpeaking && (
            <Box
              sx={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                zIndex: 1,
                backgroundColor: "rgba(255,255,255,0.8)",
                borderRadius: "50%",
                p: 1.5,
                boxShadow: 1,
                "&:hover": {
                  backgroundColor: "rgba(255,255,255,1)",
                  boxShadow: 2,
                },
              }}
            >
              <StopIcon color="error" sx={{ fontSize: 28 }} />
            </Box>
          )}

          <Box
            sx={{
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              position: "absolute",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              flexDirection: "column",
            }}
          >
            <Typography
              variant="h3"
              component="div"
              sx={{ fontWeight: "bold" }}
            >
              {timer}
            </Typography>
            <Typography variant="body2" component="div" color="text.secondary">
              seconds
            </Typography>
          </Box>
        </Box>

        {/* Action hint */}
        <Typography
          variant="body2"
          sx={{
            mt: 2,
            fontWeight: "medium",
            color: isSpeaking ? "error.main" : "text.secondary",
          }}
        >
          {phase === "preparation"
            ? "Preparing... Read the text carefully"
            : "Click the circle to finish recording"}
        </Typography>
      </Box>
    );
  };

  // Enhanced Processing UI with visual feedback
  const renderProcessingUI = () => {
    if (!recordingComplete) return null;

    return (
      <Paper
        elevation={2}
        sx={{
          mt: 5,
          p: 3,
          borderRadius: 2,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          maxWidth: "550px",
          mx: "auto",
          background: processingComplete ? "#f8f8f8" : "#f8f8f8",
          border: "1px solid #e0e0e0",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            mb: 3,
            gap: 2,
          }}
        >
          {processingComplete ? (
            <DoneIcon sx={{ color: "success.main", fontSize: 28 }} />
          ) : (
            <CircularProgress size={28} />
          )}
          <Typography
            variant="h6"
            sx={{
              fontWeight: 500,
              color: processingComplete ? "success.dark" : "text.primary",
            }}
          >
            {processingComplete ? "Complete" : "Processing Your Response"}
          </Typography>
        </Box>

        {!processingComplete && (
          <Box sx={{ width: "100%", mb: 3 }}>
            <Box
              sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}
            >
              <Typography variant="body2" color="text.secondary">
                Progress
              </Typography>
              <Typography variant="body2" fontWeight="medium">{`${Math.round(
                loadingProgress
              )}%`}</Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={loadingProgress}
              sx={{
                height: 6,
                borderRadius: 3,
                mb: 1,
                backgroundColor: "rgba(0,0,0,0.05)",
              }}
            />

            <Box
              sx={{
                mt: 2,
                p: 2,
                backgroundColor: "rgba(0,0,0,0.02)",
                borderRadius: 2,
                borderLeft: "3px solid",
                borderColor: "info.light",
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  display: "flex",
                  alignItems: "center",
                  fontWeight: 500,
                }}
              >
                <TimerIcon
                  fontSize="small"
                  sx={{ mr: 1, color: "info.main" }}
                />
                {processingStatus || "Analyzing your response..."}
              </Typography>
            </Box>
          </Box>
        )}

        <Box
          sx={{
            display: "flex",
            gap: 2,
            mt: processingComplete ? 1 : 0,
          }}
        >
          <Button
            variant={processingComplete ? "contained" : "outlined"}
            color="primary"
            onClick={handleMoveToNextQuestion}
            endIcon={<ArrowForwardIcon />}
            sx={{
              py: 1,
              px: 3,
              borderRadius: 2,
              fontWeight: processingComplete ? "bold" : "medium",
            }}
          >
            Next Question
          </Button>
        </Box>
      </Paper>
    );
  };

  return (
    <Box
      sx={{
        textAlign: "center",
        padding: { xs: 2, sm: 3 },
        maxWidth: "800px",
        mx: "auto",
        minHeight: "70vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Task header with visual cue for current phase */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          mb: 3,
          pb: 2,
          borderBottom: "1px solid rgba(0,0,0,0.08)",
        }}
      >
        <Typography
          variant="h5"
          sx={{
            mb: 1,
            fontWeight: 600,
            color: "text.primary",
          }}
        >
          Read Aloud
        </Typography>

        <Typography
          variant="subtitle1"
          color="text.secondary"
          sx={{ fontWeight: 500 }}
        >
          {question.category?.description || "Read the text aloud"}
        </Typography>
      </Box>

      {/* Main content area with prompt and interaction components */}
      <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
        {/* Only show the prompt if we're not in post-recording phase */}
        {(!recordingComplete || (recordingComplete && !processingComplete)) && (
          <Paper
            elevation={2}
            sx={{
              p: { xs: 2, sm: 3 },
              mb: { xs: 3, sm: 4 },
              maxWidth: "750px",
              mx: "auto",
              borderRadius: 2,
              backgroundColor: "#f8f9fa",
              borderLeft: "4px solid",
              borderLeftColor:
                phase === "preparation"
                  ? "info.main"
                  : phase === "speaking"
                  ? "error.light"
                  : "grey.300",
            }}
          >
            <Typography
              variant="body1"
              sx={{
                lineHeight: 1.6,
                fontSize: { xs: "1rem", sm: "1.1rem" },
                fontWeight: 400,
                color: "text.primary",
              }}
            >
              {question.prompt}
            </Typography>
          </Paper>
        )}

        {/* Interactive Speaking Circle */}
        {renderSpeakingCircle()}

        {/* Processing UI */}
        {recordingComplete && renderProcessingUI()}
      </Box>

      {/* Background processing notification */}
      <Snackbar
        open={showBackgroundProcessingNotice}
        autoHideDuration={6000}
        onClose={() => setShowBackgroundProcessingNotice(false)}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={() => setShowBackgroundProcessingNotice(false)}
          severity="info"
          sx={{ width: "100%" }}
        >
          Your response is being processed in the background
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default ReadAloud;
