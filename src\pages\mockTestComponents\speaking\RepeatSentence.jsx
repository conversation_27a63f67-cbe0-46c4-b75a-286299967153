import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  Button,
  Typography,
  CircularProgress,
  Snackbar,
  Alert,
  Paper,
  LinearProgress,
} from "@mui/material";
import DoneIcon from "@mui/icons-material/Done";
import VolumeUpIcon from "@mui/icons-material/VolumeUp";
import MicIcon from "@mui/icons-material/Mic";
import StopIcon from "@mui/icons-material/Stop";
import TimerIcon from "@mui/icons-material/Timer";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import HeadphonesIcon from "@mui/icons-material/Headphones";
import { uploadFile } from "@/api/services/controller";

function RepeatSentence({
  setAnswer,
  onNext,
  setGoNext,
  question,
  onProcessingStateChange,
  handleUploadAnswer,
  onSpeakingStateChange,
  setshowPopup,
  onManualCompletion,
}) {
  const [isPreparing, setIsPreparing] = useState(false);
  const [isListening, setIsListening] = useState(true);
  const [timer, setTimer] = useState(0); // Start at 0 to wait for audio
  const [phase, setPhase] = useState("listening");
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [audioURL, setAudioURL] = useState(null);
  const [canMoveToNext, setCanMoveToNext] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingComplete, setProcessingComplete] = useState(false);
  const [processingInBackground, setProcessingInBackground] = useState(false);
  const [showBackgroundProcessingNotice, setShowBackgroundProcessingNotice] =
    useState(false);
  const [recordingComplete, setRecordingComplete] = useState(false);
  const [onMediaEnd, setOnMediaEnd] = useState(false);
  const [isAudioPlaying, setIsAudioPlaying] = useState(false);
  const [processingStatus, setProcessingStatus] = useState("");
  const [loadingProgress, setLoadingProgress] = useState(0);

  // Refs
  const audioRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const chunksRef = useRef([]);
  const processingRef = useRef(false);
  const answerSubmittedRef = useRef(false);
  const loadingIntervalRef = useRef(null);
  const pteScoresRef = useRef(null);

  const analyzeSpeech = async (audioBlob) => {
    try {
      setProcessingStatus("Analyzing pronunciation and accuracy...");

      const formData = new FormData();
      formData.append(
        "audio",
        audioBlob,
        `recording.${audioBlob.type.split("/")[1]}`
      );
      formData.append(
        "context",
        question?.prompt || "This is a response for repeat sentence task"
      );

      const response = await fetch(
        "https://deep-ai.up.railway.app/api/pte/retell-lecture",
        {
          method: "POST",
          body: formData,
        }
      );

      if (!response.ok) {
        throw new Error("Speech analysis failed");
      }

      const data = await response.json();
      if (!data?.speech_score?.pte_score) {
        // Return zero scores if analysis fails according to PTE scoring criteria
        return {
          content: 0, // Content (0-3 points)
          pronunciation: 0, // Pronunciation (0-5 points)
          fluency: 0, // Oral Fluency (0-5 points)
        };
      }

      // Extract scores according to PTE Academic 2025 scoring criteria
      const { pte_score } = data.speech_score;
      const contentScore = Math.min(
        3,
        Math.round(((data.content?.score || 0) / 100) * 3)
      ); // Scale to 0-3

      // Create PTE-compliant scores object
      const pteCompliantScores = {
        content: contentScore, // Content (0-3 points)
        pronunciation: pte_score.pronunciation, // Pronunciation (0-5 points)
        fluency: pte_score.fluency, // Oral Fluency (0-5 points)
      };

      pteScoresRef.current = pteCompliantScores;
      return pteCompliantScores;
    } catch (error) {
      console.error("Speech analysis error:", error);
      // Return zero scores if there's an error - PTE compliant format
      return {
        content: 0, // Content (0-3 points)
        pronunciation: 0, // Pronunciation (0-5 points)
        fluency: 0, // Oral Fluency (0-5 points)
      };
    }
  };

  const simulateLoadingProgress = () => {
    // Clear any existing interval
    if (loadingIntervalRef.current) {
      clearInterval(loadingIntervalRef.current);
    }

    // Reset progress
    setLoadingProgress(0);

    // Simulate progress that never quite reaches 100% until complete
    loadingIntervalRef.current = setInterval(() => {
      setLoadingProgress((prev) => {
        if (prev < 90) {
          const increment = (90 - prev) / 10;
          return prev + Math.max(0.5, increment);
        }
        return prev;
      });
    }, 300);
  };

  const processRecording = async (blob) => {
    if (processingRef.current) return;
    processingRef.current = true;
    setIsProcessing(true);

    // Only block UI navigation if we're not submitting in background
    if (!processingInBackground) {
      onProcessingStateChange?.(true);
    }

    simulateLoadingProgress();
    setRecordingComplete(true);

    try {
      const uri = URL.createObjectURL(blob);
      setAudioURL(uri);

      setProcessingStatus("Processing audio...");

      // Process speech analysis
      const scores = await analyzeSpeech(blob);
      setProcessingStatus("Uploading recording...");

      // Handle file upload
      const fileUrl = await blobToBase64(blob);
      const uploadedUrl = await uploadFile("", fileUrl, blob.type);

      const userId = localStorage.getItem("userId");

      const answerData = {
        media: {
          url: uploadedUrl,
          type: "audio",
        },
        questionId: question?.questionId,
        mockTestId: question?.mocktestId,
        userId: userId,
        pteScores: scores,
        taskType: "repeat-sentence",
      };

      setProcessingStatus("Saving results...");
      await handleUploadAnswer(answerData);
      answerSubmittedRef.current = true;

      // Complete the loading animation
      clearInterval(loadingIntervalRef.current);
      setLoadingProgress(100);
      setProcessingStatus("Processing complete.");

      setTimeout(() => {
        setProcessingComplete(true);
        setGoNext();
        setProcessingInBackground(false);
      }, 500);
    } catch (error) {
      console.error("Error processing recording:", error);
      clearInterval(loadingIntervalRef.current);
      setLoadingProgress(0);
      setProcessingStatus("Error processing. Please try again.");

      // Still submit the answer with zero scores - PTE compliant format
      try {
        const userId = localStorage.getItem("userId");
        const zeroScores = {
          content: 0, // Content (0-3 points)
          pronunciation: 0, // Pronunciation (0-5 points)
          fluency: 0, // Oral Fluency (0-5 points)
        };

        const answerData = {
          questionId: question?.questionId,
          mockTestId: question?.mocktestId,
          userId: userId,
          pteScores: zeroScores,
          taskType: "repeat-sentence",
        };

        await handleUploadAnswer(answerData);
        answerSubmittedRef.current = true;
        setProcessingComplete(true);
        setGoNext();
      } catch (submitError) {
        console.error("Error submitting zero scores:", submitError);
      }
    } finally {
      setIsProcessing(false);
      processingRef.current = false;
      onProcessingStateChange?.(false);
    }
  };

  useEffect(() => {
    // Notify parent component when recording starts/stops
    if (onSpeakingStateChange) {
      onSpeakingStateChange(isSpeaking);
    }
    onProcessingStateChange?.(isSpeaking);
  }, [isSpeaking, onProcessingStateChange, onSpeakingStateChange]);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mimeType = MediaRecorder.isTypeSupported("audio/webm")
        ? "audio/webm"
        : "audio/wav";

      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: mimeType,
      });

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorderRef.current.onstop = async () => {
        const blob = new Blob(chunksRef.current, { type: mimeType });
        processRecording(blob);
        chunksRef.current = [];
      };

      mediaRecorderRef.current.start();
      setIsSpeaking(true);

      // Notify parent component
      if (onSpeakingStateChange) {
        onSpeakingStateChange(true);
      }
    } catch (error) {
      console.error("Error starting recording:", error);
    }
  };

  const stopRecording = () => {
    if (
      mediaRecorderRef.current &&
      mediaRecorderRef.current.state === "recording"
    ) {
      mediaRecorderRef.current.stop();
      setIsSpeaking(false);
      // Set timer to zero but don't show popup
      setTimer(0);

      // Explicitly prevent the "time's up" dialog
      if (setshowPopup) {
        setshowPopup(false);
      }

      // Notify parent component that user manually completed
      onManualCompletion?.();

      // Notify parent component
      if (onSpeakingStateChange) {
        onSpeakingStateChange(false);
      }
    }
  };

  const handleEarlySubmission = () => {
    if (isSpeaking) {
      setRecordingComplete(true); // Set recording complete before stopping to prevent popup
      stopRecording();
      setCanMoveToNext(true);
    }
  };

  const handleMoveToNextQuestion = () => {
    if (processingRef.current && !answerSubmittedRef.current) {
      // Switch to background processing mode
      setProcessingInBackground(true);
      setShowBackgroundProcessingNotice(true);
      onProcessingStateChange?.(false); // Don't block UI navigation
    }

    // Always allow moving to next question
    onNext();
  };

  function blobToBase64(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onloadend = () => resolve(reader.result.split(",")[1]);
      reader.onerror = (error) => reject(error);
    });
  }

  // Handle audio ended event
  const handleAudioEnded = () => {
    setIsAudioPlaying(false);
    setOnMediaEnd(true);
    setIsListening(false);
    // Move to preparation phase
    setPhase("preparation");
    setIsPreparing(true);
    setTimer(3); // 3 seconds for preparation after listening
  };

  // Timer and Phase Management
  useEffect(() => {
    if (timer === 0) {
      if (phase === "preparation") {
        // When preparation timer ends
        setPhase("speaking");
        setIsPreparing(false);
        setTimer(15); // PTE standard: 15 seconds for recording
        startRecording();
        setIsSpeaking(true);
      } else if (phase === "speaking" && !recordingComplete) {
        // Only stop recording if it wasn't manually completed
        stopRecording();
      }
    } else if (timer > 0) {
      const interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [timer, phase, recordingComplete]);

  // Reset states when question changes
  useEffect(() => {
    setTimer(0); // Start at 0 to wait for audio
    setPhase("listening");
    setIsListening(true);
    setIsPreparing(false);
    setIsSpeaking(false);
    setIsProcessing(false);
    setProcessingComplete(false);
    setProcessingInBackground(false);
    setRecordingComplete(false);
    setAudioURL(null);
    pteScoresRef.current = null;
    setCanMoveToNext(false);
    setOnMediaEnd(false);
    answerSubmittedRef.current = false;
    setLoadingProgress(0);
    setProcessingStatus("");
    setIsAudioPlaying(false);

    if (loadingIntervalRef.current) {
      clearInterval(loadingIntervalRef.current);
    }
  }, [question]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (mediaRecorderRef.current?.state === "recording") {
        mediaRecorderRef.current.stop();
      }
      if (chunksRef.current.length > 0) {
        chunksRef.current = [];
      }
      if (loadingIntervalRef.current) {
        clearInterval(loadingIntervalRef.current);
      }
    };
  }, []);

  // Enhanced Speaking Circle UI
  const renderSpeakingCircle = () => {
    const phaseMaxTime =
      phase === "preparation" ? 3 : phase === "speaking" ? 15 : 0;
    const progress =
      phaseMaxTime === 0 ? 0 : ((phaseMaxTime - timer) / phaseMaxTime) * 100;

    // Determine colors based on phase and progress
    const getCircleColor = () => {
      if (phase === "listening") return "#673ab7"; // Listening purple
      if (phase === "preparation") return "#3f51b5"; // Preparation blue

      // For speaking phase, change color as time runs out
      if (timer > 10) return "#4caf50"; // Plenty of time - green
      if (timer > 5) return "#ff9800"; // Getting low - orange
      return "#f44336"; // Running out - red
    };

    const circleColor = getCircleColor();
    const isNearlyDone = phase === "speaking" && timer <= 5;

    return (
      <Box
        sx={{
          position: "relative",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          cursor: isSpeaking ? "pointer" : "default",
          mt: 3,
        }}
      >
        {/* Phase indicator */}
        <Paper
          elevation={1}
          sx={{
            px: 3,
            py: 1,
            mb: 3,
            borderRadius: 2,
            backgroundColor:
              phase === "listening"
                ? "#f3e5f5"
                : phase === "preparation"
                ? "#e3f2fd"
                : "#fff8e1",
            border: "1px solid",
            borderColor:
              phase === "listening"
                ? "#ce93d8"
                : phase === "preparation"
                ? "#90caf9"
                : "#ffe082",
          }}
        >
          <Typography
            variant="body1"
            sx={{ fontWeight: "medium", display: "flex", alignItems: "center" }}
          >
            {phase === "listening" ? (
              <>
                <HeadphonesIcon sx={{ mr: 1, color: "#7b1fa2" }} />
                Listening to sentence
              </>
            ) : phase === "preparation" ? (
              <>
                <TimerIcon sx={{ mr: 1, color: "#1976d2" }} />
                Get ready to repeat
              </>
            ) : (
              <>
                <MicIcon sx={{ mr: 1, color: "#d32f2f" }} />
                Speaking in progress
              </>
            )}
          </Typography>
        </Paper>

        {/* Interactive circle */}
        <Box
          sx={{
            position: "relative",
            display: "inline-flex",
            boxShadow: isSpeaking ? 2 : 0,
            borderRadius: "50%",
            p: 2,
            backgroundColor: "rgba(255, 255, 255, 0.8)",
            animation: isNearlyDone ? "pulse 1.5s infinite" : "none",
            "@keyframes pulse": {
              "0%": { boxShadow: `0 0 0 0 rgba(244, 67, 54, 0.4)` },
              "70%": { boxShadow: `0 0 0 10px rgba(244, 67, 54, 0)` },
              "100%": { boxShadow: `0 0 0 0 rgba(244, 67, 54, 0)` },
            },
          }}
          onClick={isSpeaking ? handleEarlySubmission : undefined}
        >
          <CircularProgress
            variant="determinate"
            value={
              phase === "listening" ? (isAudioPlaying ? 100 : 0) : progress
            }
            size={120}
            thickness={5}
            sx={{
              color: circleColor,
              transition: "transform 0.3s, box-shadow 0.3s",
              "&:hover": isSpeaking
                ? {
                    transform: "scale(1.05)",
                  }
                : {},
            }}
          />

          {/* Icon in center for speaking mode */}
          {isSpeaking && (
            <Box
              sx={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                zIndex: 1,
                backgroundColor: "rgba(255,255,255,0.8)",
                borderRadius: "50%",
                p: 1.5,
                boxShadow: 1,
                "&:hover": {
                  backgroundColor: "rgba(255,255,255,1)",
                  boxShadow: 2,
                },
              }}
            >
              <StopIcon color="error" sx={{ fontSize: 28 }} />
            </Box>
          )}

          {/* Icon in center for listening mode */}
          {phase === "listening" && (
            <Box
              sx={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                zIndex: 1,
                backgroundColor: "rgba(255,255,255,0.8)",
                borderRadius: "50%",
                p: 1.5,
                boxShadow: 1,
              }}
            >
              <HeadphonesIcon color="secondary" sx={{ fontSize: 28 }} />
            </Box>
          )}

          <Box
            sx={{
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              position: "absolute",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              flexDirection: "column",
            }}
          >
            {phase !== "listening" ? (
              <>
                <Typography
                  variant="h3"
                  component="div"
                  sx={{ fontWeight: "bold" }}
                >
                  {timer}
                </Typography>
                <Typography
                  variant="body2"
                  component="div"
                  color="text.secondary"
                >
                  seconds
                </Typography>
              </>
            ) : (
              <Typography
                variant="body2"
                component="div"
                sx={{ mt: 5, fontWeight: "medium" }}
                color="text.secondary"
              >
                {isAudioPlaying ? "Playing..." : "Listen"}
              </Typography>
            )}
          </Box>
        </Box>

        {/* Action hint */}
        <Typography
          variant="body2"
          sx={{
            mt: 2,
            fontWeight: "medium",
            color: isSpeaking ? "error.main" : "text.secondary",
          }}
        >
          {phase === "listening"
            ? "Listen carefully to the sentence"
            : phase === "preparation"
            ? "Prepare to repeat the sentence"
            : "Click the circle to finish recording"}
        </Typography>
      </Box>
    );
  };

  // Enhanced Processing UI with visual feedback
  const renderProcessingUI = () => {
    if (!recordingComplete) return null;

    return (
      <Paper
        elevation={2}
        sx={{
          mt: 5,
          p: 3,
          borderRadius: 2,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          maxWidth: "550px",
          mx: "auto",
          background: processingComplete ? "#f8f8f8" : "#f8f8f8",
          border: "1px solid #e0e0e0",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            mb: 3,
            gap: 2,
          }}
        >
          {processingComplete ? (
            <DoneIcon sx={{ color: "success.main", fontSize: 28 }} />
          ) : (
            <CircularProgress size={28} />
          )}
          <Typography
            variant="h6"
            sx={{
              fontWeight: 500,
              color: processingComplete ? "success.dark" : "text.primary",
            }}
          >
            {processingComplete ? "Complete" : "Processing Your Response"}
          </Typography>
        </Box>

        {!processingComplete && (
          <Box sx={{ width: "100%", mb: 3 }}>
            <Box
              sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}
            >
              <Typography variant="body2" color="text.secondary">
                Progress
              </Typography>
              <Typography variant="body2" fontWeight="medium">{`${Math.round(
                loadingProgress
              )}%`}</Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={loadingProgress}
              sx={{
                height: 6,
                borderRadius: 3,
                mb: 1,
                backgroundColor: "rgba(0,0,0,0.05)",
              }}
            />

            <Box
              sx={{
                mt: 2,
                p: 2,
                backgroundColor: "rgba(0,0,0,0.02)",
                borderRadius: 2,
                borderLeft: "3px solid",
                borderColor: "info.light",
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  display: "flex",
                  alignItems: "center",
                  fontWeight: 500,
                }}
              >
                <TimerIcon
                  fontSize="small"
                  sx={{ mr: 1, color: "info.main" }}
                />
                {processingStatus || "Analyzing your response..."}
              </Typography>
            </Box>
          </Box>
        )}

        <Box
          sx={{
            display: "flex",
            gap: 2,
            mt: processingComplete ? 1 : 0,
          }}
        >
          <Button
            variant={processingComplete ? "contained" : "outlined"}
            color="primary"
            onClick={handleMoveToNextQuestion}
            endIcon={<ArrowForwardIcon />}
            sx={{
              py: 1,
              px: 3,
              borderRadius: 2,
              fontWeight: processingComplete ? "bold" : "medium",
            }}
          >
            Next Question
          </Button>
        </Box>
      </Paper>
    );
  };

  // Audio Player UI
  const renderAudioPlayer = () => {
    return (
      <Paper
        elevation={2}
        sx={{
          p: { xs: 2, sm: 3 },
          mb: { xs: 3, sm: 4 },
          maxWidth: "750px",
          mx: "auto",
          borderRadius: 2,
          backgroundColor: "#f8f9fa",
          border: "1px solid",
          borderColor:
            phase === "listening"
              ? "#e1bee7"
              : phase === "preparation"
              ? "#dbe9f6"
              : "#f5f5f5",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            mb: 2,
          }}
        >
          <VolumeUpIcon sx={{ mr: 1.5, color: "primary.main" }} />
          <Typography variant="h6" sx={{ fontWeight: 500 }}>
            Listen and repeat the sentence
          </Typography>
        </Box>

        <Box sx={{ position: "relative", mb: 2 }}>
          <audio
            ref={audioRef}
            onPlay={() => setIsAudioPlaying(true)}
            onPause={() => setIsAudioPlaying(false)}
            onEnded={handleAudioEnded}
            autoPlay
            controls
            src={question?.media?.url}
            style={{
              width: "100%",
              borderRadius: "4px",
              boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
            }}
          />
        </Box>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            mt: 2,
            p: 1.5,
            backgroundColor: "rgba(25, 118, 210, 0.05)",
            borderRadius: 1,
          }}
        >
          {phase === "listening" ? (
            <HeadphonesIcon color="secondary" sx={{ mr: 1, fontSize: 20 }} />
          ) : phase === "preparation" ? (
            <TimerIcon color="primary" sx={{ mr: 1, fontSize: 20 }} />
          ) : (
            <MicIcon color="error" sx={{ mr: 1, fontSize: 20 }} />
          )}
          <Typography variant="body2" color="text.secondary">
            {phase === "listening"
              ? "Listen carefully to the sentence. You'll need to repeat it exactly."
              : phase === "preparation"
              ? "Mentally practice the sentence before speaking."
              : "Repeat the sentence as accurately as possible."}
          </Typography>
        </Box>
      </Paper>
    );
  };

  // Phase status indicator
  const renderPhaseStatus = () => {
    return (
      <Box sx={{ mb: 3 }}>
        <Paper
          elevation={1}
          sx={{
            display: "inline-flex",
            px: 3,
            py: 1,
            borderRadius: 20,
            backgroundColor:
              phase === "listening"
                ? "rgba(103, 58, 183, 0.08)"
                : phase === "preparation"
                ? "rgba(63, 81, 181, 0.08)"
                : "rgba(244, 67, 54, 0.08)",
          }}
        >
          <Typography
            variant="body2"
            sx={{
              fontWeight: "medium",
              color:
                phase === "listening"
                  ? "#673ab7"
                  : phase === "preparation"
                  ? "#3f51b5"
                  : "error.main",
            }}
          >
            {phase === "listening"
              ? "Listening to sentence"
              : phase === "preparation"
              ? "Preparation phase"
              : "Speaking phase"}
          </Typography>
        </Paper>
      </Box>
    );
  };

  return (
    <Box
      sx={{
        textAlign: "center",
        padding: { xs: 2, sm: 3 },
        maxWidth: "800px",
        mx: "auto",
        minHeight: "70vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Task header with visual cue for current phase */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          mb: 3,
          pb: 2,
          borderBottom: "1px solid rgba(0,0,0,0.08)",
        }}
      >
        <Typography
          variant="h5"
          sx={{
            mb: 1,
            fontWeight: 600,
            color: "text.primary",
          }}
        >
          Repeat Sentence
        </Typography>

        <Typography
          variant="subtitle1"
          color="text.secondary"
          sx={{ fontWeight: 500 }}
        >
          {question.category?.description ||
            "Listen and repeat exactly what you hear"}
        </Typography>
      </Box>

      {/* Phase status indicator */}
      {renderPhaseStatus()}

      {/* Main content area with audio and interaction components */}
      <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
        {/* Audio player */}
        {renderAudioPlayer()}

        {/* Interactive Speaking Circle */}
        {renderSpeakingCircle()}

        {/* Processing UI */}
        {recordingComplete && renderProcessingUI()}
      </Box>

      {/* Background processing notification */}
      <Snackbar
        open={showBackgroundProcessingNotice}
        autoHideDuration={6000}
        onClose={() => setShowBackgroundProcessingNotice(false)}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={() => setShowBackgroundProcessingNotice(false)}
          severity="info"
          sx={{ width: "100%" }}
        >
          Your response is being processed in the background
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default RepeatSentence;
