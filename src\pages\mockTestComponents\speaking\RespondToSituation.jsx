import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  Button,
  Typography,
  CircularProgress,
  Snackbar,
  Alert,
  Paper,
  LinearProgress,
} from "@mui/material";
import DoneIcon from "@mui/icons-material/Done";
import VolumeUpIcon from "@mui/icons-material/VolumeUp";
import MicIcon from "@mui/icons-material/Mic";
import StopIcon from "@mui/icons-material/Stop";
import TimerIcon from "@mui/icons-material/Timer";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import ForumIcon from "@mui/icons-material/Forum";
import HeadphonesIcon from "@mui/icons-material/Headphones";
import { uploadFile } from "@/api/services/controller";

function RespondToSituation({
  setAnswer,
  onNext,
  setGoNext,
  question,
  onProcessingStateChange,
  handleUploadAnswer,
  onSpeakingStateChange,
  setshowPopup,
  onManualCompletion,
}) {
  const [isPreparing, setIsPreparing] = useState(false);
  const [isListening, setIsListening] = useState(true);
  const [timer, setTimer] = useState(0); // Start with 0 seconds during listening phase
  const [phase, setPhase] = useState("listening"); // Start with listening phase
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [audioURL, setAudioURL] = useState(null);
  const [canMoveToNext, setCanMoveToNext] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingComplete, setProcessingComplete] = useState(false);
  const [processingInBackground, setProcessingInBackground] = useState(false);
  const [showBackgroundProcessingNotice, setShowBackgroundProcessingNotice] =
    useState(false);
  const [recordingComplete, setRecordingComplete] = useState(false);
  const [onMediaEnd, setOnMediaEnd] = useState(false);
  const [isAudioPlaying, setIsAudioPlaying] = useState(false);
  const [processingStatus, setProcessingStatus] = useState("");
  const [loadingProgress, setLoadingProgress] = useState(0);

  // Refs
  const audioRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const chunksRef = useRef([]);
  const processingRef = useRef(false);
  const answerSubmittedRef = useRef(false);
  const loadingIntervalRef = useRef(null);
  const pteScoresRef = useRef(null);
  const timerIntervalRef = useRef(null); // Add a ref to track the timer interval

  // Get context keywords if available
  const situationContext = question?.additionalProp1?.keywords;

  // Trim and clean the context
  const cleanedContext = situationContext
    ?.split(",")
    ?.map((keyword) => keyword.trim())
    ?.join(",");

  const analyzeSpeech = async (audioBlob) => {
    try {
      setProcessingStatus("Analyzing your response...");

      const formData = new FormData();
      formData.append(
        "audio",
        audioBlob,
        `recording.${audioBlob.type.split("/")[1]}`
      );

      // Send the prompt as context
      formData.append(
        "context",
        cleanedContext || "This is a response for situation task"
      );

      const response = await fetch(
        "https://deep-ai.up.railway.app/api/pte/retell-lecture",
        {
          method: "POST",
          body: formData,
        }
      );

      if (!response.ok) {
        throw new Error("Speech analysis failed");
      }

      const data = await response.json();
      if (!data?.speech_score?.pte_score) {
        // Return zero scores if analysis fails
        return {
          pronunciation: 0,
          fluency: 0,
          similarity_score: 0,
        };
      }

      // Extract only the scores we want to use
      const { pte_score } = data.speech_score;
      const similarityScore = data.content?.score / 100 || 0;

      // Create simplified scores object with just what we need
      const simplifiedScores = {
        pronunciation: pte_score.pronunciation,
        fluency: pte_score.fluency,
        similarity_score: similarityScore,
      };

      pteScoresRef.current = simplifiedScores;
      return simplifiedScores;
    } catch (error) {
      console.error("Speech analysis error:", error);
      // Return zero scores if there's an error
      return {
        pronunciation: 0,
        fluency: 0,
        similarity_score: 0,
      };
    }
  };

  const simulateLoadingProgress = () => {
    // Clear any existing interval
    if (loadingIntervalRef.current) {
      clearInterval(loadingIntervalRef.current);
    }

    // Reset progress
    setLoadingProgress(0);

    // Simulate progress that never quite reaches 100% until complete
    loadingIntervalRef.current = setInterval(() => {
      setLoadingProgress((prev) => {
        if (prev < 90) {
          const increment = (90 - prev) / 10;
          return prev + Math.max(0.5, increment);
        }
        return prev;
      });
    }, 300);
  };

  const processRecording = async (blob) => {
    if (processingRef.current) return;
    processingRef.current = true;
    setIsProcessing(true);

    // Only block UI navigation if we're not submitting in background
    if (!processingInBackground) {
      onProcessingStateChange?.(true);
    }

    simulateLoadingProgress();
    setRecordingComplete(true);

    try {
      const uri = URL.createObjectURL(blob);
      setAudioURL(uri);

      setProcessingStatus("Processing audio...");

      // Process speech analysis
      const scores = await analyzeSpeech(blob);
      setProcessingStatus("Uploading recording...");

      // Handle file upload
      const fileUrl = await blobToBase64(blob);
      const uploadedUrl = await uploadFile("", fileUrl, blob.type);

      const userId = localStorage.getItem("userId");

      const answerData = {
        media: {
          url: uploadedUrl,
          type: "audio",
        },
        questionId: question?.questionId,
        mockTestId: question?.mocktestId,
        userId: userId,
        pteScores: scores,
        taskType: "respond-to-situation",
      };

      setProcessingStatus("Saving results...");
      await handleUploadAnswer(answerData);
      answerSubmittedRef.current = true;

      // Complete the loading animation
      clearInterval(loadingIntervalRef.current);
      setLoadingProgress(100);
      setProcessingStatus("Processing complete.");

      setTimeout(() => {
        setProcessingComplete(true);
        setGoNext();
        setProcessingInBackground(false);
      }, 500);
    } catch (error) {
      console.error("Error processing recording:", error);
      clearInterval(loadingIntervalRef.current);
      setLoadingProgress(0);
      setProcessingStatus("Error processing. Please try again.");

      // Still submit the answer with zero scores
      try {
        const userId = localStorage.getItem("userId");
        const zeroScores = {
          pronunciation: 0,
          fluency: 0,
          similarity_score: 0,
        };

        const answerData = {
          questionId: question?.questionId,
          mockTestId: question?.mocktestId,
          userId: userId,
          pteScores: zeroScores,
          taskType: "respond-to-situation",
        };

        await handleUploadAnswer(answerData);
        answerSubmittedRef.current = true;
        setProcessingComplete(true);
        setGoNext();
      } catch (submitError) {
        console.error("Error submitting zero scores:", submitError);
      }
    } finally {
      setIsProcessing(false);
      processingRef.current = false;
      onProcessingStateChange?.(false);
    }
  };

  useEffect(() => {
    // Notify parent component when recording starts/stops
    if (onSpeakingStateChange) {
      onSpeakingStateChange(isSpeaking);
    }
    onProcessingStateChange?.(isSpeaking);
  }, [isSpeaking, onProcessingStateChange, onSpeakingStateChange]);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mimeType = MediaRecorder.isTypeSupported("audio/webm")
        ? "audio/webm"
        : "audio/wav";

      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: mimeType,
      });

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorderRef.current.onstop = async () => {
        const blob = new Blob(chunksRef.current, { type: mimeType });
        processRecording(blob);
        chunksRef.current = [];
      };

      mediaRecorderRef.current.start();
      setIsSpeaking(true);

      // Notify parent component
      if (onSpeakingStateChange) {
        onSpeakingStateChange(true);
      }
    } catch (error) {
      console.error("Error starting recording:", error);
    }
  };

  const stopRecording = () => {
    if (
      mediaRecorderRef.current &&
      mediaRecorderRef.current.state === "recording"
    ) {
      // Clear the timer interval first to stop the countdown
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
        timerIntervalRef.current = null;
      }

      // Set timer to 0
      setTimer(0);

      mediaRecorderRef.current.stop();
      setIsSpeaking(false);

      // Explicitly prevent the "time's up" dialog
      if (setshowPopup) {
        setshowPopup(false);
      }

      // Notify parent component that user manually completed
      onManualCompletion?.();

      // Notify parent component
      if (onSpeakingStateChange) {
        onSpeakingStateChange(false);
      }
    }
  };

  const handleEarlySubmission = () => {
    if (isSpeaking) {
      setRecordingComplete(true); // Set recording complete before stopping to prevent popup
      stopRecording(); // This will now clear the timer interval
      setCanMoveToNext(true);
    }
  };

  const handleMoveToNextQuestion = () => {
    if (processingRef.current && !answerSubmittedRef.current) {
      // Switch to background processing mode
      setProcessingInBackground(true);
      setShowBackgroundProcessingNotice(true);
      onProcessingStateChange?.(false); // Don't block UI navigation
    }

    // Always allow moving to next question
    onNext();
  };

  function blobToBase64(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onloadend = () => resolve(reader.result.split(",")[1]);
      reader.onerror = (error) => reject(error);
    });
  }

  // Handle audio ended event
  const handleAudioEnded = () => {
    setIsAudioPlaying(false);
    setOnMediaEnd(true);
    setIsListening(false);
    setPhase("preparation");
    setIsPreparing(true);
    setTimer(20); // Start preparation timer after audio ends
  };

  // Timer and Phase Management
  useEffect(() => {
    if (timer === 0) {
      if (phase === "preparation") {
        // When preparation timer ends
        setPhase("speaking");
        setIsPreparing(false);
        setTimer(40); // 40 seconds for speaking phase
        startRecording();
        setIsSpeaking(true);
      } else if (phase === "speaking" && !recordingComplete) {
        // Only stop recording if it wasn't manually completed
        stopRecording();
      }
    } else if (timer > 0) {
      // Store the interval reference so we can clear it when needed
      timerIntervalRef.current = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);

      // Return cleanup function
      return () => {
        if (timerIntervalRef.current) {
          clearInterval(timerIntervalRef.current);
          timerIntervalRef.current = null;
        }
      };
    }
  }, [timer, phase, recordingComplete]);

  // Reset states when question changes
  useEffect(() => {
    // Clear any existing intervals
    if (timerIntervalRef.current) {
      clearInterval(timerIntervalRef.current);
      timerIntervalRef.current = null;
    }

    if (loadingIntervalRef.current) {
      clearInterval(loadingIntervalRef.current);
      loadingIntervalRef.current = null;
    }

    setPhase("listening");
    setIsListening(true);
    setIsPreparing(false);
    setTimer(0); // No timer during listening phase
    setIsSpeaking(false);
    setIsProcessing(false);
    setProcessingComplete(false);
    setProcessingInBackground(false);
    setRecordingComplete(false);
    setAudioURL(null);
    pteScoresRef.current = null;
    setCanMoveToNext(false);
    setOnMediaEnd(false);
    answerSubmittedRef.current = false;
    setLoadingProgress(0);
    setProcessingStatus("");
    setIsAudioPlaying(false);
  }, [question]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (mediaRecorderRef.current?.state === "recording") {
        mediaRecorderRef.current.stop();
      }
      if (chunksRef.current.length > 0) {
        chunksRef.current = [];
      }
      if (loadingIntervalRef.current) {
        clearInterval(loadingIntervalRef.current);
      }
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
      }
    };
  }, []);

  // Enhanced Speaking Circle UI
  const renderSpeakingCircle = () => {
    const phaseMaxTime =
      phase === "preparation" ? 20 : phase === "speaking" ? 40 : 0;
    const progress =
      phaseMaxTime === 0 ? 0 : ((phaseMaxTime - timer) / phaseMaxTime) * 100;

    // Determine colors based on phase and progress
    const getCircleColor = () => {
      if (phase === "listening") return "#673ab7"; // Listening purple
      if (phase === "preparation") return "#3f51b5"; // Preparation blue

      // For speaking phase, change color as time runs out
      if (timer > 15) return "#4caf50"; // Plenty of time - green
      if (timer > 5) return "#ff9800"; // Getting low - orange
      return "#f44336"; // Running out - red
    };

    const circleColor = getCircleColor();
    const isNearlyDone = phase === "speaking" && timer <= 5;

    return (
      <Box
        sx={{
          position: "relative",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          cursor: isSpeaking ? "pointer" : "default",
          mt: 3,
        }}
      >
        {/* Phase indicator */}
        <Paper
          elevation={1}
          sx={{
            px: 3,
            py: 1,
            mb: 3,
            borderRadius: 2,
            backgroundColor:
              phase === "listening"
                ? "#f3e5f5"
                : phase === "preparation"
                ? "#e3f2fd"
                : "#fff8e1",
            border: "1px solid",
            borderColor:
              phase === "listening"
                ? "#ce93d8"
                : phase === "preparation"
                ? "#90caf9"
                : "#ffe082",
          }}
        >
          <Typography
            variant="body1"
            sx={{ fontWeight: "medium", display: "flex", alignItems: "center" }}
          >
            {phase === "listening" ? (
              <>
                <HeadphonesIcon sx={{ mr: 1, color: "#7b1fa2" }} />
                Listening to audio
              </>
            ) : phase === "preparation" ? (
              <>
                <TimerIcon sx={{ mr: 1, color: "#1976d2" }} />
                Preparation time
              </>
            ) : (
              <>
                <MicIcon sx={{ mr: 1, color: "#d32f2f" }} />
                Speaking in progress
              </>
            )}
          </Typography>
        </Paper>

        {/* Interactive circle */}
        <Box
          sx={{
            position: "relative",
            display: "inline-flex",
            boxShadow: isSpeaking ? 2 : 0,
            borderRadius: "50%",
            p: 2,
            backgroundColor: "rgba(255, 255, 255, 0.8)",
            animation: isNearlyDone ? "pulse 1.5s infinite" : "none",
            "@keyframes pulse": {
              "0%": { boxShadow: `0 0 0 0 rgba(244, 67, 54, 0.4)` },
              "70%": { boxShadow: `0 0 0 10px rgba(244, 67, 54, 0)` },
              "100%": { boxShadow: `0 0 0 0 rgba(244, 67, 54, 0)` },
            },
          }}
          onClick={isSpeaking ? handleEarlySubmission : undefined}
        >
          <CircularProgress
            variant="determinate"
            value={
              phase === "listening" ? (isAudioPlaying ? 100 : 0) : progress
            }
            size={120}
            thickness={5}
            sx={{
              color: circleColor,
              transition: "transform 0.3s, box-shadow 0.3s",
              "&:hover": isSpeaking
                ? {
                    transform: "scale(1.05)",
                  }
                : {},
            }}
          />

          {/* Icon in center for speaking mode */}
          {isSpeaking && (
            <Box
              sx={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                zIndex: 1,
                backgroundColor: "rgba(255,255,255,0.8)",
                borderRadius: "50%",
                p: 1.5,
                boxShadow: 1,
                "&:hover": {
                  backgroundColor: "rgba(255,255,255,1)",
                  boxShadow: 2,
                },
              }}
            >
              <StopIcon color="error" sx={{ fontSize: 28 }} />
            </Box>
          )}

          {/* Icon in center for listening mode */}
          {phase === "listening" && (
            <Box
              sx={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                zIndex: 1,
                backgroundColor: "rgba(255,255,255,0.8)",
                borderRadius: "50%",
                p: 1.5,
                boxShadow: 1,
              }}
            >
              <HeadphonesIcon color="secondary" sx={{ fontSize: 28 }} />
            </Box>
          )}

          <Box
            sx={{
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              position: "absolute",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              flexDirection: "column",
            }}
          >
            {phase !== "listening" ? (
              <>
                <Typography
                  variant="h3"
                  component="div"
                  sx={{ fontWeight: "bold" }}
                >
                  {timer}
                </Typography>
                <Typography
                  variant="body2"
                  component="div"
                  color="text.secondary"
                >
                  seconds
                </Typography>
              </>
            ) : (
              <Typography
                variant="body2"
                component="div"
                sx={{ mt: 5, fontWeight: "medium" }}
                color="text.secondary"
              >
                {isAudioPlaying ? "Playing..." : "Listen"}
              </Typography>
            )}
          </Box>
        </Box>

        {/* Action hint */}
        <Typography
          variant="body2"
          sx={{
            mt: 2,
            fontWeight: "medium",
            color: isSpeaking ? "error.main" : "text.secondary",
          }}
        >
          {phase === "listening"
            ? "Please listen to the audio before preparing"
            : phase === "preparation"
            ? "Prepare your response to the situation"
            : "Click the circle to finish recording"}
        </Typography>
      </Box>
    );
  };

  // Enhanced Processing UI with visual feedback
  const renderProcessingUI = () => {
    if (!recordingComplete) return null;

    return (
      <Paper
        elevation={2}
        sx={{
          mt: 5,
          p: 3,
          borderRadius: 2,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          maxWidth: "550px",
          mx: "auto",
          background: processingComplete ? "#f8f8f8" : "#f8f8f8",
          border: "1px solid #e0e0e0",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            mb: 3,
            gap: 2,
          }}
        >
          {processingComplete ? (
            <DoneIcon sx={{ color: "success.main", fontSize: 28 }} />
          ) : (
            <CircularProgress size={28} />
          )}
          <Typography
            variant="h6"
            sx={{
              fontWeight: 500,
              color: processingComplete ? "success.dark" : "text.primary",
            }}
          >
            {processingComplete ? "Complete" : "Processing Your Response"}
          </Typography>
        </Box>

        {!processingComplete && (
          <Box sx={{ width: "100%", mb: 3 }}>
            <Box
              sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}
            >
              <Typography variant="body2" color="text.secondary">
                Progress
              </Typography>
              <Typography variant="body2" fontWeight="medium">{`${Math.round(
                loadingProgress
              )}%`}</Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={loadingProgress}
              sx={{
                height: 6,
                borderRadius: 3,
                mb: 1,
                backgroundColor: "rgba(0,0,0,0.05)",
              }}
            />

            <Box
              sx={{
                mt: 2,
                p: 2,
                backgroundColor: "rgba(0,0,0,0.02)",
                borderRadius: 2,
                borderLeft: "3px solid",
                borderColor: "info.light",
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  display: "flex",
                  alignItems: "center",
                  fontWeight: 500,
                }}
              >
                <TimerIcon
                  fontSize="small"
                  sx={{ mr: 1, color: "info.main" }}
                />
                {processingStatus || "Analyzing your response..."}
              </Typography>
            </Box>
          </Box>
        )}

        <Box
          sx={{
            display: "flex",
            gap: 2,
            mt: processingComplete ? 1 : 0,
          }}
        >
          <Button
            variant={processingComplete ? "contained" : "outlined"}
            color="primary"
            onClick={handleMoveToNextQuestion}
            endIcon={<ArrowForwardIcon />}
            sx={{
              py: 1,
              px: 3,
              borderRadius: 2,
              fontWeight: processingComplete ? "bold" : "medium",
            }}
          >
            Next Question
          </Button>
        </Box>
      </Paper>
    );
  };

  // Content Section UI
  const renderContentSection = () => {
    return (
      <Paper
        elevation={2}
        sx={{
          p: { xs: 2, sm: 3 },
          mb: { xs: 3, sm: 4 },
          maxWidth: "750px",
          mx: "auto",
          borderRadius: 2,
          backgroundColor: "#f8f9fa",
          border: "1px solid",
          borderColor:
            phase === "listening"
              ? "#e1bee7"
              : phase === "preparation"
              ? "#dbe9f6"
              : "#f5f5f5",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            mb: 2,
          }}
        >
          <ForumIcon sx={{ mr: 1.5, color: "primary.main" }} />
          <Typography variant="h6" sx={{ fontWeight: 500 }}>
            Listen and respond to the situation
          </Typography>
        </Box>

        <Box sx={{ position: "relative", mb: 3 }}>
          <audio
            ref={audioRef}
            onPlay={() => setIsAudioPlaying(true)}
            onPause={() => setIsAudioPlaying(false)}
            onEnded={handleAudioEnded}
            autoPlay
            controls
            src={question?.media?.url}
            style={{
              width: "100%",
              borderRadius: "4px",
              boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
            }}
          />
        </Box>

        <Paper
          elevation={1}
          sx={{
            p: 2,
            backgroundColor: "white",
            borderRadius: 2,
            mb: 2,
            border: "1px solid #e0e0e0",
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              mb: 1,
              fontWeight: "medium",
              color: "#546e7a",
              display: "flex",
              alignItems: "center",
            }}
          >
            <ForumIcon fontSize="small" sx={{ mr: 1 }} />
            Situation:
          </Typography>
          <Typography variant="body1" sx={{ color: "text.primary" }}>
            {question.prompt}
          </Typography>
        </Paper>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            mt: 2,
            p: 1.5,
            backgroundColor: "rgba(25, 118, 210, 0.05)",
            borderRadius: 1,
          }}
        >
          {phase === "listening" ? (
            <HeadphonesIcon color="secondary" sx={{ mr: 1, fontSize: 20 }} />
          ) : phase === "preparation" ? (
            <VolumeUpIcon color="primary" sx={{ mr: 1, fontSize: 20 }} />
          ) : (
            <MicIcon color="primary" sx={{ mr: 1, fontSize: 20 }} />
          )}
          <Typography variant="body2" color="text.secondary">
            {phase === "listening"
              ? "Listen carefully to the audio before proceeding."
              : phase === "preparation"
              ? "Think about an appropriate response."
              : "Respond naturally to the situation described."}
          </Typography>
        </Box>
      </Paper>
    );
  };

  return (
    <Box
      sx={{
        textAlign: "center",
        padding: { xs: 2, sm: 3 },
        maxWidth: "800px",
        mx: "auto",
        minHeight: "70vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Task header with visual cue for current phase */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          mb: 3,
          pb: 2,
          borderBottom: "1px solid rgba(0,0,0,0.08)",
        }}
      >
        <Typography
          variant="h5"
          sx={{
            mb: 1,
            fontWeight: 600,
            color: "text.primary",
          }}
        >
          Respond to Situation
        </Typography>

        <Typography
          variant="subtitle1"
          color="text.secondary"
          sx={{ fontWeight: 500 }}
        >
          {question.category?.description ||
            "Listen and provide an appropriate response"}
        </Typography>
      </Box>

      {/* Main content area with audio and interaction components */}
      <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
        {/* Content section with audio and prompt */}
        {renderContentSection()}

        {/* Interactive Speaking Circle */}
        {renderSpeakingCircle()}

        {/* Processing UI */}
        {recordingComplete && renderProcessingUI()}
      </Box>

      {/* Background processing notification */}
      <Snackbar
        open={showBackgroundProcessingNotice}
        autoHideDuration={6000}
        onClose={() => setShowBackgroundProcessingNotice(false)}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={() => setShowBackgroundProcessingNotice(false)}
          severity="info"
          sx={{ width: "100%" }}
        >
          Your response is being processed in the background
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default RespondToSituation;
