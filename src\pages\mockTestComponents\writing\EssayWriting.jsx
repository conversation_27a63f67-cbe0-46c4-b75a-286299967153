import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  Button,
  Typography,
  CircularProgress,
  Snackbar,
  Alert,
  Paper,
  LinearProgress,
  TextField,
  Chip,
  Divider,
  IconButton,
  Tooltip,
} from "@mui/material";
import DoneIcon from "@mui/icons-material/Done";
import TimerIcon from "@mui/icons-material/Timer";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import EditIcon from "@mui/icons-material/Edit";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import ArticleIcon from "@mui/icons-material/Article";
import { useAuth } from "@/components/others/AuthContext";

function EssayWriting({
  setAnswer,
  onNext,
  setGoNext,
  question,
  onProcessingStateChange,
  handleUploadAnswer,
  setshowPopup,
  onManualCompletion,
  mockTestId,
  attemptNumber,
  onAnswerSubmitted,
}) {
  const [essay, setEssay] = useState("");
  const [timeLeft, setTimeLeft] = useState(1200); // PTE standard: 20 minutes for Essay Writing
  const [phase, setPhase] = useState("reading"); // reading, writing, submitted
  const [isWriting, setIsWriting] = useState(false);
  const [canMoveToNext, setCanMoveToNext] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingComplete, setProcessingComplete] = useState(false);
  const [processingInBackground, setProcessingInBackground] = useState(false);
  const [showBackgroundProcessingNotice, setShowBackgroundProcessingNotice] =
    useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [processingStatus, setProcessingStatus] = useState("");
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [wordCount, setWordCount] = useState(0);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState("");

  // Refs
  const processingRef = useRef(false);
  const answerSubmittedRef = useRef(false);
  const loadingIntervalRef = useRef(null);
  const timerRef = useRef(null);
  const { user } = useAuth();

  // Set up next functionality - enable next button when component mounts
  useEffect(() => {
    setGoNext(() => handleMoveToNextQuestion);
  }, [setGoNext]);

  // Start timer immediately when component mounts and when question changes
  useEffect(() => {
    // Clear any existing timer first
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // Start new timer if not submitted
    if (!isSubmitted) {
      console.log("Starting timer, timeLeft:", timeLeft);
      timerRef.current = setInterval(() => {
        setTimeLeft((prev) => {
          console.log("Timer tick, prev:", prev);
          if (prev <= 1) {
            clearInterval(timerRef.current);
            handleAutoSubmit();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [question?.questionId, isSubmitted]);

  // Word count function
  const getWordCount = (text) => {
    if (typeof text !== "string") return 0;
    const words = text
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0);
    return words.length;
  };

  // Validate word count (200-300 words recommended for essay)
  const isWordCountValid = () => {
    return wordCount >= 200;
  };

  const getWordCountColor = () => {
    if (wordCount < 200) return "#f44336"; // Red
    if (wordCount < 250) return "#ff9800"; // Orange
    return "#4caf50"; // Green
  };

  // Format time
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secondsRemaining = seconds % 60;
    return `${minutes}:${secondsRemaining < 10 ? "0" : ""}${secondsRemaining}`;
  };

  // Auto-submit when time runs out
  const handleAutoSubmit = () => {
    if (!isSubmitted && essay.trim()) {
      handleSubmit();
    }
  };

  // Handle text input change
  const handleInputChange = (event) => {
    if (isSubmitted) return;

    const newText = event.target.value;
    setEssay(newText);
    setWordCount(getWordCount(newText));

    // Start writing phase when user begins typing
    if (phase === "reading" && newText.trim().length > 0) {
      setPhase("writing");
      setIsWriting(true);
    }

    // Update answer data for real-time updates
    const answerData = {
      mockTestId: mockTestId || question?.mocktestId,
      questionId: question?.questionId,
      section: question?.section,
      prompt: question?.prompt,
      categoryId: question?.categoryId,
      userId: user?.userId || user?.id,
      useranswers: [{ text: newText }],
      correctAnswer: question?.options || [],
      maxScoreIfCorrect: question?.maxScore,
      type: question?.type || "essay-writing",
      answer: newText,
      wordCount: getWordCount(newText),
      attemptNumber: attemptNumber,
    };

    setAnswer(answerData);
  };

  // Simulate loading progress
  const simulateLoadingProgress = () => {
    if (loadingIntervalRef.current) {
      clearInterval(loadingIntervalRef.current);
    }

    setLoadingProgress(0);

    loadingIntervalRef.current = setInterval(() => {
      setLoadingProgress((prev) => {
        if (prev < 90) {
          const increment = (90 - prev) / 10;
          return prev + Math.max(0.5, increment);
        }
        return prev;
      });
    }, 300);
  };

  // --- AI scoring for essay writing ---
  const submitForScoring = async (essayText) => {
    const response = await fetch(
      "https://deep-ai.up.railway.app/api/essay-scoring",
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          text: essayText,
          prompt: question?.prompt || "",
          user_id: user?.userId || user?.id,
        }),
      }
    );
    if (!response.ok) throw new Error("AI scoring failed");
    return await response.json();
  };

  // Process and submit the essay using parent's handleUploadAnswer
  const processSubmission = async (essayText) => {
    if (processingRef.current) return;
    processingRef.current = true;
    setIsProcessing(true);

    if (!processingInBackground) {
      onProcessingStateChange?.(true);
    }

    simulateLoadingProgress();

    try {
      setProcessingStatus("Analyzing essay...");

      const userId = user?.userId || user?.id || localStorage.getItem("userId");

      // 1. AI scoring
      const scoreResult = await submitForScoring(essayText);

      // 2. Prepare answer data for parent's handleUploadAnswer
      setProcessingStatus("Submitting answer...");
      const answerData = {
        score: String(scoreResult.totalScore || scoreResult.score || "0"),
        answer: essayText,
        questionId: question?.questionId || "",
        userId: userId,
        section: question?.section || "writing",
        mockTestId: mockTestId || question?.mocktestId || "",
        mocktestId: mockTestId || question?.mocktestId || "",
        attemptNumber: attemptNumber,
        type: question?.type || "essay-writing",
        prompt: question?.prompt,
        categoryId: question?.categoryId,
        useranswers: [{ text: essayText }],
        correctAnswer: question?.options || [],
        maxScoreIfCorrect: question?.maxScore || 1,
        additionalProps: {
          originalPayload: {
            text: essayText,
            prompt: question?.prompt || "",
            user_id: userId,
            category_id: question?.categoryId || "",
            section: question?.section || "writing",
          },
          scoreResponse: scoreResult,
        },
        wordCount: getWordCount(essayText),
      };

      // Use parent's handleUploadAnswer instead of direct API call
      console.log("Submitting answer via parent handler:", answerData);
      const uploadResult = await handleUploadAnswer(answerData);

      if (uploadResult) {
        answerSubmittedRef.current = true;
        if (onAnswerSubmitted) onAnswerSubmitted();

        // Complete the loading animation
        clearInterval(loadingIntervalRef.current);
        setLoadingProgress(100);
        setProcessingStatus("Submission complete.");

        setTimeout(() => {
          setProcessingComplete(true);
          setCanMoveToNext(true);
          setProcessingInBackground(false);
          setSubmitSuccess(true);
        }, 500);
      } else {
        throw new Error("Failed to upload answer");
      }
    } catch (error) {
      console.error("Error processing submission:", error);
      clearInterval(loadingIntervalRef.current);
      setLoadingProgress(0);
      setProcessingStatus("Error processing. Please try again.");
      setSubmitError("Failed to submit answer. Please try again.");
    } finally {
      setIsProcessing(false);
      processingRef.current = false;
      onProcessingStateChange?.(false);
    }
  };

  // Handle submission
  const handleSubmit = async () => {
    if (essay.trim() === "" || isProcessing || isSubmitted) return;

    setIsSubmitted(true);
    setPhase("submitted");
    setSubmitError("");

    // Clear timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // Explicitly prevent the "time's up" dialog
    if (setshowPopup) {
      setshowPopup(false);
    }

    // Notify parent component that user manually completed
    onManualCompletion?.();

    await processSubmission(essay.trim());
  };

  // Handle early submission
  const handleEarlySubmission = () => {
    if (essay.trim()) {
      handleSubmit();
    }
  };

  // Handle move to next question
  const handleMoveToNextQuestion = () => {
    if (processingRef.current && !answerSubmittedRef.current) {
      setProcessingInBackground(true);
      setShowBackgroundProcessingNotice(true);
      onProcessingStateChange?.(false);
    }

    // Always allow moving to next question
    if (onNext) {
      onNext();
    }
  };

  // Handle skip functionality
  const handleSkipQuestion = async () => {
    // Submit empty/partial answer if user wants to skip
    const userId = user?.userId || user?.id || localStorage.getItem("userId");
    const skipAnswerData = {
      mockTestId: mockTestId || question?.mocktestId,
      mocktestId: mockTestId || question?.mocktestId,
      questionId: question?.questionId,
      userId: userId,
      answer: essay.trim() || "",
      wordCount: getWordCount(essay),
      type: question?.type || "essay-writing",
      section: question?.section,
      prompt: question?.prompt,
      categoryId: question?.categoryId,
      useranswers: [{ text: essay.trim() }],
      correctAnswer: question?.options || [],
      maxScoreIfCorrect: question?.maxScore || 1,
      attemptNumber: attemptNumber,
      skipped: true,
      score: "0",
      additionalProps: {
        skipped: true,
        scoreResponse: {
          totalScore: 0,
          maxScore: question?.maxScore || 15,
          content: {
            score: 0,
            maxScore: 3,
            suggestion: "Question was skipped",
          },
          form: { score: 0, maxScore: 2, suggestion: "Question was skipped" },
          grammar: {
            score: 0,
            maxScore: 2,
            suggestion: "Question was skipped",
          },
          spelling: {
            score: 0,
            maxScore: 2,
            suggestion: "Question was skipped",
          },
          vocabularyRange: {
            score: 0,
            maxScore: 2,
            suggestion: "Question was skipped",
          },
          generalLinguisticRange: {
            score: 0,
            maxScore: 2,
            suggestion: "Question was skipped",
          },
          developmentStructureCoherence: {
            score: 0,
            maxScore: 2,
            suggestion: "Question was skipped",
          },
          wordCount: getWordCount(essay),
        },
      },
    };

    try {
      await handleUploadAnswer(skipAnswerData);
      if (onAnswerSubmitted) onAnswerSubmitted();
      setIsSubmitted(true);
      onNext();
    } catch (error) {
      console.error("Error skipping question:", error);
      setSubmitError("Failed to skip question. Please try again.");
    }
  };

  // Reset states when question changes
  useEffect(() => {
    console.log("Question changed, resetting states");
    setEssay("");
    setWordCount(0);
    setTimeLeft(1200); // Reset to 20 minutes
    setPhase("reading");
    setIsWriting(false);
    setIsProcessing(false);
    setProcessingComplete(false);
    setProcessingInBackground(false);
    setIsSubmitted(false);
    setCanMoveToNext(false);
    answerSubmittedRef.current = false;
    setLoadingProgress(0);
    setProcessingStatus("");
    setSubmitSuccess(false);
    setSubmitError("");

    if (loadingIntervalRef.current) {
      clearInterval(loadingIntervalRef.current);
    }
  }, [question?.questionId]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (loadingIntervalRef.current) {
        clearInterval(loadingIntervalRef.current);
      }
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  // Phase status indicator
  const renderPhaseStatus = () => {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", mb: 2 }}>
        <Chip
          icon={
            phase === "reading" ? (
              <ArticleIcon />
            ) : phase === "writing" ? (
              <EditIcon />
            ) : (
              <CheckCircleOutlineIcon />
            )
          }
          label={
            phase === "reading"
              ? "Reading the prompt"
              : phase === "writing"
              ? "Writing essay"
              : "Submitted"
          }
          color={
            phase === "reading"
              ? "primary"
              : phase === "writing"
              ? "secondary"
              : "success"
          }
          size="medium"
          sx={{ fontWeight: 500 }}
        />
      </Box>
    );
  };

  // Timer display
  const renderTimer = () => {
    const isTimeRunningOut = timeLeft <= 300; // Last 5 minutes

    return (
      <Paper
        elevation={1}
        sx={{
          p: 2,
          mb: 3,
          backgroundColor: isTimeRunningOut ? "#ffebee" : "#e3f2fd",
          border: "1px solid",
          borderColor: isTimeRunningOut ? "#f44336" : "#2196f3",
          borderRadius: 2,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
          <TimerIcon
            sx={{ mr: 1, color: isTimeRunningOut ? "#d32f2f" : "#1976d2" }}
          />
          <Typography
            variant="body1"
            sx={{
              fontWeight: 600,
              color: isTimeRunningOut ? "#d32f2f" : "inherit",
            }}
          >
            Time Remaining: {formatTime(timeLeft)}
            {isTimeRunningOut && " ⚠️"}
          </Typography>
        </Box>
        <LinearProgress
          variant="determinate"
          value={((1200 - timeLeft) / 1200) * 100}
          sx={{
            height: 8,
            borderRadius: 4,
            "& .MuiLinearProgress-bar": {
              backgroundColor: isTimeRunningOut ? "#f44336" : "#2196f3",
            },
          }}
        />
      </Paper>
    );
  };

  // Essay prompt display
  const renderEssayPrompt = () => {
    return (
      <Paper
        elevation={2}
        sx={{
          p: { xs: 2, sm: 3 },
          mb: 3,
          borderRadius: 2,
          backgroundColor: "#f8f9fa",
          border: "1px solid #e6e8eb",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 500,
              display: "flex",
              alignItems: "center",
              color: "text.primary",
            }}
          >
            <ArticleIcon sx={{ mr: 1, color: "primary.main" }} />
            Essay Prompt
          </Typography>

          <Chip
            icon={<ArticleIcon />}
            label={question?.questionName || "Essay Writing"}
            color="primary"
            size="small"
            variant="outlined"
            sx={{ fontWeight: 500 }}
          />
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Essay prompt content */}
        <Box
          sx={{
            p: 3,
            backgroundColor: "white",
            borderRadius: 2,
            border: "1px solid #e0e0e0",
          }}
        >
          <Typography
            variant="body1"
            sx={{
              lineHeight: 1.8,
              fontSize: "1rem",
              color: "text.primary",
              textAlign: "justify",
            }}
          >
            {question?.prompt || "No prompt available"}
          </Typography>
        </Box>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            p: 1.5,
            mt: 2,
            backgroundColor: "rgba(25, 118, 210, 0.05)",
            borderRadius: 1,
          }}
        >
          <InfoOutlinedIcon color="primary" sx={{ mr: 1, fontSize: 20 }} />
          <Typography variant="body2" color="text.secondary">
            Write a well-structured essay addressing the prompt. Minimum 200
            words recommended.
          </Typography>
        </Box>
      </Paper>
    );
  };

  // Writing interface
  const renderWritingInterface = () => {
    return (
      <Paper
        elevation={1}
        sx={{
          p: { xs: 2, sm: 3 },
          mb: 3,
          borderRadius: 2,
          backgroundColor: "white",
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 500,
              display: "flex",
              alignItems: "center",
            }}
          >
            <EditIcon sx={{ mr: 1, color: "primary.main" }} />
            Write your essay (minimum 200 words)
          </Typography>

          {essay.trim() && (
            <Chip
              icon={<CheckCircleOutlineIcon />}
              label="Writing in progress"
              color="secondary"
              size="small"
              sx={{ fontWeight: 500 }}
            />
          )}
        </Box>

        <Divider sx={{ mb: 2 }} />

        <TextField
          multiline
          rows={15}
          fullWidth
          value={essay}
          onChange={handleInputChange}
          disabled={isSubmitted}
          variant="outlined"
          placeholder="Write your essay here... Develop your argument with clear structure, supporting evidence, and examples."
          sx={{
            mb: 2,
            "& .MuiInputBase-input": {
              backgroundColor: isSubmitted ? "#f5f5f5" : "white",
            },
            "& .MuiOutlinedInput-root": {
              minHeight: "400px",
              alignItems: "flex-start",
            },
          }}
        />

        {/* Word count and validation */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            flexWrap: "wrap",
            gap: 1,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <Typography
              variant="body2"
              sx={{ color: getWordCountColor(), fontWeight: 600 }}
            >
              Word Count: {wordCount}
            </Typography>
            <Chip
              label={
                isWordCountValid() ? "Sufficient Length" : "Need More Words"
              }
              color={isWordCountValid() ? "success" : "warning"}
              size="small"
            />
          </Box>
          <Typography variant="body2" sx={{ color: "#666" }}>
            Recommended: 200-300 words
          </Typography>
        </Box>
      </Paper>
    );
  };

  // Processing UI
  const renderProcessingUI = () => {
    if (!isSubmitted) return null;

    return (
      <Paper
        elevation={2}
        sx={{
          mt: 3,
          p: 3,
          borderRadius: 2,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          maxWidth: "550px",
          mx: "auto",
          background: processingComplete ? "#f8f8f8" : "#f8f8f8",
          border: "1px solid #e0e0e0",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", mb: 3, gap: 2 }}>
          {processingComplete ? (
            <DoneIcon sx={{ color: "success.main", fontSize: 28 }} />
          ) : (
            <CircularProgress size={28} />
          )}
          <Typography
            variant="h6"
            sx={{
              fontWeight: 500,
              color: processingComplete ? "success.dark" : "text.primary",
            }}
          >
            {processingComplete ? "Complete" : "Processing Your Essay"}
          </Typography>
        </Box>

        {!processingComplete && (
          <Box sx={{ width: "100%", mb: 3 }}>
            <Box
              sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}
            >
              <Typography variant="body2" color="text.secondary">
                Progress
              </Typography>
              <Typography variant="body2" fontWeight="medium">
                {`${Math.round(loadingProgress)}%`}
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={loadingProgress}
              sx={{
                height: 6,
                borderRadius: 3,
                mb: 1,
                backgroundColor: "rgba(0,0,0,0.05)",
              }}
            />

            <Box
              sx={{
                mt: 2,
                p: 2,
                backgroundColor: "rgba(0,0,0,0.02)",
                borderRadius: 2,
                borderLeft: "3px solid",
                borderColor: "info.light",
              }}
            >
              <Typography
                variant="body2"
                sx={{ display: "flex", alignItems: "center", fontWeight: 500 }}
              >
                <TimerIcon
                  fontSize="small"
                  sx={{ mr: 1, color: "info.main" }}
                />
                {processingStatus || "Analyzing your essay..."}
              </Typography>
            </Box>
          </Box>
        )}

        <Button
          variant={processingComplete ? "contained" : "outlined"}
          color="primary"
          onClick={handleMoveToNextQuestion}
          endIcon={<ArrowForwardIcon />}
          sx={{
            py: 1,
            px: 3,
            borderRadius: 2,
            fontWeight: processingComplete ? "bold" : "medium",
          }}
        >
          Next Question
        </Button>
      </Paper>
    );
  };

  return (
    <Box
      sx={{
        maxWidth: "1000px",
        mx: "auto",
        p: { xs: 2, sm: 3 },
        minHeight: "70vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Task header */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          mb: 3,
          pb: 2,
          borderBottom: "1px solid rgba(0,0,0,0.08)",
        }}
      >
        <Typography
          variant="h5"
          sx={{
            mb: 1,
            fontWeight: 600,
            color: "text.primary",
          }}
        >
          Writing: Essay Writing
        </Typography>

        <Typography
          variant="subtitle1"
          color="text.secondary"
          sx={{ fontWeight: 500, maxWidth: "800px", textAlign: "center" }}
        >
          Read the prompt carefully and write a well-structured essay presenting
          your argument. You have 20 minutes to complete your response.
        </Typography>
      </Box>

      {/* Phase status indicator */}
      {renderPhaseStatus()}

      {/* Timer */}
      {!isSubmitted && renderTimer()}

      {/* Main content area */}
      <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
        {/* Essay prompt */}
        {renderEssayPrompt()}

        {/* Writing interface */}
        {renderWritingInterface()}

        {/* Submit button and Skip option */}
        {!isSubmitted && (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              gap: 2,
              mt: 2,
              mb: 3,
            }}
          >
            <Button
              variant="contained"
              onClick={handleEarlySubmission}
              disabled={!essay.trim() || isProcessing}
              sx={{
                backgroundColor: "#140342",
                "&:hover": { backgroundColor: "#281C63" },
                "&:disabled": { backgroundColor: "#ccc" },
                px: 4,
                py: 1.5,
                fontSize: "1rem",
                fontWeight: 600,
              }}
            >
              {isProcessing ? "Submitting..." : "Submit Essay"}
            </Button>

            <Button
              variant="outlined"
              onClick={handleSkipQuestion}
              disabled={isProcessing}
              sx={{
                borderColor: "#140342",
                color: "#140342",
                "&:hover": {
                  borderColor: "#281C63",
                  backgroundColor: "rgba(20, 3, 66, 0.04)",
                },
                px: 3,
                py: 1.5,
              }}
            >
              Skip Question
            </Button>
          </Box>
        )}

        {/* Processing UI */}
        {renderProcessingUI()}
      </Box>

      {/* Success/Error Messages */}
      <Snackbar
        open={submitSuccess}
        autoHideDuration={3000}
        onClose={() => setSubmitSuccess(false)}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert severity="success" onClose={() => setSubmitSuccess(false)}>
          Essay submitted successfully! Moving to next question...
        </Alert>
      </Snackbar>

      {submitError && (
        <Alert
          severity="error"
          sx={{ mb: 2 }}
          onClose={() => setSubmitError("")}
        >
          {submitError}
        </Alert>
      )}

      {/* Background processing notification */}
      <Snackbar
        open={showBackgroundProcessingNotice}
        autoHideDuration={6000}
        onClose={() => setShowBackgroundProcessingNotice(false)}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={() => setShowBackgroundProcessingNotice(false)}
          severity="info"
          sx={{ width: "100%" }}
        >
          Your essay is being processed in the background
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default EssayWriting;
