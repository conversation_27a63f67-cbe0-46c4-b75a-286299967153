import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  TextField,
  Typography,
  Box,
  LinearProgress,
} from "@mui/material";

const WriteEmail = ({ setAnswer, onNext, setGoNext, question }) => {
  // Initial props or data for the question

  //   const questionData = {
  //     "questionId": "6783e8cc0482cc11a31cb8eb",
  //     "questionNumber": "134",
  //     "prompt": "The local animal shelter is aiming to increase awareness and support for animal welfare within the community. You are an animal welfare advocate and volunteer at the shelter, and you would like to make suggestions to the shelters management. Write an email to Mr<PERSON>, the shelter director, proposing three suggestions for ways to raise awareness and support for animal welfare. Your email should be at least 100 words.",
  //     "blanksData": [
  //         "organizing community pet adoption fairs",
  //         "creating educational programs on responsible pet ownership",
  //         "developing partnerships with local businesses for fundraising events"
  //     ],
  //     "section": "writing",
  //     "difficulty": "easy",
  //     "condition": "Your suggestions must focus on the following three themes:",
  //     "categoryId": "678392a883cd4009d9cddafc",
  //     "category": {
  //         "categoryId": "678392a883cd4009d9cddafc",
  //         "name": "Write Email",
  //         "isAiBased": true,
  //         "description": "Read a description of a situation. Then write an email about the situation. You will have 9 minutes. You should aim to write at least 100 words. Write using complete sentences.",
  //         "section": "writing"
  //     }
  // }
  const questionData = question;

  const [summary, setSummary] = useState("");
  const [timeLeft, setTimeLeft] = useState(540); // 10 minutes in seconds
  const [timerActive, setTimerActive] = useState(true);
  const [timerStarted, setTimerStarted] = useState(false);

  useEffect(() => {
    setGoNext();
  }, [setGoNext]);

  // Timer logic
  useEffect(() => {
    if (!timerActive || !timerStarted) return;

    const interval = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime === 0) {
          clearInterval(interval);
          handleSubmit();
          setTimerActive(false);
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [timerActive, timerStarted]);

  // Handle input change
  const handleInputChange = (event) => {
    const newText = event.target.value;
    setSummary(newText);

    // Prepare answer data for mock test
    const answerData = {
      mockTestId: questionData?.mocktestId,
      questionId: questionData?.questionId,
      section: questionData?.category?.section,
      prompt: questionData?.prompt,
      categoryId: questionData?.categoryId,
      userId: localStorage.getItem("userId"),
      useranswers: [{ text: newText }],
      correctAnswer: questionData?.options || [],
      maxScoreIfCorrect: questionData?.maxScore,
      type: questionData?.category?.name,
      answer: newText,
      wordCount: getWordCount(newText),
      condition: questionData?.condition,
      blanksData: questionData?.blanksData,
    };

    setAnswer(answerData);

    if (!timerStarted) {
      setTimerStarted(true);
    }
  };

  // Submit function
  const handleSubmit = async () => {
    if (summary.trim() === "") return;

    const answerData = {
      mockTestId: questionData?.mocktestId,
      questionId: questionData?.questionId,
      section: questionData?.category?.section,
      prompt: questionData?.prompt,
      categoryId: questionData?.categoryId,
      userId: localStorage.getItem("userId"),
      useranswers: [{ text: summary }],
      correctAnswer: questionData?.options || [],
      maxScoreIfCorrect: questionData?.maxScore,
      type: questionData?.category?.name,
      answer: summary,
      wordCount: getWordCount(summary),
      condition: questionData?.condition,
      blanksData: questionData?.blanksData,
      submittedAt: new Date().toISOString(),
    };

    setAnswer(answerData);
  };

  // Format time left
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secondsRemaining = seconds % 60;
    return `${minutes}:${secondsRemaining < 10 ? "0" : ""}${secondsRemaining}`;
  };
  function getWordCount(text) {
    if (typeof text !== "string") return 0; // Handle non-string input
    const words = text.trim().split(/\s+/); // Split by spaces or multiple spaces
    return words.length;
  }
  return (
    <Box sx={{ padding: 3 }}>
      <Typography variant="body2" sx={{ marginBottom: 2, fontWeight: "bold" }}>
        {questionData?.category?.description}
      </Typography>
      <Typography variant="h6" sx={{ marginBottom: 2 }}>
        {questionData.prompt}
      </Typography>
      <Typography variant="h6" sx={{ marginBottom: 2 }}>
        {questionData.condition}
      </Typography>
      <p
        style={{
          color: "#333",

          fontSize: "18px",
          lineHeight: 1.5,
          textAlign: "justify",
          marginBottom: "10px",
        }}
      >
        {questionData?.blanksData?.map((item) => (
          <>
            - {item}
            <br />
          </>
        ))}
      </p>

      {/* Timer */}
      <Box sx={{ marginBottom: 2, backgroundColor: "lightblue", padding: 2 }}>
        <Typography variant="body2">
          Time Remaining: {formatTime(timeLeft)}
        </Typography>
        <LinearProgress
          variant="determinate"
          value={((540 - timeLeft) / 540) * 100}
        />
      </Box>

      {/* Textfield for writing summary */}
      <TextField
        label="Write your summary here"
        multiline
        rows={20}
        fullWidth
        value={summary}
        onChange={handleInputChange}
        disabled={!timerActive}
        sx={{ marginBottom: 2 }}
      />
      <Typography variant="body2">
        Word Count: {summary.length == 0 ? 0 : getWordCount(summary)}
      </Typography>
      {/* Additional Instructions */}

      {/* Submit Button */}
    </Box>
  );
};

export default WriteEmail;
