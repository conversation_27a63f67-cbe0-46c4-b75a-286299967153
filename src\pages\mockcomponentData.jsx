import FillintheBlanks from "./mockTestComponents/listning/FillintheBlanks";
import HighlightIncorrectWords from "./mockTestComponents/listning/HighLightWords";
import MissingWord from "./mockTestComponents/listning/MissingWord";
import MultipleChoiceListen from "./mockTestComponents/listning/MultiChoice";
import MultipleChoiceSingleListen from "./mockTestComponents/listning/MultiSingle";
import WriteFromDiction from "./mockTestComponents/listning/WriteFromDiction";
import MultipleChoiceQuestion from "./mockTestComponents/reading/MultiMulti";
import MultipleChoiceSingle from "./mockTestComponents/reading/MultiSingle";
import ReadingWritingFillInTheBlanks from "./mockTestComponents/reading/ReadWriteFillIntheBlanks";
import ReorderParagraphs from "./mockTestComponents/reading/ReorderParagraph";
import AnswerShort from "./mockTestComponents/speaking/AnswerShort";
import DescribeImage from "./mockTestComponents/speaking/DescribeImage";
import ReadAloud from "./mockTestComponents/speaking/ReadAloud";
import RepeatSentence from "./mockTestComponents/speaking/RepeatSentence";
import RespondToSituation from "./mockTestComponents/speaking/RespondToSituation";
import RetellLecture from "./mockTestComponents/speaking/RetellLecture";
import SummarizeWrittenText from "./mockTestComponents/writing/SummerizeWrittenText";
import WriteEmail from "./mockTestComponents/writing/WriteEmail";
import EssayWriting from "./mockTestComponents/writing/EssayWriting";
import SummarizeSpokenText from "./mockTestComponents/listning/SummarizeSpokenText";
import HighlightCorrectSummary from "./mockTestComponents/listning/HighlightCorrectSummary";
import ReadingFillInBlanks from "./mockTestComponents/reading/ReadingFillInBlanks";
import ListeningFillInTheBlanks from "./mockTestComponents/listning/FillintheBlanks";

export const ComponentData = [
  {
    categoryId: "67801456e0dfdc154eff11ba",
    Component: ReadingWritingFillInTheBlanks,
  },
  {
    categoryId: "67800fc7d4e7d9147dd9525d",
    Component: MultipleChoiceSingle,
  },
  {
    categoryId: "6780113ae0dfdc154eff11b6",
    Component: MultipleChoiceQuestion,
  },
  {
    categoryId: "678011ade0dfdc154eff11b8",
    Component: ReorderParagraphs,
  },
  {
    categoryId: "67801bda382bce18e30d11ca",
    Component: MultipleChoiceListen,
  },
  {
    categoryId: "67801ec5382bce18e30d11ce",
    Component: MultipleChoiceSingleListen,
  },
  {
    categoryId: "678022c1382bce18e30d11d0",
    Component: MissingWord,
  },

  {
    categoryId: "678024ac382bce18e30d11d3",
    Component: WriteFromDiction,
  },
  {
    categoryId: "678023f5382bce18e30d11d2",
    Component: HighlightIncorrectWords,
  },
  {
    categoryId: "67894ed2102a6d6548ceec90",
    Component: ReadingFillInBlanks,
  },
  {
    categoryId: "6787cc2b486e6c04269a34e1",
    Component: ReadAloud,
  },
  {
    categoryId: "67838fcf2e266d08ba713eed",
    Component: RepeatSentence,
  },
  {
    categoryId: "678391852e266d08ba713eee",
    Component: DescribeImage,
  },

  {
    categoryId: "67b34f839ab23dd6196d1a91",
    Component: RespondToSituation,
  },
  {
    categoryId: "6783926b83cd4009d9cddafb",
    Component: SummarizeWrittenText,
  },
  {
    categoryId: "678392a883cd4009d9cddafc",
    Component: WriteEmail,
  },
  {
    categoryId: "67801d77382bce18e30d11cc",
    Component: FillintheBlanks,
  },
  {
    categoryId: "68556cb8d0001a608d09171b",
    Component: ListeningFillInTheBlanks,
  },
  {
    categoryId: "68556cb6d0001a608d09171a",
    Component: HighlightCorrectSummary,
  },
  {
    categoryId: "6783923483cd4009d9cddafa",
    Component: AnswerShort,
  },
  {
    categoryId: "67e5acdd73e32a15ba3d88c8", // Retell Lecture category ID
    Component: RetellLecture,
  },
  {
    categoryId: "67894ed2102a6d6548ceec90",
    Component: ReadingFillInBlanks,
  },
  // New Essay Writing component for mock tests
  {
    categoryId: "67d893d673e32a15ba3d88c1", // New category for Essay Writing
    Component: EssayWriting,
  },
  // New Summarize Spoken Text component for listening
  {
    categoryId: "68556cb5d0001a608d091719", // Updated category ID for Summarize Spoken Text
    Component: SummarizeSpokenText,
  },
  // Highlight Correct Summary component for listening
  {
    categoryId: "6849c969d0001a608d091537", // New category for Highlight Correct Summary
    Component: HighlightCorrectSummary,
  },
];
