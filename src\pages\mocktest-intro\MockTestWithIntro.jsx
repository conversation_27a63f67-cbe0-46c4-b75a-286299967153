import React, { useState } from "react";
import { Box, Typography, Button, Paper } from "@mui/material";
import { IntroductionFlow } from "../../components/mocktest-intro";

const MockTestWithIntro = () => {
  const [showIntroduction, setShowIntroduction] = useState(true);
  const [isReady, setIsReady] = useState(false);

  const handleIntroductionComplete = () => {
    setShowIntroduction(false);
    setIsReady(true);
  };

  const startMockTest = () => {
    // Here you would redirect to your actual mock test
    // For now, we'll just show a placeholder
    console.log("Starting mock test...");
    // You can replace this with your actual mock test navigation
    // Example: navigate('/mocktest/start');
  };

  if (showIntroduction) {
    return <IntroductionFlow onComplete={handleIntroductionComplete} />;
  }

  if (isReady) {
    return (
      <Box sx={{ minHeight: "100vh", backgroundColor: "#f5f5f5", py: 4 }}>
        <Box sx={{ maxWidth: 800, mx: "auto", px: 3 }}>
          <Paper elevation={2} sx={{ p: 4, textAlign: "center" }}>
            <Typography
              variant="h3"
              sx={{ color: "#140342", fontWeight: 700, mb: 2 }}
            >
              Ready to Start!
            </Typography>

            <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
              You've completed the setup process. You're now ready to begin your
              PTE practice test.
            </Typography>

            <Box display="flex" gap={2} justifyContent="center">
              <Button
                variant="outlined"
                onClick={() => {
                  setShowIntroduction(true);
                  setIsReady(false);
                }}
              >
                Go Back to Setup
              </Button>

              <Button
                variant="contained"
                size="large"
                onClick={startMockTest}
                sx={{
                  px: 4,
                  py: 1.5,
                  fontSize: "1.1rem",
                  backgroundColor: "#140342",
                  color: "white",
                  fontWeight: "700",
                  "&:hover": {
                    backgroundColor: "#1e0a5c",
                  },
                }}
              >
                Start PTE Practice Test
              </Button>
            </Box>
          </Paper>
        </Box>
      </Box>
    );
  }

  return null;
};

export default MockTestWithIntro;
