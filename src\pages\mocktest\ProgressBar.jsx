import React from 'react';
import { LinearProgress, Box, Typography } from '@mui/material';

function ProgressBar({ total, current }) {
  const progress = (current / total) * 100;
  return (
    <Box sx={{ marginBottom: 2 }}>
      <Typography variant="body1" gutterBottom>
        Question {current} of {total}
      </Typography>
      <LinearProgress variant="determinate" value={progress} />
    </Box>
  );
}

export default ProgressBar;
