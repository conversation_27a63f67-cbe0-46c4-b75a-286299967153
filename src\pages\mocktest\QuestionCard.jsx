import React from 'react';
import { Card, CardContent, Typography, Box } from '@mui/material';

function QuestionCard({ question }) {
  return (
    <Card variant="outlined">
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Question #{question.id} - {question.type}
        </Typography>
        <Box sx={{ padding: 2 }}>
          <Typography variant="h5">{question.text}</Typography>
        </Box>
      </CardContent>
    </Card>
  );
}

export default QuestionCard;
