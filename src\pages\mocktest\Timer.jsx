import React, { useState, useEffect } from 'react';
import { Typography } from '@mui/material';

function Timer({totalTime}) {
  const [timeLeft, setTimeLeft] = useState(totalTime); // Timer starts from 10 minutes (600 seconds)

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => (prev > 0 ? prev - 1 : 0));
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const minutes = Math.floor(timeLeft / 60);
  const seconds = timeLeft % 60;

  return (
    <Typography variant="h6" color="yellow" sx={{fontWeight:'bold'}}>
      Time Left: {minutes}:{seconds < 10 ? `0${seconds}` : seconds}
    </Typography>
  );
}

export default Timer;
