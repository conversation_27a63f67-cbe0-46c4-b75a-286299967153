import Preloader from "@/components/common/Preloader";
import HeaderAuth from "@/components/layout/headers/HeaderAuth";
import AuthImageMove from "@/components/others/AuthImageMove";
import ForgotPasswordForm from "@/components/others/ForgotPasswordForm";
import React from "react";
import MetaComponent from "@/components/common/MetaComponent";

const metadata = {
  title:
    "Forgot Password || Deep Insight Academy - Professional LMS Online Education Course ReactJS Template",
  description:
    "Reset your password for Deep Insight Academy, the most impressive LMS template for online courses, education and LMS platforms.",
};

export default function ForgotPasswordPage() {
  return (
    <div className="main-content  ">
      <MetaComponent meta={metadata} />
      <Preloader />

      <HeaderAuth />
      <div className="content-wrapper js-content-wrapper overflow-hidden">
        <section className="form-page js-mouse-move-container">
          <AuthImageMove />
          <ForgotPasswordForm />
        </section>
      </div>
    </div>
  );
}
