import Preloader from "@/components/common/Preloader";
import HeaderAuth from "@/components/layout/headers/HeaderAuth";
import AuthImageMove from "@/components/others/AuthImageMove";
import SignupVerifyOtpForm from "@/components/others/SignupVerifyOtpForm";
import React from "react";
import MetaComponent from "@/components/common/MetaComponent";

const metadata = {
  title:
    "Verify Email || Deep Insight Academy - Professional LMS Online Education Course ReactJS Template",
  description:
    "Verify your email for Deep Insight Academy, the most impressive LMS template for online courses, education and LMS platforms.",
};

export default function SignupVerifyOtpPage() {
  return (
    <div className="main-content  ">
      <MetaComponent meta={metadata} />
      <Preloader />

      <HeaderAuth />
      <div className="content-wrapper js-content-wrapper overflow-hidden">
        <section className="form-page js-mouse-move-container">
          <AuthImageMove />
          <SignupVerifyOtpForm />
        </section>
      </div>
    </div>
  );
}
