import Preloader from "@/components/common/Preloader";
import HeaderAuth from "@/components/layout/headers/HeaderAuth";
import AuthImageMove from "@/components/others/AuthImageMove";
import VerifyOtpForm from "@/components/others/VerifyOtpForm";
import React from "react";
import MetaComponent from "@/components/common/MetaComponent";

const metadata = {
  title:
    "Verify OTP || Deep Insight Academy - Professional LMS Online Education Course ReactJS Template",
  description:
    "Verify your OTP for Deep Insight Academy, the most impressive LMS template for online courses, education and LMS platforms.",
};

export default function VerifyOtpPage() {
  return (
    <div className="main-content  ">
      <MetaComponent meta={metadata} />
      <Preloader />

      <HeaderAuth />
      <div className="content-wrapper js-content-wrapper overflow-hidden">
        <section className="form-page js-mouse-move-container">
          <AuthImageMove />
          <VerifyOtpForm />
        </section>
      </div>
    </div>
  );
}
