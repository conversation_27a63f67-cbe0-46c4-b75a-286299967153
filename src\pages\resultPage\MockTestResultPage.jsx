import { useState } from "react";
import { useSearchParams } from "react-router-dom";

import MetaComponent from "@/components/common/MetaComponent";

import Wrapper from "../Wrapper";
import Header from "@/components/layout/headers/Header";
import SubCategoryModal from "@/components/SubCategoryModal";
import { useAuth } from "@/components/others/AuthContext";
import ResultComponent from "@/components/speech-ace-component/MockTestResult";

const metadata = {
  title: "Test Result - Deep Insight Academy",
  description:
    "View your test results and performance analysis on Deep Insight Academy.",
};

export default function ResultPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [subCategoryData, setSubCategoryData] = useState([]);
  const [searchParams] = useSearchParams();
  const { user } = useAuth();

  const handleSubCategoryOpen = (data) => {
    setSubCategoryData(data);
    setIsModalOpen(true);
  };

  // Get mockTestId and attempt number from URL parameters or localStorage
  const mockTestId =
    searchParams.get("testId") ||
    localStorage.getItem("completedTestId") ||
    "6849c969d0001a608d091534";
  const attemptNumber = searchParams.get("attempt")
    ? parseInt(searchParams.get("attempt"))
    : parseInt(localStorage.getItem("completedAttemptNumber") || "1");

  return (
    <Wrapper>
      <MetaComponent meta={metadata} />
      <Header onSubCategoryOpen={handleSubCategoryOpen} />;
      {isModalOpen && (
        <SubCategoryModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          data={subCategoryData}
        />
      )}
      <ResultComponent
        mockTestId={mockTestId}
        userId={`${user?.id}`}
        attemptNumber={attemptNumber}
      />
    </Wrapper>
  );
}
