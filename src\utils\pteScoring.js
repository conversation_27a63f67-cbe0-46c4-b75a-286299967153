// PTE Academic Cross-Sectional Scoring Utility
// Based on official PTE scoring methodology

export class PTECrossSectionalScoring {
  constructor() {
    this.skillScores = {
      speaking: 0,
      writing: 0,
      reading: 0,
      listening: 0,
    };

    // Cross-sectional weightage matrix based on PTE scoring rules
    this.crossSectionalMatrix = {
      // Speaking & Writing Section
      "read-aloud": { speaking: 0.6, reading: 0.4 },
      "read aloud": { speaking: 0.6, reading: 0.4 },
      "repeat-sentence": { speaking: 0.7, listening: 0.3 },
      "repeat sentence": { speaking: 0.7, listening: 0.3 },
      "describe-image": { speaking: 1.0 },
      "describe image": { speaking: 1.0 },
      "re-tell-lecture": { speaking: 0.5, listening: 0.5 },
      "retell-lecture": { speaking: 0.5, listening: 0.5 },
      "retell lecture": { speaking: 0.5, listening: 0.5 },
      "answer-short-question": { speaking: 0.8, listening: 0.2 },
      "answer short question": { speaking: 0.8, listening: 0.2 },
      "summarize-written-text": { writing: 0.6, reading: 0.4 },
      "summarize written text": { writing: 0.6, reading: 0.4 },
      "write-essay": { writing: 1.0 },
      "essay-writing": { writing: 1.0 },
      "essay writing": { writing: 1.0 },
      "write-email": { writing: 1.0 },
      "write email": { writing: 1.0 },
      "respond-to-situation": { speaking: 1.0 },
      "respond to situation": { speaking: 1.0 },

      // Reading Section
      "reading-writing-fill-in-the-blanks": { reading: 0.5, writing: 0.5 },
      "reading & writing：fill in the blanks": { reading: 0.5, writing: 0.5 },
      "reading-&-writing-fill-in-the-blanks": { reading: 0.5, writing: 0.5 },
      "multiple-choice-multiple": { reading: 1.0 },
      "multiple choice (multiple)": { reading: 1.0 },
      "re-order-paragraphs": { reading: 1.0 },
      "reorder-paragraphs": { reading: 1.0 },
      "re-order paragraphs": { reading: 1.0 },
      "reading-fill-in-blanks": { reading: 1.0 },
      "reading：fill in the blanks": { reading: 1.0 },
      "reading:fill in the blanks": { reading: 1.0 },
      "reading fill in the blanks": { reading: 1.0 },
      "fill-the-blanks": { reading: 1.0 },
      "multiple-choice-single": { reading: 1.0 },
      "multiple choice (single)": { reading: 1.0 },
      "multi-single": { reading: 1.0 },
      "multi-multi": { reading: 1.0 },

      // Listening Section
      "summarize-spoken-text": { listening: 0.6, writing: 0.4 },
      "summarize spoken text": { listening: 0.6, writing: 0.4 },
      "listening-fill-in-blanks": { listening: 0.7, writing: 0.3 },
      "listening：fill in the blanks": { listening: 0.7, writing: 0.3 },
      "listening:fill in the blanks": { listening: 0.7, writing: 0.3 },
      "fill-in-blanks": { listening: 0.7, writing: 0.3 },
      "fill-in-the-blanks": { listening: 0.7, writing: 0.3 },
      "highlight-correct-summary": { listening: 1.0 },
      "highlight correct summary": { listening: 1.0 },
      "select-missing-word": { listening: 1.0 },
      "select missing word": { listening: 1.0 },
      "highlight-incorrect-words": { listening: 0.8, reading: 0.2 },
      "highlight incorrect words": { listening: 0.8, reading: 0.2 },
      "highlight-words": { listening: 0.8, reading: 0.2 },
      "write-from-dictation": { listening: 0.6, writing: 0.4 },
      "write from dictation": { listening: 0.6, writing: 0.4 },
      "multi-choice": { listening: 1.0 },
      "missing-word": { listening: 1.0 },
    };
  }

  // Calculate cross-sectional score for a question
  calculateCrossSectionalScore(questionType, rawScore, maxScore = 1) {
    const normalizedType = questionType?.toLowerCase().trim();
    const weightage = this.crossSectionalMatrix[normalizedType];

    if (!weightage) {
      console.warn(
        `Unknown question type for cross-sectional scoring: ${questionType}`
      );
      return { speaking: 0, writing: 0, reading: 0, listening: 0 };
    }

    // Convert raw score to percentage
    const scorePercentage = maxScore > 0 ? rawScore / maxScore : 0;

    // Apply cross-sectional weightage and scale to 90
    const crossSectionalScores = {};
    Object.keys(this.skillScores).forEach((skill) => {
      crossSectionalScores[skill] =
        (weightage[skill] || 0) * scorePercentage * 90;
    });

    return crossSectionalScores;
  }

  // Aggregate scores across all questions
  aggregateScores(questionResults) {
    const aggregatedScores = {
      speaking: { total: 0, count: 0, questions: [] },
      writing: { total: 0, count: 0, questions: [] },
      reading: { total: 0, count: 0, questions: [] },
      listening: { total: 0, count: 0, questions: [] },
    };

    questionResults.forEach((result) => {
      const crossSectionalScores = this.calculateCrossSectionalScore(
        result.type,
        result.score,
        result.maxScore
      );

      Object.keys(aggregatedScores).forEach((skill) => {
        if (crossSectionalScores[skill] > 0) {
          aggregatedScores[skill].total += crossSectionalScores[skill];
          aggregatedScores[skill].count += 1;
          aggregatedScores[skill].questions.push({
            type: result.type,
            score: crossSectionalScores[skill],
            contribution:
              this.crossSectionalMatrix[result.type?.toLowerCase().trim()]?.[
                skill
              ] || 0,
          });
        }
      });
    });

    // Calculate final scores (average of contributing questions)
    const finalScores = {};
    Object.keys(aggregatedScores).forEach((skill) => {
      finalScores[skill] =
        aggregatedScores[skill].count > 0
          ? Math.round(
              aggregatedScores[skill].total / aggregatedScores[skill].count
            )
          : 0;
    });

    return finalScores;
  }

  // Calculate overall score using PTE methodology (not simple average)
  calculateOverallScore(skillScores) {
    // Filter out skills with zero scores
    const validScores = Object.values(skillScores).filter((score) => score > 0);

    if (validScores.length === 0) return 0;

    // PTE uses complex algorithmic weighting, but for mock tests we'll use weighted average
    // with slight emphasis on speaking and writing as they're tested first
    const weights = {
      speaking: 1.1,
      writing: 1.1,
      reading: 1.0,
      listening: 1.0,
    };

    let weightedSum = 0;
    let totalWeight = 0;

    Object.keys(skillScores).forEach((skill) => {
      if (skillScores[skill] > 0) {
        weightedSum += skillScores[skill] * weights[skill];
        totalWeight += weights[skill];
      }
    });

    const overallScore = totalWeight > 0 ? weightedSum / totalWeight : 0;

    // Ensure score is within 10-90 range as per PTE standards
    return Math.max(10, Math.min(90, Math.round(overallScore)));
  }

  // Get question type weightage information
  getQuestionTypeWeightage(questionType) {
    const normalizedType = questionType?.toLowerCase().trim();
    return this.crossSectionalMatrix[normalizedType] || {};
  }

  // Validate if a question type is supported
  isQuestionTypeSupported(questionType) {
    const normalizedType = questionType?.toLowerCase().trim();
    return normalizedType in this.crossSectionalMatrix;
  }
}

// Export a singleton instance for convenience
export const pteScoring = new PTECrossSectionalScoring();

// Helper function to format question type names
export const formatQuestionTypeName = (type) => {
  return type
    ?.split(/[ -]/)
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
};

// Helper function to get skill color for UI
export const getSkillColor = (skill) => {
  const colors = {
    speaking: "#FF6B6B",
    writing: "#4ECDC4",
    reading: "#45B7D1",
    listening: "#96CEB4",
  };
  return colors[skill] || "#140342";
};
