// Test file for PTE Cross-Sectional Scoring
import { PTECrossSectionalScoring } from './pteScoring.js';

// Test the PTE scoring system
function testPTEScoring() {
  console.log('Testing PTE Cross-Sectional Scoring System...');
  
  const pteScoring = new PTECrossSectionalScoring();
  
  // Test sample questions
  const sampleQuestions = [
    {
      type: "read-aloud",
      score: 80,
      maxScore: 100,
      section: "speaking"
    },
    {
      type: "repeat-sentence", 
      score: 75,
      maxScore: 100,
      section: "speaking"
    },
    {
      type: "summarize-written-text",
      score: 8,
      maxScore: 10,
      section: "writing"
    },
    {
      type: "reading & writing：fill in the blanks",
      score: 6,
      maxScore: 8,
      section: "reading"
    },
    {
      type: "write from dictation",
      score: 7,
      maxScore: 10,
      section: "listening"
    },
    {
      type: "highlight incorrect words",
      score: 4,
      maxScore: 5,
      section: "listening"
    }
  ];

  console.log('\nSample Questions:');
  sampleQuestions.forEach((q, i) => {
    console.log(`${i + 1}. ${q.type}: ${q.score}/${q.maxScore} (${Math.round(q.score/q.maxScore*100)}%)`);
  });

  // Test individual question scoring
  console.log('\nCross-Sectional Contributions:');
  sampleQuestions.forEach((question, i) => {
    const crossSectionalScores = pteScoring.calculateCrossSectionalScore(
      question.type, 
      question.score, 
      question.maxScore
    );
    
    console.log(`\n${i + 1}. ${question.type}:`);
    Object.keys(crossSectionalScores).forEach(skill => {
      if (crossSectionalScores[skill] > 0) {
        console.log(`   ${skill}: ${crossSectionalScores[skill].toFixed(1)}`);
      }
    });
  });

  // Test aggregated scoring
  const aggregatedScores = pteScoring.aggregateScores(sampleQuestions);
  console.log('\nAggregated Cross-Sectional Scores:');
  Object.keys(aggregatedScores).forEach(skill => {
    console.log(`${skill}: ${aggregatedScores[skill]}`);
  });

  // Test overall score calculation
  const overallScore = pteScoring.calculateOverallScore(aggregatedScores);
  console.log(`\nOverall Score: ${overallScore}`);

  // Test question type support
  console.log('\nTesting Question Type Support:');
  const testTypes = [
    "read-aloud",
    "unknown-type",
    "essay-writing",
    "multiple choice (single)",
    "write from dictation"
  ];
  
  testTypes.forEach(type => {
    const isSupported = pteScoring.isQuestionTypeSupported(type);
    const weightage = pteScoring.getQuestionTypeWeightage(type);
    console.log(`${type}: ${isSupported ? 'Supported' : 'Not Supported'} - ${JSON.stringify(weightage)}`);
  });

  console.log('\nPTE Scoring Test Complete!');
}

// Run the test if this file is executed directly
if (typeof window === 'undefined') {
  // Node.js environment
  testPTEScoring();
} else {
  // Browser environment - expose test function
  window.testPTEScoring = testPTEScoring;
}

export { testPTEScoring };
