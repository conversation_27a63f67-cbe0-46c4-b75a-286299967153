import axios from "axios";
import { server } from "../api/services/server";

// Constants
const MAX_FREE_ANSWERS = 5;
const API_BASE_URL = server.uri.slice(0, -1); // Remove trailing slash

/**
 * Get user's current answer count from the API
 * @param {string} userId - User ID
 * @returns {Promise<number>} - Current answer count
 */
export const getUserAnswerCount = async (userId) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/answers/count?where={"userId":"${userId}"}`
    );
    return response.data.count || 0;
  } catch (error) {
    console.error("Error fetching answer count:", error);
    return 0;
  }
};

/**
 * Update user's answer count by incrementing by 1
 * @param {string} userId - User ID
 * @returns {Promise<boolean>} - Success status
 */
export const incrementUserAnswerCount = async (userId) => {
  try {
    // First get current user data
    const userResponse = await axios.get(`${API_BASE_URL}/users/${userId}`);
    const userData = userResponse.data;

    // Increment answer count
    const newAnswerCount = (userData.answerCount || 0) + 1;

    // Update user data with new answer count
    await axios.put(`${API_BASE_URL}/users/${userId}`, {
      ...userData,
      answerCount: newAnswerCount,
    });

    return true;
  } catch (error) {
    console.error("Error updating answer count:", error);
    return false;
  }
};

/**
 * Check if user can submit answers (has subscription or within free limit)
 * @param {string} userId - User ID
 * @returns {Promise<object>} - Object containing canSubmit status and user data
 */
export const checkSubmissionEligibility = async (userId) => {
  try {
    // Get user data
    const userResponse = await axios.get(`${API_BASE_URL}/users/${userId}`);
    const userData = userResponse.data;

    // Check if user has active subscription
    const hasActiveSubscription =
      userData.membershipStatus === "active" ||
      (userData.membershipStatus === "cancelled" &&
        new Date(userData.membershipEndAt) > new Date());

    // If user has active subscription, they can always submit
    if (hasActiveSubscription) {
      return {
        canSubmit: true,
        canViewAnswer: true,
        reason: "active_subscription",
        userData: userData,
        answersRemaining: -1, // Unlimited
      };
    }

    // If no active subscription, check answer count
    const currentAnswerCount = userData.answerCount || 0;
    const canSubmit = currentAnswerCount < MAX_FREE_ANSWERS;
    const answersRemaining = Math.max(0, MAX_FREE_ANSWERS - currentAnswerCount);

    return {
      canSubmit: canSubmit,
      canViewAnswer: canSubmit, // Can only view answers if can submit
      reason: canSubmit ? "within_free_limit" : "free_limit_exceeded",
      userData: userData,
      answersRemaining: answersRemaining,
      currentAnswerCount: currentAnswerCount,
    };
  } catch (error) {
    console.error("Error checking submission eligibility:", error);
    return {
      canSubmit: false,
      canViewAnswer: false,
      reason: "error",
      userData: null,
      answersRemaining: 0,
    };
  }
};

/**
 * Process answer submission with subscription check
 * @param {string} userId - User ID
 * @param {function} onSuccess - Callback when answer can be submitted
 * @param {function} onSubscriptionRequired - Callback when subscription is required
 * @returns {Promise<object>} - Result of the submission check
 */
export const processAnswerSubmission = async (
  userId,
  onSuccess,
  onSubscriptionRequired
) => {
  try {
    const eligibility = await checkSubmissionEligibility(userId);

    if (eligibility.canSubmit) {
      // User can submit - increment count and call success callback
      const incrementSuccess = await incrementUserAnswerCount(userId);

      if (incrementSuccess) {
        if (onSuccess) {
          await onSuccess(eligibility);
        }
        return {
          success: true,
          message: "Answer submitted successfully",
          eligibility: eligibility,
        };
      } else {
        return {
          success: false,
          message: "Failed to update answer count",
          eligibility: eligibility,
        };
      }
    } else {
      // User needs subscription - call subscription required callback
      if (onSubscriptionRequired) {
        onSubscriptionRequired(eligibility);
      }
      return {
        success: false,
        message: "Subscription required",
        eligibility: eligibility,
      };
    }
  } catch (error) {
    console.error("Error processing answer submission:", error);
    return {
      success: false,
      message: "Error processing submission",
      eligibility: null,
    };
  }
};

/**
 * Get user ID from various sources (localStorage, user object, etc.)
 * @param {object} user - User object from auth context (optional)
 * @returns {string} - User ID
 */
export const getUserId = (user = null) => {
  // Try different sources for user ID
  const sources = [
    localStorage.getItem("isUserId"),
    localStorage.getItem("userId"),
    user?.id,
    user?.userId,
  ];

  for (const source of sources) {
    if (source) {
      try {
        // Handle JSON stringified IDs
        if (typeof source === "string" && source.startsWith("{")) {
          const parsed = JSON.parse(source);
          return parsed.id || parsed.userId || source;
        }
        return source;
      } catch (e) {
        // If parsing fails, return as is
        return source;
      }
    }
  }

  return null;
};

/**
 * Format remaining answers message for UI
 * @param {number} answersRemaining - Number of answers remaining
 * @returns {string} - Formatted message
 */
export const formatRemainingAnswersMessage = (answersRemaining) => {
  if (answersRemaining === -1) {
    return "Unlimited answers with your subscription";
  } else if (answersRemaining === 0) {
    return "You've used all your free answers. Subscribe to continue.";
  } else if (answersRemaining === 1) {
    return "1 free answer remaining";
  } else {
    return `${answersRemaining} free answers remaining`;
  }
};

export { MAX_FREE_ANSWERS };
